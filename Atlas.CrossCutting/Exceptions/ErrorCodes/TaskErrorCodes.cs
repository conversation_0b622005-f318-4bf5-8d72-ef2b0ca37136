namespace Atlas.CrossCutting.Exceptions.ErrorCodes
{
    public static class TaskErrorCodes
    {
        /// <summary>
        /// Error creating a task
        /// </summary>
        public const string TaskCreateError = "TASK_CREATE_ERROR";
        public const string TaskGetError = "TASK_GET_ERROR";
        public const string TaskUpdateError = "TASK_UPDATE_ERROR";
        public const string TaskDeleteError = "TASK_DELETE_ERROR";
        public const string TaskResendEmailError = "TASK_RESEND_EMAIL_ERROR";
        
        public const string TaskSetStepByNameError = "TASK_SET_STEP_BY_NAME_ERROR";
        public const string TaskSetNextStepError = "TASK_SET_NEXT_STEP_ERROR";
        public const string TaskSetPreviousStepError = "TASK_SET_PREVIOUS_STEP_ERROR";
    }
}
