namespace Atlas.CrossCutting.Exceptions.ErrorCodes
{
    public static class MeetingErrorCodes
    {
        /// <summary>
        /// Error creating a recurring meeting
        /// </summary>
        public const string MeetingRecurringCreateError = "MEETING_RECURRING_CREATE_ERROR";

        /// <summary>
        /// Error getting a meeting
        /// </summary>
        public const string MeetingGetError = "MEETING_GET_ERROR";

        /// <summary>
        /// Error creating a meeting
        /// </summary>
        public const string MeetingCreateError = "MEETING_CREATE_ERROR";
    }
}
