namespace Atlas.CrossCutting.Exceptions.ErrorCodes
{
    public static class WorkgroupErrorCodes
    {
        /// <summary>
        /// Error creating a workgroup.
        /// </summary>
        public const string WorkgroupCreateError = "WORKGROUP_CREATE_ERROR";

        /// <summary>
        /// Error creating a workgroup column.
        /// </summary>
        public const string WorkgroupColumnCreateError = "WORKGROUP_COLUMN_CREATE_ERROR";

        /// <summary>
        /// Error updating a workgroup task list.
        /// </summary>
        public const string WorkgroupTaskListUpdateError = "WORKGROUP_TASKLIST_UPDATE_ERROR";

        /// <summary>
        /// Error deleting a workgroup task list.
        /// </summary>
        public const string WorkgroupTaskListDeleteError = "WORKGROUP_TASKLIST_DELETE_ERROR";

        /// <summary>
        /// Error reordering workgroup task lists.
        /// </summary>
        public const string WorkgroupTaskListReorderError = "WORKGROUP_TASKLIST_REORDER_ERROR";
        
        public const string WorkgroupInvalid = "INVALID_WORKGROUP";

        /// <summary>
        /// Error reordering tasks.
        /// </summary>
        public const string TaskReorderError = "TASK_REORDER_ERROR";
        
        /// <summary>
        /// Error reordering workgroup tasks.
        /// </summary>
        public const string WorkgroupTaskReorderError = "WORKGROUP_TASK_REORDER_ERROR";
    }
}
