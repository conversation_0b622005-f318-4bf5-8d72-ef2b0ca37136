namespace Atlas.CrossCutting.Exceptions.ErrorCodes
{
    public static class AnnouncementErrorCodes
    {
        /// <summary>
        /// Error getting annoucement details
        /// </summary>
        public const string AnnouncementDetailsGetError = "ANNOUNCEMENT_DETAILS_GET_ERROR";
        
        /// <summary>
        /// Error creating an announcement 
        /// </summary>
        public const string AnnouncementCreationError = "ANNOUNCEMENT_CREATION_ERROR";

        /// <summary>
        /// Error changing Read property
        /// </summary>
        public const string AnnouncementSetReadError = "ANNOUNCEMENT_SET_READ_ERROR";

        /// <summary>
        /// Error: user is not a subscriber   
        /// </summary>
        public const string AnnouncementUserIsNotSubscriber = "USER_IS_NOT_A_SUBSCRIBER";
    }
}
