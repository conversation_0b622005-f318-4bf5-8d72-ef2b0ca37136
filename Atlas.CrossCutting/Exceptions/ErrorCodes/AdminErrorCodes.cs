namespace Atlas.CrossCutting.Exceptions.ErrorCodes
{
    public static class AdminErrorCodes
    {
        public const string UserInfoGetError = "USERINFO_GET_ERROR";
        public const string UploadClientLogoError = "UPLOAD_CLIENT_LOGO_ERROR";
        public const string UploadClientDetailsError = "UNEXPECTED_ERROR_WHILE_UPDATING_CLIENT";
        public const string ClientUsersGetError = "CLIENT_USERS_GET_ERROR";
    }
}