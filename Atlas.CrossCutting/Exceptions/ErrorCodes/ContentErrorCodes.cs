namespace Atlas.CrossCutting.Exceptions.ErrorCodes
{
    public static class ContentErrorCodes
    {
        /// <summary>
        /// Error retrieving user mention
        /// </summary>
        public const string WorkgroupDataOfContentError = "WORKGROUP_DATA_OF_CONTENT_ERROR";
        /// <summary>
        /// Error retrieving content identifiers
        /// </summary>
        public const string InvalidContent = "INVALID_CONTENT";
        
        /// <summary>
        /// Error deleteing content
        /// </summary>
        public const string ErrorDeletingContent = "ERROR_DELETING_CONTENT";

        /// <summary>
        /// Error restoring content
        /// </summary>
        public const string ErrorRestoringContent = "ERROR_RESTORING_CONTENT";
    }
}
