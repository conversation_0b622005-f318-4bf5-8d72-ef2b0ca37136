namespace Atlas.CrossCutting.Exceptions.ErrorCodes
{
    public class KnowledgeBaseErrorCodes
    {
        public const string InvalidFolderName = "INVALID_FOLDER_NAME";

        public static string FolderNotFound = "FOLDER_NOT_FOUND";

        public static string MustInformContentId = "MUST_INFORM_CONTENT_UUID";
        
        public static string MustInformContentAttachmentId = "MUST_INFORM_CONTENT_ATTACHMENT_ID";
        
        public static string MustInformContentType = "MUST_INFORM_CONTENT_TYPE";

        public static string MustInformWorkgroupOrContentUuid = "MUST_INFORM_WORKGROUP_OR_CONTENT_UUID";

        public static string HasOpenBackupRequest = "HAS_OPEN_BACKUP_REQUEST";
        
        public static string NotAllowed = "NOT_ALLOWED";
        
        public static string InvalidEntity = "INVALID_ENTITY";
    }
}
