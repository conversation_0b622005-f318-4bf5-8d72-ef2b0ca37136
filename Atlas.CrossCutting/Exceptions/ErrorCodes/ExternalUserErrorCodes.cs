namespace Atlas.CrossCutting.Exceptions.ErrorCodes
{
    public static class ExternalUserErrorCodes
    {
        /// <summary>
        /// Error retrieving external users data
        /// </summary>
        public const string GetExternalUsersError = "GET_EXTERNAL_USERS_ERROR";

        /// <summary>
        /// Error saving new external user
        /// </summary>
        public const string SaveExternalUserError = "SAVE_EXTERNAL_USER_ERROR";

        /// <summary>
        /// Error updating external user data
        /// </summary>
        public const string UpdateExternalUserError = "UPDATE_EXTERNAL_USER_ERROR";

        /// <summary>
        /// Error deleting external user
        /// </summary>
        public const string DeleteExternalUserError = "DELETE_EXTERNAL_USER_ERROR";

        /// <summary>
        /// Error verifying external user key
        /// </summary>
        public const string VerifyExternalKeyError = "VERIFY_EXTERNAL_KEY_ERROR";

        /// <summary>
        /// Error sending two-factor authentication to external user
        /// </summary>
        public const string SendExternalUser2FAError = "SEND_EXTERNAL_USER_2FA_ERROR";

        /// <summary>
        /// Error validating external user two-factor authentication
        /// </summary>
        public const string ValidateExternalUser2FAError = "VALIDATE_EXTERNAL_USER_2FA_ERROR";
    }
}