namespace Atlas.CrossCutting.Exceptions.ErrorCodes
{
    public static class AuthErrorCodes
    {
        public const string InvalidSession = "INVALID_SESSION";
        public const string InvalidToken = "INVALID_TOKEN";
        public const string InvalidOperation = "INVALID_OPERATION";
        public const string ExpiredToken = "EXPIRED_TOKEN";
        public const string UserBlocked = "USER_BLOCKED";
        public const string AbusePrevention = "ABUSE_PREVENTION";
        public const string UnexpectedError = "AUTH_UNEXPECTED_ERROR";
        public const string SessionKeyMissing = "SESSION_KEY_CLAIM_NULL";
        public const string InvalidConfirmationCode = "INVALID_CODE";
        public const string ServiceNotFound = "SERVICE_NOT_FOUND";
        public const string AuthenticationCodeNotInformed = "AUTHENTICATION_CODE_NOT_INFORMED";
        public const string UserIntegrationNotRemoved = "USER_INTEGRATION_NOT_REMOVED";
    }
}
