namespace Atlas.CrossCutting.Exceptions.ErrorCodes
{
    public static class GeneralErrorCodes
    {
        /// <summary>
        /// Access is denied
        /// </summary>
        public const string AccessDenied = "ACCESS_DENIED";

        /// <summary>
        /// Invalid operation performed
        /// </summary>
        public const string InvalidOperation = "INVALID_OPERATION";

        /// <summary>
        /// Unexpected internal server error
        /// </summary>
        public const string UnexpectedError = "UNEXPECTED_ERROR";

        /// <summary>
        /// Restricted action
        /// </summary>
        public const string Restricted = "RESTRICTED";

        /// <summary>
        /// Information type is invalid. Allowed are "email", "mobile"
        /// </summary>
        public const string InfoTypeInvalid = "INFO_TYPE_INVALID";

        /// <summary>
        /// Feature is unauthorized for the current user
        /// </summary>
        public const string FeatureUnauthorized = "FEATURE_UNAUTHORIZED";
        
        /// <summary>
        /// Resource not found
        /// </summary>
        public const string NotFound = "NOT_FOUND";
    }
}
