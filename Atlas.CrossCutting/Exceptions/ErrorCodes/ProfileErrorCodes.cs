namespace Atlas.CrossCutting.Exceptions.ErrorCodes
{
    public static class ProfileErrorCodes
    {
        /// <summary>
        /// The profile is not valid
        /// </summary>
        public const string ProfileNotValid = "PROFILE_NOT_VALID";

        /// <summary>
        /// Error retrieving sensitive profile information
        /// </summary>
        public const string ProfileSensitiveInfoError = "PROFILE_SENSITIVE_INFO_ERROR";

        /// <summary>
        /// Error deleting profile file
        /// </summary>
        public const string ProfileDeleteFileError = "PROFILE_DELETE_FILE_ERROR";

        /// <summary>
        /// Profile picture exceeds the maximum allowed size
        /// </summary>
        public const string ProfilePictureSizeExceeded = "PROFILE_PICTURE_SIZE_EXCEEDED";

        /// <summary>
        /// Profile file is empty
        /// </summary>
        public const string ProfileFileEmpty = "PROFILE_FILE_EMPTY";

        /// <summary>
        /// Error uploading profile file
        /// </summary>
        public const string ProfileUploadFileError = "PROFILE_UPLOAD_FILE_ERROR";
        /// <summary>
        /// Profile user name size exceeded the maximum allowed length
        /// </summary>
        public const string ProfileUserNameSizeExceeded = "PROFILE_USER_NAME_SIZE_EXCEEDED";
        /// <summary>
        /// Profile user bio size exceeded the maximum allowed length
        /// </summary>
        public const string ProfileUserBioSizeExceeded = "PROFILE_USER_BIO_SIZE_EXCEEDED";
        /// <summary>
        /// Profile user update data error
        /// </summary>
        public const string ProfileUserUpdateDataError = "PROFILE_USER_UPDATE_DATA_ERROR";

        /// <summary>
        /// Block or Unblock user error
        /// </summary>
        public const string ProfileBlockUserError = "PROFILE_BLOCK_USER_ERROR";

        /// <summary>
        /// Represents the error code for a failed user approval operation in the system.
        /// </summary>
        /// <remarks>This constant can be used to identify unexpected errors related to user approval processes.</remarks>
        public const string ProfileApproveUserError = "PROFILE_APPROVE_USER_ERROR";

        /// <summary>
        /// Represents the error code for a failed operation to set a user as administrator in the system.
        /// </summary>
        /// <remarks>This constant can be used to identify unexpected errors related to the process of assigning administrator privileges to a user.</remarks>
        public const string ProfileSetUserAdminError = "PROFILE_SET_USER_ADMIN_ERROR";

        /// <summary>
        /// Error deleting a user (archive).
        /// </summary>
        public const string ProfileDeleteArchiveError = "PROFILE_DELETE_ARCHIVE_ERROR";

        /// <summary>
        /// Error resending invite
        /// </summary>
        public const string ProfileResendInviteError = "PROFILE_RESEND_INVITE_ERROR";

        /// <summary>
        /// Error getting user sessions CSV
        /// </summary>
        public const string ProfileGetUserSessionsError = "PROFILE_GET_USER_SESSIONS_ERROR";
    }
}
