namespace Atlas.CrossCutting.Exceptions.ErrorCodes
{
    public static class ExternalDocumentRequestErrorCodes
    {
        /// <summary>
        /// Error retrieving external document request data
        /// </summary>
        public const string GetExternalDocumentRequestError = "EXTERNAL_DOCUMENT_REQUEST_GET_ERROR";

        /// <summary>
        /// Error sending external document request
        /// </summary>
        public const string SendExternalDocumentRequestError = "EXTERNAL_DOCUMENT_REQUEST_SEND_ERROR";

        /// <summary>
        /// Error resending external document request
        /// </summary>
        public const string ResendExternalDocumentRequestError = "EXTERNAL_DOCUMENT_REQUEST_RESEND_ERROR";

        /// <summary>
        /// Error deleting external document request
        /// </summary>
        public const string DeleteExternalDocumentRequestError = "EXTERNAL_DOCUMENT_REQUEST_DELETE_ERROR";

        /// <summary>
        /// Error retrieving external document for upload
        /// </summary>
        public const string GetExternalDocumentForUploadError = "EXTERNAL_DOCUMENT_FOR_UPLOAD_GET_ERROR";

        /// <summary>
        /// Error uploading document by external user
        /// </summary>
        public const string UploadDocumentByExternalUserError = "EXTERNAL_DOCUMENT_UPLOAD_ERROR";

        /// <summary>
        /// Error finishing external document upload process
        /// </summary>
        public const string FinishExternalDocumentUploadError = "EXTERNAL_DOCUMENT_UPLOAD_FINISH_ERROR";

        /// <summary>
        /// Error deleting attachment by external user
        /// </summary>
        public const string DeleteAttachmentByExternalUserError = "EXTERNAL_ATTACHMENT_DELETE_ERROR";
    }
}