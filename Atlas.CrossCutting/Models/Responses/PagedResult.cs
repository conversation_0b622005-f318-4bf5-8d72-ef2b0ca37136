using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Atlas.CrossCutting.Models.Responses
{
    public class PagedResult<T>
        where T : class
    {
        [JsonPropertyName("currentPage")]
        public int CurrentPage { get; set; }

        [JsonPropertyName("pageSize")]
        public int PageSize { get; set; }

        [JsonPropertyName("totalItems")]
        public int TotalItems { get; set; }

        [JsonPropertyName("totalPages")]
        public int TotalPages => PageSize > 0 ? (int)Math.Ceiling((double)TotalItems / PageSize) : 0;

        [JsonPropertyName("items")]
        public List<T> Items { get; set; }

        public PagedResult()
        {
            Items = new List<T>();
            CurrentPage = 1;
            PageSize = 10;
            TotalItems = 0;
        }

        public PagedResult(List<T> items, int currentPage, int pageSize, int totalItems)
        {
            Items = items ?? new List<T>();
            CurrentPage = currentPage;
            PageSize = pageSize;
            TotalItems = totalItems;
        }
    }
}
