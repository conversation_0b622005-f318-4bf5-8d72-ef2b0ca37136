using System.Text.Json.Serialization;

namespace Atlas.CrossCutting.Models.Responses
{
    public class ApiResponse<T>
    {
        [JsonPropertyName("success")]
        public bool Success { get; set; }

        [JsonPropertyName("message")]
        public string Message { get; set; }

        [JsonPropertyName("data")]
        public T? Data { get; set; }

        public ApiResponse(bool success, string message, T data)
        {
            Success = success;
            Message = message;
            Data = data;
        }

        public static ApiResponse<T> CreateSuccess(T data, string message = "Operation completed successfully")
        {
            return new ApiResponse<T>(true, message, data);
        }

        public static ApiResponse<T> CreateError(string message, T data = default)
        {
            return new ApiResponse<T>(false, message, data);
        }
    }

    public class ApiResponse
    {
        [JsonPropertyName("success")]
        public bool Success { get; set; }

        [JsonPropertyName("message")]
        public string Message { get; set; }

        public ApiResponse(bool success, string message)
        {
            Success = success;
            Message = message;
        }

        public static ApiResponse CreateSuccess(string message = "Operation completed successfully")
        {
            return new ApiResponse(true, message);
        }

        public static ApiResponse CreateError(string message)
        {
            return new ApiResponse(false, message);
        }
    }
}
