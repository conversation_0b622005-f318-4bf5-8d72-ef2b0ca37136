using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace Atlas.CrossCutting.Models.FeatureFlag
{
    /// <summary>
    /// Response model for the feature flag API
    /// </summary>
    public class FeatureFlagApiResponse
    {
        [JsonProperty("success")]
        public bool Success { get; set; }

        [JsonProperty("message")]
        public string Message { get; set; }

        [JsonProperty("data")]
        public FeatureFlagData Data { get; set; }
    }

    /// <summary>
    /// Data model for the feature flag
    /// </summary>
    public class FeatureFlagData
    {
        [JsonProperty("isEnabled")]
        public bool IsEnabled { get; set; }
    }

    /// <summary>
    /// Response model for multiple tenants feature flag API
    /// </summary>
    public class FeatureFlagMultiTenantApiResponse
    {
        [JsonProperty("success")]
        public bool Success { get; set; }

        [JsonProperty("message")]
        public string Message { get; set; }

        [JsonProperty("data")]
        public FeatureFlagMultiTenantData Data { get; set; }
    }

    /// <summary>
    /// Data model for multiple tenants feature flags
    /// </summary>
    public class FeatureFlagMultiTenantData
    {
        [JsonProperty("tenants")]
        public List<TenantFeatureFlags> Tenants { get; set; }
    }

    /// <summary>
    /// Model for tenant feature flags
    /// </summary>
    public class TenantFeatureFlags
    {
        [JsonProperty("tenantId")]
        public string TenantId { get; set; }

        [JsonProperty("flags")]
        public Dictionary<string, bool> Flags { get; set; }
    }

    /// <summary>
    /// Detailed response model for feature flag validation with complete tenant information
    /// </summary>
    public class FeatureFlagDetailedResponse
    {
        /// <summary>
        /// Overall success of the validation request
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Response message
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Dictionary of feature keys and their overall enabled status (true if enabled for ANY tenant)
        /// </summary>
        public Dictionary<string, bool> FeatureResults { get; set; }

        /// <summary>
        /// Detailed results per tenant
        /// </summary>
        public List<TenantFeatureFlagDetail> TenantDetails { get; set; }

        /// <summary>
        /// List of client IDs that were processed
        /// </summary>
        public int[] ProcessedClientIds { get; set; }

        /// <summary>
        /// List of tenant IDs (UUIDs) that were successfully resolved
        /// </summary>
        public Guid[] ResolvedTenantIds { get; set; }
    }

    /// <summary>
    /// Detailed feature flag information for a specific tenant
    /// </summary>
    public class TenantFeatureFlagDetail
    {
        /// <summary>
        /// The tenant ID (UUID)
        /// </summary>
        public Guid TenantId { get; set; }

        /// <summary>
        /// The corresponding client ID
        /// </summary>
        public int ClientId { get; set; }

        /// <summary>
        /// Dictionary of feature flags and their status for this tenant
        /// </summary>
        public Dictionary<string, bool> Features { get; set; }
    }
}