namespace Atlas.CrossCutting.UAC.Workgroup
{
    public class WorkgroupUAC
    {
        public bool isOwner { get; set; }
        public bool manage_owners { get; set; }
        public bool create_meeting { get; set; }
        public bool manage_members { get; set; }

        public bool delete { get; set; }
        public bool archive { get; set; }
        public bool update { get; set; }
        public bool copy { get; set; }
        public bool backup { get; set; }
        public bool export { get; set; }
        public bool create_announcement { get; set; }


        public bool kb_create { get; set; }
        public bool kb_delete { get; set; }
        public bool kb_update { get; set; }

        public bool kb_category_add { get; set; }
        public bool kb_category_reorder { get; set; }
        public bool kb_category_update { get; set; }
        public bool kb_category_delete { get; set; }

        public bool kb_article_add { get; set; }
        public bool kbAddDirectory { get; set; }
        public bool kbDeleteDirectory { get; set; }
        public bool kbRequestESignature { get; set; }
        public bool createForm { get; set; }
        public bool corporateBook { get; set; }
    }
}
