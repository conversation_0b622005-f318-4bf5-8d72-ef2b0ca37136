using System.Linq;
using System.Security;

namespace Atlas.CrossCutting.AppEnums
{
    public static class ContentStatuses
    {
        public static class ResolutionStatus
        {
            public const string OPEN = "OPEN";
            public const string CLOSED = "CLOSED";
            public const string AWAITING_VOTES = "AWAITING_VOTES";
            public const string CANCELLED = "CANCELLED";
            public const string USER_VOTED = "USER_VOTED";
        }

        public static class ResolutionTypes
        {
            public const string APPROVAL = "APPROVAL";
            public const string CUSTOM = "CUSTOM";
        }

        public static class ContentMeetingStatus
        {
            public const string OPEN = "OPEN";
            public const string CLOSED = "CLOSED";
            public const string CANCELLED = "CANCELLED";
            public const string READY = "READY";
            public const string MEETINGCONCLUDED = "MEETINGCONCLUDED";
            public const string DRAFT = "DRAFT";
        }

        public static readonly string[] ReadyClosedMeetingStatus = { ContentMeetingStatus.READY, ContentMeetingStatus.CLOSED };
        public static readonly string[] OpenDraftMeetingStatus = { ContentMeetingStatus.OPEN, ContentMeetingStatus.DRAFT };
        public static readonly string[] CancellableMeetingStatus = { ContentMeetingStatus.OPEN, ContentMeetingStatus.READY };
        public static readonly string[] DeletableMeetingStatus = { ContentMeetingStatus.CANCELLED, ContentMeetingStatus.OPEN };
        public static readonly string[] MeetingRestrictedParticipantsStatus = { ContentMeetingStatus.CLOSED, ContentMeetingStatus.CANCELLED };
        public static readonly string[] DurationMeetingStatus = [.. ReadyClosedMeetingStatus, ContentMeetingStatus.MEETINGCONCLUDED];
        public static readonly string[] FinalResolutionStatus =
{
            ResolutionStatus.CLOSED,
            ResolutionStatus.CANCELLED
        };

        public static void ValidateContentStatus(string type, string status, string parentStatus = null)
        {
            var isChildContent = ContentTypes.MeetingChildContents.Contains(type);
            var isContentStatusCancelled = status == ContentMeetingStatus.CANCELLED || status == ResolutionStatus.CANCELLED;
            var isParentCancelled = parentStatus == ContentMeetingStatus.CANCELLED;

            if ((isChildContent && isParentCancelled) || isContentStatusCancelled)
                throw new SecurityException("UNAUTHORIZED_CONTENT");
        }
    }
}