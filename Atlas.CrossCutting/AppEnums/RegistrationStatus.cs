using System.Collections.Generic;
using System.Linq;
using System.Reflection;
namespace Atlas.CrossCutting.AppEnums
{
    public static class RegistrationStatus
    {
        //descriptive purposes only. Not final values
        public const string INVITED = "INVITED";
        public const string CODE_VERIFIED = "CODE_VERIFIED"; // email
        public const string TOKEN_VERIFIED = "TOKEN_VERIFIED"; // token (sms, whatsapp, voice)
        public const string REGISTERED = "REGISTERED";
        public const string STATUS_RETRIEVED = "STATUS_RETRIEVED";
        // additional states for checking the status of the code and token
        public const string SENDING_CODE = "SENDING_CODE"; // email
        public const string SENDING_TOKEN = "SENDING_TOKEN"; // token (sms, whatsapp, voice)
        private static readonly Dictionary<string, List<string>> validTransitions = new Dictionary<string, List<string>>
        {
            // FLOW WITH CODE IN THE ROUTE
            { INVITED, new List<string> { STATUS_RETRIEVED, SENDING_CODE } },
            { STATUS_RETRIEVED, new List<string> { TOKEN_VERIFIED, SENDING_TOKEN } },
            { TOKEN_VERIFIED, new List<string> { REGISTERED, STATUS_RETRIEVED } },
            { SENDING_CODE, new List<string> { CODE_VERIFIED } },
            { CODE_VERIFIED, new List<string> { STATUS_RETRIEVED } },
            { SENDING_TOKEN, new List<string> { TOKEN_VERIFIED } },
        };
        public static bool IsValidTransition(string currentStatus, string nextStatus)
        {
            if (string.IsNullOrWhiteSpace(currentStatus) || string.IsNullOrWhiteSpace(nextStatus) || !IsValidStatus(nextStatus))
            {
                return false;
            }
            return validTransitions.TryGetValue(currentStatus, out var validNextStatuses) && validNextStatuses.Contains(nextStatus);
        }
        public static bool IsValidStatus(string status)
        {
            // TODO: check if we can have performance issues with this approach
            // Get all public constant fields of type string in this class
            var validStatuses = typeof(RegistrationStatus)
                .GetFields(BindingFlags.Public | BindingFlags.Static | BindingFlags.DeclaredOnly)
                .Where(field => field.FieldType == typeof(string))
                .Select(field => field.GetValue(null).ToString())
                .ToList();
            return validStatuses.Contains(status);
        }
    }
}
