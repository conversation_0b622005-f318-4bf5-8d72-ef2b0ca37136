using System.Collections.Generic;

namespace Atlas.CrossCutting.AppEnums
{
    public static class ActivityProcessors
    {
        public static class ActivityTypes
        {
            public const string Created = "CREATED";
            public const string Updated = "UPDATED";
            public const string Deleted = "DELETED";
            public const string StatusReady = "STATUS_READY";
            public const string StatusClosed = "STATUS_CLOSED";
            public const string CommentAdd = "COMMENT_ADD";
            public const string CommentDelete = "COMMENT_DELETE";
            public const string CommentUndelete = "COMMENT_UNDELETE";
            public const string AttachmentDelete = "ATTACHMENT_DELETE";
            public const string AttachmentUndelete = "ATTACHMENT_UNDELETE";
            public const string BluebookView = "BLUEBOOK_VIEW";
            public const string SubscribersUpdated = "SUBSCRIBER_UPDATE";
            public const string SubscribersAdded = "SUBSCRIBER_ADD";
            public const string AttachmentView = "ATTACHMENT_VIEW";
            public const string PollReportView = "POLL_REPORT_VIEW";
            public const string ESignatureRequested = "ESIGNATURE_REQUEST";
            public const string ESignatureClosed = "ESIGNATURE_REQUEST_CLOSED";
            public const string DSignatureRequested = "DIGITAL_SIGNATURE_REQUEST";
            public const string DSignatureClosed = "DIGITAL_SIGNATURE_REQUEST_CLOSED";
            public const string SignatureCancelled = "SIGNATURE_REQUEST_CANCELLED";
            public const string Published = "STATUS_PUBLISHED";
            public const string Unpublished = "STATUS_UNPUBLISHED";
        }

        public static class ContentTypes
        {
            public const string Meeting = "MEETING";
            public const string Poll = "POLL";
            public const string MeetingAgendaItem = "MEETINGAGENDAITEM";
            public const string MeetingMinute = "MEETINGMINUTE";
        }

        private static readonly HashSet<(string ActivityType, string ContentType)> _map = new HashSet<(string, string)>
        {
            // Meeting
            (ActivityTypes.Created, ContentTypes.Meeting),
            (ActivityTypes.Updated, ContentTypes.Meeting),
            (ActivityTypes.Deleted, ContentTypes.Meeting),
            (ActivityTypes.StatusReady, ContentTypes.Meeting),
            (ActivityTypes.StatusClosed, ContentTypes.Meeting),
            (ActivityTypes.SubscribersUpdated, ContentTypes.Meeting),
            (ActivityTypes.SubscribersAdded, ContentTypes.Meeting),
            (ActivityTypes.BluebookView, ContentTypes.Meeting),
            // Poll
            (ActivityTypes.Created, ContentTypes.Poll),
            (ActivityTypes.Updated, ContentTypes.Poll),
            (ActivityTypes.Deleted, ContentTypes.Poll),
            (ActivityTypes.AttachmentView, ContentTypes.Poll),
            (ActivityTypes.PollReportView, ContentTypes.Poll),
            (ActivityTypes.CommentAdd, ContentTypes.Poll),
            (ActivityTypes.CommentDelete, ContentTypes.Poll),
            (ActivityTypes.CommentUndelete, ContentTypes.Poll),
            (ActivityTypes.AttachmentUndelete, ContentTypes.Poll),
            (ActivityTypes.AttachmentDelete, ContentTypes.Poll),
            // Agenda
            (ActivityTypes.Created, ContentTypes.MeetingAgendaItem),
            (ActivityTypes.Updated, ContentTypes.MeetingAgendaItem),
            (ActivityTypes.Deleted, ContentTypes.MeetingAgendaItem),
            (ActivityTypes.AttachmentView, ContentTypes.MeetingAgendaItem),
            (ActivityTypes.AttachmentDelete, ContentTypes.MeetingAgendaItem),
            (ActivityTypes.AttachmentUndelete, ContentTypes.MeetingAgendaItem),
            (ActivityTypes.SubscribersUpdated, ContentTypes.MeetingAgendaItem),
            (ActivityTypes.CommentAdd, ContentTypes.MeetingAgendaItem),
            (ActivityTypes.CommentDelete, ContentTypes.MeetingAgendaItem),
            (ActivityTypes.CommentUndelete, ContentTypes.MeetingAgendaItem),
            // Minute
            (ActivityTypes.Published, ContentTypes.MeetingMinute),
            (ActivityTypes.Unpublished, ContentTypes.MeetingMinute),
            (ActivityTypes.ESignatureRequested, ContentTypes.MeetingMinute),
            (ActivityTypes.ESignatureClosed, ContentTypes.MeetingMinute),
            (ActivityTypes.DSignatureRequested, ContentTypes.MeetingMinute),
            (ActivityTypes.DSignatureClosed, ContentTypes.MeetingMinute),
            (ActivityTypes.SignatureCancelled, ContentTypes.MeetingMinute)
        };

        public static bool IsValid(string activityType, string contentType)
        {
            return _map.Contains((activityType.ToUpperInvariant(), contentType.ToUpperInvariant()));
        }
    }
}
