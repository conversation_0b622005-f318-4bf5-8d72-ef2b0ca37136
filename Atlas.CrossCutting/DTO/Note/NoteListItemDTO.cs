using System;

namespace Atlas.CrossCutting.DTO.Note
{
    public sealed class NoteListItemDTO
    {
        public int NoteId { get; set; }
        public int ContentId { get; set; }
        public Guid ContentUuid { get; set; }
        public int WorkgroupId { get; set; }
        public int ClientId { get; set; }
        public string Title { get; set; } = "";
        public string TextPreview { get; set; } = "";
        public DateTime LastUpdate { get; set; }
        public string Status { get; set; } = "";
        public int TotalCount { get; set; }
    }
}
