using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Atlas.CrossCutting.DTO.KnowledgeBase
{
    public class KnowledgeBaseRecycleBinDTO
    {
        public int contentId { get; set; }
        public Guid contentUuid { get; set; }
        public int clientId { get; set; }
        public Guid clientUuid { get; set; }
        public int workgroupId { get; set; }
        public string workgroupType { get; set; }
        public bool? deleted { get; set; }
        [NotMapped]
        public string name { get; set; }
        public string type { get; set; }
        public int level { get; set; }
        public int? parentContentId { get; set; }
        public string breadCrumb { get; set; }
        public string levelTree { get; set; }
        public string folderName { get; set; }
        public string fileName { get; set; }
        [NotMapped]
        public string trashPath { get; set; }
        public DateTime createDate { get; set; }
        public DateTime lastUpdate { get; set; }
        public int createUser { get; set; }
    }
}
