using System;

namespace Atlas.CrossCutting.DTO.ContentPermission
{
    /// <summary>
    /// DTO básico para retorno de ContentPermission
    /// </summary>
    public class ContentPermissionDTO
    {
        public int contentId { get; set; }

        public Guid contentUuid { get; set; }

        public int userId { get; set; }

        public bool allowed { get; set; }

        public DateTime createDate { get; set; }

        public int createUser { get; set; }

        public string creationRule { get; set; }

        public string userName { get; set; }

        public string userEmail { get; set; }

        public string createUserName { get; set; }

        public string profilePic { get; set; } = string.Empty;
    }
}
