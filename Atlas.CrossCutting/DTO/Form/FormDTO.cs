using Atlas.CrossCutting.Enums;
using System;
using System.Collections.Generic;

namespace Atlas.CrossCutting.DTO.Form
{
    public class FormDTO
    {
        public string title { get; set; }
        public EnFormStatus status { get; set; }
        public bool isOwner { get; set; }
        public bool anonymousAnswer { get; set; }
        public Guid contentUuid { get; set; }

        public int totalAnswers { get; set; }
        public DateTime? publishDate { get; set; }
        public DateTime expirationDate { get; set; }
        public ICollection<FormSectionDTO> formSections { get; set; }
        public int totalQuestions { get; set; }
    }
}
