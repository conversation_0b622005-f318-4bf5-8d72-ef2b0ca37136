using System.Collections.Generic;

namespace Atlas.CrossCutting.DTO.Form
{
    public class FormSectionDTO
    {
        public int formSectionId { get; set; }

        public string title { get; set; }
        public short sectionOrder { get; set; }
        public bool deleted { get; set; }
        public int formId { get; set; }

        public ICollection<FormQuestionDTO> formQuestions { get; set; }
    }
}
