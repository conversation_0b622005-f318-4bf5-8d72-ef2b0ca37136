using System.Collections.Generic;

namespace Atlas.CrossCutting.DTO.Form
{
    public class FormQuestionOptionDTO
    {
        public int formQuestionOptionId { get; set; }
        public string title { get; set; }
        public string description { get; set; }
        public bool hidden { get; set; }
        public short optionOrder { get; set; }
        public int formQuestionId { get; set; }
        public bool requiredJustification { get; set; } = false;
        public ICollection<FormQuestionResponseDTO> formQuestionResponses { get; set; }
    }
}
