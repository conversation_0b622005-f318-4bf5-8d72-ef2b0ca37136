namespace Atlas.CrossCutting.DTO.Resolution
{
    namespace Atlas.CrossCutting.DTO.Resolution
    {
        /// <summary>
        /// Main data section containing all template variables
        /// </summary>
        public class ResolutionReportDataDto
        {
            // Header variables
            public string ClientName { get; set; }
            public string MeetingTitle { get; set; }
            public string WorkgroupName { get; set; }
            public string WorkgroupColor { get; set; }

            // Title section variables
            public ResolutionReportDateDto DeliberationDate { get; set; }
            public string DeliberationName { get; set; }
            public string ConclusionDate { get; set; }
            public string VotersList { get; set; }
            public string PollType { get; set; }
            public bool DisclosureVotes { get; set; }

            // Electronic votes section
            public ResolutionReportElectronicVoteDto[] ElectronicVotes { get; set; }

            // Voting options with counts
            public ResolutionReportVotingOptionDto[] VotingOptions { get; set; }

            // Comments section
            public ResolutionReportCommentDto[] Comments { get; set; }

            // Footer variables
            public string Email { get; set; }
            public string ReportDate { get; set; }
            public string ReportTime { get; set; }
        }

        /// <summary>
        /// Date information for deliberation
        /// </summary>
        public class ResolutionReportDateDto
        {
            public string Day { get; set; }
            public string Month { get; set; }
            public string Year { get; set; }
        }

        /// <summary>
        /// Electronic vote information
        /// </summary>
        public class ResolutionReportElectronicVoteDto
        {
            public string VoterInitials { get; set; }
            public string VoterAvatarUrl { get; set; }
            public string VoterName { get; set; }
            public string VoteDate { get; set; }
            public string VoteOption { get; set; }
            public string VoteDeletedDate { get; set; }
        }

        /// <summary>
        /// Voting option with count information
        /// </summary>
        public class ResolutionReportVotingOptionDto
        {
            public string OptionName { get; set; }
            public int VoteCount { get; set; }
        }

        /// <summary>
        /// Comment information for the resolution
        /// </summary>
        public class ResolutionReportCommentDto
        {
            public string AuthorName { get; set; }
            public string CommentDate { get; set; }
            public string CommentText { get; set; }
        }
    }
}
