using System;

namespace Atlas.CrossCutting.DTO.Resolution
{
    public class ResolutionCreateDto
    {
        public int WorkgroupId { get; set; }
        public string ResolutionType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime DueDate { get; set; } = DateTime.UtcNow;
        public bool? NotifyVoters { get; set; } = true;
        public bool? Hidden { get; set; } = false;
        public int[] ContentSubscriber { get; set; } = Array.Empty<int>(); //Can otherwise be an array of a custom type that represents the ContentSubscriber entity in the database
                                                                           //but for now I don't see the need, as for now the client just passes the subscriber's user ids.
        public CustomOptions[] Options { get; set; } = Array.Empty<CustomOptions>();
        public string Title { get; set; } = string.Empty;
        public int? ParentContentId { get; set; } = null;
        public Guid? ParentContentUuId { get; set; }
        public bool? VoteDisclosureOnMinute { get; set; } = true;
        public int? Duration { get; set; } = null;
    }

    public class CustomOptions
    {
        public string title { get; set; } = string.Empty; //might need sanitization
    }
}
