using System;
using System.Collections.Generic;

namespace Atlas.CrossCutting.DTO.ExternalDocumentRequest
{
    public class ExternalDocumentRequestDTO
    {
        public int externalDocumentRequestId { get; set; } = 0;
        public int contentId { get; set; }
        public Guid contentUuId { get; set; }
        public List<ExternalUserDTO> ExternalUsers { get; set; } = new();
        public string requesterInstructions { get; set; }
    }
}
