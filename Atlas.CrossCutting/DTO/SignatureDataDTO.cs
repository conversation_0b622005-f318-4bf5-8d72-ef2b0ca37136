using System;

namespace Atlas.CrossCutting.DTO
{
    public class SignatureDataDTO
    {

        public int contentSignatureRequestId { get; set; }

        public int userId { get; set; }

        public int contentId { get; set; }

        public DateTime signDate { get; set; }

        public string ipAddress { get; set; }

        public string location { get; set; }

        public string signer<PERSON>ey { get; set; }

        public bool rejected { get; set; }

        public string message { get; set; }

        public DateTime? rejectionDate { get; set; }

    }
}
