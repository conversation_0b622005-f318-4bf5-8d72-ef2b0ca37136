namespace Atlas.CrossCutting.DTO.UserInboxItem
{
    /// <summary>
    /// DTO para representar usuários bloqueados encontrados pelo método GetAllBlockedUsers
    /// </summary>
    public class ClientSimpleUserDto
    {
        /// <summary>
        /// ID do cliente ao qual o usuário bloqueado pertence
        /// </summary>
        public int ClientId { get; set; }

        /// <summary>
        /// ID do usuário bloqueado
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Nome do usuário bloqueado
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// E-mail do usuário bloqueado
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// Status de bloqueio do usuário
        /// </summary>
        public bool IsBlocked { get; set; }

        /// <summary>
        /// URL da foto de perfil do usuário bloqueado
        /// </summary>
        public string ProfilePicUrl { get; set; }

        /// <summary>
        /// Indica se o usuário requer aprovação
        /// </summary>
        public bool RequireApproval { get; set; }

        /// <summary>
        /// Tipo do item (sempre será "PendingUserAccess" para usuários bloqueados)
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// Nome do cliente ao qual o usuário bloqueado pertence
        /// </summary>
        public string ClientName { get; set; }
    }
}