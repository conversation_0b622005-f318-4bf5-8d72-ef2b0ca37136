using System;

namespace Atlas.CrossCutting.DTO.UserInboxItem
{
    /// <summary>
    /// DTO para dispositivos pendentes de aprovação
    /// </summary>
    public class PendingApprovalDeviceDto
    {
        /// <summary>
        /// ID do dispositivo pendente de aprovação
        /// </summary>
        public int DeviceId { get; set; }

        /// <summary>
        /// ID do usuário associado ao dispositivo
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Nome do usuário associado ao dispositivo
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// Nome do dispositivo pendente de aprovação
        /// </summary>
        public string DeviceName { get; set; }

        /// <summary>
        /// Data de solicitação do dispositivo
        /// </summary>
        public DateTime RequestDate { get; set; }

        /// <summary>
        /// ID do cliente associado ao dispositivo
        /// </summary>
        public int ClientId { get; set; }

        /// <summary>
        /// Tipo do item (sempre será "PendingDeviceAccess" para dispositivos pendentes)
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// Nome do cliente associado ao dispositivo
        /// </summary>
        public string ClientName { get; set; }

        /// <summary>
        /// Nome do plano do cliente
        /// </summary>
        public string ClientPlanName { get; set; }
    }
}
