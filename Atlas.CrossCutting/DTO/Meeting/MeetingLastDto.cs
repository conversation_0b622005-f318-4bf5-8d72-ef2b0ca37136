using System;

namespace Atlas.CrossCutting.DTO.Meeting
{
    public class MeetingLastDto
    {
        public Guid contentUuid { get; set; }
        public int meetingId { get; set; }
        public string title { get; set; }
        public DateTime date { get; set; }
        public string location { get; set; }
        public int? duration { get; set; }
        public int workgroupId { get; set; }
        public int clientId { get; set; }
        public int[] subscriberUserIds { get; set; }
        public string conferenceType { get; set; }
        public bool? aIEnabled { get; set; }
    }
}
