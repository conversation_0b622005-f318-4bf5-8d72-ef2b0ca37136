using System;

namespace Atlas.CrossCutting.DTO
{
    public class AuditLogItemView
    {
        public int ContentActivityId { get; set; }
        public string Type { get; set; }
        public string SubItemType { get; set; }
        public string ContentData { get; set; }
        public int? ActivityUser { get; set; }
        public string UserName { get; set; }
        public int ClientId { get; set; }
        public DateTime Date { get; set; }
        public string BulletColor { get; set; }
        public string WorkgroupName { get; set; }
        public int WorkgroupId { get; set; }
        public int ContentId { get; set; }
        public string ContentType { get; set; }
        public string IPAddress { get; set; }
        public string SessionKey { get; set; }
        public int? SubItemId { get; set; }
        public string Device { get; set; }
    }
}