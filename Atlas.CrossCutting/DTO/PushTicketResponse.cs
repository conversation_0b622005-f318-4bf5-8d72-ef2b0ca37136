using System.Collections.Generic;

namespace Atlas.CrossCutting.DTO
{
    public class PushTicketResponse
    {
        public PushTicketData Data { get; set; }
        public List<PushTicketError> Errors { get; set; }
    }

    public class PushTicketData
    {
        public string Status { get; set; }
        public string Id { get; set; } // this is the Receipt ID
        public string Message { get; set; } // if status === "error"
        public object Details { get; set; } // JSON
    }

    public class PushTicketError
    {
        public string Code { get; set; }
        public string Message { get; set; }
    }
}
