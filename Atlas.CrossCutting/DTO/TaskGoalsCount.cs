using Newtonsoft.Json;

namespace Atlas.CrossCutting.DTO
{
    public class TaskGoalsCount
    {
        [JsonProperty(PropertyName = "total")]
        public int Total { get; set; }

        [JsonProperty(PropertyName = "closed")]
        public int Closed { get; set; }

        [JsonProperty(PropertyName = "expired")]
        public int Expired { get; set; }

        [JsonProperty(PropertyName = "closer")]
        public int Closer { get; set; }
    }
}
