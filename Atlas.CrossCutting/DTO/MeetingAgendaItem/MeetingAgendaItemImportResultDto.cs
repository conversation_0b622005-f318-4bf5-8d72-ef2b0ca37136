using System.Collections.Generic;

namespace Atlas.CrossCutting.DTO.MeetingAgendaItem
{
    public class MeetingAgendaItemImportResultDto
    {
        public List<ImportedAgendaItemResultDto> ImportedItems { get; set; } = new List<ImportedAgendaItemResultDto>();
        public int TotalProcessed { get; set; }
        public int SuccessfulImports { get; set; }
        public int FailedImports { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
    }
}
