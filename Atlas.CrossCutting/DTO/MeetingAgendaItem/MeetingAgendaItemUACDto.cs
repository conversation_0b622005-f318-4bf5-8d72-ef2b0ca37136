namespace Atlas.CrossCutting.DTO.MeetingAgendaItem
{
    public class MeetingAgendaItemUACDto
    {
        // Core permissions
        public bool isOwner { get; set; }
        public bool update { get; set; }
        public bool delete { get; set; }
        public bool restore { get; set; }

        // Management permissions
        public bool manage_owners { get; set; }
        public bool manage_subscribers { get; set; }

        // Comment permissions
        public bool add_comment { get; set; }
        public bool add_answer { get; set; }
        public bool delete_comment { get; set; }

        // Attachment permissions
        public bool add_attachment { get; set; }
        public bool delete_attachment { get; set; }
        public bool undelete_attachment { get; set; }

        // Communication permissions
        public bool can_resend { get; set; }

        // Meeting agenda specific permissions
        public bool add_child_agenda { get; set; }
        public bool can_add_checklist_item { get; set; }
        public bool can_edit_checklist_item { get; set; }
        public bool can_delete_checklist_item { get; set; }
        public bool can_postpone_meeting_agenda { get; set; }
    }
}
