using System;
using System.Collections.Generic;

namespace Atlas.CrossCutting.DTO.BlueBook.Data
{
    public class BlueBookDataAgendaDTO
    {
        public string Title { get; set; }

        public int Time { get; set; }

        public BlueBookDataAgendaUserDTO AssignedUser { get; set; }

        public List<BlueBookDataAgendaUserDTO> Guests { get; set; }

        public List<BlueBookDataCommentDTO> Comments { get; set; }


        public string AgendaType { get; set; }

        public bool HasPermission { get; set; }

        public List<BlueBookDataAttachmentDTO> Attachments { get; set; }

        public int AgendaContentId { get; set; }

        public DateTime DateTimeStart { get; set; }

        public DateTime DateTimeEnd { get; set; }

    }
}
