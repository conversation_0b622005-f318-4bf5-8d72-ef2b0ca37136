namespace Atlas.CrossCutting.DTO.BlueBook.Data
{
    public class BlueBookDocumentDataDTO
    {

        public int? attachmentId { get; set; }
        public int? contentAttachmentId { get; set; }
        public int? contentId { get; set; }

        public int pageLen { get; set; }
        public int pageStart { get; set; }
        public int pageEnd { get; set; }
        public int documentPosition { get; set; }
        public bool hasPermission { get; set; }

        public string fileName { get; set; }

        public bool isOutline { get; set; }
        public int? outlineId { get;  set; }
    }
}
