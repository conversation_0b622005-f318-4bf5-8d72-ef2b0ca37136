using System.Collections.Generic;

namespace Atlas.CrossCutting.DTO.AtlasInsights
{
    public class InsightsReportDTO
    {
        public string Period { get; set; } // Format: "MM/YYYY"
        public int TotalCount { get; set; }
        public int UniqueCount { get; set; }
        public string Status { get; set; }
        public int StatusCount { get; set; }
        public List<StatusCountDTO> StatusCounts { get; set; }
    }

    public class StatusCountDTO
    {
        public string Status { get; set; }
        public int Count { get; set; }
    }

    public class ReportORM
    {
        public string Period { get; set; }
        public int Meetings { get; set; }
        public int Agendas { get; set; }
        public int TotalMinutes { get; set; }
        // minutes
        public int MinutesPublished { get; set; }
        public int MinutesWaitingSignature { get; set; }
        public int MinutesSigned { get; set; }
        // bluebook views
        public int AllBbViews { get; set; }
        public int SingleBbViews { get; set; }
        //material views
        public int AllMaterialViews { get; set; }
        public int SingleMaterialViews { get; set; }
    }
}
