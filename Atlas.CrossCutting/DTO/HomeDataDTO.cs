using System;
using System.Collections.Generic;

namespace Atlas.CrossCutting.DTO
{
    public class MeetingList
    {
        private string _status;

        public int contentId { get; set; }
        public DateTime createDate { get; set; }
        public int createUser { get; set; }
        public string createUserName { get; set; }
        public string createUserPic { get; set; }
        public string status { get => GetStatus(); set => _status = value; }
        public int meetingId { get; set; }
        public string title { get; set; }
        public DateTime date { get; set; }
        public DateTime dateEnd { get; set; }
        public string location { get; set; }
        public int workgroupId { get; set; }
        public string workgroupName { get; set; }
        public string workgroupColor { get; set; }
        public string clientName { get; set; }
        public bool isPastMeeting { get; set; }
        public bool liveMeeting { get; set; }
        public string conferenceType { get; set; }
        public List<PlanFeatureDTO> PlanFeature { get;  set; }

        private string GetStatus()
        {
            if (_status.ToUpper() == "READY" && date.Date < DateTime.UtcNow.Date)
            {
                return "MEETINGCONCLUDED";
            }
            return _status;
        }

    }
    public class HomeDataDTO
    {
        public int inbox_totalCount { get; set; }
        public int outbox_totalCount { get; set; }
        public List<object> lastUpdates { get; set; }
        public List<MeetingList> upcomingEvents { get; set; }
        public IEnumerable<ContentAggregationObject> contentSummary { get; set; }
    }

    public class PlanFeatureDTO
    {
        public string planName { get; set; }
        public string featureName { get; set; }
        public bool featureEnabled { get; set; }
        public decimal? featureQuotaAmount { get; set; }
        public string featureQuotaFrequency { get; set; }
    }
}
