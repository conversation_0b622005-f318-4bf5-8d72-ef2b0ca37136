using System;

namespace Atlas.CrossCutting.DTO.User
{
    public class ClientUserDetailsDto
    {
        public int userId { get; set; }
        public string name { get; set; }
        public string email { get; set; }
        public string profilePic { get; set; }
        public string bio { get; set; }
        public string mobile { get; set; }
        public string gender { get; set; }
        public DateTime? birthday { get; set; }
        public int? companyId { get; set; }
        public int clientId { get; set; }
        public string defaultTimezone { get; set; }
        public string defaultLanguage { get; set; }
        public bool deleted { get; set; }
        public bool blocked { get; set; }
        public string fullName { get; set; }
        public Nullable<bool> skippedTour { get; set; }
        public bool invitationPending { get; set; }
        public bool mobileConfirmed { get; set; }
        public bool emailConfirmed { get; set; }
        public int accessFailedCount { get; set; }
        public DateTime? lastInviteMail { get; set; }
        public DateTime? lastDigestMail { get; set; }
        public DateTime? lastRegisterRequestMail { get; set; }
        public DateTime? lastRegisterNoCompanyMail { get; set; }
        public bool pushChatEnabled { get; set; }
        public bool? hideSignDocumentTutorial { get; set; }
        public string cpf { get; set; }
        public int? initialsAttachmentId { get; set; }
        public int? signatureAttachmentId { get; set; }
        public DateTime? inviteDate { get; set; }
        public DateTime? lastGroupedMail { get; set; }
        public string preferredSignatureTokenMethod { get; set; }
        public bool? requireApproval { get; set; }
        public bool? approved { get; set; }
        public int? inviteWorkgroup { get; set; }
        public bool? disableSSOForAccount { get; set; }
        public bool isDeleted { get; set; }
        public DateTime? LastActivity { get; set; }

        public int? invitedBy { get; set; }
        public int? invitedWorkgroup { get; set; }
        public bool isAdmin { get; set; }
    }
}
