using System;

namespace Atlas.CrossCutting.DTO.AuditLog
{
    public class AuditLogFiltersDTO
    {
        public int? contentId { get; set; }
        public int? clientId { get; set; }

        public string[]? types { get; set; }
        public int[]? actionUsers { get; set; }
        public int[]? workgroups { get; set; }

        public int? pageNumber { get; set; }
        public int? pageSize { get; set; }

        public DateTime? createDateMin { get; set; }
        public DateTime? createDateMax { get; set; }
    }
}
