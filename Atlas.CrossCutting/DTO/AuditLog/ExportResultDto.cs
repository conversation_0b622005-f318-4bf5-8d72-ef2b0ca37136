namespace Atlas.CrossCutting.DTO.AuditLog
{
    public class ExportResultDto
    {
        /// <summary>
        /// Conteúdo do arquivo exportado
        /// </summary>
        public byte[] FileContent { get; set; }

        /// <summary>
        /// Nome do arquivo para download
        /// </summary>
        public string FileName { get; set; } = "Atlas_Export";

        /// <summary>
        /// Tipo do arquivo exportado
        /// </summary>
        public ExportFileType FileType { get; set; } = ExportFileType.Excel;

        /// <summary>
        /// Obtém a extensão do arquivo com base no tipo
        /// </summary>
        public string FileExtension => FileType == ExportFileType.Excel ? ".xlsx" : ".csv";

        /// <summary>
        /// Obtém o nome completo do arquivo incluindo a extensão
        /// </summary>
        public string FullFileName => $"{FileName}{FileExtension}";

        /// <summary>
        /// Obtém o tipo MIME com base no tipo de arquivo
        /// </summary>
        public string ContentType { get; set; }
    }

    public enum ExportFileType
    {
        Excel,
        Csv
    }
}
