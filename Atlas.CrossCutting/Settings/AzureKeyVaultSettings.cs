namespace Atlas.CrossCutting.Settings
{
    public class AzureKeyVaultSettings
    {
        public AzureKeyVaultSettings(
            string akvTenantId,
            string akvClientId,
            string akvClientSecret)
        {
            AkvTenantId = akvTenantId;
            AkvClientId = akvClientId;
            AkvClientSecret = akvClientSecret;
        }

        public string AkvTenantId { get; }
        public string AkvClientId { get; }
        public string AkvClientSecret { get; }
    }
}
