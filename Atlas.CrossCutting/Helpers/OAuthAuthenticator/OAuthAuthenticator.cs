using Newtonsoft.Json;
using SharpRaven;
using SharpRaven.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Cryptography;

namespace Atlas.CrossCutting.Helpers.OAuthAuthenticator
{
    public class OAuthAuthenticator
    {
        public OAuth2ServiceConfiguration svc_config { get; set; }
        public OAuthAuthenticator(OAuth2ServiceConfiguration _svc_config)
        {
            this.svc_config = _svc_config;
        }
        public OAuth2AuthenticationResult AuthenticateCode(string authorization_code, string redirect_uri = "https://beta.atlasgov.com")
        {

            using (var client = new HttpClient())
            {

                //configure headers
                if (svc_config.serviceName == "zoom")
                {
                    client.DefaultRequestHeaders.Add("Authorization", svc_config.oauth_client_secret);
                }

                //configure postdata
                var postData = new List<KeyValuePair<string, string>>();
                postData.Add(new KeyValuePair<string, string>("client_id", svc_config.oauth_client_id));
                if (svc_config.serviceName == "msgraph")
                {
                    postData.Add(new KeyValuePair<string, string>("scope", svc_config.oauth_scope));
                }
                postData.Add(new KeyValuePair<string, string>("code", authorization_code));
                postData.Add(new KeyValuePair<string, string>("redirect_uri", redirect_uri));
                postData.Add(new KeyValuePair<string, string>("grant_type", "authorization_code"));
                if (redirect_uri.StartsWith("http"))
                {
                    postData.Add(new KeyValuePair<string, string>("client_secret", svc_config.oauth_client_secret));
                }

                HttpContent content = new FormUrlEncodedContent(postData);
                content.Headers.ContentType = new MediaTypeHeaderValue("application/x-www-form-urlencoded");

                var responseResult = client.PostAsync(svc_config.token_url, content).Result;
                if (responseResult.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    string result = responseResult.Content.ReadAsStringAsync().Result;
                    var result_obj = JsonConvert.DeserializeObject<OAuth2AuthenticationResult>(result);
                    return result_obj;
                }
                else
                {
                    try
                    {
                        string result = responseResult.Content.ReadAsStringAsync().Result;
                        var result_obj = JsonConvert.DeserializeObject<OAuth2AuthenticationResult>(result);
                        return result_obj;
                    }
                    catch (Exception)
                    {
                        return new OAuth2AuthenticationResult()
                        {
                            error = "EXCEPTION",
                            error_description = responseResult.Content.ReadAsStringAsync().Result
                        };

                    }

                }

            }
        }
        public OAuth2AuthenticationResult AuthenticateRefreshToken(string refresh_token, string redirect_uri = "https://beta.atlasgov.com")
        {
            using (var client = new HttpClient())
            {
                //configure headers
                if (svc_config.serviceName == "zoom")
                {
                    client.DefaultRequestHeaders.Add("Authorization", svc_config.oauth_client_secret);
                }

                var postData = new List<KeyValuePair<string, string>>();
                postData.Add(new KeyValuePair<string, string>("client_id", svc_config.oauth_client_id));
                postData.Add(new KeyValuePair<string, string>("scope", svc_config.oauth_scope));
                postData.Add(new KeyValuePair<string, string>("refresh_token", refresh_token));
                postData.Add(new KeyValuePair<string, string>("grant_type", "refresh_token"));
                postData.Add(new KeyValuePair<string, string>("redirect_uri", redirect_uri));
                if (redirect_uri.StartsWith("http"))
                {
                    postData.Add(new KeyValuePair<string, string>("client_secret", svc_config.oauth_client_secret));
                }

                HttpContent content = new FormUrlEncodedContent(postData);
                content.Headers.ContentType = new MediaTypeHeaderValue("application/x-www-form-urlencoded");

                var responseResult = client.PostAsync(svc_config.token_url, content).Result;
                try
                {
                    if (responseResult.StatusCode == System.Net.HttpStatusCode.OK)
                    {
                        string result = responseResult.Content.ReadAsStringAsync().Result;
                        return JsonConvert.DeserializeObject<OAuth2AuthenticationResult>(result);
                    }
                    else
                    {
                        string responseBody = "<<NONE>>";
                        try
                        {
                            responseBody = responseResult.Content.ReadAsStringAsync().Result;
                            string result = responseResult.Content.ReadAsStringAsync().Result;
                            var resultObject = JsonConvert.DeserializeObject<OAuth2AuthenticationResult>(result);

                            //invalid_grant = Changed password, user invalid, etc
                            //AADSTS50173: The provided grant has expired due to it being revoked, a fresh auth token is needed. The user might have changed or reset their password. The grant was issued on '2021-07-08T22:17:56.8424256Z' and the TokensValidFrom date (before which tokens are not valid) for this user is '2021-07-24T15:11:38.0000000Z'
                            //AADSTS50158: External security challenge not satisfied.User will be redirected to another page or authentication provider to satisfy additional authentication challenges.
                            if (resultObject.error != "invalid_grant")
                            {
                                //if different from  "invalid_grant" we need to capture details of the error 
                                var ravenClient = new RavenClient("https://ab28683383204d07a3d36712ce88a5a0:<EMAIL>/162538");
                                string ENVIRONMENT = Environment.GetEnvironmentVariable("ENVIRONMENT");
                                ravenClient.Environment = ENVIRONMENT;
                                var resSentry2 = ravenClient.Capture(new SentryEvent(new Exception("AuthenticateRefreshToken Failed. NON-INVALID_GRANT " + responseResult.StatusCode + ". " + responseBody)));
                            }
                            return resultObject;
                        }
                        catch
                        {
                            responseBody = "<<FAILED TO READ>>";
                        }
                        //for cases when the body could not be read
                        throw new Exception("AuthenticateRefreshToken Failed. StatusCode " + responseResult.StatusCode + ". " + responseBody);
                    }
                }
                catch (Exception ex)
                {
                    string ENVIRONMENT = Environment.GetEnvironmentVariable("ENVIRONMENT");

                    var ravenClient = new RavenClient("https://ab28683383204d07a3d36712ce88a5a0:<EMAIL>/162538");
                    ravenClient.Environment = ENVIRONMENT;
                    var resSentry2 = ravenClient.Capture(new SentryEvent(ex));
                    Console.WriteLine("====================================================================");
                    Console.WriteLine("=================");
                    Console.WriteLine("=================");
                    Console.WriteLine("=================");
                    Console.WriteLine("AuthenticateRefreshToken Failed. StatusCode");
                    Console.WriteLine("====================================================================");
                    Console.WriteLine("====================================================================");
                    throw ex;
                }
            }
        }

        public static OAuth2ServiceConfiguration GetOAuthServiceByName(string serviceName, string services_json)
        {
            //var services_json = ConfigurationManager.AppSettings["OAuthServices"];
            var services = JsonConvert.DeserializeObject<List<OAuth2ServiceConfiguration>>(services_json);
            var requested_service = services.Where(o => o.serviceName == serviceName).First();

            return requested_service;
        }

        public static OAuth2ServiceConfiguration GetOAuthServiceByName(string serviceName, List<OAuth2ServiceConfiguration> services)
        {
            var requested_service = services.Where(o => o.serviceName == serviceName).First();

            return requested_service;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="serviceName">The type of the integration to return the authorization URL (msgraph, zoom)</param>
        /// <param name="redirect_uri">URL encodeded redirect URI. Default: https%3A%2F%2Fbeta.atlasgov.com</param>
        /// <returns></returns>
        public static string GetAuthorizationLink(OAuth2ServiceConfiguration svc, string redirect_uri = "https%3A%2F%2Fbeta.atlasgov.com")
        {
            RNGCryptoServiceProvider provider = new RNGCryptoServiceProvider();
            var byteArray = new byte[4];
            provider.GetBytes(byteArray);

            //convert 4 bytes to an integer
            var randomInteger = BitConverter.ToUInt32(byteArray, 0);

            switch (svc.serviceName)
            {
                case "msgraph":
                    return svc.authorization_url + $"?client_id={svc.oauth_client_id}&response_type=code&scope={svc.oauth_scope_authorize}&redirect_uri={redirect_uri}&state={randomInteger}";
                case "zoom":
                    return svc.authorization_url + $"?_rnd={randomInteger}&client_id={svc.oauth_client_id}&redirect_uri={redirect_uri}&response_type=code";
                default:
                    throw new ArgumentException($"Argument \"{svc.serviceName}\" provided on \"serviceName\" parameter is invalid");
            }
        }


    }

    public class OAuth2AuthorizationResult
    {
        public string code { get; set; }
        public string session { get; set; }
        public string session_state { get; set; }
        public string error { get; set; }
        public string error_description { get; set; }
        public List<int> error_codes { get; set; }
        public string timestamp { get; set; }
        public string trace_id { get; set; }
        public string correlation_id { get; set; }
        public string error_uri { get; set; }
        public string redirect_uri { get; set; }
    }
    public class OAuth2AuthenticationResult
    {
        public string reason { get; set; }

        public string token_type { get; set; }
        public string scope { get; set; }
        public int expires_in { get; set; }
        public int ext_expires_in { get; set; }
        public string access_token { get; set; }
        public string id_token { get; set; }
        public string refresh_token { get; set; }
        public string error { get; set; }
        public string error_description { get; set; }
        public List<int> error_codes { get; set; }
        public string timestamp { get; set; }
        public string trace_id { get; set; }
        public string correlation_id { get; set; }
        public string error_uri { get; set; }
    }

    public class OAuth2ServiceConfiguration
    {
        public string serviceName { get; set; }
        public string oauth_client_id { get; set; }
        public bool oauth_secret_basicAuth { get; set; }
        public string oauth_client_secret { get; set; }
        public string oauth_scope { get; set; }
        public string oauth_scope_authorize { get; set; }
        public string token_url { get; set; }
        public string authorization_url { get; set; }
        public bool isEnabled { get; set; }
        public bool disableChange { get; set; }
    }

    // Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);
    public class ZoomDeauthorizationEvent
    {
        public string account_id { get; set; }
        public string user_id { get; set; }
        public string signature { get; set; }
        public DateTime deauthorization_time { get; set; }
        public string client_id { get; set; }
        public string plainToken { get; set; }
    }

    public class ZoomEventWebhookObject
    {
        public string @event { get; set; }
        public ZoomDeauthorizationEvent payload { get; set; }
    }


}
