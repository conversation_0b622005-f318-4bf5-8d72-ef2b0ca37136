using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Atlas.CrossCutting.Helpers.Resolution
{
    public class ResolutionHelperClasses
    {
        public static class ResolutionTypes
        {
            public const string Custom = "CUSTOM";
            public const string Approval = "APPROVAL";
        }

        public static class ResolutionValidationMessages
        {
            public const string InvalidGrant = "INVALID_GRANT";
            public const string InvalidType = "INVALID_TYPE";
            public const string InvalidTitle = "INVALID_TITLE";
            public const string InvalidOptions = "INVALID_OPTIONS";
            public const string InvalidVoters = "INVALID_VOTERS";
            public const string InvalidDueDate = "INVALID_DUE_DATE";
            public const string InvalidDuration = "INVALID_DURATION";
            public const string ResolutionNotFound = "RESOLUTION_NOT_FOUND";
            public const string ResolutionDataNotFound = "RESOLUTION_DATA_NOT_FOUND";
            public const string CannotChangeTitleWithVotes = "CANNOT_CHANGE_TITLE_WITH_VOTES";
            public const string CannotUpdateClosedResolution = "CANNOT_UPDATE_CLOSED_RESOLUTION";
            public const string ArchivedWorkgroup = "ARCHIVED_WORKGROUP";
        }
    }
}
