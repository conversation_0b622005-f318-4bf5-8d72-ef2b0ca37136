using System;
using System.Collections.Generic;
using System.Linq;

namespace Atlas.Business.Helpers
{
    public static class IsoCountryCodeConverter
    {
        private static readonly Dictionary<string, string> iso2ToIso3 = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            // A
            {"AD", "AND"}, // Andorra
            {"AE", "ARE"}, // United Arab Emirates
            {"AF", "AFG"}, // Afghanistan
            {"AG", "ATG"}, // Antigua and Barbuda
            {"AI", "AIA"}, // Anguilla
            {"AL", "ALB"}, // Albania
            {"AM", "ARM"}, // Armenia
            {"AO", "AGO"}, // Angola
            {"AQ", "ATA"}, // Antarctica
            {"AR", "ARG"}, // Argentina
            {"AS", "ASM"}, // American Samoa
            {"AT", "AUT"}, // Austria
            {"AU", "AUS"}, // Australia
            {"AW", "ABW"}, // Aruba
            {"AX", "ALA"}, // Åland Islands
            {"AZ", "AZE"}, // Azerbaijan
            
            // B
            {"BA", "BIH"}, // Bosnia and Herzegovina
            {"BB", "BRB"}, // Barbados
            {"BD", "BGD"}, // Bangladesh
            {"BE", "BEL"}, // Belgium
            {"BF", "BFA"}, // Burkina Faso
            {"BG", "BGR"}, // Bulgaria
            {"BH", "BHR"}, // Bahrain
            {"BI", "BDI"}, // Burundi
            {"BJ", "BEN"}, // Benin
            {"BL", "BLM"}, // Saint Barthélemy
            {"BM", "BMU"}, // Bermuda
            {"BN", "BRN"}, // Brunei Darussalam
            {"BO", "BOL"}, // Bolivia
            {"BQ", "BES"}, // Bonaire, Sint Eustatius and Saba
            {"BR", "BRA"}, // Brazil
            {"BS", "BHS"}, // Bahamas
            {"BT", "BTN"}, // Bhutan
            {"BV", "BVT"}, // Bouvet Island
            {"BW", "BWA"}, // Botswana
            {"BY", "BLR"}, // Belarus
            {"BZ", "BLZ"}, // Belize
            
            // C
            {"CA", "CAN"}, // Canada
            {"CC", "CCK"}, // Cocos (Keeling) Islands
            {"CD", "COD"}, // Congo, Democratic Republic of the
            {"CF", "CAF"}, // Central African Republic
            {"CG", "COG"}, // Congo
            {"CH", "CHE"}, // Switzerland
            {"CI", "CIV"}, // Côte d'Ivoire
            {"CK", "COK"}, // Cook Islands
            {"CL", "CHL"}, // Chile
            {"CM", "CMR"}, // Cameroon
            {"CN", "CHN"}, // China
            {"CO", "COL"}, // Colombia
            {"CR", "CRI"}, // Costa Rica
            {"CU", "CUB"}, // Cuba
            {"CV", "CPV"}, // Cabo Verde
            {"CW", "CUW"}, // Curaçao
            {"CX", "CXR"}, // Christmas Island
            {"CY", "CYP"}, // Cyprus
            {"CZ", "CZE"}, // Czechia
            
            // D
            {"DE", "DEU"}, // Germany
            {"DJ", "DJI"}, // Djibouti
            {"DK", "DNK"}, // Denmark
            {"DM", "DMA"}, // Dominica
            {"DO", "DOM"}, // Dominican Republic
            {"DZ", "DZA"}, // Algeria
            
            // E
            {"EC", "ECU"}, // Ecuador
            {"EE", "EST"}, // Estonia
            {"EG", "EGY"}, // Egypt
            {"EH", "ESH"}, // Western Sahara
            {"ER", "ERI"}, // Eritrea
            {"ES", "ESP"}, // Spain
            {"ET", "ETH"}, // Ethiopia
            
            // F
            {"FI", "FIN"}, // Finland
            {"FJ", "FJI"}, // Fiji
            {"FK", "FLK"}, // Falkland Islands
            {"FM", "FSM"}, // Micronesia
            {"FO", "FRO"}, // Faroe Islands
            {"FR", "FRA"}, // France
            
            // G
            {"GA", "GAB"}, // Gabon
            {"GB", "GBR"}, // United Kingdom
            {"GD", "GRD"}, // Grenada
            {"GE", "GEO"}, // Georgia
            {"GF", "GUF"}, // French Guiana
            {"GG", "GGY"}, // Guernsey
            {"GH", "GHA"}, // Ghana
            {"GI", "GIB"}, // Gibraltar
            {"GL", "GRL"}, // Greenland
            {"GM", "GMB"}, // Gambia
            {"GN", "GIN"}, // Guinea
            {"GP", "GLP"}, // Guadeloupe
            {"GQ", "GNQ"}, // Equatorial Guinea
            {"GR", "GRC"}, // Greece
            {"GS", "SGS"}, // South Georgia and the South Sandwich Islands
            {"GT", "GTM"}, // Guatemala
            {"GU", "GUM"}, // Guam
            {"GW", "GNB"}, // Guinea-Bissau
            {"GY", "GUY"}, // Guyana
            
            // H
            {"HK", "HKG"}, // Hong Kong
            {"HM", "HMD"}, // Heard Island and McDonald Islands
            {"HN", "HND"}, // Honduras
            {"HR", "HRV"}, // Croatia
            {"HT", "HTI"}, // Haiti
            {"HU", "HUN"}, // Hungary
            
            // I
            {"ID", "IDN"}, // Indonesia
            {"IE", "IRL"}, // Ireland
            {"IL", "ISR"}, // Israel
            {"IM", "IMN"}, // Isle of Man
            {"IN", "IND"}, // India
            {"IO", "IOT"}, // British Indian Ocean Territory
            {"IQ", "IRQ"}, // Iraq
            {"IR", "IRN"}, // Iran
            {"IS", "ISL"}, // Iceland
            {"IT", "ITA"}, // Italy
            
            // J
            {"JE", "JEY"}, // Jersey
            {"JM", "JAM"}, // Jamaica
            {"JO", "JOR"}, // Jordan
            {"JP", "JPN"}, // Japan
            
            // K
            {"KE", "KEN"}, // Kenya
            {"KG", "KGZ"}, // Kyrgyzstan
            {"KH", "KHM"}, // Cambodia
            {"KI", "KIR"}, // Kiribati
            {"KM", "COM"}, // Comoros
            {"KN", "KNA"}, // Saint Kitts and Nevis
            {"KP", "PRK"}, // Korea, Democratic People's Republic of
            {"KR", "KOR"}, // Korea, Republic of
            {"KW", "KWT"}, // Kuwait
            {"KY", "CYM"}, // Cayman Islands
            {"KZ", "KAZ"}, // Kazakhstan
            
            // L
            {"LA", "LAO"}, // Lao People's Democratic Republic
            {"LB", "LBN"}, // Lebanon
            {"LC", "LCA"}, // Saint Lucia
            {"LI", "LIE"}, // Liechtenstein
            {"LK", "LKA"}, // Sri Lanka
            {"LR", "LBR"}, // Liberia
            {"LS", "LSO"}, // Lesotho
            {"LT", "LTU"}, // Lithuania
            {"LU", "LUX"}, // Luxembourg
            {"LV", "LVA"}, // Latvia
            {"LY", "LBY"}, // Libya
            
            // M
            {"MA", "MAR"}, // Morocco
            {"MC", "MCO"}, // Monaco
            {"MD", "MDA"}, // Moldova
            {"ME", "MNE"}, // Montenegro
            {"MF", "MAF"}, // Saint Martin (French part)
            {"MG", "MDG"}, // Madagascar
            {"MH", "MHL"}, // Marshall Islands
            {"MK", "MKD"}, // North Macedonia
            {"ML", "MLI"}, // Mali
            {"MM", "MMR"}, // Myanmar
            {"MN", "MNG"}, // Mongolia
            {"MO", "MAC"}, // Macao
            {"MP", "MNP"}, // Northern Mariana Islands
            {"MQ", "MTQ"}, // Martinique
            {"MR", "MRT"}, // Mauritania
            {"MS", "MSR"}, // Montserrat
            {"MT", "MLT"}, // Malta
            {"MU", "MUS"}, // Mauritius
            {"MV", "MDV"}, // Maldives
            {"MW", "MWI"}, // Malawi
            {"MX", "MEX"}, // Mexico
            {"MY", "MYS"}, // Malaysia
            {"MZ", "MOZ"}, // Mozambique
            
            // N
            {"NA", "NAM"}, // Namibia
            {"NC", "NCL"}, // New Caledonia
            {"NE", "NER"}, // Niger
            {"NF", "NFK"}, // Norfolk Island
            {"NG", "NGA"}, // Nigeria
            {"NI", "NIC"}, // Nicaragua
            {"NL", "NLD"}, // Netherlands
            {"NO", "NOR"}, // Norway
            {"NP", "NPL"}, // Nepal
            {"NR", "NRU"}, // Nauru
            {"NU", "NIU"}, // Niue
            {"NZ", "NZL"}, // New Zealand
            
            // O
            {"OM", "OMN"}, // Oman
            
            // P
            {"PA", "PAN"}, // Panama
            {"PE", "PER"}, // Peru
            {"PF", "PYF"}, // French Polynesia
            {"PG", "PNG"}, // Papua New Guinea
            {"PH", "PHL"}, // Philippines
            {"PK", "PAK"}, // Pakistan
            {"PL", "POL"}, // Poland
            {"PM", "SPM"}, // Saint Pierre and Miquelon
            {"PN", "PCN"}, // Pitcairn
            {"PR", "PRI"}, // Puerto Rico
            {"PS", "PSE"}, // Palestine, State of
            {"PT", "PRT"}, // Portugal
            {"PW", "PLW"}, // Palau
            {"PY", "PRY"}, // Paraguay
            
            // Q
            {"QA", "QAT"}, // Qatar
            
            // R
            {"RE", "REU"}, // Réunion
            {"RO", "ROU"}, // Romania
            {"RS", "SRB"}, // Serbia
            {"RU", "RUS"}, // Russian Federation
            {"RW", "RWA"}, // Rwanda
            
            // S
            {"SA", "SAU"}, // Saudi Arabia
            {"SB", "SLB"}, // Solomon Islands
            {"SC", "SYC"}, // Seychelles
            {"SD", "SDN"}, // Sudan
            {"SE", "SWE"}, // Sweden
            {"SG", "SGP"}, // Singapore
            {"SH", "SHN"}, // Saint Helena, Ascension and Tristan da Cunha
            {"SI", "SVN"}, // Slovenia
            {"SJ", "SJM"}, // Svalbard and Jan Mayen
            {"SK", "SVK"}, // Slovakia
            {"SL", "SLE"}, // Sierra Leone
            {"SM", "SMR"}, // San Marino
            {"SN", "SEN"}, // Senegal
            {"SO", "SOM"}, // Somalia
            {"SR", "SUR"}, // Suriname
            {"SS", "SSD"}, // South Sudan
            {"ST", "STP"}, // Sao Tome and Principe
            {"SV", "SLV"}, // El Salvador
            {"SX", "SXM"}, // Sint Maarten (Dutch part)
            {"SY", "SYR"}, // Syrian Arab Republic
            {"SZ", "SWZ"}, // Eswatini
            
            // T
            {"TC", "TCA"}, // Turks and Caicos Islands
            {"TD", "TCD"}, // Chad
            {"TF", "ATF"}, // French Southern Territories
            {"TG", "TGO"}, // Togo
            {"TH", "THA"}, // Thailand
            {"TJ", "TJK"}, // Tajikistan
            {"TK", "TKL"}, // Tokelau
            {"TL", "TLS"}, // Timor-Leste
            {"TM", "TKM"}, // Turkmenistan
            {"TN", "TUN"}, // Tunisia
            {"TO", "TON"}, // Tonga
            {"TR", "TUR"}, // Turkey
            {"TT", "TTO"}, // Trinidad and Tobago
            {"TV", "TUV"}, // Tuvalu
            {"TW", "TWN"}, // Taiwan
            {"TZ", "TZA"}, // Tanzania
            
            // U
            {"UA", "UKR"}, // Ukraine
            {"UG", "UGA"}, // Uganda
            {"UM", "UMI"}, // United States Minor Outlying Islands
            {"US", "USA"}, // United States of America
            {"UY", "URY"}, // Uruguay
            {"UZ", "UZB"}, // Uzbekistan
            
            // V
            {"VA", "VAT"}, // Holy See
            {"VC", "VCT"}, // Saint Vincent and the Grenadines
            {"VE", "VEN"}, // Venezuela
            {"VG", "VGB"}, // Virgin Islands (British)
            {"VI", "VIR"}, // Virgin Islands (U.S.)
            {"VN", "VNM"}, // Viet Nam
            {"VU", "VUT"}, // Vanuatu
            
            // W
            {"WF", "WLF"}, // Wallis and Futuna
            {"WS", "WSM"}, // Samoa
            
            // Y
            {"YE", "YEM"}, // Yemen
            {"YT", "MYT"}, // Mayotte
            
            // Z
            {"ZA", "ZAF"}, // South Africa
            {"ZM", "ZMB"}, // Zambia
            {"ZW", "ZWE"}  // Zimbabwe
        };

        /// <summary>
        /// Converte código ISO de país de 2 dígitos para 3 dígitos
        /// </summary>
        /// <param name="iso2Code">Código ISO de 2 dígitos (ex: "BR")</param>
        /// <returns>Código ISO de 3 dígitos (ex: "BRA") ou null se não encontrado</returns>
        public static string ConvertToIso3(string iso2Code)
        {
            if (string.IsNullOrWhiteSpace(iso2Code))
                return null;

            return iso2ToIso3.TryGetValue(iso2Code.Trim(), out string iso3Code)
                ? iso3Code
                : null;
        }

        /// <summary>
        /// Converte código ISO de país de 2 dígitos para 3 dígitos com fallback
        /// </summary>
        /// <param name="iso2Code">Código ISO de 2 dígitos</param>
        /// <param name="defaultValue">Valor padrão se não encontrado</param>
        /// <returns>Código ISO de 3 dígitos ou valor padrão</returns>
        public static string ConvertToIso3(string iso2Code, string defaultValue)
        {
            return ConvertToIso3(iso2Code) ?? defaultValue;
        }

        /// <summary>
        /// Tenta converter código ISO de país de 2 dígitos para 3 dígitos
        /// </summary>
        /// <param name="iso2Code">Código ISO de 2 dígitos</param>
        /// <param name="iso3Code">Código ISO de 3 dígitos resultante</param>
        /// <returns>True se conversão bem-sucedida, False caso contrário</returns>
        public static bool TryConvertToIso3(string iso2Code, out string iso3Code)
        {
            iso3Code = ConvertToIso3(iso2Code);
            return iso3Code != null;
        }

        /// <summary>
        /// Converte lista de códigos ISO de 2 dígitos para 3 dígitos
        /// </summary>
        /// <param name="iso2Codes">Lista de códigos ISO de 2 dígitos</param>
        /// <param name="skipInvalid">Se true, pula códigos inválidos; se false, retorna null para inválidos</param>
        /// <returns>Lista de códigos ISO de 3 dígitos</returns>
        public static List<string> ConvertMultipleToIso3(IEnumerable<string> iso2Codes, bool skipInvalid = false)
        {
            if (iso2Codes == null)
                return new List<string>();

            return skipInvalid
                ? iso2Codes.Select(ConvertToIso3).Where(code => code != null).ToList()
                : iso2Codes.Select(ConvertToIso3).ToList();
        }

        /// <summary>
        /// Obtém todos os códigos ISO2 disponíveis
        /// </summary>
        public static IEnumerable<string> GetAllIso2Codes()
        {
            return iso2ToIso3.Keys;
        }

        /// <summary>
        /// Obtém todos os códigos ISO3 disponíveis
        /// </summary>
        public static IEnumerable<string> GetAllIso3Codes()
        {
            return iso2ToIso3.Values;
        }

        /// <summary>
        /// Verifica se um código ISO2 existe no dicionário
        /// </summary>
        public static bool IsValidIso2Code(string iso2Code)
        {
            return !string.IsNullOrWhiteSpace(iso2Code) &&
                   iso2ToIso3.ContainsKey(iso2Code.Trim());
        }
    }
}
