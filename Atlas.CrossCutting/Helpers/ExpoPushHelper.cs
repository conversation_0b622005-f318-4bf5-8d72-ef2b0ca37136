using Atlas.CrossCutting.DTO;
using Newtonsoft.Json;
using System;
using System.Net;

namespace Atlas.CrossCutting.Helpers
{
    static public class ExpoPushHelper
    {
        /// <summary>
        /// Offical wrapper to send a generic PUSH message to a given token.
        /// </summary>
        /// <param name="token"></param>
        /// <param name="title"></param>
        /// <param name="body"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        public static PushTicketResponse SendMessage(string token, string title, string body, dynamic data)
        {
            string json = JsonConvert.SerializeObject(new
            {
                to = token,
                title,
                body,
                sound = "default",
                data,
                priority = "high"
            });

            // TODO: WebRequest, HttpWebRequest, ServicePoint, and WebClient are obsolete, and you shouldn't use them for new development. Use HttpClient instead.
            using (WebClient client = new WebClient())
            {
                client.Headers.Add("accept", "application/json");
                client.Headers.Add("Host", "exp.host");
                client.Headers.Add("accept-encoding", "gzip, deflate");
                client.Headers.Add("Content-Type", "application/json");
                client.Encoding = System.Text.Encoding.UTF8;

                string response = client.UploadString("https://exp.host/--/api/v2/push/send", json);

                PushTicketResponse pushTicketResponse = JsonConvert.DeserializeObject<PushTicketResponse>(response);
                return pushTicketResponse;
            }
        }

        /// <summary>
        /// Simplified wrapper to rapidly test a predefined annoucement push message.
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        public static dynamic SendAnnouncementMessage(string token)
        {
            var v = new
            {
                announcementId = 939,
                contentId = 33511,
                type = "MEETING_READY",
                systemGenerated = true
            };

            Console.WriteLine(v.contentId);

            string title = $"Annoucement Title for Content Id #{v.contentId}";
            string body = $"Specific informations about the annoucement";

            return SendMessage(token, title, body, v);
        }
    }
}
