using System;
using System.Net;
using System.Configuration;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace Atlas.CrossCutting.Helpers
{
    public class ImpossibleTravelHelper
    {
        private static readonly string[] microsoftIpAddresses = new string[]
        {
            "************/20",
            "************/19",
            "*************/21",
            "*************/18",
            "************/18",
            "*************/19",
            "************/20",
            "************/19",
            "*************/20"
        };

        private static readonly int defaultSpeedLimit = 1200;

        public static bool IsIpInRange(string ipAddress, string cidr)
        {
            var enableWhitelist = ConfigurationManager.AppSettings["ENABLE_IP_WHITELIST"];
            if (enableWhitelist == null || !enableWhitelist.Equals("true", StringComparison.OrdinalIgnoreCase))
            {
                return false;
            }

            try
            {
                var parts = cidr.Split('/');
                var baseAddress = IPAddress.Parse(parts[0]);
                int prefixLength = int.Parse(parts[1]);

                var addressBytes = baseAddress.GetAddressBytes();
                var ipBytes = IPAddress.Parse(ipAddress).GetAddressBytes();

                if (addressBytes.Length != ipBytes.Length)
                    return false;

                int byteCount = prefixLength / 8;
                int bitCount = prefixLength % 8;

                for (int i = 0; i < byteCount; i++)
                {
                    if (addressBytes[i] != ipBytes[i])
                        return false;
                }

                if (bitCount > 0)
                {
                    int mask = (byte)~(255 >> bitCount);
                    if ((addressBytes[byteCount] & mask) != (ipBytes[byteCount] & mask))
                        return false;
                }
            }
            catch (Exception)
            {
                return false;
            }

            return true;
        }

        public static async Task<ImpossibleTravelAnalysis> Analyze(string InitialGeoData, DateTime StartTime, string FinalGeoData, DateTime FinalTime)
        {
            var ITSetting = ConfigurationManager.AppSettings["ImpossibleTravelDisabled"];
            var ITFallbackSetting = (ConfigurationManager.AppSettings["ImpossibleTravelUseFallback"] ?? "1") == "1";

            var IT_IPIgnoreList = ConfigurationManager.AppSettings["ImpossibleTravelIPIgnoreList"] ?? "**************;";
            var IT_ASNIgnoreList = ConfigurationManager.AppSettings["ImpossibleTravelASNIgnoreList"] ?? "22616;";

            var ImpossibleTravel_BlockUnkownASNs = (ConfigurationManager.AppSettings["ImpossibleTravel_BlockUnkownASNs"] ?? "1") == "1";
            var minimumDistanceSetting = ConfigurationManager.AppSettings["ImpossibleTravel_MinimumDistance"] ?? "100.0";
            int maximumSpeed = int.TryParse(ConfigurationManager.AppSettings["ImpossibleTravel_MaxSpeed"], out int maxSpeed) ? maxSpeed : defaultSpeedLimit;

            GeoLocalization initial = GeoIPHelper.ParseGeoJS(InitialGeoData);
            GeoLocalization final = GeoIPHelper.ParseGeoJS(FinalGeoData);
            if (initial == null)
            {
                //no previous data
                return new ImpossibleTravelAnalysis() { IsImpossibleTravel = false };

            }

            //if IP is in ignore lists
            if (IT_IPIgnoreList.Split(';').Any(ip => ip.Trim() == initial.ip) || IT_IPIgnoreList.Split(';').Any(ip => ip.Trim() == final.ip))
            {
                return new ImpossibleTravelAnalysis() { IsImpossibleTravel = false };
            }

            //if ASN is in ignore lists
            if (IT_ASNIgnoreList.Split(';').Any(asn => asn.Trim() == initial.asn.ToString()) || IT_ASNIgnoreList.Split(';').Any(asn => asn.Trim() == final.asn.ToString()))
            {
                return new ImpossibleTravelAnalysis() { IsImpossibleTravel = false };
            }



            if (microsoftIpAddresses.Any(cidr => IsIpInRange(initial.ip, cidr)) || microsoftIpAddresses.Any(cidr => IsIpInRange(final.ip, cidr)))
            {
                return new ImpossibleTravelAnalysis() { IsImpossibleTravel = false };
            }

            if (final.organization.ToLower().Contains("unknown") && ImpossibleTravel_BlockUnkownASNs)
            {
                // If the organization is unknown, we cannot determine impossible travel.
                return new ImpossibleTravelAnalysis() { IsImpossibleTravel = true };
            }

            var distance = GetDistance(Double.Parse(initial.longitude, CultureInfo.InvariantCulture), Double.Parse(initial.latitude, CultureInfo.InvariantCulture), Double.Parse(final.longitude, CultureInfo.InvariantCulture), Double.Parse(final.latitude, CultureInfo.InvariantCulture));
            var time = (FinalTime - StartTime);

            //  Distance: km (from meters)
            var distanceKm = distance / 1000;

            // Units:
            //  Speed: km/h
            var speed = ((distanceKm) / Math.Abs(time.TotalHours));

            var minimumDistance = double.Parse(minimumDistanceSetting, CultureInfo.InvariantCulture);
            bool impossible;
            if (distanceKm <= minimumDistance)
            {
                // It's a mininum threshold that must exists to avoid a false positive between neighboring towns with centers very far apart.
                impossible = false;
            }
            else
            {
                impossible = speed > maximumSpeed;
            }

            if (impossible && ITFallbackSetting)
            {
                GeoLocalization fb_Final = null;
                GeoLocalization fb_Initial = null;

                // If impossible travel is detected, but the fallback is enabled, we will request to the secondary service
                if (final.geoService?.ToUpper() == "MAXMIND")
                {
                    fb_Final = await GeoIPHelper.GetGeoJS(final.ip);
                }
                else
                {
                    fb_Final = await GeoIPHelper.GetMaxMind(final.ip);
                }

                if (final.geoService?.ToUpper() == "MAXMIND")
                {
                    fb_Initial = await GeoIPHelper.GetGeoJS(initial.ip);
                }
                else
                {
                    fb_Initial = await GeoIPHelper.GetMaxMind(initial.ip);
                }

                // get the shortest distance betwwen original initial and final, and the new initial and final. example: try compare the original initial or the new initial, with the original final or new final. The shortest distance will be used to calculate the impossible travel.
                var originalDistance = GetDistance(Double.Parse(initial.longitude, CultureInfo.InvariantCulture), Double.Parse(initial.latitude, CultureInfo.InvariantCulture), Double.Parse(final.longitude, CultureInfo.InvariantCulture), Double.Parse(final.latitude, CultureInfo.InvariantCulture));
                var newDistance = GetDistance(Double.Parse(fb_Initial.longitude, CultureInfo.InvariantCulture), Double.Parse(fb_Initial.latitude, CultureInfo.InvariantCulture), Double.Parse(fb_Final.longitude, CultureInfo.InvariantCulture), Double.Parse(fb_Final.latitude, CultureInfo.InvariantCulture));
                var originalInitialNewFinalDistance = GetDistance(Double.Parse(initial.longitude, CultureInfo.InvariantCulture), Double.Parse(initial.latitude, CultureInfo.InvariantCulture), Double.Parse(fb_Final.longitude, CultureInfo.InvariantCulture), Double.Parse(fb_Final.latitude, CultureInfo.InvariantCulture));
                var newInitialOriginalFinalDistance = GetDistance(Double.Parse(fb_Initial.longitude, CultureInfo.InvariantCulture), Double.Parse(fb_Initial.latitude, CultureInfo.InvariantCulture), Double.Parse(final.longitude, CultureInfo.InvariantCulture), Double.Parse(final.latitude, CultureInfo.InvariantCulture));

                // Defina uma tupla para armazenar o nome da combinação e a distância
                (string Name, double Distance, GeoLocalization Initial, GeoLocalization Final)[] distances = new[]
                {
                    ("original-original", originalDistance, initial, final),
                    ("fallback-fallback", newDistance, fb_Initial, fb_Final),
                    ("original-fallback", originalInitialNewFinalDistance, initial, fb_Final),
                    ("fallback-original", newInitialOriginalFinalDistance, fb_Initial, final)
                };

                // Encontre a menor distância e a combinação correspondente
                var shortest = distances.OrderBy(d => d.Distance).First();
                var shortestDistance = shortest.Distance;
                var shortestCombination = shortest.Name;

                //  Distance: km (from meters)
                var fb_distanceKm = shortestDistance / 1000;

                // Units:
                //  Speed: km/h
                var fb_speed = ((fb_distanceKm) / Math.Abs(time.TotalHours));

                if (fb_distanceKm <= minimumDistance)
                {
                    // It's a mininum threshold that must exists to avoid a false positive between neighboring towns with centers very far apart.
                    impossible = false;
                }
                else
                {
                    impossible = fb_speed > maximumSpeed;
                }

                return new ImpossibleTravelAnalysis()
                {
                    IsImpossibleTravel = impossible,
                    Speed = fb_speed,
                    Distance = fb_distanceKm,
                    Time = time,
                    InitialIp = initial.ip,
                    InitialGeoLocation = shortest.Initial,
                    FinalIp = final.ip,
                    FinalGeoLocation = shortest.Final,
                    ShortestCombination = shortestCombination
                };


            }

            return new ImpossibleTravelAnalysis()
            {
                IsImpossibleTravel = impossible,
                Speed = speed,
                Distance = distanceKm,
                Time = time,
                InitialIp = initial.ip,
                FinalIp = final.ip,
                ShortestCombination = "no-fallback-used"
            };
        }
        public static double GetDistance(double longitude, double latitude, double otherLongitude, double otherLatitude)
        {
            var d1 = latitude * (Math.PI / 180.0);
            var num1 = longitude * (Math.PI / 180.0);
            var d2 = otherLatitude * (Math.PI / 180.0);
            var num2 = otherLongitude * (Math.PI / 180.0) - num1;
            var d3 = Math.Pow(Math.Sin((d2 - d1) / 2.0), 2.0) + Math.Cos(d1) * Math.Cos(d2) * Math.Pow(Math.Sin(num2 / 2.0), 2.0);

            return 6376500.0 * (2.0 * Math.Atan2(Math.Sqrt(d3), Math.Sqrt(1.0 - d3)));
        }


    }

    public class ImpossibleTravelAnalysis
    {
        public bool IsImpossibleTravel { get; set; }
        public double Speed { get; set; }
        public double Distance { get; set; }
        public TimeSpan Time { get; set; }
        public string InitialIp { get; set; }
        public string FinalIp { get; set; }
        public GeoLocalization InitialGeoLocation { get; internal set; }
        public GeoLocalization FinalGeoLocation { get; internal set; }
        public string ShortestCombination { get; internal set; }
    }
}
