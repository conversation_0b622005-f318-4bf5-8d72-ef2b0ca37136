using Atlas.Application.Models.Authentication;
using Atlas.Business.ViewModels;

namespace Atlas.Application.Interfaces
{
    public interface IAuthenticationApplication
    {
        Task OldUserRegister(UserRegisterObject request);
        Task<RegistrationStatusResponse> GetRegistrationStatusAsync(RegistrationStatusRequest request);
        Task UpdateSessionLastActivityAsync();
        Task<CheckSessionResponse> CheckSession();
        Task LockSession();
        Task<bool> ValidateLogin(ValidateLoginRequest request, string ip);
    }
}
