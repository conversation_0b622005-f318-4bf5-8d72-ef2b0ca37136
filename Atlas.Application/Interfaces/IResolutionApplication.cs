using Atlas.Application.Models.AfsApiRequest;
using Atlas.Application.Models.Resolution;
using Atlas.Application.Models.Resolution.Atlas.Application.Models.Resolution;
using Atlas.CrossCutting.DTO.Resolution;
using Atlas.CrossCutting.DTO.Resolution.Atlas.Application.Models.Resolution;

namespace Atlas.Application.Interfaces
{
    public interface IResolutionApplication
    {
        /// <summary>
        /// Creates a new resolution
        /// </summary>
        /// <param name="request">The resolution creation request</param>
        /// <returns>The ID of the created resolution</returns>
        Task<Guid> CreateAsync(ResolutionCreateRequest request);

        /// <summary>
        /// Registers a vote for a resolution poll option
        /// </summary>
        /// <param name="request">The vote registration request containing the content UUID and poll option ID</param>
        /// <returns>The ID of the registered vote</returns>
        Task<int> VoteRegisterAsync(ResolutionVoteRegisterRequest request);

        /// <summary>
        /// Removes the current user's vote from a resolution poll
        /// </summary>
        /// <param name="contentUuid">The unique identifier of the resolution content</param>
        Task VoteRemoveAsync(Guid contentUuid);

        /// <summary>
        /// Retrieves a resolution with all its details including poll options, votes, permissions, and user access control information
        /// </summary>
        /// <param name="contentUuId">The unique identifier of the resolution content</param>
        /// <returns>A complete resolution response containing all resolution data and metadata</returns>
        Task<ResolutionGetResponse> GetResolutionAsync(Guid contentUuId);

        /// <summary>
        /// Retrieves the most recent resolution of a specified type
        /// </summary>
        /// <param name="request">The request containing the resolution type to filter by</param>
        /// <returns>The last resolution data including title, description, status, and type information</returns>
        Task<ResolutionGetLastResponse> GetLastResolutionAsync(ResolutionGetLastRequest request);

        /// <summary>
        /// Removes a specific vote from a resolution poll by vote ID, allowing users to undo their voting action
        /// </summary>
        /// <param name="contentUuid">The unique identifier of the resolution content</param>
        /// <param name="voteId">The unique identifier of the specific vote to remove</param>
        Task UnvoteAsync(Guid contentUuid, int voteId);

        /// <summary>
        /// Advances or moves back the resolution workflow step based on the forward parameter, handling state transitions and business rules validation
        /// </summary>
        /// <param name="request">The set step request containing workflow transition parameters including direction and optional comment</param>
        /// <returns>The result of the workflow state change operation including the new state</returns>
        Task<ResolutionSetStepResponse> SetStepAsync(ResolutionSetStepRequest request);

        /// <summary>
        /// Updates an existing resolution with new information such as title, description, due date, and poll settings
        /// </summary>
        /// <param name="request">The resolution update request containing the changes to be applied</param>
        /// <returns>True if the update was successful, false otherwise</returns>
        Task<ResolutionGetResponse> UpdateAsync(ResolutionUpdateRequest request);

        /// <summary>
        /// Sets the resolution workflow state to a specific named state, bypassing sequential workflow progression
        /// </summary>
        /// <param name="request">The set step by name request containing the target state and optional comment</param>
        /// <returns>The result of the workflow state change operation including the new state</returns>
        Task<ResolutionSetStepResponse> SetStepByNameAsync(ResolutionSetStepByNameRequest request);

        /// <summary>
        /// Deletes a resolution and its associated meeting agenda item. If the resolution has votes, it will be cancelled instead of physically deleted
        /// </summary>
        /// <param name="contentUuid">The unique identifier of the resolution content to delete</param>
        Task DeleteAsync(Guid contentUuid);

        /// <summary>
        /// Resends email notifications for a resolution to its subscribers
        /// </summary>
        /// <param name="contentUuid">The unique identifier of the resolution content</param>
        /// <returns>True if the resend operation was successful</returns>
        Task<bool> ResendAsync(Guid contentUuid);

        /// <summary>
        /// Retrieves resolution voting data and report attachment information formatted for reporting purposes
        /// </summary>
        /// <param name="contentUuId">The unique identifier of the resolution content</param>
        /// <returns>A resolution report response containing voters list with their voting details and poll report attachment information (if available)</returns>
        Task<ResolutionReportResponse> GetForReportAsync(Guid contentUuId);

        /// <summary>
        /// Gets counts of contentAttachments, contentComments, and recycleBin items for a specific resolution
        /// </summary>
        /// <param name="contentUuid">The unique identifier of the resolution content</param>
        /// <returns>Count information for the resolution</returns>
        Task<ResolutionCountGetResponse> GetCountsAsync(Guid contentUuid);

        /// <summary>
        /// Manually generates a PDF report for a closed resolution when automatic generation failed
        /// </summary>
        /// <param name="contentUuid">The unique identifier of the resolution content</param>
        /// <returns>A task representing the async operation</returns>
        Task GenerateReportAsync(Guid contentUuid);

        Task ProcessReportWebhookAsync(AfsApiRequestWebhook request);
    }
}