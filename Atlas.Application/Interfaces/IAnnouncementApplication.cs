using Atlas.Application.Models.Announcement;
using Atlas.CrossCutting.Models.Responses;

namespace Atlas.Application.Interfaces
{
    public interface IAnnouncementApplication
    {
        Task<AnnouncementsGetResponse> GetAnnouncementsAsync(AnnouncementsGetRequest request);
        Task<PagedResult<AnnouncementGetResponse>> GetFilteredAnnouncementsAsync(AnnouncementsFilteredGetRequest request);
        Task<AnnouncementDetailsGetResponse> GetAnnouncementDetailsAsync(Guid contentId);
        Task<AnnouncementCreateResponse> CreateAsync(AnnouncementCreateRequest request);
        Task<bool> SetRead(AnnouncementSetReadPostRequest request);
    }
}
