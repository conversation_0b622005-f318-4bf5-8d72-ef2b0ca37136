using Atlas.Application.Models.Note;
using Atlas.Application.Models.Task;
using Atlas.CrossCutting.DTO.Note;
using Atlas.CrossCutting.Models.Responses;

namespace Atlas.Application.Interfaces
{
    public interface INoteApplication
    {
        Task<PagedResult<NoteListItem>> GetNoteListAsync(NoteListGetRequest request);

        Task<NoteDetailGetResponse> GetNoteByContentUuidAsync(NoteDetailGetRequest request);

        Task<CreateNoteResponse> CreateNoteAsync(CreateNoteDto createNoteDto);

        public Task<object> UpdateNoteAsync(UpdateNoteRequest updateNoteRequest);

        Task<bool>DeleteNoteAsync(Guid contentUuid, string userAgent, bool individuallyDeleted = true);
    }
}