using Atlas.Application.Models.Task;

namespace Atlas.Application.Interfaces
{
    public interface ITaskApplication
    {
        Task<TaskGetResponse> GetTaskByContentUuid(int clientId, int workgroupId, Guid contentUuid);
        Task<TasksGetResponse> GetTasksList(TaskGetRequest request);
        Task<List<TaskGetResponse>> GetTasksByParentContentAsync(int clientId, int workgroupId, Guid parentContentUuid);
        Task<TaskGetCountersResponse> GetCounters(int workgroupId);
        Task<List<TaskGetPermissionsResponse>> GetTaskPermissions(Guid contentUuid);
        Task<TaskCreateResponse> CreateAsync(TaskCreateRequest request);
        Task DeleteAsync(Guid contentUuid);
        Task<TaskResendEmailResponse> ResendEmailAsync(Guid contentUuid);
        Task<TaskSetStepByNameResponse> SetStepByNameAsync(Guid contentUuid, string stepName);
        Task<TaskSetNextStepResponse> SetNextStepAsync(Guid contentUuid, bool forward);
        Task<TaskSetNextStepResponse> SetPreviousStepAsync(Guid contentUuid, bool forward, string? comment);
        Task<TaskGetResponse> UpdateAsync(TaskUpdateRequest request);

        // Checklist endpoints
        Task<ChecklistItemCreateResponse> CreateChecklistItemAsync(ChecklistItemCreateRequest request);
        Task UpdateChecklistItemAsync(ChecklistItemUpdateRequest request);
        Task DeleteChecklistItemAsync(ChecklistItemDeleteRequest request);
        Task ReorderChecklistItemsAsync(ChecklistItemReorderRequest request);
        Task<TaskChecklistGetResponse> GetChecklistAsync(Guid contentUuid);
    }
}
