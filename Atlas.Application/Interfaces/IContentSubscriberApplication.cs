using Atlas.Application.Models.ContentSubscriber;

public interface IContentSubscriberApplication
{
    Task<ContentSubscriberListResponse> GetSubscribersAsync(ContentSubscriberListRequest request);
    Task<bool> UpdateSubscribersAsync(ContentSubscriberUpdateRequest request);
    Task<bool> ResendInviteAsync(ContentSubscriberResendInviteRequest request);
    Task<bool> DeleteSubscriberAsync(ContentSubscriberDeleteRequest request);
    Task<bool> SetRSVPResponseAsync(ContentSubscriberRSVPRequest request);
    Task<bool> SendRSVPAsync(ContentSubscriberRSVPRequest request);
    Task<ContentSubscriberInfoResponse> GetSubscribersInfoAsync(ContentSubscriberListRequest request);
    Task<ContentSubscriberListResponse> GetPossibleAssignedUsersAsync(ContentSubscriberListRequest request);
}