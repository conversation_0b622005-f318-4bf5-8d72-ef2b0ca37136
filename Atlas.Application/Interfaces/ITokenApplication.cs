using Atlas.Application.Models.Token;

namespace Atlas.Application.Interfaces
{
    public interface ITokenApplication
    {
        Task SendEmailConfirmationCodeAsync(EmailConfirmRequest request);
        Task<EmailConfirmVerifyResponse> VerifyEmailConfirmationCodeAsync(EmailConfirmVerifyRequest request);
        Task SendAuthorizationCodeAsync();
        Task ValidateTwoFactorCodeAsync(VerifyCodeRequest request);
        Task SendTwoFactorVoiceCodeAsync(SendCodeByVoiceRequest request);
        Task SendTwoFactorWhatsAppCodeAsync(SendCodeByWhatsAppRequest request);
        Task<MobileConfirmSendCodeResponse> SendMobileConfirmationCodeAsync(MobileConfirmSendCodeRequest request);
        Task<MobileConfirmVerifyResponse> VerifyMobileConfirmationCodeAsync(MobileConfirmVerifyRequest request);
    }
}
