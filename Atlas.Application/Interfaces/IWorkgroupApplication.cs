using Atlas.Application.Models.Workgroup;
using Atlas.CrossCutting.DTO.AuditLog;

namespace Atlas.Application.Interfaces
{
    public interface IWorkgroupApplication
    {
        Task<WorkgroupCreateResponse> CreateAsync(WorkgroupCreateRequest request);
        Task UpdateAsync(WorkgroupUpdateRequest request);
        Task UpdateOwnerAsync(int workgroupId, List<WorkgroupOwnerUpdateRequest> request);
        Task<WorkgroupGetAllResponse> GetAllWorkgroup(WorkgroupGetAllRequest request);
        Task<WorkgroupGetSingleResponse> GetSingleWorkgroup(WorkgroupGetSingleRequest request);
        Task<List<WorkgroupGetSingleSimplifiedResponse>> GetWorkgroupsForAudit(int clientId);
        Task<ExportResultDto> GetWorkgroupList(WorkgroupGetListRequest request);
        Task<WorkgroupColumnCreateResponse> CreateColumnAsync(WorkgroupColumnCreateRequest request);
        Task<List<WorkgroupColumnListResponse>> GetColumnsAsync(WorkgroupColumnListRequest request);
        Task UpdateTaskListAsync(WorkgroupTaskListUpdateRequest request);
        Task DeleteTaskListAsync(WorkgroupTaskListDeleteRequest request);
        Task ReorderTaskListsAsync(WorkgroupTaskListReorderRequest request);
        Task ReorderTasksAsync(TaskReorderRequest request);
    }
}
