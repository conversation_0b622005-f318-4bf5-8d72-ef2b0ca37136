using Atlas.Application.Models.ContentAttachment;

namespace Atlas.Application.Interfaces
{
    public interface IContentAttachmentApplication
    {
        Task<ContentAttachmentGetResponse> UploadFileAsync(FileUploadRequest request);
        Task<ContentAttachmentGetResponse> ToggleDelete(ContentAttachmentDeleteRequest request);
        Task<ContentAttachmentGetResponse> Reorder(ContentAttachmentReorderPutRequest request);
        Task<List<ContentAttachmentGetResponse>> GetContentAttachments(ContentAttachmentGetRequest request);
        Task<ContentAttachmentGetResponse> ToggleLock(ContentAttachmentLockPostRequest request);
        Task<ContentAttachmentFileWatermarkedGetResponse> GetFileWaterMarked(ContentAttachmentFileWatermarkedGetRequest request);
    }
}
