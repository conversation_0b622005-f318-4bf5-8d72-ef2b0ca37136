using Atlas.Application.Models.WorkgroupUsers;
using Atlas.CrossCutting.Models.Responses;

namespace Atlas.Application.Interfaces
{
    public interface IWorkgroupUsersApplication
    {
        Task<PagedResult<ClientPossibleUsersGetResponse>> PossibleUsersForClient(ClientPossibleUsersGetRequest request);
        Task<PagedResult<WorkgroupUsersListGetResponse>> List(WorkgroupUsersListGetRequest request);
    }
}
