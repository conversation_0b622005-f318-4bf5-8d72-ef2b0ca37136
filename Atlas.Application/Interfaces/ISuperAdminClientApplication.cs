using Atlas.Application.Models.Admin;
using Atlas.Application.Models.AdminArea;
using Atlas.CrossCutting.DTO.Client;

namespace Atlas.Application.Interfaces
{
    public interface ISuperAdminClientApplication
    {
        Task<SuperAdminClientGetResponse> GetSuperAdminClientAsync();
        Task<SuperAdminClientGetByIdResponse> GetSuperAdminClientByIdAsync(ClientGetRequest request);
        Task<UserInfoGetResponse> GetUserInfoByEmailAsync(UserInfoGetRequest request);
        Task<SuperAdminClientCreateResponse> CreateClientAsync(SuperAdminClientCreateRequest request);
        Task<SuperAdminClientApproveUsersResponse> ApproveUser(SuperAdminClientApproveUsersRequest request);
        Task<SuperAdminClientBlockUsersResponse> BlockUser(SuperAdminClientBlockUsersRequest request);
        Task<bool> UpdateClientDetails(ClientDetailsDTO request, int clientId);
        Task<bool> UpdateSuperAdminClientAsync(ClientUpdateRequest request);
        Task<bool> BlockSuperAdminClientAsync(ClientBlockRequest request);
        Task<bool> DeleteSuperAdminClientAsync(ClientDeleteRequest request, bool sending);
        Task<AdminUploadLogoResponse> UploadLogoAsync(AdminUploadLogoRequest request);
        Task<AdminClientUsersGetResponse> GetUsersForClientAsync(AdminClientUsersGetRequest request);
        Task<AdminClientWorkgroupsGetResponse> GetWorkgroupsForClientAsync(AdminClientWorkgroupsGetRequest request);
    }
}
