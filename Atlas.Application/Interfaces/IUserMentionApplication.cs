using Atlas.Application.Models.UserMention;
using Atlas.CrossCutting.Models.Responses;

namespace Atlas.Application.Interfaces
{
    public interface IUserMentionApplication
    {
        Task<PagedResult<UserMentionGetResponse>> GetUserMentions(UserMentionGetRequest request);
        Task<PagedResult<UserMentionGetResponse>> GetUserMentionByWorkgroupId(UserMentionByWorkgroupGetRequest request);
        Task<PagedResult<UserMentionSimplifiedGetResponse>> GetUserMentionByClientId(int clientId, UserMentionSimplifiedGetRequest request);
        Task<PagedResult<UserMentionGetResponse>> GetUsersWorkgroupOwnersList(UserMentionByWorkgroupGetRequest request);
        Task<PagedResult<UserMentionMeetingGetResponse>> GetUserMentionMeetingByContentUuid(int clientId, Guid contentUuid, UserMentionMeetingGetRequest request);
    }
}
