using Atlas.Application.Models.Content;

namespace Atlas.Application.Interfaces
{
    public interface IContentApplication
    {
        Task<ContentWorkgroupGetResponse> GetContentWorkgroup(ContentWorkgroupGetRequest request);
        Task<bool> Delete(ContentDeleteRequest request);
        Task<int> ResendEmail(ContentResendNotificationsPostRequest request);
        Task<ContentRestoreDeletedItemsResponse> Restore(ContentRestoreDeletedItemsRequest request);
    }

}