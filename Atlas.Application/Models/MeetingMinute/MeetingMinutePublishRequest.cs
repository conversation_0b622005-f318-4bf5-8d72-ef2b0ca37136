namespace Atlas.Application.Models.MeetingMinute
{
    /// <summary>
    /// DTO para publicação de meeting minutes
    /// </summary>
    public class MeetingMinutePublishRequest
    {
        /// <summary>
        /// ID do cliente
        /// </summary>
        public int clientId { get; set; }

        /// <summary>
        /// ID do workgroup
        /// </summary>
        public int workgroupId { get; set; }

        /// <summary>
        /// ID da reunião
        /// </summary>
        public int meetingId { get; set; }

        /// <summary>
        /// ID da ata
        /// </summary>
        public int minuteId { get; set; }
    }
}