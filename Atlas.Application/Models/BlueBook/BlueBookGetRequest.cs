using Atlas.CrossCutting.Models.Requests;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Atlas.Application.Models.BlueBook
{
    public class BlueBookGetRequest
    {
        [FromRoute]
        public Guid contentUuId { get; set; }
        [FromQuery]
        public bool refresh { get; set; }

    }
}
