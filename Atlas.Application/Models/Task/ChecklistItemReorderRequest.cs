namespace Atlas.Application.Models.Task
{
    public class ChecklistItemReorderRequest
    {
        public int clientId { get; set; }
        public int workgroupId { get; set; }
        public Guid contentUuid { get; set; }
        public List<ChecklistItemOrderRequest> items { get; set; } = new List<ChecklistItemOrderRequest>();
    }

    public class ChecklistItemOrderRequest
    {
        public int checklistItemId { get; set; }
        public short itemOrder { get; set; }
    }
}