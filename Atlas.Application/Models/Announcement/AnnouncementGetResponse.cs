namespace Atlas.Application.Models.Announcement
{
    public class AnnouncementGetResponse
    {
        public int announcementId { get; set; }
        public string body { get; set; }
        public string bodyPreview { get; set; }
        public string type { get; set; }
        public bool systemGenerated { get; set; }
        public int? originalActivityId { get; set; }
        public int? originalContentId { get; set; }
        public Guid? originalContentUuid { get; set; }
        public string originalContentType { get; set; }
        public string parentContentTitle { get; set; }
        public string title { get; set; }
        public DateTime createDate { get; set; }
        public string createUserName { get; set; }
        public string workgroupName { get; set; }
        public string workgroupColor { get; set; }
        public string clientName { get; set; }
        public DateTime? readDate { get; set; }
        public bool? isRead { get; set; }
        public int clientId { get; set; }

    }
}
