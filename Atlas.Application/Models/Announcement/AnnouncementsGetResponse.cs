namespace Atlas.Application.Models.Announcement
{
    public class AnnouncementsGetResponse
    {
        /// <summary>
        /// Indicates whether the results are grouped.
        /// </summary>
        public bool isGrouped { get; set; }

        /// <summary>
        /// List of announcements (if no grouping is applied).
        /// </summary>
        public List<AnnouncementGetResponse> Announcements { get; set; }

        /// <summary>
        /// List of announcement groups (if grouping is requested).
        /// </summary>
        public List<AnnouncementGroupGetResponse> Groups { get; set; }
        
    }
}
