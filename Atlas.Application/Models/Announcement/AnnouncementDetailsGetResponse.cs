using Atlas.Business.ViewModels;
using Atlas.Business.ViewModels.Announcemets;
using Atlas.Business.ViewModels.Attachments;
using Atlas.Business.ViewModels.Subscriber;

namespace Atlas.Application.Models.Announcement
{
    public class AnnouncementDetailsGetResponse
    {
        public AnnouncementDetailsUserViewModel announcement { get; set; }
        public List<SubscriberViewModel> subscribers { get; set; }
        public List<AttachmentsViewModel> attachments { get; set; }
        public List<ContentOwnerViewModel> owners { get; set; }
        public bool isWorkgroupOwner { get; set; }
    }
}
