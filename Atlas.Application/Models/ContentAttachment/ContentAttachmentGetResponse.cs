namespace Atlas.Application.Models.ContentAttachment
{
    public class ContentAttachmentGetResponse
    {
        public int contentAttachmentId { get; set; }
        public bool includeOnBlueBook { get; set; }
        public string fileName { get; set; }
        public string path { get; set; }
        public int bytesCount { get; set; }
        public DateTime createDate { get; set; }
        public int userId { get; set; }
        public string createdBy { get; set; }
        public bool? locked { get; set; }
        public bool? deleted { get; set; }
        public int? itemOrder { get; set; }
        public int? importedFrom { get; set; }
    }
}
