using Microsoft.AspNetCore.Mvc;

namespace Atlas.Application.Models.ContentAttachment
{
    public class ContentAttachmentFileWatermarkedGetRequest
    {
        public int clientId {  get; set; }
        public int workgroupId { get; set; }
        public Guid contentUuid {  get; set; }
        public int contentAttachmentId { get; set; }
        [FromQuery]
        public bool? isDownload { get; set; }
    }
}
