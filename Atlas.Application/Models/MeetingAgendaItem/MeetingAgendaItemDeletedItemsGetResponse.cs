namespace Atlas.Application.Models.MeetingAgendaItem
{
    public class MeetingAgendaItemDeletedItemsGetResponse
    {
        public Guid contentUuid { get; set; }
        public string contentTitle { get; set; }
        public string contentType { get; set; }
        public List<DeletedCommentItem> deletedComments { get; set; } = new();
        public List<DeletedAttachmentItem> deletedAttachments { get; set; } = new();
        public List<DeletedChildContentItem> deletedChildContent { get; set; } = new();
    }

    public class DeletedCommentItem
    {
        public int contentCommentId { get; set; }
        public string text { get; set; }
        public int? parentCommentId { get; set; }
        public DateTime date { get; set; }
        public string profilePic { get; set; } = string.Empty;
        public string name { get; set; } = string.Empty;
        public List<DeletedCommentItem> replies { get; set; } = new();
    }

    public class DeletedAttachmentItem
    {
        public int contentAttachmentId { get; set; }
        public string fileName { get; set; } = string.Empty;
        public int fileSize { get; set; }
    }

    public class DeletedChildContentItem
    {
        public Guid contentUuid { get; set; }
        public string title { get; set; }
        public string contentType { get; set; }
    }
}