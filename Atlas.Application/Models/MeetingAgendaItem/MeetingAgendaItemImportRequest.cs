using System.Text.Json.Serialization;

namespace Atlas.Application.Models.MeetingAgendaItem
{
    public class MeetingAgendaItemImportRequest
    {
        [JsonIgnore]
        public Guid targetMeetingContentUuid { get; set; }
        public Guid? sourceMeetingContentUuid { get; set; }
        public bool importFromFolder { get; set; } = false;
        public List<string> folderNames { get; set; } = new List<string>();
        public bool importAgendaComments { get; set; } = false;
        public bool importAgendaAttachments { get; set; } = false;
        [JsonIgnore]
        public int clientId { get; set; }
        [JsonIgnore]
        public int workgroupId { get; set; }
    }
}
