namespace Atlas.Application.Models.MeetingAgendaItem
{
    /// <summary>
    /// Response model for meeting agenda item counts
    /// </summary>
    public class MeetingAgendaItemCountGetResponse
    {
        /// <summary>
        /// Count of active content attachments
        /// </summary>
        public int contentAttachmentsCount { get; set; }

        /// <summary>
        /// Count of active content comments
        /// </summary>
        public int contentCommentsCount { get; set; }

        /// <summary>
        /// Count of items in recycle bin (deleted attachments, comments, and child content)
        /// </summary>
        public int recycleBinCount { get; set; }
    }
}
