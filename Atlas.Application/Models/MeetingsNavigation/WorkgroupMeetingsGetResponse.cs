using Atlas.CrossCutting.DTO.MeetingsNavigational;

namespace Atlas.Application.Models.MeetingsNavigation
{
    public class WorkgroupMeetingsGetResponse
    {
        public MeetingsDTO[] FutureMeetings { get; set; } = Array.Empty<MeetingsDTO>();
        public MeetingsDTO[] PastMeetings { get; set; } = Array.Empty<MeetingsDTO>();
        public int totalPastPages { get; set; }
        public int totalFuturePages { get; set; }
        public int futureCurrentPage { get; set; }
        public int pastCurrentPage { get; set; }
        public int pastTotalItems { get; set; }
        public int futureTotalItems { get; set; }
    }
}
