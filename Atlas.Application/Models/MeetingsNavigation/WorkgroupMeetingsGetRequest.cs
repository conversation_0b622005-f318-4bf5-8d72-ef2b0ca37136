using System.ComponentModel.DataAnnotations;

namespace Atlas.Application.Models.MeetingsNavigation
{
    public class WorkgroupMeetingsGetRequest
    {
        public int[] workgroupIds { get; set; } = Array.Empty<int>();
        public int futurePageNumber { get; set; }
        public int pastPageNumber { get; set; }
        [Required]
        public int pageSize { get; set; }
        public bool getFutureMeetings { get; set; }
        public bool getPastMeetings { get; set; }
        public bool fetchArchivedWorkgroups { get; set; }
        public bool isFirstRequest { get; set; } = true;
    }
}
