namespace Atlas.Application.Models.Admin
{
    public class AdminClientUsersGetResponse
    {
        public List<AdminClientUserBaseInfo> Users { get; set; } = new();
    }

    public class AdminClientUserBaseInfo
    {
        public int userId { get; set; }
        public string name { get; set; }
        public string email { get; set; }
        public string profilePic { get; set; }
        public bool blocked { get; set; }
        public bool deleted { get; set; }
        public bool invitationPending { get; set; }
        public DateTime? lastActivity { get; set; }
        public bool? approved { get; set; }
        public bool? requireApproval { get; set; }
        public string fullName { get; set; }
        public bool isAdmin { get; set; }
    }
}
