using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Atlas.Application.Models.Form
{
    public class FormUserFormsGetRequest
    {
        [FromQuery]
        public int page { get; set; }
        [FromQuery]
        public bool? answered { get; set; }
        [FromBody]
        public int[] workgroupIds { get; set; }
        }
}
