using System.Text.Json.Serialization;

namespace Atlas.Application.Models.ExternalUser
{
    /// <summary>
    /// Request model for sending 2FA code to external user.
    /// </summary>
    public class ExternalUser2FASendRequest
    {
        public int ExternalUserId { get; set; }
        public string Hash { get; set; } = string.Empty;
        public string TokenRecaptcha { get; set; } = string.Empty;
        public string Method2FA { get; set; } = string.Empty;
        public string Language { get; set; } = string.Empty;
        [JsonIgnore]
        public string? BaseUrl { get; set; } = null;
    }
}