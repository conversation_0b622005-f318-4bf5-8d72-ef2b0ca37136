using Atlas.CrossCutting.DTO.Workgroup;

namespace Atlas.Application.Models.Workgroup
{
    public class WorkgroupPatchConfiguration
    {
        public ICollection<WorkgroupCreateUserRolesRequest>? UserList { get; set; }
        public string[]? inviteList { get; set; }
        public string? name { get; set; }
        public string? newName { get; set; }
        public string? color { get; set; }
        public string? type { get; set; }
        public int? clientId { get; set; }
        public bool? archive { get; set; }
        public bool? createInvite { get; set; }
        public bool? removeInvite { get; set; }
        public string? description { get; set; }
        public string? newDescription { get; set; }
        public ICollection<WorkgroupTaskListDTO>? TaskList { get; set; }

        public ICollection<WorkgroupUpdateTaskListDTO>? SimpleTaskList { get; set; }
    }
}