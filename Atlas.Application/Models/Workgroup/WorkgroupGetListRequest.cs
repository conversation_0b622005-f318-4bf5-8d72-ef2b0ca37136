namespace Atlas.Application.Models.Workgroup
{
    public class WorkgroupGetListRequest
    {
        public int[] WorkgroupIds { get; set; }
        public string Type { get; set; } = "DEFAULT";
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool ExportMeetings { get; set; }
        public bool ExportKnowledgeBase { get; set; }
        public bool ExportPolls { get; set; }
        public bool ExportSignatures { get; set; }
        public bool ExportTasks { get; set; }
        public string FileType { get; set; } = "Excel";
        public bool IsDataExport { get; set; }
    }
}