using Atlas.CrossCutting.DTO.Meeting;

namespace Atlas.Application.Models.Meeting
{
    public class MeetingGetAllResponse
    {
        public Guid uuid { get; init; }
        public string title { get; init; }
        public DateTime startDate { get; init; }
        public DateTime? endDate { get; init; }
        public string status { get; init; }
        public int workgroupId { get; init; }
        public int clientId { get; init; }
        public string clientName { get; init; }
        public string workgroupName { get; init; }
        public string workgroupColor { get; init; }
        public string workgroupType { get; init; }
        public string workgroupDescription { get; init; }
        public string conferenceType { get; init; }
        public string createdByUserName { get; init; }
        public DateTime createdAt { get; init; }
        public bool workgroupArchived { get; init; }
        public bool hasAgendaItems { get; set; }
        public string location { get; init; }
        public ICollection<MeetingParticipantDto> participants { get; init; }
        public ICollection<MeetingGuestDto> guests { get; set; }
    }
}
