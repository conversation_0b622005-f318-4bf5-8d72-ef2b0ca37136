namespace Atlas.Application.Models.Profile
{
    public class ProfileGetUserInfoResponse
    {
        public int userId { get; set; }
        public int clientId { get; set; }
        public string name { get; set; }
        public string fullName { get; set; }
        public string email { get; set; }
        public string mobile { get; set; }
        public string profilePic { get; set; }
        public bool? canEdit { get; set; }

    }
}
