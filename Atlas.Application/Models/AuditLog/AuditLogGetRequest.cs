namespace Atlas.Application.Models.AuditLog
{
    public class AuditLogGetRequest
    {
        public int? contentId { get; set; }
        public int? clientId { get; set; }

        public string[]? types { get; set; }
        public int[]? actionUsers { get; set; }
        public int[]? workgroups { get; set; }

        public int? pageNumber
        {
            get => (_pageNumber == 0 || _pageNumber == null) ? 1 : _pageNumber;
            set => _pageNumber = value;
        }

        private int? _pageNumber;
        public int? pageSize { get; set; }

        public DateTime? createDateMin { get; set; }
        public DateTime? createDateMax { get; set; }
    }
}
