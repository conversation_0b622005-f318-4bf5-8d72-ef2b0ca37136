namespace Atlas.Application.Models.Note;
public sealed class NoteListItem
{
    public int NoteId { get; set; }
    public int ContentId { get; set; }
    public int WorkgroupId { get; set; }
    public Guid ContentUuId { get; set; }
    public int ClientId { get; set; }
    public string Title { get; set; } = "";
    public string TextPreview { get; set; } = "";
    public DateTime LastUpdate { get; set; }
    public string Status { get; set; } = "";
}

public class NoteListGetResponse
{
    public IEnumerable<NoteListItem> Items { get; set; } = Enumerable.Empty<NoteListItem>();
    public int Total { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
}
