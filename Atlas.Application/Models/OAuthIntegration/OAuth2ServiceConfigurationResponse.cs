namespace Atlas.Application.Models.OAuthIntegration
{
    public class OAuth2ServiceConfigurationResponse
    {
        public string serviceName { get; set; }
        public string oauthClientId { get; set; }
        public bool oauthSecretBasicAuth { get; set; }
        public string oauthScope { get; set; }
        public string oauthScopeAuthorize { get; set; }
        public string tokenUrl { get; set; }
        public string authorizationUrl { get; set; }
        public bool isEnabled { get; set; }
        public bool disableChange { get; set; }
        
    }
}
