using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Atlas.Application.Models.ContentAttachment;
using Atlas.CrossCutting.DTO.ContentUserMentions;

namespace Atlas.Application.Models.ContentComment
{
    public class ContentCommentCreateRequest
    {
        [JsonIgnore]
        public Guid contentUuid { get; set; }

        [Required]
        public string text { get; set; }
        
        public int? parentCommentId { get; set; }
        public ICollection<ContentAttachmentCreateRequest>? attachments { get; set; }
        public ICollection<ContentUserMentionsDto>? userMentions { get; set; }
    }
}