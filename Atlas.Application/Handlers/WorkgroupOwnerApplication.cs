using Atlas.Application.Interfaces;
using Atlas.Application.Models.WorkgroupOwners;
using Atlas.Business.Core.WorkgroupOwners;
using Atlas.CrossCutting.Exceptions;
using Atlas.CrossCutting.Exceptions.ErrorCodes;
using Atlas.CrossCutting.Helpers;
using Atlas.CrossCutting.Logging;
using Atlas.CrossCutting.Models.Responses;
using Atlas.CrossCutting.Services;
using MapsterMapper;
using System.Net;
using System.Security;
using System.Security.Principal;

namespace Atlas.Application.Handlers
{
    internal class WorkgroupOwnerApplication(
        IPrincipal principal,
        IStructuredLogger logger,
        IWorkgroupOwnerService workgroupOwnerService,
        IMapper mapper,
        IRequestContextService requestContext
    ) : IWorkgroupOwnerApplication
    {
        private readonly AuthUtil _authUtil = new AuthUtil(principal);
        private readonly IStructuredLogger _logger = logger;
        private readonly IWorkgroupOwnerService _workgroupOwnerService = workgroupOwnerService;
        private readonly IMapper _mapper = mapper;
        private readonly IRequestContextService _requestContext = requestContext;

        public async Task<PagedResult<WorkgroupOwnerGetResponse>> GetOwners(WorkgroupOwnerGetRequest request)
        {
            try
            {
                var ownersResponse = await _workgroupOwnerService.GetOwners(request.workgroupId, request.pageNumber, request.pageSize);
                
                return new PagedResult<WorkgroupOwnerGetResponse>(
                    ownersResponse.Items.Select(map => _mapper.Map<WorkgroupOwnerGetResponse>(map)).ToList(),
                    ownersResponse.CurrentPage,
                    ownersResponse.PageSize,
                    ownersResponse.TotalItems);
            }
            catch (SecurityException ex)
            {
                _logger.LogError("Security error while retrieving the announcement details.", ex);
                throw new HttpCustomException(GeneralErrorCodes.AccessDenied, HttpStatusCode.Forbidden);
            }
            catch (Exception ex)
            {
                _logger.LogError("Error while retrieving the announcement details.", ex);
                throw new HttpCustomException(AnnouncementErrorCodes.AnnouncementDetailsGetError, HttpStatusCode.UnprocessableEntity);
            }
        }
    }
}
