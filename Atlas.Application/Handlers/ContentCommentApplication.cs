using System.Net;
using System.Security;
using Atlas.Application.Interfaces;
using Atlas.Application.Models.ContentComment;
using Atlas.Business.Core.ContentComment;
using Atlas.CrossCutting.AppEnums;
using Atlas.CrossCutting.DTO.ContentComment;
using Atlas.CrossCutting.Exceptions;
using Atlas.CrossCutting.Exceptions.ErrorCodes;
using Atlas.CrossCutting.Logging;
using Atlas.CrossCutting.Services;
using Atlas.Data.Entities;
using MapsterMapper;
using Microsoft.EntityFrameworkCore;

namespace Atlas.Application.Handlers
{
    public class ContentCommentApplication
    (
        IStructuredLogger logger,
        IMapper mapper,
        IRequestContextService requestContext,
        IContentCommentService contentCommentService
    ) : IContentCommentApplication
    {
        private readonly IStructuredLogger _logger = logger;
        private readonly IMapper _mapper = mapper;
        private readonly IRequestContextService _requestContext = requestContext;
        private readonly IContentCommentService _contentCommentService = contentCommentService;

        /// <summary>
        /// Creates a new comment for content and returns the ID of the newly created comment.
        /// </summary>
        /// <param name="request">The request containing the comment data.</param>
        /// <returns>The ID of the newly created comment.</returns>
        public async Task<ContentCommentResponse> CreateCommentAsync(ContentCommentCreateRequest request)
        {
            try
            {
                var dto = _mapper.Map<ContentCommentCreateDto>(request);

                var contentComment = await _contentCommentService.AddComment(
                    dto,
                    _requestContext.UserAgent);

                var result = _mapper.Map<ContentCommentResponse>(contentComment);

                return result;
            }
            catch (SecurityException ex)
            {
                _logger.LogError("Security error while creating comment.", ex);
                throw new HttpCustomException(GeneralErrorCodes.AccessDenied, HttpStatusCode.Forbidden);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError("Invalid operation error while creating comment.", ex);
                throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.BadRequest);
            }
        }

        /// <summary>
        /// Retrieves all comments for the specified content.
        /// </summary>
        /// <param name="contentUuid">The unique identifier of the content to get comments for.</param>
        /// <returns>A list of comments associated with the content.</returns>
        public async Task<GetContentCommentResponse> GetCommentsAsync(Guid contentUuid, bool includeDeleted = false)
        {
            try
            {
                using var atlasModel = new AtlasModelCore();

                var response = new GetContentCommentResponse();

                var content = await atlasModel.Content
                    .Include(c => c.Workgroup)
                    .Include(c => c.Meeting)
                    .Include(c => c.ContentPermission)
                    .Include(c => c.Parent_Content)
                        .ThenInclude(pc => pc.ContentPermission)
                    .AsSplitQuery()
                    .FirstOrDefaultAsync(c => c.contentUuid == contentUuid && c.deleted != true);

                if (content == null)
                    return response;

                var allComments = await atlasModel.ContentComment
                    .Include(cc => cc.User)
                    .Include(cc => cc.ContentAttachment.Where(ca => ca.deleted != true))
                        .ThenInclude(ca => ca.Attachment)
                    .Include(cc => cc.ContentUserMention)
                        .ThenInclude(cum => cum.User)
                    .AsSplitQuery()
                    .Where(cc => cc.contentUuid == contentUuid)
                    .Where(cc => includeDeleted ? cc.deleted == true : cc.deleted != true)
                    .OrderBy(cc => cc.date)
                    .ToListAsync();

                if (allComments.Count == 0)
                    return response;

                if (content.type == ContentTypes.MeetingAgendaItem && content.parentContentId != null)
                {
                    await ProcessMeetingAgendaItemComments(allComments, content, atlasModel);
                }

                if (content.type == ContentTypes.Task)
                {
                    await ProcessTaskComments(allComments, content, atlasModel);
                }

                var parentComments = allComments.Where(c => !c.parentCommentId.HasValue).ToList();
                var childComments = allComments.Where(c => c.parentCommentId.HasValue).ToList();

                var groupedChildren = childComments.GroupBy(c => c.parentCommentId.Value).ToDictionary(g => g.Key, g => g.OrderBy(c => c.date).ToList());

                bool taskClosedOrCancelled = content.type == ContentTypes.Task && (content.status == ContentStatuses.ResolutionStatus.CLOSED || content.status == ContentStatuses.ResolutionStatus.CANCELLED);

                bool parentTaskClosedOrCancelled = false;

                if (content.type == ContentTypes.Task && content.parentContentUuid != null)
                {
                    var parentTask = await atlasModel.Content
                        .Where(c => c.contentUuid == content.parentContentUuid && c.deleted != true && c.type == ContentTypes.Task)
                        .Select(c => new { c.status })
                        .FirstOrDefaultAsync();

                    parentTaskClosedOrCancelled = parentTask != null && (parentTask.status == ContentStatuses.ResolutionStatus.CLOSED || parentTask.status == ContentStatuses.ResolutionStatus.CANCELLED);
                }

                foreach (var parent in parentComments)
                {
                    if (taskClosedOrCancelled || parentTaskClosedOrCancelled)
                    {
                        parent.ContentCommentAnswers = new List<ContentComment>();
                    }
                    else
                    {
                        parent.ContentCommentAnswers = groupedChildren.ContainsKey(parent.contentCommentId)
                            ? groupedChildren[parent.contentCommentId]
                            : new List<ContentComment>();
                    }
                }

                response.Comments = _mapper.Map<List<ContentCommentResponse>>(parentComments);

                var permissionedAgenda = content.ContentPermission
                    .Select(cp => cp.userId)
                    .ToHashSet();

                var permissionedParentContent = content.Parent_Content?.ContentPermission
                    .Select(cp => cp.userId)
                    .ToHashSet();

                if (permissionedParentContent != null && permissionedAgenda != null)
                {
                    var authorizedUsers = permissionedAgenda.Intersect(permissionedParentContent).ToHashSet();

                    foreach (var comment in response.Comments)
                    {
                        comment.userIsUnauthorized = !authorizedUsers.Contains(comment.userId);

                        if (comment.answers != null && comment.answers.Any())
                        {
                            foreach (var answer in comment.answers)
                            {
                                answer.userIsUnauthorized = !authorizedUsers.Contains(answer.userId);
                            }
                        }
                    }
                }

                response.count = allComments.Count;

                return response;
            }
            catch (SecurityException ex)
            {
                _logger.LogError("Security error while getting comments.", ex);
                throw new HttpCustomException(GeneralErrorCodes.AccessDenied, HttpStatusCode.Forbidden);
            }
            catch (Exception ex)
            {
                _logger.LogError("Error while getting comments.", ex);
                throw new HttpCustomException("Error retrieving comments", HttpStatusCode.InternalServerError);
            }
        }

        private async System.Threading.Tasks.Task ProcessMeetingAgendaItemComments(List<ContentComment> contentComments, Content content, AtlasModelCore atlasModel)
        {
            var parentContent = await atlasModel.Content
                .Include(c => c.Meeting)
                .FirstOrDefaultAsync(c => c.contentId == content.parentContentId && c.deleted != true);

            var dateMeeting = parentContent?.Meeting.FirstOrDefault()?.date;

            if (!dateMeeting.HasValue)
                return;

            var userZone = TimeZoneInfo.FindSystemTimeZoneById("E. South America Standard Time"); // Default timezone

            var meetingDateUtc = TimeZoneInfo.ConvertTimeFromUtc(dateMeeting.Value, userZone);
            var limitDate = new DateTime(meetingDateUtc.Year, meetingDateUtc.Month, meetingDateUtc.Day, 23, 59, 59);

            foreach (var contentComment in contentComments)
            {
                var commentDateUtc = TimeZoneInfo.ConvertTimeFromUtc(contentComment.date, userZone);
                var compareDate = DateTime.Compare(commentDateUtc, limitDate);
                contentComment.isAddedAfterMeeting = compareDate > 0;
            }
        }

        private async System.Threading.Tasks.Task ProcessTaskComments(List<ContentComment> contentComments, Content content, AtlasModelCore atlasModel)
        {
            bool taskLocked = content.status == ContentStatuses.ResolutionStatus.CLOSED || content.status == ContentStatuses.ResolutionStatus.CANCELLED;

            bool parentLocked = false;

            if (content.parentContentUuid != null)
            {
                var parentTask = await atlasModel.Content
                    .Where(c => c.contentUuid == content.parentContentUuid && c.deleted != true && c.type == ContentTypes.Task)
                    .Select(c => new { c.status })
                    .FirstOrDefaultAsync();

                parentLocked = parentTask != null && (parentTask.status == ContentStatuses.ResolutionStatus.CLOSED || parentTask.status == ContentStatuses.ResolutionStatus.CANCELLED);
            }

            if (taskLocked || parentLocked)
            {
                foreach (var comment in contentComments)
                {
                    if (comment.ContentAttachment == null || comment.ContentAttachment.Count == 0) continue;

                    foreach (var ca in comment.ContentAttachment)
                    {
                        ca.locked = true;
                    }
                }
            }
        }

        /// <summary>
        /// Deletes or marks a comment as deleted based on the request parameters.
        /// </summary>
        /// <param name="request">The request containing the comment deletion details.</param>
        /// <returns>True if the comment was successfully deleted; otherwise, false.</returns>
        public async Task<ContentCommentResponse> DeleteCommentAsync(ContentCommentDeleteRequest request)
        {
            try
            {
                var result = await _contentCommentService.DeleteCommentAsync(request.contentUuid, request.commentId, true, _requestContext.UserAgent);

                return _mapper.Map<ContentCommentResponse>(result);
            }
            catch (SecurityException ex)
            {
                _logger.LogError("Security error while deleting comment.", ex);
                throw new HttpCustomException(GeneralErrorCodes.AccessDenied, HttpStatusCode.Forbidden);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError("Invalid operation error while deleting comment.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.BadRequest);
            }
            catch (Exception ex)
            {
                _logger.LogError("Error while deleting comment.", ex);
                throw new HttpCustomException("Error deleting comment", HttpStatusCode.InternalServerError);
            }
        }
    }
}