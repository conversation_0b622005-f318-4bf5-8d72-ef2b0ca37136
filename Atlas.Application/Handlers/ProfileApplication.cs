using System.Net;
using System.Security.Principal;
using Atlas.Application.Interfaces;
using Atlas.Application.Models.Profile;
using Atlas.Business;
using Atlas.Business.Helpers;
using Atlas.Business.ViewModels;
using Atlas.CrossCutting.DTO.AuditLog;
using Atlas.CrossCutting.Exceptions;
using Atlas.CrossCutting.Exceptions.ErrorCodes;
using Atlas.CrossCutting.Helpers;
using Atlas.CrossCutting.Logging;
using Atlas.CrossCutting.Services;
using Atlas.CrossCutting.Settings;
using MapsterMapper;

namespace Atlas.Application.Handlers
{
    internal class ProfileApplication
    (
        IPrincipal principal,
        IStructuredLogger logger,
        IMapper mapper,
        StorageSettings storageSettings,
        IRequestContextService requestContext
    ) : IProfileApplication
    {
        private readonly IStructuredLogger _logger = logger;
        private readonly AuthUtil _authUtil = new AuthUtil(principal);
        private readonly IMapper _mapper = mapper;
        private readonly StorageSettings _storageSettings = storageSettings;
        private readonly IRequestContextService _requestContext = requestContext;

        /// <summary>
        /// Asynchronously retrieves the current user's profile.
        /// </summary>
        /// <returns>A task that represents the asynchronous operation. The task result contains the profile response.</returns>
        /// <exception cref="HttpCustomException">Thrown when an error occurs during data retrieval.</exception>
        public async Task<ProfileGetResponse> GetAsync()
        {
            try
            {
                UserService svc_usr = new UserService();

                ProfileViewModel profile = await svc_usr.GetProfile(currentUser: _authUtil.UserId, requested_userId: _authUtil.UserId);

                var response = _mapper.Map<ProfileGetResponse>(profile);

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error retrieving profile.", ex);
                throw new HttpCustomException(ProfileErrorCodes.ProfileNotValid, HttpStatusCode.BadRequest);
            }
        }

        public async Task<ProfileSimplifiedGetResponse> GetSimplifiedAsync()
        {
            try
            {
                UserService svc_usr = new UserService();

                var user = await svc_usr.GetSimplifiedProfile(_authUtil.UserId);

                var simplifiedResponse = _mapper.Map<ProfileSimplifiedGetResponse>(user);

                return simplifiedResponse;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error retrieving profile.", ex);
                throw new HttpCustomException(ProfileErrorCodes.ProfileNotValid, HttpStatusCode.BadRequest);
            }
        }

        /// <summary>
        /// Asynchronously retrieves the user's profile with the given ID.
        /// </summary>
        /// <param name="request">The request containing the user ID.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the profile response.</returns>
        /// <exception cref="HttpCustomException">Thrown when an error occurs during data retrieval.</exception>
        public async Task<ProfileGetResponse> GetAsync(ProfileGetRequest request)
        {
            try
            {
                UserService svc_usr = new UserService();
                var user = svc_usr.GetUserByEmail(_authUtil.Email);

                ProfileViewModel profile = await svc_usr.GetProfile(user.userId, request.id);

                var response = _mapper.Map<ProfileGetResponse>(profile);

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error retrieving profile.", ex);
                throw new HttpCustomException(ProfileErrorCodes.ProfileNotValid, HttpStatusCode.BadRequest);
            }
        }

        /// <summary>
        /// Asynchronously retrieves the user's profile with the given ID,
        /// but hides the user's email and mobile phone number.
        /// </summary>
        /// <param name="request">The request containing the user ID.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the profile response.</returns>
        /// <exception cref="HttpCustomException">Thrown when an error occurs during data retrieval.</exception>
        public async Task<ProfileGetUserInfoResponse> GetUserInfoAsync(ProfileGetRequest request)
        {
            try
            {
                UserService svc_usr = new UserService();
                var userData = await svc_usr.GetProfile(_authUtil.UserId, request.id, shouldHideSensibleInfo: true);

                var response = _mapper.Map<ProfileGetUserInfoResponse>(userData);

                bool isClientAdmin = userData.isClientAdminForUser ?? false;
                bool isSuperAdmin = userData.isSuperAdmin ?? false;
                bool isYourself = userData.userId == _authUtil.UserId;
                response.canEdit = isClientAdmin || isSuperAdmin || isYourself;

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error retrieving profile info.", ex);
                throw new HttpCustomException(ProfileErrorCodes.ProfileNotValid, HttpStatusCode.BadRequest);
            }
        }

        /// <summary>
        /// Retrieves a list of users for the specified client as a byte array.
        /// </summary>
        /// <param name="clientId">The ID of the client for which to retrieve the user list.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the user list as a byte array.</returns>
        /// <exception cref="HttpCustomException">Thrown when an error occurs during user list retrieval.</exception>
        public async Task<ExportResultDto> GetUserListAsync(int clientId)
        {
            try
            {
                UserService svc_usr = new();
                return await svc_usr.GetUserList(_authUtil.UserId, clientId);
            }
            catch (Exception ex)
            {
                _logger.LogError("Error retrieving user list info.", ex);
                throw new HttpCustomException(ProfileErrorCodes.ProfileNotValid, HttpStatusCode.BadRequest);
            }
        }

        /// <summary>
        /// Asynchronously retrieves the current user's email or mobile phone number
        /// based on the given request.
        /// </summary>
        /// <param name="request">The request containing the type of sensible user info to retrieve.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the retrieved email or mobile number.</returns>
        /// <exception cref="HttpCustomException">
        /// Thrown when the type of sensible user info is invalid or an error occurs during the retrieval.
        /// </exception>
        public async Task<ProfileGetSensibleUserInfoResponse> GetSensibleUserInfoAsync(ProfileGetSensibleUserInfoRequest request)
        {
            try
            {
                var userAgent = _requestContext.UserAgent;
                UserService svc_usr = new UserService();
                string[] infoTypes = { "email", "mobile" };
                var type = request.Type.ToLower();
                if (!infoTypes.Contains(type))
                {
                    throw new HttpCustomException(GeneralErrorCodes.InfoTypeInvalid, HttpStatusCode.BadRequest);
                }
                var userData = await svc_usr.GetSensibleUserInfo(_authUtil.UserId, request.UserId, type, userAgent: userAgent);
                string? emailOrMobile = userData.GetType().GetProperty(type)?.GetValue(userData, null)?.ToString();
                if (type == "email" && string.IsNullOrEmpty(emailOrMobile))
                {
                    throw new HttpCustomException(GeneralErrorCodes.InfoTypeInvalid, HttpStatusCode.BadRequest);
                }
                return new ProfileGetSensibleUserInfoResponse { value = emailOrMobile, type = type };
            }
            catch (Exception ex)
            {
                _logger.LogError("Error retrieving sensible user info.", ex);
                throw new HttpCustomException(ProfileErrorCodes.ProfileSensitiveInfoError, HttpStatusCode.BadRequest);
            }
        }

        /// <summary>
        /// Asynchronously updates the current user's profile.
        /// </summary>
        /// <param name="request">The request containing the updated profile data.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains true if the update was successful.</returns>
        /// <exception cref="HttpCustomException">
        /// Thrown when the update request is invalid or an error occurs during the update.
        /// </exception>
        public async Task<bool> UpdateAsync(ProfileUpdateRequest request)
        {
            try
            {
                var userAgent = _requestContext.UserAgent;
                UserService svc_usr = new UserService(_storageSettings, principal);

                var profile = _mapper.Map<ProfileUpdateViewModel>(request);
                profile.userId = _authUtil.UserId;
                profile.userAgent = userAgent;

                await svc_usr.UpdateSelfProfile(profile);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error updating profile.", ex);
                throw new HttpCustomException(ProfileErrorCodes.ProfileNotValid, HttpStatusCode.BadRequest);
            }
        }

        /// <summary>
        /// Asynchronously updates the profile of a user with the specified ID.
        /// </summary>
        /// <param name="request">The request containing the updated profile data.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains true if the update was successful.</returns>
        /// <exception cref="HttpCustomException">
        /// Thrown when the update request is invalid or an error occurs during the update.
        /// </exception>
        public async Task<bool> UpdateAsync(int requestedUserId, int clientId, ProfileUpdateUserRequest request)
        {
            try
            {
                var userAgent = _requestContext.UserAgent;
                UserService svc_usr = new UserService(_storageSettings, principal);

                var profile = _mapper.Map<ProfileUpdateViewModel>(request);
                profile.userAgent = userAgent;

                await svc_usr.UpdateUserProfile(_authUtil.UserId, clientId, profile);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error updating profile.", ex);
                throw new HttpCustomException(ProfileErrorCodes.ProfileNotValid, HttpStatusCode.BadRequest);
            }
        }

        /// <summary>
        /// Asynchronously uploads a profile picture for the current user.
        /// </summary>
        /// <param name="request">The request containing the profile picture file.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains true if the upload was successful.</returns>
        /// <exception cref="HttpCustomException">
        /// Thrown when the file is empty, exceeds the maximum size, or an error occurs during upload.
        /// </exception>
        public async Task<ProfileUploadPictureResponse> UploadProfilePictureAsync(ProfileUploadPictureRequest request)
        {
            try
            {
                var permittedImageTypes = new[] { "image/jpeg", "image/png", "image/gif", "image/webp", "image/bmp", "image/tiff" };

                if (!permittedImageTypes.Contains(request.file.ContentType.ToLower()))
                {
                    throw new HttpCustomException(ProfileErrorCodes.ProfileFileEmpty, HttpStatusCode.BadRequest);
                }

                if (request.file == null || request.file.Length == 0)
                {
                    throw new HttpCustomException(ProfileErrorCodes.ProfileFileEmpty, HttpStatusCode.BadRequest);
                }

                if (request.file.Length > FileUtils.MaxFileSize)
                {
                    throw new HttpCustomException(ProfileErrorCodes.ProfilePictureSizeExceeded, HttpStatusCode.RequestEntityTooLarge);
                }

                byte[] fileBytes;

                using (var memoryStream = new MemoryStream())
                {
                    await request.file.CopyToAsync(memoryStream);
                    fileBytes = memoryStream.ToArray();
                }

                UserService svc = new UserService(_storageSettings);

                var result = await svc.UploadProfilePictureFromWebFlow(_authUtil.UserId, _authUtil.UserId, fileBytes);

                return new ProfileUploadPictureResponse() { url = result.Url };
            }
            catch (Exception ex)
            {
                _logger.LogError("Error uploading profile picture.", ex);
                throw new HttpCustomException(ProfileErrorCodes.ProfileUploadFileError, HttpStatusCode.BadRequest);
            }
        }

        public async Task<bool> RemoveProfilePictureAsync()
        {
            try
            {
                UserService svc = new UserService(_storageSettings);

                var result = await svc.RemoveProfilePicture(_authUtil.UserId, _authUtil.UserId);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error removing profile picture.", ex);
                throw new HttpCustomException(ProfileErrorCodes.ProfileUploadFileError, HttpStatusCode.BadRequest);
            }
        }

        /// <summary>
        /// Deletes the user to archive based on the request.
        /// </summary>
        /// <param name="request">Request containing the user ID to be deleted.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains true if the deletion was successful, false otherwise.</returns>
        public async Task<bool> DeleteUserToArchiveAsync(ProfileDeleteArchiveRequest request)
        {
            try
            {
                AdminService adm_svc = new AdminService(_authUtil, request.ClientId);
                var deletedUser = await adm_svc.DeleteUserToArchive(request.UserId, request.ClientId);

                return deletedUser != null;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error deleting user to archive.", ex);
                throw new HttpCustomException(ProfileErrorCodes.ProfileDeleteArchiveError, HttpStatusCode.BadRequest);
            }
        }

        public async Task<BlockUserResponse> BlockOrUnblockUser(BlockUserRequest request)
        {
            try
            {
                AdminService adminService = new AdminService(_authUtil);
                bool result = await adminService.BlockUser(request.UserId, request.Block);

                return new BlockUserResponse
                {
                    Success = result,
                };
            }
            catch (Exception ex)
            {
                _logger.LogError("Error blocking/unblocking user.", ex);
                throw new HttpCustomException(ProfileErrorCodes.ProfileBlockUserError, HttpStatusCode.BadRequest);
            }
        }

        public async Task<ApproveUserResponse> ApproveOrRejectUser(ApproveUserRequest request)
        {
            try
            {
                int currentUserId = _authUtil.UserId;
                AdminService adminService = new AdminService(_authUtil, request.ClientId);
                bool result = await adminService.ApproveUser(request.UserId, request.Approved);

                return new ApproveUserResponse
                {
                    Success = result
                };
            }
            catch (Exception ex)
            {
                _logger.LogError("Error approving/rejecting user.", ex);
                throw new HttpCustomException(ProfileErrorCodes.ProfileApproveUserError, HttpStatusCode.BadRequest);
            }
        }

        /// <summary>
        /// Sets or removes the admin status for a user in a specific client.
        /// </summary>
        /// <param name="request">The request containing the user ID, client ID, and the desired admin status.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the result of the operation.</returns>
        public async Task<ProfileSetUserAdminResponse> SetUserAdminStatus(ProfileSetUserAdminRequest request)
        {
            try
            {
                AdminService adminService = new(_authUtil, request.ClientId);
                bool result = await adminService.SetUserClientAdmin(request.ClientId,
                                                                    request.UserId,
                                                                    request.SetAdmin);
                return new ProfileSetUserAdminResponse
                {
                    Success = result
                };
            }
            catch (Exception ex)
            {
                _logger.LogError("Error setting user admin status.", ex);
                throw new HttpCustomException(ProfileErrorCodes.ProfileSetUserAdminError, HttpStatusCode.BadRequest);
            }
        }

        public async Task<bool> ResendInviteAsync(ResendInviteRequest request)
        {
            try
            {
                AdminService adminService = new(_authUtil.UserId, request.ClientId);
                bool result = await adminService.ResendInvite(request.UserId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error resending invite.", ex);
                throw new HttpCustomException(ProfileErrorCodes.ProfileResendInviteError, HttpStatusCode.BadRequest);
            }
        }

        public async Task<ProfileUserSessionsCsvGetResponse> GetUserSessionsCsvData(ProfileUserSessionsCsvGetRequest request)
        {
            try
            {
                var exportService = new Atlas.Business.Core.Export.ExportService(_storageSettings, _authUtil.UserId);

                var result = await exportService.ExportUserSessionsCsv(new ExportUserSessionsDataViewModel
                {
                    clientId = request.clientId,
                    startDate = request.startDate,
                    endDate = request.endDate
                });

                return new ProfileUserSessionsCsvGetResponse
                {
                    FileContent = result,
                    ContentType = "text/csv",
                    Filename = $"Atlas_AuditLog_{DateTime.UtcNow:yyyyMMdd_HHmmss}.csv"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError("ProfileApplication: Error while getting user sessions CSV.", ex);
                throw new HttpCustomException(ProfileErrorCodes.ProfileGetUserSessionsError, HttpStatusCode.BadRequest);
            }
        }
    }
}