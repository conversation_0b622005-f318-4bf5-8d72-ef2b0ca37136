using Atlas.Application.Interfaces;
using Atlas.Application.Models.Session;
using Atlas.Business.Core.FeatureFlag;
using Atlas.Business.Core.Session;
using Atlas.Business.Policies.FeatureFlag;
using Atlas.CrossCutting.Exceptions;
using Atlas.CrossCutting.Exceptions.ErrorCodes;
using Atlas.CrossCutting.Logging;
using MapsterMapper;
using System.Net;
using System.Security.Principal;

namespace Atlas.Application.Handlers
{
    internal class SessionApplication
    (
        IPrincipal principal,
        IStructuredLogger logger,
        IMapper mapper,
        IFeatureFlagService featureFlagService
    ) : ISessionApplication
    {
        private readonly IStructuredLogger _logger = logger;
        private readonly IMapper _mapper = mapper;
        private readonly IFeatureFlagService _featureFlagService = featureFlagService;

        public async Task<List<SessionGetResponse>> GetSessions(SessionGetRequest request)
        {
            // Validate remote session control feature flag permission
            var hasFeatureAccess = await _featureFlagService.ValidateUserFeatureFlag(FeatureFlagKeys.RemoteSessionControl);

            if (!hasFeatureAccess)
            {
                throw new FeatureFlagUnauthorizedException(FeatureFlagKeys.RemoteSessionControl);
            }

            try
            {
                SessionManagerService svc = new SessionManagerService(principal.Identity);
                
                var sessions = await svc.GetSessions(request.userId);

                var response = _mapper.Map<List<SessionGetResponse>>(sessions);

                return response;

            }
            catch (Exception ex)
            {
                _logger.LogError("Error getting sessions.", ex);
                throw new HttpCustomException(SessionErrorCodes.ErrorGettingUserSessions, HttpStatusCode.UnprocessableContent);
            }
        }

        public async Task<SessionRevokeDeleteResponse> RevokeSession(SessionRevokeDeleteRequest request)
        {
            try
            {
                SessionManagerService svc = new SessionManagerService(principal.Identity);
                
                var revoked = await svc.RevokeSession(request.sessionKey, request.isLogoff);

                return new SessionRevokeDeleteResponse { revoked = revoked };
            }
            catch (Exception ex)
            {
                _logger.LogError("Error revoking session.", ex);
                throw new HttpCustomException(SessionErrorCodes.ErrorRevokingSession, HttpStatusCode.UnprocessableContent);
            }
        }

        public async Task<SessionRevokeAnonymouslyDeleteResponse> RevokeSessionAnonymously(SessionRevokeAnonymouslyDeleteRequest request)
        {
            try
            {
                await new SessionManagerService(anonymousRequest: true).RevokeSessionAnonymously(request.sessionKey);
                
                return new SessionRevokeAnonymouslyDeleteResponse { revoked = true };
            }
            catch (Exception ex)
            {
                _logger.LogError("Error revoking session.", ex);
                throw new HttpCustomException(SessionErrorCodes.RevokeTicketNotValid, HttpStatusCode.BadRequest);
            }
        }
    }
}
