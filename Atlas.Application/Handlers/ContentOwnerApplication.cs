using System.Net;
using System.Security;
using System.Security.Principal;
using Atlas.Application.Interfaces;
using Atlas.Application.Models.ContentOwner;
using Atlas.Business.Core.ContentOwner;
using Atlas.CrossCutting.DTO.ContentOwner;
using Atlas.CrossCutting.Exceptions;
using Atlas.CrossCutting.Exceptions.ErrorCodes;
using Atlas.CrossCutting.Helpers;
using Atlas.CrossCutting.Logging;
using Atlas.CrossCutting.Settings;
using MapsterMapper;
using Microsoft.Extensions.Configuration;

namespace Atlas.Application.Handlers
{
    internal class ContentOwnerApplication(
        IPrincipal principal,
        IStructuredLogger logger,
        IConfiguration configuration,
        IMapper mapper,
        StorageSettings storageSettings,
        IContentOwnerService contentOwnerService
    ) : IContentOwnerApplication
    {
        private readonly IStructuredLogger _logger = logger;
        private readonly AuthUtil _authUtil = new AuthUtil(principal);
        private readonly IConfiguration _configuration = configuration;
        private readonly IMapper _mapper = mapper;
        private readonly StorageSettings _storageSettings = storageSettings;
        private readonly IContentOwnerService _contentOwnerService = contentOwnerService;

        public async Task<ContentOwnerGetResponse> GetOwnersAsync(ContentOwnerGetRequest request)
        {
            try
            {

                var owners = await _contentOwnerService.GetOwnersAsync(request.contentUuId);
                var availableUsers = await _contentOwnerService.GetAvailableUsersAsync(
                                                                request.workgroupId,
                                                                request.contentUuId,
                                                                owners.Select(o => o.userId).ToArray());

                var result = owners.Concat(availableUsers).ToList();

                var response = new ContentOwnerGetResponse
                {
                    Users = _mapper.Map<List<OwnerUserRequest>>(result).OrderBy(u => u.userName).ToList()
                };

                return response;
            }
            catch (SecurityException ex)
            {
                _logger.LogError("Security error while getting content owners.", ex);
                throw new HttpCustomException(GeneralErrorCodes.AccessDenied, HttpStatusCode.Forbidden);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError("Invalid operation error while getting content owners.", ex);
                throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.Forbidden);
            }
            catch (ArgumentException ex)
            {
                _logger.LogError("Validation error while getting content owners.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.UnprocessableEntity);
            }
            catch (Exception ex)
            {
                _logger.LogError("Unexpected error while getting content owners.", ex);
                throw new HttpCustomException(ContentErrorCodes.WorkgroupDataOfContentError, HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ContentOwnerCreateResponse> AddOwnerAsync(ContentOwnerCreateRequest request)
        {
            try
            {
                var result = await _contentOwnerService.AddOwnerAsync(request.contentUuId, request.userId);

                return new ContentOwnerCreateResponse
                {
                    contentOwnerId = result
                };
            }
            catch (SecurityException ex)
            {
                _logger.LogError("Security error while adding content owner.", ex);
                throw new HttpCustomException(GeneralErrorCodes.AccessDenied, HttpStatusCode.Forbidden);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError("Invalid operation error while adding content owner.", ex);
                throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.Forbidden);
            }
            catch (ArgumentException ex)
            {
                _logger.LogError("Validation error while adding content owner.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.UnprocessableEntity);
            }
            catch (Exception ex)
            {
                _logger.LogError("Unexpected error while adding content owner.", ex);
                throw new HttpCustomException(ContentErrorCodes.WorkgroupDataOfContentError, HttpStatusCode.InternalServerError);
            }
        }

        public async Task<bool> RemoveOwnerAsync(ContentOwnerDeleteRequest request)
        {
            try
            {
                return await _contentOwnerService.RemoveOwnerAsync(request.contentUuId, request.userId);
            }
            catch (SecurityException ex)
            {
                _logger.LogError("Security error while removing content owner.", ex);
                throw new HttpCustomException(GeneralErrorCodes.AccessDenied, HttpStatusCode.Forbidden);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError("Invalid operation error while removing content owner.", ex);
                throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.Forbidden);
            }
            catch (ArgumentException ex)
            {
                _logger.LogError("Validation error while removing content owner.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.UnprocessableEntity);
            }
            catch (Exception ex)
            {
                _logger.LogError("Unexpected error while removing content owner.", ex);
                throw new HttpCustomException(ContentErrorCodes.WorkgroupDataOfContentError, HttpStatusCode.InternalServerError);
            }
        }

        public async Task<bool> UpdateOwnersAsync(ContentOwnerUpdateRequest request)
        {
            try
            {
                var ownerDTOs = _mapper.Map<List<ContentOwnerDTO>>(request.Owners);
                return await _contentOwnerService.UpdateOwnersAsync(request.contentUuId, ownerDTOs);
            }
            catch (SecurityException ex)
            {
                _logger.LogError("Security error while updating content owners.", ex);
                throw new HttpCustomException(GeneralErrorCodes.AccessDenied, HttpStatusCode.Forbidden);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError("Invalid operation error while updating content owners.", ex);
                throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.Forbidden);
            }
            catch (ArgumentException ex)
            {
                _logger.LogError("Validation error while updating content owners.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.UnprocessableEntity);
            }
            catch (Exception ex)
            {
                _logger.LogError("Unexpected error while updating content owners.", ex);
                throw new HttpCustomException(ContentErrorCodes.WorkgroupDataOfContentError, HttpStatusCode.InternalServerError);
            }
        }
    }
}
