using Atlas.Application.Interfaces;
using Atlas.Application.Models.Admin;
using Atlas.Application.Models.AdminArea;
using Atlas.Business;
using Atlas.Business.Core.Client;
using Atlas.Business.Helpers;
using Atlas.Business.ViewModels;
using Atlas.CrossCutting.DTO.Client;
using Atlas.CrossCutting.Exceptions;
using Atlas.CrossCutting.Exceptions.ErrorCodes;
using Atlas.CrossCutting.Helpers;
using Atlas.CrossCutting.Logging;
using Atlas.CrossCutting.Settings;
using Atlas.Data.Entities;
using MapsterMapper;
using System.Net;
using System.Security;
using System.Security.Principal;
using Atlas.Business.Core.Workgroup;
using Atlas.CrossCutting.DTO.Workgroup;
using Atlas.Business.Core.Email;

namespace Atlas.Application.Handlers
{
    internal class SuperAdminClientApplication
        (IPrincipal principal,
        IStructuredLogger logger,
        IMapper mapper,
        StorageSettings storageSettings,
        IClientManagerService clientService,
        IEmailService emailService)

        : ISuperAdminClientApplication
    {
        private readonly AuthUtil _authUtil = new AuthUtil(principal);
        private readonly IStructuredLogger _logger = logger;
        private readonly IMapper _mapper = mapper;
        private readonly StorageSettings _storageSettings = storageSettings;

        private readonly IClientManagerService _clientService = clientService;
        private readonly IWorkgroupService _workgroupService = new Business.Core.Workgroup.WorkgroupService(storageSettings, principal, emailService);

        public async Task<SuperAdminClientGetResponse> GetSuperAdminClientAsync()
        {
            try
            {
                Atlas.Business.ClientService _clientService = new(_authUtil.UserId);
                List<Client> clientsData = await _clientService.GetClientsList();

                var clients = clientsData.Select(client => _mapper.Map<SuperAdminClientGet>(client)).ToList();

                return new SuperAdminClientGetResponse(clients);
            }

            catch (SecurityException ex)
            {
                _logger.LogError("Security error while trying to get list of clients.", ex);
                throw new HttpCustomException(GeneralErrorCodes.AccessDenied, HttpStatusCode.Forbidden);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError("Invalid operation error while trying to get list of clients.", ex);
                throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.Forbidden);
            }
            catch (Exception ex)
            {
                _logger.LogError("Unexpected error while trying to get list of clients.", ex);
                throw new HttpCustomException(GeneralErrorCodes.UnexpectedError, HttpStatusCode.InternalServerError);
            }
        }

        public async Task<SuperAdminClientGetByIdResponse> GetSuperAdminClientByIdAsync(ClientGetRequest request)
        {
            try
            {
                ClientService _clientService = new(_authUtil.UserId);
                Client clientData = await _clientService.Get(request.ClientId);
                return _mapper.Map<SuperAdminClientGetByIdResponse>(clientData);
            }

            catch (SecurityException ex)
            {
                _logger.LogError("Security error while trying to get client data.", ex);
                throw new HttpCustomException(GeneralErrorCodes.AccessDenied, HttpStatusCode.Forbidden);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError("Invalid operation error while trying to get client data.", ex);
                throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.Forbidden);
            }
            catch (Exception ex)
            {
                _logger.LogError("Unexpected error while trying to get client data.", ex);
                throw new HttpCustomException(GeneralErrorCodes.UnexpectedError, HttpStatusCode.InternalServerError);
            }
        }

        public async Task<SuperAdminClientCreateResponse> CreateClientAsync(SuperAdminClientCreateRequest request)
        {
            try
            {
                ClientService _clientService = new(_authUtil.UserId);
                var clientDetails = _mapper.Map<ClientManageViewModel>(request);
                var result = await _clientService.Add(clientDetails, _authUtil);

                // Return the response with the created client ID
                return new SuperAdminClientCreateResponse { id = result.clientId, Uuid = result.clientUuid };
            }

            catch (SecurityException ex)
            {
                _logger.LogError("Security error while creating client.", ex);
                throw new HttpCustomException(GeneralErrorCodes.AccessDenied, HttpStatusCode.Forbidden);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError("Invalid operation error while creating client.", ex);
                throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.Forbidden);
            }
            catch (Exception ex)
            {
                _logger.LogError("Unexpected error while creating client.", ex);
                throw new HttpCustomException(GeneralErrorCodes.UnexpectedError, HttpStatusCode.InternalServerError);
            }
        }

        public async Task<bool> UpdateClientDetails(ClientDetailsDTO request, int clientId)
        {
            try
            {
                await _clientService.UpdateClient(request, clientId);
                return true;
            }

            catch (SecurityException ex)
            {
                _logger.LogError("Security error while updating client.", ex);
                throw new HttpCustomException(GeneralErrorCodes.AccessDenied, HttpStatusCode.Forbidden);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError("Invalid operation error while updating client.", ex);
                throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.Forbidden);
            }
            catch (Exception ex)
            {
                _logger.LogError("Unexpected error while updating client.", ex);
                throw new HttpCustomException(AdminErrorCodes.UploadClientDetailsError, HttpStatusCode.InternalServerError);
            }
        }

        public async Task<bool> UpdateSuperAdminClientAsync(ClientUpdateRequest request)
        {
            try
            {
                ClientService clientService = new(_authUtil.UserId);
                UserRoleService userRoleService = new(_authUtil.UserId);

                var isSuperOrSupportAdmin = await userRoleService.IsSuperAdminOrSupportAdmin();
                var isClientAdmin = await userRoleService.IsAdminOnClient(request.ClientId);

                Client updatedClient = _mapper.Map<Client>(request);

                if (isClientAdmin)
                    await clientService.SelfUpdate(updatedClient);

                if (isSuperOrSupportAdmin)
                    await clientService.Update(updatedClient);

                return true;
            }

            catch (SecurityException ex)
            {
                _logger.LogError("Security error while updating client.", ex);
                throw new HttpCustomException(GeneralErrorCodes.AccessDenied, HttpStatusCode.Forbidden);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError("Invalid operation error while updating client.", ex);
                throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.Forbidden);
            }
            catch (Exception ex)
            {
                _logger.LogError("Unexpected error while updating client.", ex);
                throw new HttpCustomException(GeneralErrorCodes.UnexpectedError, HttpStatusCode.InternalServerError);
            }
        }

        public async Task<bool> BlockSuperAdminClientAsync(ClientBlockRequest request)
        {
            try
            {
                AdminService adminService = new(_authUtil, request.ClientId);
                var result = await adminService.BlockClient(request.ClientId, request.Blocked);
                return result;
            }
            catch (SecurityException ex)
            {
                _logger.LogError("Security error while blocking client.", ex);
                throw new HttpCustomException(GeneralErrorCodes.AccessDenied, HttpStatusCode.Forbidden);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError("Invalid operation error while blocking client.", ex);
                throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.Forbidden);
            }
            catch (Exception ex)
            {
                _logger.LogError("Unexpected error while blocking client.", ex);
                throw new HttpCustomException(GeneralErrorCodes.UnexpectedError, HttpStatusCode.InternalServerError);
            }
        }

        public async Task<bool> DeleteSuperAdminClientAsync(ClientDeleteRequest request, bool sending)
        {
            try
            {
                AdminService adminService = new(_authUtil.UserId, request.Id);

                if (sending)
                {
                    await adminService.SendTokenToWipeClient(request.Id, request.TwoFactorMethod, principal.Identity);
                    return true;
                }

                await adminService.VerifyTokenAndWipeClient(request.Id, request.TwoFactorMethod, principal.Identity);
                return true;
            }

            catch (SecurityException ex)
            {
                _logger.LogError("Security error while deleting client.", ex);
                throw new HttpCustomException(GeneralErrorCodes.AccessDenied, HttpStatusCode.Forbidden);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError("Invalid operation error while deleting client.", ex);
                throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.Forbidden);
            }
            catch (Exception ex)
            {
                _logger.LogError("Unexpected error while deleting client.", ex);
                throw new HttpCustomException(GeneralErrorCodes.UnexpectedError, HttpStatusCode.InternalServerError);
            }
        }

        public async Task<UserInfoGetResponse> GetUserInfoByEmailAsync(UserInfoGetRequest request)
        {
            try
            {
                var adminService = new AdminService(_authUtil.UserId, 0);

                var result = await adminService.GetAllEmailRelatedInformation(request.email);

                var response = _mapper.Map<UserInfoGetResponse>(result);
                return response;
            }
            catch (SecurityException ex)
            {
                _logger.LogError("Security error while trying to get user info by email.", ex);
                throw new HttpCustomException(GeneralErrorCodes.AccessDenied, HttpStatusCode.Forbidden);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError("Invalid operation error while trying to get user info by email.", ex);
                throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.BadRequest);
            }
            catch (Exception ex)
            {
                _logger.LogError("Unexpected error while trying to get user info by email.", ex);
                throw new HttpCustomException(AdminErrorCodes.UserInfoGetError, HttpStatusCode.InternalServerError);
            }
        }

        /// <summary>
        /// Uploads a logo for a client.
        /// </summary>
        /// <param name="request">
        /// The <see cref="AdminUploadLogoRequest"/> containing the logo file and client information.
        /// <list type="bullet">
        /// <item>
        /// <description>
        /// <c>CanvasImage</c>: The image file to upload as the client's logo. Must be a valid image type and not exceed the maximum allowed file size.
        /// </description>
        /// </item>
        /// <item>
        /// <description>
        /// <c>Action</c>: Optional. If set to "RESET_LOGO", the client's logo will be reset to the default.
        /// </description>
        /// </item>
        /// <item>
        /// <description>
        /// <c>clientId</c>: The ID of the client for which the logo is being uploaded or reset.
        /// </description>
        /// </item>
        /// </list>
        /// </param>
        /// <returns>
        /// An <see cref="AdminUploadLogoResponse"/> containing the URL of the uploaded logo or a flag indicating if the logo was reset.
        /// </returns>
        /// <exception cref="HttpCustomException">
        /// Thrown if the file is empty, the file type is invalid, the file size is exceeded, or an error occurs during upload or reset.
        /// </exception>
        public async Task<AdminUploadLogoResponse> UploadLogoAsync(AdminUploadLogoRequest request)
        {
            try
            {
                if (request.Action == "RESET_LOGO")
                {
                    var clientService = new ClientService(_authUtil.UserId, _storageSettings);
                    var resetResult = await clientService.ResetLogo(request.clientId);
                    return new AdminUploadLogoResponse { reset = resetResult.Success };
                }

                if (request.CanvasImage == null || request.CanvasImage.Length == 0)
                {
                    throw new HttpCustomException("EMPTY_FILE", HttpStatusCode.BadRequest);
                }

                var permittedImageTypes = new[] { "image/jpeg", "image/png", "image/gif", "image/webp", "image/bmp", "image/tiff" };

                if (!permittedImageTypes.Contains(request.CanvasImage.ContentType.ToLower()))
                {
                    throw new HttpCustomException("INVALID_FILE_TYPE", HttpStatusCode.BadRequest);
                }

                if (request.CanvasImage.Length > FileUtils.MaxFileSize)
                {
                    throw new HttpCustomException("FILE_SIZE_EXCEEDED", HttpStatusCode.RequestEntityTooLarge);
                }

                byte[] fileBytes;
                using (var memoryStream = new MemoryStream())
                {
                    await request.CanvasImage.CopyToAsync(memoryStream);
                    fileBytes = memoryStream.ToArray();
                }

                var clientServiceUpload = new ClientService(_authUtil.UserId, _storageSettings);
                var saveResult = await clientServiceUpload.SaveLogo(request.clientId, fileBytes, "default");

                return new AdminUploadLogoResponse { url = saveResult.Url, reset = false };
            }
            catch (Exception ex)
            {
                _logger.LogError("Error uploading client logo.", ex);
                throw new HttpCustomException(AdminErrorCodes.UploadClientLogoError, HttpStatusCode.BadRequest);
            }
        }

        public async Task<AdminClientUsersGetResponse> GetUsersForClientAsync(AdminClientUsersGetRequest request)
        {
            try
            {
                ClientService clientService = new(_authUtil.UserId);
                var users = await clientService.GetAllDetailedUsersDataFromClientAsync(request.ClientId);

                users.ForEach(user =>
                {
                    if (!user.isDeleted && user.requireApproval != true)
                    {
                        user.email = string.Empty;
                        user.mobile = string.Empty;
                    }
                });

                AdminClientUsersGetResponse response = new()
                {
                    Users = users.Select(u => _mapper.Map<AdminClientUserBaseInfo>(u)).ToList()
                };

                return response;
            }
            catch (SecurityException ex)
            {
                _logger.LogError("Security error while getting users for client.", ex);
                throw new HttpCustomException(GeneralErrorCodes.AccessDenied, HttpStatusCode.Forbidden);
            }
            catch (Exception ex)
            {
                _logger.LogError("Unexpected error while getting users for client.", ex);
                throw new HttpCustomException(AdminErrorCodes.ClientUsersGetError, HttpStatusCode.InternalServerError);
            }
        }

        public async Task<AdminClientWorkgroupsGetResponse> GetWorkgroupsForClientAsync(AdminClientWorkgroupsGetRequest request)
        {
            try
            {
                var clientWorkgroups = await _workgroupService.GetAllUserWorkgroups([request.clientId], [], null, isAdminView: true);

                var workgroupsData = new List<WorkgroupSimpleDTO>();

                if (clientWorkgroups?.Workgroups != null && clientWorkgroups.Workgroups.Any())
                {
                    workgroupsData = clientWorkgroups.Workgroups
                        .Select(workgroup => _mapper.Map<WorkgroupSimpleDTO>(workgroup))
                        .ToList();
                }

                var response = new AdminClientWorkgroupsGetResponse
                {
                    Workgroups = workgroupsData
                };

                return response;
            }
            catch (SecurityException ex)
            {
                _logger.LogError("Security error while getting users for client.", ex);
                throw new HttpCustomException(GeneralErrorCodes.AccessDenied, HttpStatusCode.Forbidden);
            }
            catch (Exception ex)
            {
                _logger.LogError("Unexpected error while getting users for client.", ex);
                throw new HttpCustomException(AdminErrorCodes.ClientUsersGetError, HttpStatusCode.InternalServerError);
            }
        }

        public async Task<SuperAdminClientApproveUsersResponse> ApproveUser(SuperAdminClientApproveUsersRequest request)
        {
            try
            {
                var userService = new UserService(_storageSettings);
                var success = await userService.ApproveUser(request.clientId, request.userId, request.approve, _authUtil.UserId);

                return new SuperAdminClientApproveUsersResponse { Success = success };
            }

            catch (SecurityException ex)
            {
                _logger.LogError("Security error while approving user.", ex);
                throw new HttpCustomException(GeneralErrorCodes.AccessDenied, HttpStatusCode.Forbidden);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError("Invalid operation error while approving user.", ex);
                throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.Forbidden);
            }
            catch (Exception ex)
            {
                _logger.LogError("Unexpected error while approving user.", ex);
                throw new HttpCustomException(GeneralErrorCodes.UnexpectedError, HttpStatusCode.InternalServerError);
            }
        }

        public async Task<SuperAdminClientBlockUsersResponse> BlockUser(SuperAdminClientBlockUsersRequest request)
        {
            try
            {
                var userService = new UserService(_storageSettings);
                var success = await userService.BlockUser(request.userId, request.block, _authUtil.UserId);

                return new SuperAdminClientBlockUsersResponse { Success = success };
            }

            catch (SecurityException ex)
            {
                _logger.LogError("Security error while blocking user.", ex);
                throw new HttpCustomException(GeneralErrorCodes.AccessDenied, HttpStatusCode.Forbidden);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError("Invalid operation error while blocking user.", ex);
                throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.Forbidden);
            }
            catch (Exception ex)
            {
                _logger.LogError("Unexpected error while blocking user.", ex);
                throw new HttpCustomException(GeneralErrorCodes.UnexpectedError, HttpStatusCode.InternalServerError);
            }
        }
    }
}
