using Atlas.Application.Interfaces;
using Atlas.Application.Models.ContentAttachment;
using Atlas.Business;
using Atlas.Business.Core.FeatureFlag;
using Atlas.Business.Policies.FeatureFlag;
using Atlas.CrossCutting.AppEnums;
using Atlas.CrossCutting.Exceptions;
using Atlas.CrossCutting.Exceptions.ErrorCodes;
using Atlas.CrossCutting.Helpers;
using Atlas.CrossCutting.Logging;
using Atlas.CrossCutting.Services;
using Atlas.CrossCutting.Settings;
using Atlas.Data.Entities;
using Atlas.Data.Repository;
using MapsterMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System.Net;
using System.Security;
using System.Security.Principal;
using static Atlas.CrossCutting.AppEnums.ContentStatuses;

namespace Atlas.Application.Handlers
{
    internal class ContentAttachmentApplication(
        IPrincipal principal,
        IStructuredLogger logger,
        IConfiguration configuration,
        IMapper mapper,
        StorageSettings storageSettings,
        IRequestContextService requestContext,
        IFeatureFlagService featureFlagService
    ) : IContentAttachmentApplication
    {
        private readonly AuthUtil _authUtil = new AuthUtil(principal);
        private readonly IStructuredLogger _logger = logger;
        private readonly IConfiguration _configuration = configuration;
        private readonly IMapper _mapper = mapper;
        private readonly StorageSettings _storageSettings = storageSettings;
        private readonly IRequestContextService _requestContext = requestContext;
        private readonly IFeatureFlagService _featureFlagService = featureFlagService;

        public async Task<ContentAttachmentGetResponse> UploadFileAsync(FileUploadRequest request)
        {
            try
            {
                if (request == null)
                {
                    _logger.LogError("FileUploadRequest is null.");
                    throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.BadRequest);
                }

                if (request.qqfile == null)
                {
                    _logger.LogError("No file provided in the file upload request.");
                    throw new HttpCustomException(FileUploadErrorCodes.InvalidOperation, HttpStatusCode.BadRequest);
                }

                if (request.contentUuid == Guid.Empty)
                {
                    _logger.LogError("Invalid content UUID provided in file upload request.");
                    throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.BadRequest);
                }

                await ValidateFileUpload(request);

                var userAgent = _requestContext.UserAgent;
                IFormFile file = request.qqfile;

                // When you upload a file to Azure via the web role, it will be stored in its
                // temporary storage. You need to understand that this storage is limited 
                // and also is transient if the web role gets destroyed. You need to take the file
                // from there ASAP. We do that in the same call and save it to blob storage.
                string originalPath = Path.GetTempPath() + request.contentUuid.ToString() + "\\";

                if (!Directory.Exists(originalPath))
                {
                    Directory.CreateDirectory(originalPath);
                }

                // Determine the file name. Prefer qqfilename if provided; otherwise, use the file's FileName.
                string fileName = !string.IsNullOrWhiteSpace(request.qqfilename) ? request.qqfilename : file.FileName;

                if (string.IsNullOrWhiteSpace(fileName))
                {
                    _logger.LogError("Invalid file name provided in upload request.");
                    throw new HttpCustomException(FileUploadErrorCodes.InvalidOperation, HttpStatusCode.BadRequest);
                }

                string tempFilePath = Path.Combine(originalPath, fileName);

                // Save the uploaded file (or chunk) to the temporary location.
                using (var stream = new FileStream(tempFilePath, FileMode.Create, FileAccess.Write))
                {
                    await file.CopyToAsync(stream);
                }

                bool isEnable = await _featureFlagService.ValidateFeatureFlagAsync(FeatureFlagKeys.ProtectedPdfWatermark, request.clientId);

                var contentAttachmentService = new ContentAttachmentService(_authUtil.UserId, request.contentUuid, _storageSettings, _configuration);
                var attachmentId = await contentAttachmentService.AddFileOrChunks(
                    request.contentUuid,
                    request.qqfile,
                    originalPath,
                    request.qquuid,
                    request.qqpartindex,
                    request.qqtotalparts,
                    request.qqtotalfilesize,
                    request.qqfilename,
                    userAgent,
                    overrideLocked: isEnable);

                var contentAttachmentResult = await contentAttachmentService.GetContentAttachmentById(attachmentId.contentAttachmentId);

                return _mapper.Map<ContentAttachmentGetResponse>(contentAttachmentResult);
            }
            catch (SecurityException ex)
            {
                _logger.LogError("Security error during file upload.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.Forbidden);
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogError("Unauthorized access during file upload.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.Forbidden);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError("Invalid operation error during file upload.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.Forbidden);
            }
            catch (ArgumentOutOfRangeException ex) when (ex.Message.StartsWith("File size exceeded"))
            {
                _logger.LogError("File upload failed due to file size limit being exceeded.", ex);
                throw new HttpCustomException(FileUploadErrorCodes.FileSizeExceeded, HttpStatusCode.RequestEntityTooLarge);
            }
            catch (ArgumentException ex)
            {
                _logger.LogError("Validation error during file upload.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.UnprocessableEntity);
            }
            catch (DirectoryNotFoundException ex)
            {
                _logger.LogError("Directory not found during file upload.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.InternalServerError);
            }
            catch (IOException ex)
            {
                _logger.LogError("IO error during file upload.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.InternalServerError);
            }
            catch (HttpCustomException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError("Unexpected error during file upload.", ex);
                throw new HttpCustomException(FileUploadErrorCodes.UnexpectedError, HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ContentAttachmentGetResponse> ToggleDelete(ContentAttachmentDeleteRequest request)
        {
            try
            {
                if (request == null)
                {
                    _logger.LogError("ContentAttachmentDeleteRequest is null.");
                    throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.BadRequest);
                }

                if (request.contentUuid == Guid.Empty)
                {
                    _logger.LogError("Invalid content UUID provided in delete request.");
                    throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.BadRequest);
                }

                if (request.contentAttachmentId <= 0)
                {
                    _logger.LogError("Invalid content attachment ID provided in delete request.");
                    throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.BadRequest);
                }

                var contentAttachmentService = new ContentAttachmentService(_authUtil.UserId, request.contentUuid);

                await contentAttachmentService.ToggleDelete(request.contentAttachmentId, deletedByExternalUser: false, _requestContext.UserAgent);

                var contentAttachmentResult = await contentAttachmentService.GetContentAttachmentById(request.contentAttachmentId);

                return _mapper.Map<ContentAttachmentGetResponse>(contentAttachmentResult);
            }
            catch (SecurityException ex)
            {
                _logger.LogError("Security error while toggling delete on content attachment.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.Forbidden);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError("Invalid operation error while toggling delete on content attachment.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.Forbidden);
            }
            catch (ArgumentException ex)
            {
                _logger.LogError("Validation error while toggling delete on content attachment.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.UnprocessableEntity);
            }
            catch (HttpCustomException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError("Unexpected error while toggling delete on content attachment.", ex);
                throw new HttpCustomException(GeneralErrorCodes.UnexpectedError, HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ContentAttachmentGetResponse> Reorder(ContentAttachmentReorderPutRequest request)
        {
            try
            {
                if (request == null)
                {
                    _logger.LogError("ContentAttachmentReorderPutRequest is null.");
                    throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.BadRequest);
                }

                if (request.contentUuid == Guid.Empty)
                {
                    _logger.LogError("Invalid content UUID provided in reorder request.");
                    throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.BadRequest);
                }

                if (request.contentAttachmentId <= 0)
                {
                    _logger.LogError("Invalid content attachment ID provided in reorder request.");
                    throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.BadRequest);
                }

                if (request.newPosition < 0)
                {
                    _logger.LogError("Invalid new position provided in reorder request.");
                    throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.BadRequest);
                }

                var contentService = new ContentService(_authUtil.UserId);
                await contentService.SetContentAttachmentOrder(request.contentUuid, request.contentAttachmentId, request.newPosition, _requestContext.UserAgent);

                var contentAttachmentService = new ContentAttachmentService(_authUtil.UserId, request.contentUuid);
                var contentAttachmentResult = await contentAttachmentService.GetContentAttachmentById(request.contentAttachmentId);

                return _mapper.Map<ContentAttachmentGetResponse>(contentAttachmentResult);
            }
            catch (SecurityException ex)
            {
                _logger.LogError("Security error while reordering content attachment.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.Forbidden);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError("Invalid operation error while reordering content attachment.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.Forbidden);
            }
            catch (ArgumentException ex)
            {
                _logger.LogError("Validation error while reordering content attachment.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.UnprocessableEntity);
            }
            catch (HttpCustomException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError("Unexpected error while reordering content attachment.", ex);
                throw new HttpCustomException(GeneralErrorCodes.UnexpectedError, HttpStatusCode.InternalServerError);
            }
        }

        public async Task<List<ContentAttachmentGetResponse>> GetContentAttachments(ContentAttachmentGetRequest request)
        {
            try
            {
                if (request == null)
                {
                    _logger.LogError("ContentAttachmentGetRequest is null.");
                    throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.BadRequest);
                }

                if (request.contentUuid == Guid.Empty)
                {
                    _logger.LogError("Invalid content UUID provided in get content attachments request.");
                    throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.BadRequest);
                }

                var contentAttachmentService = new ContentAttachmentService(_authUtil.UserId);
                var contentAttachments = await contentAttachmentService.GetContentAttachments(request.contentUuid);

                using var atlasModel = new AtlasModelCore();

                var isPollContent = await atlasModel.Content.AnyAsync(c => c.contentUuid == request.contentUuid && c.type == ContentTypes.Poll);

                if (isPollContent)
                {
                    var pollReportContentAttachmentId = await atlasModel.Poll.Where(p => p.contentUuid == request.contentUuid)
                        .Select(p => p.reportContentAttachmentId)
                        .FirstOrDefaultAsync();

                    contentAttachments = contentAttachments.Where(ca => ca.contentAttachmentId != pollReportContentAttachmentId && ca.deleted != true).ToList();
                }

                var contentAttachmentsResponse = _mapper.Map<List<ContentAttachmentGetResponse>>(contentAttachments);
                return contentAttachmentsResponse;
            }
            catch (SecurityException ex)
            {
                _logger.LogError("Security error while getting content attachments.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.Forbidden);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError("Invalid operation error while getting content attachments.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.Forbidden);
            }
            catch (ArgumentException ex)
            {
                _logger.LogError("Validation error while getting content attachments.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.UnprocessableEntity);
            }
            catch (HttpCustomException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError("Unexpected error while getting content attachments.", ex);
                throw new HttpCustomException(GeneralErrorCodes.UnexpectedError, HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ContentAttachmentGetResponse> ToggleLock(ContentAttachmentLockPostRequest request)
        {
            try
            {
                if (request == null)
                {
                    _logger.LogError("ContentAttachmentLockPostRequest is null.");
                    throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.BadRequest);
                }

                if (request.contentUuid == Guid.Empty)
                {
                    _logger.LogError("Invalid content UUID provided in toggle lock request.");
                    throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.BadRequest);
                }

                if (request.contentAttachmentId <= 0)
                {
                    _logger.LogError("Invalid content attachment ID provided in toggle lock request.");
                    throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.BadRequest);
                }

                var contentAttachmentService = new ContentAttachmentService(_authUtil.UserId, request.contentUuid);

                await contentAttachmentService.ToggleLock(request.contentAttachmentId, _requestContext.UserAgent, request.lockAttachment);

                var contentAttachmentResult = await contentAttachmentService.GetContentAttachmentById(request.contentAttachmentId);

                return _mapper.Map<ContentAttachmentGetResponse>(contentAttachmentResult);
            }
            catch (SecurityException ex)
            {
                _logger.LogError("Security error while toggling lock on content attachment.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.Forbidden);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError("Invalid operation error while toggling lock on content attachment.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.Forbidden);
            }
            catch (ArgumentException ex)
            {
                _logger.LogError("Validation error while toggling lock on content attachment.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.UnprocessableEntity);
            }
            catch (HttpCustomException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError("Unexpected error while toggling lock on content attachment.", ex);
                throw new HttpCustomException(GeneralErrorCodes.UnexpectedError, HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ContentAttachmentFileWatermarkedGetResponse> GetFileWaterMarked(ContentAttachmentFileWatermarkedGetRequest request)
        {
            try
            {
                if (request == null)
                {
                    _logger.LogError("ContentAttachmentFileWatermarkedGetRequest is null.");
                    throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.BadRequest);
                }

                if (request.contentUuid == Guid.Empty)
                {
                    _logger.LogError("Invalid content UUID provided in get file watermarked request.");
                    throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.BadRequest);
                }

                if (request.contentAttachmentId <= 0)
                {
                    _logger.LogError("Invalid content attachment ID provided in get file watermarked request.");
                    throw new HttpCustomException(GeneralErrorCodes.InvalidOperation, HttpStatusCode.BadRequest);
                }

                var userService = new UserService();

                var user = userService.GetUserByUserId(_authUtil.UserId);
                if (user == null)
                {
                    _logger.LogError($"User not found: {_authUtil.UserId}");
                    throw new HttpCustomException(GeneralErrorCodes.AccessDenied, HttpStatusCode.Forbidden);
                }

                var contentAttachmentService = new ContentAttachmentService(_authUtil.UserId, request.contentUuid, _storageSettings);

                bool isDownload = request.isDownload ?? false;

                var fileWaterMarkedViewModel = await contentAttachmentService.GetFileWaterMarked(request.contentAttachmentId, user, _requestContext.UserAgent, isDownload);

                var response = _mapper.Map<ContentAttachmentFileWatermarkedGetResponse>(fileWaterMarkedViewModel);

                return response;
            }
            catch (SecurityException ex)
            {
                _logger.LogError("Security error while getting watermarked file.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.Forbidden);
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogError("Unauthorized access while getting watermarked file.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.Forbidden);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError("Invalid operation error while getting watermarked file.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.Forbidden);
            }
            catch (ArgumentException ex)
            {
                _logger.LogError("Validation error while getting watermarked file.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.UnprocessableEntity);
            }
            catch (FileNotFoundException ex)
            {
                _logger.LogError("File not found while getting watermarked file.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.NotFound);
            }
            catch (HttpCustomException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError("Unexpected error while getting watermarked file.", ex);
                throw new HttpCustomException(GeneralErrorCodes.UnexpectedError, HttpStatusCode.InternalServerError);
            }
        }
        private async System.Threading.Tasks.Task ValidateFileUpload(FileUploadRequest request)
        {
            var fileName = !string.IsNullOrWhiteSpace(request.qqfilename) ? request.qqfilename : request.qqfile.FileName;
            var extension = Path.GetExtension(fileName)?.ToLowerInvariant();

            if (string.IsNullOrWhiteSpace(extension))
            {
                throw new InvalidOperationException("INVALID_FILE_EXTENSION");
            }

            if (BannedFileTypes.Extensions.Contains(extension))
            {
                throw new InvalidOperationException("BANNED_FILE_TYPE");
            }

            var contentService = new ContentService(_authUtil.UserId);

            var content = await contentService.GetContent(request.contentUuid)
                ?? throw new InvalidOperationException("INVALID_CONTENT");

            var contentRepo = new ContentRepository(_authUtil.UserId);

            if (!await contentRepo.CheckPermissionsForWorkgroup(Operations.ATTACHMENT_ADD, request.contentUuid))
            {
                throw new InvalidOperationException("UNAUTHORIZED_WORKGROUP");
            }

            if (!await contentRepo.CheckPermissionsAsync(request.contentUuid))
            {
                throw new InvalidOperationException("UNAUTHORIZED_CONTENT");
            }

            ValidateContentStatus(content.type, content.status, content.parent_status);

            var workgroupService = new WorkgroupService(_authUtil.UserId);

            var workgroup = await workgroupService.Get(request.workgroupId, includeHomeData: false)
                ?? throw new InvalidOperationException("INVALID_WORKGROUP");

            if (workgroup.archived)
            {
                throw new InvalidOperationException("BOARD_ARCHIVED");
            }

            var featureManagerService = new FeatureManagerService(_authUtil.UserId);
            await featureManagerService.checkPlanQuota(request.clientId, PlanFeatureNames.BOARD_QUOTA, false);
            await featureManagerService.checkPlanQuota(request.clientId, PlanFeatureNames.STORAGE_QUOTA);

            var fileSize = request.qqtotalfilesize > 0 ? request.qqtotalfilesize : request.qqfile.Length;

            if (await contentRepo.AttachmentAlreadySent(request.contentUuid, (int)fileSize))
            {
                throw new InvalidOperationException("DUPLICATED_ATTACHMENT");
            }
        }
    }
}
