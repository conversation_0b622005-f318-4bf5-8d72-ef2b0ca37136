<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <OutputType>Library</OutputType>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>
  <ItemGroup>
    <EmbeddedResource Include="js\zxcvbn.js">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="jint" Version="4.2.0" />
  </ItemGroup>
  <PropertyGroup>
    <AssemblyTitle>Hexasoft.Zxcvbn</AssemblyTitle>
    <Company>Hexasoft</Company>
    <Product>Hexasoft.Zxcvbn</Product>
    <Copyright>Copyright © 2016-2020</Copyright>
    <AssemblyVersion>1.1.0.0</AssemblyVersion>
    <FileVersion>1.1.0.0</FileVersion>
  </PropertyGroup>
</Project>