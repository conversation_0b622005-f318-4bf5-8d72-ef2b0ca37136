using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace Atlas.Data.Abstract
{
    public abstract class EfRepositoryBase<TEntity, TContext> : IRepository<TEntity> where TEntity : class where TContext : DbContext
    {
        internal TContext _context;
        internal DbSet<TEntity> dbSet;
        public EfRepositoryBase(TContext context)
        {
            this._context = context;
            this.dbSet = context.Set<TEntity>();
        }
        public bool ExistsId(object id)
        {
            return dbSet.Find(id) != null;
        }
        public bool Exists(TEntity entity)
        {
            return dbSet.Any(a => a == entity);
        }
        public async Task<bool> ExistsIdAsync(object id)
        {
            return await dbSet.FindAsync(id) != null;
        }
        public async Task<bool> ExistsAsync(TEntity entity)
        {
            return await dbSet.AnyAsync(a => a == entity);
        }
        public virtual TEntity GetByID(object id)
        {
            return dbSet.Find(id);
        }
        public virtual TDto GetByID<TDto>(object id) where TDto : DtoBase<TEntity>, new()
        {
            var entity = dbSet.Find(id);
            if (entity == null)
            {
                return null;
            }
            TDto dto = new TDto();
            dto.SetProperties(entity);
            return dto;
        }
        public virtual async Task<TEntity> GetByIDAsync(object id)
        {
            return await dbSet.FindAsync(id);
        }
        public virtual async Task<TDto> GetByIDAsync<TDto>(object id) where TDto : DtoBase<TEntity>, new()
        {
            var entity = await dbSet.FindAsync(id);
            if (entity == null)
            {
                return null;
            }
            TDto dto = new TDto();
            dto.SetProperties(entity);
            return dto;
        }
        public virtual IEnumerable<TEntity> GetAll()
        {
            IEnumerable<TEntity> items = dbSet;
            return items;
        }
        public virtual IEnumerable<TDto> GetAll<TDto>() where TDto : DtoBase<TEntity>, new()
        {
            IEnumerable<TDto> items = dbSet.Select(x => GetDto<TDto>(x));
            return items;
        }
        public virtual async Task<IEnumerable<TEntity>> GetAllAsync()
        {
            IEnumerable<TEntity> items = await dbSet.ToListAsync();
            return items;
        }
        public virtual async Task<IEnumerable<TDto>> GetAllAsync<TDto>() where TDto : DtoBase<TEntity>, new()
        {
            var items = await dbSet.Select(x => GetDto<TDto>(x)).ToListAsync();
            return items;
        }
        private static TDto GetDto<TDto>(TEntity entityToSetProperties) where TDto : DtoBase<TEntity>, new()
        {
            TDto dto = new TDto();
            dto.SetProperties(entityToSetProperties);
            return dto;
        }
        public IQueryable<TEntity> GetQueryable(
            Expression<Func<TEntity, bool>> filter = null, Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>> orderBy = null,
            string includeProperties = "", bool asNoTracking = false)
        {
            IQueryable<TEntity> query = dbSet;
            if (asNoTracking)
            {
                query = query.AsNoTracking();
            }

            if (filter != null)
            {
                query = query.Where(filter);
            }

            if (includeProperties != null)
            {
                foreach (var includeProperty in includeProperties.Split
                (new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries))
                {
                    query = query.Include(includeProperty);
                }
            }
            if (orderBy != null)
            {
                query = orderBy(query);
            }
            return query;
        }
        public IQueryable<TEntity> RecursiveQuery(IQueryable<TEntity> query,
           Expression<Func<TEntity, bool>> filter = null, Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>> orderBy = null,
           string includeProperties = "")
        {
            if (filter != null)
            {
                query = query.Where(filter);
            }

            if (includeProperties != null)
            {
                foreach (var includeProperty in includeProperties.Split
                (new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries))
                {
                    query = query.Include(includeProperty);
                }
            }
            if (orderBy != null)
            {
                query = orderBy(query);
            }
            return query;
        }
        public IEnumerable<TDto> ConvertRange<TDto>(IEnumerable<TEntity> entities) where TDto : DtoBase<TEntity>, new()
        {
            return entities.Select(x => GetDto<TDto>(x));
        }
        public virtual IEnumerable<TEntity> GetAll(
            Expression<Func<TEntity, bool>> filter = null, Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>> orderBy = null,
            string includeProperties = "")
        {
            var query = GetQueryable(filter, orderBy, includeProperties);
            return query.ToList();
        }
        public virtual IEnumerable<TDto> GetAll<TDto>(
           Expression<Func<TEntity, bool>> filter = null, Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>> orderBy = null,
           string includeProperties = "") where TDto : DtoBase<TEntity>, new()
        {
            var query = GetQueryable(filter, orderBy, includeProperties);
            return query.Select(x => GetDto<TDto>(x)).ToList();
        }
        public virtual async Task<IEnumerable<TEntity>> GetAllAsync(
            Expression<Func<TEntity, bool>> filter = null, Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>> orderBy = null,
            string includeProperties = "")
        {
            var query = GetQueryable(filter, orderBy, includeProperties);
            return await query.ToListAsync();
        }
        public virtual async Task<IEnumerable<TDto>> GetAllAsync<TDto>(
            Expression<Func<TEntity, bool>> filter = null, Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>> orderBy = null,
            string includeProperties = "") where TDto : DtoBase<TEntity>, new()
        {
            var query = GetQueryable(filter, orderBy, includeProperties);
            return await query.Select(x => GetDto<TDto>(x)).ToListAsync();
        }
        public virtual PagedList<TEntity> GetPagedList(int pageNumber, int pageSize)
        {
            pageNumber = pageNumber == 0 ? 1 : pageNumber;
            var entities = pageSize == 0 ? GetAll() : dbSet.Skip(pageNumber == 1 ? 0 : (pageNumber * pageSize) - pageSize).Take(pageSize).ToList();
            var pagedList = new PagedList<TEntity>(dbSet.Count(), pageNumber, pageSize, entities);
            return pagedList;
        }
        public virtual IEnumerable<TDto> GetPagedList<TDto>(int pageNumber, int pageSize) where TDto : DtoBase<TEntity>, new()
        {
            if (pageSize == 0)
            {
                return GetAll<TDto>();
            }
            pageNumber = pageNumber == 0 ? 1 : pageNumber;
            return dbSet.Skip(pageNumber == 1 ? 0 : (pageNumber * pageSize) - pageSize).Take(pageSize).Select(x => GetDto<TDto>(x)).ToList();
        }
        public virtual PagedList<TEntity> GetPagedList(int pageNumber = 1, int pageSize = 0,
            Expression<Func<TEntity, bool>> filter = null,
            Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>> orderBy = null,
            string includeProperties = "")
        {
            pageNumber = pageNumber == 0 ? 1 : pageNumber;

            var query = GetQueryable(filter, orderBy, includeProperties);
            var entities = pageSize == 0 ? query.ToList() : query.Skip(pageNumber == 1 ? 0 : (pageNumber * pageSize) - pageSize).Take(pageSize).ToList();
            var pagedList = new PagedList<TEntity>(query.Count(), pageNumber, pageSize, entities);
            return pagedList;
        }
        public virtual PagedList<TDto> GetPagedList<TDto>(int pageNumber = 1, int pageSize = 0,
            Expression<Func<TEntity, bool>> filter = null,
            Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>> orderBy = null,
            string includeProperties = "") where TDto : DtoBase<TEntity>, new()
        {
            pageNumber = pageNumber == 0 ? 1 : pageNumber;

            var query = GetQueryable(filter, orderBy, includeProperties);
            var entities = pageSize == 0 ? query.Select(x => GetDto<TDto>(x)).ToList() :
                query.Skip(pageNumber == 1 ? 0 : (pageNumber * pageSize) - pageSize).Take(pageSize).Select(x => GetDto<TDto>(x)).ToList();
            var pagedList = new PagedList<TDto>(query.Count(), pageNumber, pageSize, entities);
            return pagedList;
        }
        public virtual async Task<PagedList<TEntity>> GetPagedListAsync(int pageNumber, int pageSize)
        {

            pageNumber = pageNumber == 0 ? 1 : pageNumber;
            var entities = pageSize == 0 ? await GetAllAsync() : await dbSet.Skip(pageNumber == 1 ? 0 : (pageNumber * pageSize) - pageSize).Take(pageSize).ToListAsync();
            var pagedList = new PagedList<TEntity>(await dbSet.CountAsync(), pageNumber, pageSize, entities);
            return pagedList;
        }
        public virtual async Task<IEnumerable<TDto>> GetPagedListAsync<TDto>(int pageNumber, int pageSize) where TDto : DtoBase<TEntity>, new()
        {
            if (pageSize == 0)
            {
                return await GetAllAsync<TDto>();
            }
            pageNumber = pageNumber == 0 ? 1 : pageNumber;
            return await dbSet.Skip(pageNumber == 1 ? 0 : (pageNumber * pageSize) - pageSize).Take(pageSize).Select(x => GetDto<TDto>(x)).ToListAsync();
        }
        public virtual async Task<PagedList<TEntity>> GetPagedListAsync(int pageNumber = 1, int pageSize = 0,
            Expression<Func<TEntity, bool>> filter = null,
            Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>> orderBy = null,
            string includeProperties = "")
        {
            pageNumber = pageNumber == 0 ? 1 : pageNumber;

            var query = GetQueryable(filter, orderBy, includeProperties);
            var entities = pageSize == 0 ? await query.ToListAsync() : await query.Skip(pageNumber == 1 ? 0 : (pageNumber * pageSize) - pageSize).Take(pageSize).ToListAsync();
            var pagedList = new PagedList<TEntity>(await query.CountAsync(), pageNumber, pageSize, entities);
            return pagedList;
        }
        public virtual async Task<PagedList<TDto>> GetPagedListAsync<TDto>(int pageNumber = 1, int pageSize = 0,
            Expression<Func<TEntity, bool>> filter = null,
            Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>> orderBy = null,
            string includeProperties = "") where TDto : DtoBase<TEntity>, new()
        {
            pageNumber = pageNumber == 0 ? 1 : pageNumber;
            var query = GetQueryable(filter, orderBy, includeProperties);
            var entities = pageSize == 0 ? await query.Select(x => GetDto<TDto>(x)).ToListAsync() :
                await query.Skip(pageNumber == 1 ? 0 : (pageNumber * pageSize) - pageSize).Take(pageSize).Select(x => GetDto<TDto>(x)).ToListAsync();
            var pagedList = new PagedList<TDto>(await query.CountAsync(), pageNumber, pageSize, entities);
            return pagedList;

        }
        public virtual void Insert(TEntity entity)
        {
            dbSet.Add(entity);
        }
        public virtual void Delete(object id)
        {
            TEntity entityToDelete = dbSet.Find(id);
            Delete(entityToDelete);
        }
        public virtual void Delete(TEntity entityToDelete)
        {
            if (_context.Entry(entityToDelete).State == EntityState.Detached)
            {
                dbSet.Attach(entityToDelete);
            }
            dbSet.Remove(entityToDelete);
        }
        public virtual void Update(TEntity entityToUpdate)
        {
            dbSet.Attach(entityToUpdate);
            _context.Entry(entityToUpdate).State = EntityState.Modified;
        }
        public virtual void Update(TEntity entity, string[] properties)
        {
            var entry = _context.Entry(entity);
            dbSet.Attach(entity);

            foreach (var property in properties)
            {
                entry.Property(property).IsModified = true;
            }
        }
        public void Commit()
        {
            _context.SaveChanges();
        }
        public async Task CommitAsync()
        {
            await _context.SaveChangesAsync();
        }
        public virtual void ReloadRepository()
        {
            foreach (var entity in _context.ChangeTracker.Entries())
            {
                entity.Reload();
            }
        }
        public virtual async Task ReloadRepositoryAsync()
        {
            foreach (var entity in _context.ChangeTracker.Entries())
            {
                await entity.ReloadAsync();
            }
        }
        public virtual void ReloadEntity(TEntity entity)
        {
            _context.Entry(entity).Reload();
        }
        public virtual async Task ReloadEntityAsync(TEntity entity)
        {
            await _context.Entry(entity).ReloadAsync();
        }

    }
}
