using System.Collections.Generic;

namespace Atlas.Data.Abstract
{
    public interface IPagedList<TEntity>
    {
        int CurrentPageNumber { get; set; }
        IEnumerable<TEntity> Entities { get; set; }
        int IndexSize { get; set; }
        int PageFirstEntityIndex { get; }
        int PageLastEntityIndex { get; }
        int PagesAfter { get; }
        int PagesBefore { get; }
        int PageSize { get; set; }
        int TotalPages { get; }
        bool ValidPage { get; }

        int CalculateLastEntityIndex();
        IPagedList<TDto> Convert<TDto>() where TDto : DtoBase<TEntity>, new();
    }
}
