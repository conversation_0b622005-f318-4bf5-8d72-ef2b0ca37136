namespace Atlas.Data.Helpers
{
    /// <summary>
    /// Helper class para operações de indexação de conteúdo de reuniões
    /// </summary>
    public static class MeetingContentIndexingHelper
    {
        #region Constants

        /// <summary>
        /// Tipos de conteúdo indexáveis
        /// </summary>
        public static class ContentTypes
        {
            public const string MEETING = "MEETING";
            public const string MEETING_AGENDA = "MEETING_AGENDA";
            public const string MEETING_AGENDA_COMMENT = "MEETING_AGENDA_COMMENT";
            public const string MEETING_AGENDA_ATTACHMENT = "MEETING_AGENDA_ATTACHMENT";
            public const string MEETING_POLL = "MEETING_POLL";
            public const string MEETING_POLL_COMMENT = "MEETING_POLL_COMMENT";
            public const string MEETING_POLL_ATTACHMENT = "MEETING_POLL_ATTACHMENT";
            public const string MEETING_TASK = "MEETING_TASK";
            public const string MEETING_TASK_COMMENT = "MEETING_TASK_COMMENT";
            public const string MEETING_TASK_ATTACHMENT = "MEETING_TASK_ATTACHMENT";
            public const string MEETING_MINUTE = "MEETING_MINUTE";
            public const string MEETING_TRANSCRIPTION = "MEETING_TRANSCRIPTION";
            public const string MEETING_TRANSCRIPTION_SUMMARY = "MEETING_TRANSCRIPTION_SUMMARY";
        }

        /// <summary>
        /// Status de indexação
        /// </summary>
        public static class IndexingStatus
        {
            // Fluxo de indexação
            public const string REQUESTED = "REQUESTED";
            public const string PROCESSING = "PROCESSING";
            public const string SUCCESS = "SUCCESS";
            public const string FAILURE = "FAILURE";
            
            // Fluxo de remoção
            public const string DELETE_REQUESTED = "DELETE_REQUESTED";
            public const string DELETE_PROCESSING = "DELETE_PROCESSING";
            public const string DELETED = "DELETED";
            public const string DELETE_FAILURE = "DELETE_FAILURE";
        }

        /// <summary>
        /// Tabelas de entidades
        /// </summary>
        public static class EntityTables
        {
            public const string Content = "Content";
            public const string ContentComment = "ContentComment";
            public const string ContentAttachment = "ContentAttachment";
            public const string MeetingTranscription = "MeetingTranscription";
            public const string MeetingTranscriptionSummary = "MeetingTranscriptionSummary";
        }

        /// <summary>
        /// Status consolidado da reunião
        /// </summary>
        public static class MeetingStatus
        {
            public const string PENDING = "PENDING";
            public const string PROCESSING = "PROCESSING";
            public const string COMPLETE = "COMPLETE";
            public const string PARTIAL_FAILURE = "PARTIAL_FAILURE";
            public const string NO_CONTENT = "NO_CONTENT";
            public const string UNKNOWN = "UNKNOWN";
        }

        #endregion
    }
} 
