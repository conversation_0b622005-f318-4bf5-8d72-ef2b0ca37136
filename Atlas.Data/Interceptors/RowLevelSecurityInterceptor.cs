using Atlas.CrossCutting.Helpers;
using Atlas.Data.Entities;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore.Diagnostics;
using System;
using System.Data.Common;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using System.Text.Json;

namespace Atlas.Data.Interceptors
{
    // Model for JWT clients claim
    public class ClientClaim
    {
        public int clientId { get; set; }
        public string clientName { get; set; }
        public string clientUuid { get; set; }
    }

    public class RowLevelSecurityInterceptor : DbCommandInterceptor
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public RowLevelSecurityInterceptor(
            IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        // Non-Query (Insert, Update, Delete)
        public override InterceptionResult<int> NonQueryExecuting(DbCommand command, CommandEventData eventData, InterceptionResult<int> result)
        {
            HandleSessionContext(command);
            return base.NonQueryExecuting(command, eventData, result);
        }

        public override ValueTask<InterceptionResult<int>> NonQueryExecutingAsync(
            DbCommand command,
            CommandEventData eventData,
            InterceptionResult<int> result,
            CancellationToken cancellationToken = default)
        {
            HandleSessionContext(command);
            return base.NonQueryExecutingAsync(command, eventData, result, cancellationToken);
        }

        // Reader (SELECT queries)
        public override InterceptionResult<DbDataReader> ReaderExecuting(
            DbCommand command, CommandEventData eventData, InterceptionResult<DbDataReader> result)
        {
            HandleSessionContext(command);
            return base.ReaderExecuting(command, eventData, result);
        }
        public override ValueTask<InterceptionResult<DbDataReader>> ReaderExecutingAsync(
            DbCommand command,
            CommandEventData eventData,
            InterceptionResult<DbDataReader> result,
            CancellationToken cancellationToken = default)
        {
            HandleSessionContext(command);
            return base.ReaderExecutingAsync(command, eventData, result, cancellationToken);
        }

        // Scalar(Single Value Queries like COUNT, SUM, MAX, etc.)
        public override InterceptionResult<object> ScalarExecuting(
        DbCommand command, CommandEventData eventData, InterceptionResult<object> result)
        {
            HandleSessionContext(command);
            return base.ScalarExecuting(command, eventData, result);
        }

        public override ValueTask<InterceptionResult<object>> ScalarExecutingAsync(
            DbCommand command,
            CommandEventData eventData,
            InterceptionResult<object> result,
            CancellationToken cancellationToken = default)
        {
            HandleSessionContext(command);
            return base.ScalarExecutingAsync(command, eventData, result, cancellationToken);
        }

        private void HandleSessionContext(DbCommand command)
        {

            string commandText = command.CommandText;

            bool commandHasContentId = commandText != null && 
                                       (commandText.IndexOf("contentId", StringComparison.OrdinalIgnoreCase) >= 0 ||
                                        commandText.IndexOf("contentUuId", StringComparison.OrdinalIgnoreCase) >= 0);

            // Step 1 - Is the command a candidate for RLS?
            // Must be a select statement that retrieves (or joins) with the Content table (by the contentId field)
            if (commandHasContentId)
            {

                var httpContext = _httpContextAccessor.HttpContext;

                string userClientIdsString = null;
                bool isHttpContextUser = httpContext != null && httpContext.User != null && httpContext.User.Identity.IsAuthenticated;

                // Step 2 - Obtain the user clientIds (if it is a proper authenticated user)
                if (isHttpContextUser)
                {
                    ClaimsIdentity claimsIdentity = httpContext.User.Identity as ClaimsIdentity;

                    // First, try to get clientIds from the new "clients" JSON claim
                    Claim clientsClaim = claimsIdentity.Claims.FirstOrDefault(c => c.Type == "clients");
                    
                    if (clientsClaim != null && !string.IsNullOrEmpty(clientsClaim.Value))
                    {
                        try
                        {
                            // Parse the JSON array of clients
                            var clients = JsonSerializer.Deserialize<ClientClaim[]>(clientsClaim.Value);
                            if (clients != null && clients.Length > 0)
                            {
                                // Extract clientIds and join them as comma-separated string
                                var clientIds = clients.Select(c => c.clientId.ToString()).ToArray();
                                userClientIdsString = string.Join(",", clientIds);
                                Console.WriteLine($"RLS - JWT Clients - Obtained ids {userClientIdsString}");
                            }
                        }
                        catch (JsonException ex)
                        {
                            Console.WriteLine($"RLS - JWT Clients - Failed to parse JSON: {ex.Message}");
                            // Fall back to legacy methods if JSON parsing fails
                        }
                    }

                    // Fallback: Try legacy user_clientIds claim (for backwards compatibility)
                    if (string.IsNullOrEmpty(userClientIdsString))
                    {
                        Claim userClientIdsClaim = claimsIdentity.Claims.FirstOrDefault(c => c.Type == "user_clientIds");

                        if (userClientIdsClaim != null && userClientIdsClaim.Value != null)
                        {
                            // userClientIds already present on the claim, no need for anything else
                            userClientIdsString = userClientIdsClaim.Value;
                            Console.WriteLine($"RLS - Legacy Claims - Obtained ids {userClientIdsString}");
                        }
                    }

                    // Last resort: Redis/Cache lookup (for very old tokens)
                    if (string.IsNullOrEmpty(userClientIdsString))
                    // Last resort: Redis/Cache lookup (for very old tokens)
                    if (string.IsNullOrEmpty(userClientIdsString))
                    {
                        // This is for the cases where the user hasn't performed a new login yet and the clients/user_clientIds claim is not available
                        // In this case, we will look for it on the cache/redis (eventually everyone will have the claim and this code will be removed)
                        Claim userIdClaim = claimsIdentity.Claims.FirstOrDefault(c => c.Type == "userId");
                        int userId = 0;

                        if (int.TryParse(userIdClaim?.Value, out userId))
                        {
                            string userClientIdsKey = $"{userId}_user_clientIds";
                            userClientIdsString = CacheManager.Get(userClientIdsKey);

                            Console.WriteLine($"RLS - Redis - Obtained ids {userClientIdsString}");

                            if (userClientIdsString == null)
                            {
                                // If the value is unavailable even at Redis/Cache, we can enable this contingency to look for the ids in the database
                                // Not desirable though as this will be less performatic (but possibly with minimal to no effect given the simplicity of the query)
                                //bool rlsClientIdQueryContingencyEnabled = CloudConfigurationManager.GetSetting("RLS_CLIENTID_QUERY_CONTINGENCY") == "ENABLED";
                                bool rlsClientIdQueryContingencyEnabled = false;

                                if (rlsClientIdQueryContingencyEnabled)
                                {
                                    AtlasModelCore _md = new AtlasModelCore();

                                    int?[] userClientIdsWithNullables = _md.UserRole.Where(ur => ur.userId == userId)
                                                                                    .Select(ur => ur.clientId).ToArray();

                                    // Since this is a performance critical scenario, it is actually worth it to filter the null clientIds in memory and avoid a AND operation on the database
                                    int[] userClientIds = userClientIdsWithNullables.Where(c => c.HasValue).Distinct().Select(i => i.Value).ToArray();

                                    userClientIdsString = string.Join(",", userClientIds);
                                    Console.WriteLine($"RLS - Database - Obtained ids {userClientIdsString}");

                                    if (userClientIds.Any())
                                    {
                                        CacheManager.Set(userClientIdsKey, userClientIdsString);
                                    }
                                }
                            }
                        }
                    }
                }
                else
                {
                    userClientIdsString = "-1"; // System-User marker
                }

                // Step 3 - If any clientIds are present in the claims or if it is a async/functions context
                if (userClientIdsString != null)
                {

                    string rowLevelSecuritySQL = $"EXEC sp_set_session_context @key=N'UserClientIds', @value='{userClientIdsString}';\n";

                    command.CommandText = rowLevelSecuritySQL + command.CommandText;

                    /*
                     *  This will be deleted if tests are successful
                     * 
                     * DbCommand sessionCommand = command.Connection.CreateCommand();

                    if (command.Transaction != null)
                    {
                        sessionCommand.Transaction = command.Transaction;
                    }

                    sessionCommand.CommandText = "EXEC sp_set_session_context @key, @value";

                    // The user clientIds are provided from its claims to the database RLS function
                    sessionCommand.Parameters.Add(new SqlParameter("@key", "UserClientIds"));
                    sessionCommand.Parameters.Add(new SqlParameter("@value", userClientIdsString)); // Replace with actual client IDs
                    
                    sessionCommand.ExecuteNonQuery();
                    */

                }
            }
        }

    }
}
