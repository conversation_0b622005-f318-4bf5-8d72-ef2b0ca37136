using Newtonsoft.Json;
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Atlas.Data.Entities
{
    [Table("BlueBookVersion")]
    public class BlueBookVersion
    {

        [System.ComponentModel.DataAnnotations.Key]
        public int bbVersionId { get; set; }

        public int meetingId { get; set; }
        public int userId { get; set; }
        public int? contentActivityId { get; set; }
        public DateTime versionDate { get; set; }

        //public string bbLink { get; set; }


        public int attachmentId { get; set; }

        public int? pageLen { get; set; }

        [NotMapped]
        [JsonIgnore]
        public string downloadLink { get; set; }

        public string bbres { get; set; }

        // The payload used for the base bluebook approach
        public string basePayload { get; set; }

        public Guid? contentUuid { get; set; }

        public int? contentId { get; set; }

        public string blueBookContentHash { get; set; }

        public virtual Attachment Attachment { get; set; }
    }
}
