namespace Atlas.Data.Entities
{
    using System;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("ATLAS1_Pendencia")]
    public partial class ATLAS1_Pendencia
    {

        [Key]
        public int PendenciaId { get; set; }
        public int Responsavel_UsuarioId { get; set; }
        public string assunto { get; set; }
        public string acao { get; set; }
        public string descricao { get; set; }
        public DateTime? prazo { get; set; }
        public bool? requerAnexo { get; set; }
        public int StatusPendenciaId { get; set; }
        public int? ReuniaoId { get; set; }
        public int? Insercao_UsuarioId { get; set; }
        public DateTime? dataInsercao { get; set; }
        public int GrupoEconomicoId { get; set; }
        public int? permissionLevel { get; set; }
        public int? objectPermissionsId { get; set; }
        public DateTime? dataFinalizacao { get; set; }
        public int? Finalizacao_UsuarioId { get; set; }
        public DateTime? dataReabertura { get; set; }
        public int? Reabertura_UsuarioId { get; set; }
       
    }
}
