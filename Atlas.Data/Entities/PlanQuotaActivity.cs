using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Atlas.Data.Entities
{
    [Table("PlanQuotaActivity")]
    public class PlanQuotaActivity
    {
       
        public PlanQuotaActivity()
        {
        }

        [Key]
        public int quotaActivityId { get; set; }
        public int clientId { get; set; }
        public string featureName { get; set; }
        public int quotaValue { get; set; }
        public DateTime logDate { get; set; }
        public int? logUserId { get; set; }
        public string logDetails { get; set; }
        public string updateReason { get; set; }

        public DateTime? referenceDate { get; set; }
        public string referenceType { get; set; }
    }
}
