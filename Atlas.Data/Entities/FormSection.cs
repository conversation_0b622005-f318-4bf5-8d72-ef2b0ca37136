namespace Atlas.Data.Entities
{
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("FormSection")]
    public class FormSection
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public FormSection()
        {
            FormQuestions = new HashSet<FormQuestion>();
        }
        [Key]
        public int formSectionId { get; set; }

        [StringLength(1000)]
        public string title { get; set; }
        public short sectionOrder { get; set; }
        public bool deleted { get; set; }
        public int formId { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        public virtual Form Form { get; internal set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<FormQuestion> FormQuestions { get; set; }
    }
}
