namespace Atlas.Data.Entities
{
    using System;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("WorkgroupUser")]
    public partial class WorkgroupUser
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public WorkgroupUser()
        {
        }

        [Key]
        public int wuId { get; set; }

        public int userId { get; set; }

        public int workgroupId { get; set; }

        public DateTime? createDate { get; set; }

        public virtual User User { get; set; }
        public DateTime? tenureStart { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        public virtual Workgroup Workgroup { get; set; }

        public int? createUser { get; set; }
        public virtual User User_Create { get; set; }

    }
}
