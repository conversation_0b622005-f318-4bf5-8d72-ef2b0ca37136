namespace Atlas.Data.Entities
{
    using System;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("PollVote")]
    public class PollVote
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public PollVote()
        {
        }

        [Key]
        public int voteId { get; set; }
        public int pollId { get; set; }
        public int userId { get; set; }
        public int pollOptionId { get; set; }
        public DateTime? voteDate { get; set; }
        public bool? deleted { get; set; }
        public int? deleteUser { get; set; }
        public DateTime? deleteDate { get; set; }


        [Newtonsoft.Json.JsonIgnore]
        public virtual Poll Poll { get; set; }
        //[Newtonsoft.Json.JsonIgnore] //comentado S27-342
        public virtual User User_Vote { get; set; }
        [Newtonsoft.Json.JsonIgnore]
        public virtual PollOption Option { get; set; }
        public virtual User User_Deleted { get; set; }

        #region Campos customizados (nao estao no SQL)
        [NotMapped]
        public string pollOptionTitle
        {
            get
            {
                var titleToReturn = "";
                if (this.Poll.voteDisclosureOnMinute == true && this.Option != null)
                {
                    titleToReturn = this.Option.title;
                   
                }
                return titleToReturn;

            }
        } 
        #endregion
    }
}
