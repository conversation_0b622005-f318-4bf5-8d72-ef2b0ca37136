using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Atlas.Data.Entities
{
    [Table("GeneralActivityNotification")]
    public class GeneralActivityNotification
    {
        [Key]
        public int activityNotificationId { get; set; }
        public int activityId { get; set; }
        public int userId { get; set; }
        public string notificationReasonFlag { get; set; }
        public bool sendMail { get; set; }
        public bool groupable { get; set; }
        public DateTime generationDate { get; set; }
        public DateTime? sendDate { get; set; }
        public bool processed { get; set; }
        public bool? pushNotification { get; set; }
        public bool? sms { get; set; }
        public string msg { get; set; }
        public string msgId { get; set; }
        public int? systemActivityId { get; set; }
        public Activity Activity { get; set; }
        public User User { get; set; }
    }
}
