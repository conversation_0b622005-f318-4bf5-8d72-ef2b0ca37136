namespace Atlas.Data.Entities
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("Role")]
    public partial class Role
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Role()
        {
            RoleOperation = new HashSet<RoleOperation>();
            UserRole = new HashSet<UserRole>();
        }

        public int roleId { get; set; }

        [Required]
        [StringLength(100)]
        public string name { get; set; }

        public bool isWorkgroupRole { get; set; }

        public bool isGlobalRole { get; set; }
        public bool isBaseRole { get; set; }
        public Nullable<int> customRoleClientId { get; set; }
        

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]

        public virtual ICollection<RoleOperation> RoleOperation { get; set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]

        public virtual ICollection<UserRole> UserRole { get; set; }
    }
}
