namespace Atlas.Data.Entities
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("Poll")]
    public class Poll
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Poll()
        {
            this.Options = new HashSet<PollOption>();
            this.Votes = new HashSet<PollVote>();
        }

        [Key]
        public int pollId { get; set; }
        public int contentId { get; set; }
        public Guid contentUuid { get; set; }
        public string pollType { get; set; }
        public bool? voteDisclosureOnMinute { get; set; }
        public string description { get; set; }
        public DateTime dueDate { get; set; }

        public bool? notifyVoters { get; set; }

        public int? reportContentAttachmentId { get; set; }

        public bool? hidden { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        public virtual Content Content { get; set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<PollOption> Options { get; set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<PollVote> Votes { get; set; }


        [NotMapped]
        public bool? autoGeneratedReport { get; set; }
        [NotMapped]
        public int? time { get; set; }
        [NotMapped]
        public Guid? agendaItemContentUuId { get; set; }
        [NotMapped]
        public bool hasVoted { get; set; }
        [NotMapped]
        public int totalVotes { get; set; }
    }
}
