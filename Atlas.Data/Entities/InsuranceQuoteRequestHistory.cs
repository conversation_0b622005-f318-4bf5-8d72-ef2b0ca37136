namespace Atlas.Data.Entities
{
    using System;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("InsuranceQuoteRequestHistory")]
    public partial class InsuranceQuoteRequestHistory
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public InsuranceQuoteRequestHistory()
        {

        }

        [Key]
        public int insQuoteHistoryId { get; set; }
        public int insQuoteId { get; set; }
        public string historyType { get; set; }
        public int clientId { get; set; }
        public DateTime createDate { get; set; }
        public int? createUser { get; set; }
        public string historyText { get; set; }
        public bool deleted { get; set; }
        public int? attachmentId { get; set; }
        public virtual User Create_User { get; set; }
        public virtual InsuranceQuoteRequest Request { get; set; }
    }
}
