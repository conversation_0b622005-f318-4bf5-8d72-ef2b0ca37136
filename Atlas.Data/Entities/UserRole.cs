namespace Atlas.Data.Entities
{
    using System;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("UserRole")]
    public partial class UserRole
    {
        [Key]
        public int urId { get; set; }
        public int roleId { get; set; }
        public int userId { get; set; }
        public int createUser { get; set; }
        public DateTime? createDate { get; set; }
        public int? clientId { get; set; }
        public virtual Role Role { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        public virtual User User { get; set; }
        public virtual User User_Create { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        public virtual Client Client { get; set; }
    }
}
