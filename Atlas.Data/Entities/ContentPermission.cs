namespace Atlas.Data.Entities
{
    using System;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("ContentPermission")]
    public partial class ContentPermission
    {
        [Key]
        [Column(Order = 0)]
        public int contentId { get; set; }

        public Guid contentUuid { get; set; }

        [Key]
        [Column(Order = 1)]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int userId { get; set; }

        public bool allowed { get; set; }

        public DateTime createDate { get; set; }

        public int createUser { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        public virtual Content Content { get; set; }

        public virtual User User_create { get; set; }

        public virtual User User { get; set; }

        public string creationRule { get; set; }
    }
}
