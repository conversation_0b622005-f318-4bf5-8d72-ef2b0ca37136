namespace Atlas.Data.Entities
{
    using System;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("ContentUserFavorite")]
    public partial class ContentUserFavorite
    {
        [Key]
        public int favId { get; set; }

        public int userId { get; set; }

        public int contentId { get; set; }
        public Guid contentUuid { get; set; }
        public DateTime date { get; set; }
        public int? itemOrder { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        public Content Content { get; set; }
    }
}
