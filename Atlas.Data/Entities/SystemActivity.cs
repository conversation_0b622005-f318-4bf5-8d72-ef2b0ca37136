namespace Atlas.Data.Entities
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("SystemActivity")]
    public partial class SystemActivity
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public SystemActivity()
        {
            ActivityNotification = new HashSet<ActivityNotification>();
        }

        public int contentActivityId { get; set; }

        public int contentId { get; set; }

        public Guid contentUuid { get; set; }

        [Required]
        [StringLength(50)]
        public string type { get; set; }

        public DateTime date { get; set; }

        public int activityUser { get; set; }

        public bool processed { get; set; }

        public DateTime? processedDate { get; set; }

        public string contentData { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        public virtual Content Content { get; set; }
        public virtual User User { get; set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<ActivityNotification> ActivityNotification { get; set; }

        public string subItemType { get; set; }
        public int? subItemId { get; set; }
        public string newData { get; set; }
        public string oldData { get; set; }
        public string updatedFields { get; set; }
    }
}
