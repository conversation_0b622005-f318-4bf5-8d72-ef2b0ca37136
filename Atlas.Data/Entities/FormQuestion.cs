namespace Atlas.Data.Entities
{
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("FormQuestion")]
    public class FormQuestion
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public FormQuestion()
        {
            FormQuestionOptions = new HashSet<FormQuestionOption>();
            FormQuestionComments = new HashSet<FormQuestionComment>();
        }

        [Key]
        public int formQuestionId { get; set; }

        [StringLength(1000)]
        public string title { get; set; }

        [StringLength(30)]
        public string type { get; set; }
        public bool required { get; set; }
        public bool multipleAnswer { get; set; }
        public bool showComment { get; set; }
        public short questionOrder { get; set; }
        public bool deleted { get; set; }
        public int formSectionId { get; set; }
        public bool requiredJustification { get; set; } = false;

        [Newtonsoft.Json.JsonIgnore]
        public virtual FormSection FormSection { get; internal set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<FormQuestionOption> FormQuestionOptions { get; set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<FormQuestionComment> FormQuestionComments { get; set; }
    }
}
