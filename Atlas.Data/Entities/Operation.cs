namespace Atlas.Data.Entities
{
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("Operation")]
    public partial class Operation
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Operation()
        {
            RoleOperation = new HashSet<RoleOperation>();
        }

        public int operationId { get; set; }

        [Required]
        [StringLength(50)]
        public string operationKey { get; set; }

        [StringLength(50)]
        public string moduleName { get; set; }

        [StringLength(50)]
        public string submoduleName { get; set; }

        [StringLength(50)]
        public string operationName { get; set; }

        [StringLength(50)]
        public string operationType { get; set; }

        public bool? readData { get; set; }

        public bool? writeData { get; set; }

        [StringLength(50)]
        public string activitytype { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<RoleOperation> RoleOperation { get; set; }
        public bool? notification_default { get; set; }
        public bool? notification_push { get; set; }
        public bool? notification_sms { get; set; }
        public bool? notification_mail { get; set; }
        public bool? notification_mail_group { get; set; }
    }
}
