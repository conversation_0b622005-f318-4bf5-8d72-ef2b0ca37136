namespace Atlas.Data.Entities
{
    using System;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("ContentSubscriber")]
    public partial class ContentSubscriber
    {
        [Key]
        public int csubId { get; set; }

        public int userId { get; set; }

        public int contentId { get; set; }

        public Guid contentUuid { get; set; }

        [StringLength(50)]
        public string frequencyType { get; set; }

        public DateTime? createDate { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        public virtual Content Content { get; set; }

        public virtual User User { get; set; }
        public Nullable<bool> rsvp { get; set; }
        public Nullable<DateTime> rsvpDate { get; set; }

        public Nullable<bool> isRead { get; set; }
        public Nullable<DateTime> readDate { get; set; }


    }
}
