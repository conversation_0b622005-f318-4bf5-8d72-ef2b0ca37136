namespace Atlas.Data.Entities
{
    using System;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("Report")]
    public partial class Report
    {
        public int reportId { get; set; }

        public int year { get; set; }

        public int? month { get; set; }

        [Required]
        [StringLength(1)]
        public string periodType { get; set; }

        [StringLength(255)]
        public string shortDescription { get; set; }

        public string reportHighlights { get; set; }

        public int contentId { get; set; }

        public Guid contentUuid { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        public virtual Content Content { get; set; }
    }
}
