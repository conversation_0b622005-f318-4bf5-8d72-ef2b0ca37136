namespace Atlas.Data.Entities
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("FormQuestionResponse")]
    public class FormQuestionResponse
    {
        [Key]
        public int formQuestionResponseId { get; set; }
        public int userId { get; set; }
        public int formQuestionOptionId { get; set; }
        public string value { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        public virtual User User { get; internal set; }

        [Newtonsoft.Json.JsonIgnore]
        public virtual FormQuestionOption FormQuestionOption { get; internal set; }
    }
}
