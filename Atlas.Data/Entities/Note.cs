namespace Atlas.Data.Entities
{
    using System;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("Note")]
    public class Note
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Note()
        {
        }

        [Key]
        public int noteId { get; set; }
        public int contentId { get; set; }
        public Guid contentUuid { get; set; }
        public string text { get; set; }
        public DateTime? lastUpdate { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        public virtual Content Content { get; set; }

    }
}
