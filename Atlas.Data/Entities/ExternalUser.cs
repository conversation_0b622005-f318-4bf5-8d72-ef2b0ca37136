using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Atlas.Data.Entities
{
    [Table("ExternalUser")]
    public class ExternalUser
    {
        public int ExternalUserId { get; set; }
        public int ContentId { get; set; }
        public Guid contentUuid { get; set; }
        public int? ExternalDocumentRequestId { get; set; }
        [StringLength(100)]
        public string ExternalName { get; set; }

        [StringLength(200)]
        public string ExternalMail { get; set; }

        [StringLength(20)]
        public string ExternalMobile { get; set; }
        public DateTime? SentDate { get; set; }

        public Guid? ExternalKey { get; set; }

        public virtual ExternalDocumentRequest ExternalDocumentRequest { get; set; }
        [JsonIgnore]
        public virtual Content Content { get; set; }

        [NotMapped]
        public bool IsRecurring { get; set; }

        public virtual ICollection<ExternalAttachment> ExternalAttachments { get; set; }
    }
}
