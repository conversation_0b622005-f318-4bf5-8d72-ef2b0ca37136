namespace Atlas.Data.Entities
{
    using System.ComponentModel.DataAnnotations.Schema;

    public partial class ContentComment
    {
        [NotMapped]
        public UACClass UAC = new UACClass();

        [NotMapped]
        public class UACClass
        {
            public bool delete { get; set; }
            public bool undelete { get; set; }
            public bool delete_answer { get; set; }
            public bool undelete_answer { get; set; }
        }
    }

}
