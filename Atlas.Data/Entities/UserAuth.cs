namespace Atlas.Data.Entities
{
    using System;
    using System.ComponentModel.DataAnnotations.Schema;
    using System.Runtime.Serialization;

    [Table("UserAuth")]
    public partial class UserAuth
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public UserAuth()
        {
            
        }

        public int userAuthId { get; set; }
        public int userId { get; set; }
        public User User { get; set; }

        [IgnoreDataMember]
        public string hash { get; set; }

        [IgnoreDataMember]
        public string salt { get; set; }

        public DateTime date { get; set; }

    }
}
