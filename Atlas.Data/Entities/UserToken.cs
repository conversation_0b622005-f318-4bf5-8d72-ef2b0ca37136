namespace Atlas.Data.Entities
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("UserToken")]
    public partial class UserToken
    {
        [Key]
        public int tokenId { get; set; }

        public Nullable<int> userSessionId { get; set; }

        [Required]
        public string token { get; set; }

        public DateTime expireDate { get; set; }

        public virtual UserSession UserSession { get; set; }

        public bool used { get; set; }
        public bool revoked { get; set; }
        public string tokenType { get; set; }

        public string ProtectedTicket { get; set; }
        public Nullable<int> userId { get; set; }

        public DateTime issueDate { get; set; }
        public bool verified { get; set; }

        public int? resendCount { get; set; }

        // Nova propriedade
        // [Required]
        // [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public DateTime lastUpdate { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual ICollection<WipeRequestToken> WipeRequestTokens { get; set; }
    }
}
