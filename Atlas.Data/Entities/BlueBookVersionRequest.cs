using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Atlas.Data.Entities
{
    [Table("BlueBookVersionRequest")]
    public class BlueBookVersionRequest
    {

        [System.ComponentModel.DataAnnotations.Key]
        public int bbvReqId { get; set; }
        public int contentId { get; set; }
        public int contentUuid { get; set; }
        public int userId { get; set; }
        public int contentActivityId { get; set; }
        public DateTime requestDate { get; set; }

        public bool? processed { get; set; }
        public DateTime? processedDate { get; set; }
        public int bbVersionId { get; set; }
        public int retries { get; set; }
        public bool? error { get; set; }
        public string lastError { get; set; }

        public string batchId { get; set; }
        public string sessionId { get; set; }
        public bool obsolete { get; set; }
    }
}
