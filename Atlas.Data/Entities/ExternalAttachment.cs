using Newtonsoft.Json;
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Atlas.Data.Entities
{
    [Table("ExternalAttachment")] // This is always needed when a table is created with non-plural names
    public class ExternalAttachment
    {
        public int ExternalAttachmentId { get; set; }
        public int ContentAttachmentId { get; set; }
        
        [ForeignKey("Content")]
        public int ContentId { get; set; }
        public Guid contentUuid { get; set; }
        public int ExternalDocumentRequestId { get; set; }
        public int ExternalUserId { get; set; }
        public bool HasError { get; set; }
        public string ErrorMessage { get; set; }
        public bool Deleted { get; set; }

        public virtual ContentAttachment ContentAttachment { get; set; }
        [JsonIgnore]
        public virtual Content Content { get; set; }
        public virtual ExternalUser ExternalUser { get; set; }
    }
}
