namespace Atlas.Data.Entities
{
    using System;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("KnowledgeArticle")]
    public class KnowledgeArticle
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public KnowledgeArticle()
        {
        }

        [Key]
        public int kbArticleId { get; set; }
        public string title { get; set; }
        public string body { get; set; }
        public int kbCategoryId { get; set; }
        public int contentId { get; set; }
        public Guid contentUuid { get; set; }
        public int? version { get; set; }
        public int? contentIdExportedTo { get; set; }
        public Guid? contentUuidExportedTo { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        public virtual Content Content { get; set; }

        public KnowledgeCategory Category { get; set; }
    }
}
