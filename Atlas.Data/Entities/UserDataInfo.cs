namespace Atlas.Data.Entities
{
    public class UserDataInfo
    {
        //This class is here for the sole purpose of fixing a problem within the different response timings between registered and non registered users
        //when the api/Token request call fails. The intended result is for it to allow us to simplify and reduce database calls on the aforementioned 
        //proccess, improving the backend performance and possibly reducing the response time to be closer to the one desired.
        //It can be erased/moved/redone/ignored or anything of the sort as it seems fit by decision of the development team.

        public int userId { get; set; }
        public int accessFailedCount { get; set; }
        public bool blocked { get; set; }
        public string clientIds { get; set; }
        public int minPossibleWrongPassAttempts { get; set; }
        public string salt { get; set; }
        public string hash { get; set; }
    }

    public class UserDataExtendedInfo : UserDataInfo
    {
        public string mobile { get; set; }
        public string email { get; set; }
    }
}
