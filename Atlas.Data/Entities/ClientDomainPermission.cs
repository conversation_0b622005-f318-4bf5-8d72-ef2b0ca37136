namespace Atlas.Data.Entities
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("ClientDomainPermission")]
    public partial class ClientDomainPermission
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public ClientDomainPermission()
        {
        }
        [Key]
        public int domainId { get; set; }
        public int clientId { get; set; }
        public string domain { get; set; }
        public bool blocked { get; set; }


        [Newtonsoft.Json.JsonIgnore]
        public virtual Client Client { get; set; }

    }
}
