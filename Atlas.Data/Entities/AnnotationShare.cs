using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace Atlas.Data.Entities
{
    [Table("AnnotationShare")]
    public class AnnotationShare
    {
        public AnnotationShare()
        {
        }

        [Key]
        public int anShareId { get; set; }
        public Guid uuid { get; set; }

        public int userId { get; set; }
        [JsonPropertyName("shareUserId")]
        public int shareUser { get; set; }
        public DateTime shareDate { get; set; }
        public int contentId { get; set; }
        public int contentUuid { get; set; }
        public int? contentAttachmentId { get; set; }
        [JsonPropertyName("shareUser")]
        public User ShareUser { get; set; }
        public User User { get; set; }
        public Content Content { get; internal set; }
        public ContentAttachment ContentAttachment { get; internal set; }
    }
}
