namespace Atlas.Data.Entities
{
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("Company")]
    public partial class Company
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Company()
        {
            ContentTemplate = new HashSet<ContentTemplate>();
            User = new HashSet<User>();
            Workgroup = new HashSet<Workgroup>();
        }

        public int companyId { get; set; }

        [Required]
        [StringLength(100)]
        public string name { get; set; }

        [StringLength(250)]
        public string fullName { get; set; }

        public int clientId { get; set; }

        [Required]
        [StringLength(20)]
        public string foreColor { get; set; }

        [Required]
        [StringLength(20)]
        public string backColor { get; set; }

        [StringLength(20)]
        public string alternateColor1 { get; set; }

        [StringLength(20)]
        public string alternateColor2 { get; set; }

        [StringLength(20)]
        public string alternateColor3 { get; set; }

        [StringLength(20)]
        public string alternateColor4 { get; set; }

        [StringLength(20)]
        public string alternateColor5 { get; set; }

        [StringLength(200)]
        public string defaultTimezone { get; set; }

        [StringLength(200)]
        public string defaultLanguage { get; set; }
        public bool blocked { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        public virtual Client Client { get; set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ContentTemplate> ContentTemplate { get; set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<User> User { get; set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<Workgroup> Workgroup { get; set; }
    }
}
