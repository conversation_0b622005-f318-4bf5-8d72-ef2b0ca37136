namespace Atlas.Data.Entities
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("ContentActivity")]
    public partial class ContentActivity
    {

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public ContentActivity()
        {
            ContentActivityNotification = new HashSet<ActivityNotification>();
            DerivatedAnnouncements = new HashSet<Announcement>();
        }

        public int contentActivityId { get; set; }

        public int contentId { get; set; }
        public Guid contentUuid { get; set; }

        [Required]
        [StringLength(50)]
        public string type { get; set; }

        public DateTime date { get; set; }

        public int? activityUser { get; set; }

        public bool processed { get; set; }

        public DateTime? processedDate { get; set; }

        //[StringLength(2000)]
        public string contentData { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        public virtual Content Content { get; set; }
        [Newtonsoft.Json.JsonIgnore]
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Announcement> DerivatedAnnouncements { get; set; }
        public virtual User User { get; set; }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<ActivityNotification> ContentActivityNotification { get; set; }

        public string subItemType { get; set; }
        public int? subItemId { get; set; }
        [StringLength(255)]
        public string newData { get; set; }
        [StringLength(255)]
        public string oldData { get; set; }
        public string updatedFields { get; set; }
        public bool? hidden { get; set; }

        [StringLength(255)]
        public string IPAddress { get; set; }

        [StringLength(255)]
        public string sessionKey { get; set; }
        public int? originalContentActivityId { get; set; }
        public string device { get; set; }
    }
}
