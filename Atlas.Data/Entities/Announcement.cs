using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Atlas.Data.Entities
{
    [Table("Announcement")]
    public class Announcement
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Announcement()
        {
        }

        [Key]
        public int announcementId { get; set; }
        public int contentId { get; set; }
        public Guid contentUuid { get; set; }

        public string body { get; set; }
        public string type { get; set; }
        public bool systemGenerated { get; set; }

        public int? originalActivityId { get; set; }
        public int? originalContentId { get; set; }
        public Guid? originalContentUuid { get; set; }
        public string originalContentType { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        public virtual Content Content { get; set; }
        public virtual ContentActivity Original_Activity { get; set; }
        [Newtonsoft.Json.JsonIgnore]
        public virtual Content Original_Content { get; set; }
        [NotMapped]
        public string parentContentTitle { get; set; }

    }
}
