namespace Atlas.Data.Entities
{
    using System;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("LogMailFailed")]
    public partial class LogMailFailed
    {
        [Key]
        public int mailId { get; set; }
        public string template   { get; set; }
        public string data { get; set; }
        public string sendTo { get; set; }
        public DateTime createDate { get; set; }
        public DateTime lastAttempt { get; set; }
        public int countAttempt { get; set; }
        public string lastError { get; set; }
    }
}
