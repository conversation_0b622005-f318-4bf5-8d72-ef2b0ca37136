namespace Atlas.Data.Entities
{
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("PollOption")]
    public class PollOption
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public PollOption()
        {
            this.Votes = new HashSet<PollVote>();
        }

        [Key]
        public int pollOptionId { get; set; }
        public int pollId { get; set; }
        public string title { get; set; }
        public string title_secondaryLanguage { get; set; }
        public int itemOrder { get; set; }  

        [Newtonsoft.Json.JsonIgnore]
        public virtual Poll Poll { get; set; }

        //[Newtonsoft.Json.JsonIgnore]
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<PollVote> Votes { get; set; }
    }
}
