namespace Atlas.Data.Entities
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    [Table("FormQuestionComment")]
    public class FormQuestionComment
    {
        [Key]
        public int formQuestionCommentId { get; set; }
        public int userId { get; set; }
        public int formQuestionId { get; set; }

        [StringLength(1000)]
        public string comment { get; set; }

        [Newtonsoft.Json.JsonIgnore]
        public virtual User User { get; internal set; }

        [Newtonsoft.Json.JsonIgnore]
        public virtual FormQuestion FormQuestion { get; internal set; }
    }
}
