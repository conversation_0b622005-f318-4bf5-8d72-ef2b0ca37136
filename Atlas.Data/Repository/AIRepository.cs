using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Atlas.CrossCutting.AppEnums;
using Atlas.CrossCutting.DTO.Workgroup;
using Atlas.CrossCutting.Helpers.Notetaker;
using Atlas.Data.Entities;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;

namespace Atlas.Data.Repository
{
    public class AIRepository
    {
        private readonly AtlasModelCore _model;
        private int _currentUser;

        public AIRepository(int currentUser)
        {
            _currentUser = currentUser;
            _model = new AtlasModelCore();
        }

        public async Task<MeetingTranscription> GetMeetingTranscription(int contentId, bool withSummary = false)
        {
            var query = _model.MeetingTranscription
                .Include(mt => mt.Content)
                .Where(mt => mt.contentId == contentId && mt.Content.type == ContentTypes.Meeting && mt.Content.deleted != true);

            if (withSummary)
            {
                query = query.Include(mt => mt.MeetingTranscriptionSummaries);
            }

            var transcription = await query
                .OrderByDescending(mt => mt.createdAt)
                .AsNoTracking()
                .FirstOrDefaultAsync();

            return transcription;
        }

        public async Task<MeetingAISummaryVersioned> GetLastSummaryVersioned
            (int contentId, MeetingTranscription meetingTranscription, bool detailed = false)
        {
            MeetingTranscriptionSummary originalSummary = meetingTranscription.MeetingTranscriptionSummaries.FirstOrDefault();
            detailed = detailed || originalSummary.CurrentSummaryType == NotetakerMeetingSummaryType.DETAILED;

            var summary = await _model.MeetingAISummaryVersioned
                .OrderByDescending(mt => mt.Version)
                .AsNoTracking()
                .FirstOrDefaultAsync(mt => mt.ContentId == contentId && mt.Detailed == detailed);

            meetingTranscription.MeetingAISummaryVersioned = summary;

            return summary;
        }

        public async Task<bool> UpdateMeetingAISummaryType(int contentId, string currentSummaryType)
        {
            currentSummaryType = currentSummaryType.ToUpper();

            var sql = "UPDATE MeetingTranscriptionSummary SET currentSummaryType = @currentSummaryType WHERE contentId = @contentId";

            var parameters = new[]
            {
                new SqlParameter("@currentSummaryType", currentSummaryType),
                new SqlParameter("@contentId", contentId)
            };

            var entries = await _model.Database.ExecuteSqlRawAsync(sql, parameters);
            return entries > 0;
        }

        public async Task<MeetingAISummaryVersioned> CreateSummaryVersioned(MeetingAISummaryVersioned summaryVersioned)
        {
            var newSummaryVersioned = _model.MeetingAISummaryVersioned.Add(summaryVersioned);
            if (await _model.SaveChangesAsync() > 0) return newSummaryVersioned.Entity;

            return null;
        }

        public async Task<bool> UpdateSummaryVersioned(MeetingAISummaryVersioned summaryVersioned)
        {
            _model.Entry(summaryVersioned).State = EntityState.Modified;
            var entries = await _model.SaveChangesAsync();
            return entries > 0;
        }

        public async Task<IEnumerable<MeetingTranscriptionTask>> GetMeetingTasksByTranscription(Guid transcriptionId)
        {
            var query = from tt in _model.MeetingTranscriptionTask
                        join c in _model.Content on tt.contentId equals c.contentId
                        where tt.transcriptionId == transcriptionId && c.deleted != true
                        select tt;

            var transcriptionTasks = await query
                .Include(tt => tt.CreatedContent.Task)
                .Include(tt => tt.CreatedContent.User_Assigned)
                .ToArrayAsync();

            return transcriptionTasks;
        }

        public MeetingTranscription SaveAIModel(MeetingTranscription model)
        {
            _model.MeetingTranscription.Add(model);
            _model.SaveChanges();
            return model;
        }

        public async Task<Content> GetContent(int contentId)
        {
            return await _model.Content.FindAsync(contentId);
        }

        public async Task<Content> GetContentWithMeeting(int contentId)
        {

            var query = from c in _model.Content
                        join cp in _model.ContentPermission on c.contentId equals cp.contentId
                        where
                            c.contentId == contentId && cp.userId == _currentUser &&
                            c.type == ContentTypes.Meeting && c.deleted != true
                        select c;

            return await query
                .Include(c => c.Meeting)
                .FirstOrDefaultAsync();
        }

        public async Task<Content> GetContentMeeting(int contentId)
        {
            var query = from c in _model.Content
                        join cp in _model.ContentPermission on c.contentId equals cp.contentId
                        where
                            c.contentId == contentId && cp.userId == _currentUser &&
                            c.type == ContentTypes.Meeting && c.deleted != true
                        select c;

            var content = await query
                .Include(c => c.ContentOwner)
                .FirstOrDefaultAsync();

            return content;
        }

        public async Task<Content> GetContentMeetingMinute(int parentContentId)
        {
            var query = from c in _model.Content
                        join cp in _model.ContentPermission on c.contentId equals cp.contentId
                        where
                            c.parentContentId == parentContentId && cp.userId == _currentUser &&
                            c.type == ContentTypes.MeetingMinute && c.deleted != true
                        select c;

            var meetingMinuteContent = await query.OrderByDescending(c => c.createDate)
                .Include(c => c.MeetingMinute)
                .Include(c => c.Parent_Content)
                .FirstOrDefaultAsync();

            return meetingMinuteContent;
        }

        public async Task<MeetingTranscriptionTask> GetMeetingTranscriptionTaskById(Guid taskId)
        {
            var task = await _model.MeetingTranscriptionTask.FindAsync(taskId);

            return task;
        }

        public async Task<bool> DeleteMeetingTranscriptionTaskAsync(MeetingTranscriptionTask task)
        {
            _model.MeetingTranscriptionTask.Remove(task);
            var entries = await _model.SaveChangesAsync();

            return entries > 0;
        }

        public async Task<Content> GetMeetingTask(int contentId, bool includeDeleted = false)
        {
            var query = from c in _model.Content
                        join cp in _model.ContentPermission on c.contentId equals cp.contentId
                        join t in _model.Task on c.contentId equals t.contentId
                        join tt in _model.MeetingTranscriptionTask on c.contentId equals tt.createdContentId
                        where
                            c.contentId == contentId && cp.userId == _currentUser &&
                            c.type == ContentTypes.Task && (includeDeleted || c.deleted != true)
                        select c;

            var content = await query
                .Include(c => c.Task)
                .FirstOrDefaultAsync();

            return content;
        }

        public async Task<bool> DeleteMeetingTaskAsync(Content content)
        {

            var transcriptionTasks = await _model.MeetingTranscriptionTask
                .Where(tt => tt.createdContentId == content.contentId)
                .ToArrayAsync();

            _model.MeetingTranscriptionTask.RemoveRange(transcriptionTasks);

            var entries = await _model.SaveChangesAsync();
            return entries > 0;
        }

        public async Task<WorkgroupDTO> GetWorkgroupByIdAsync(int workgroupId)
        {
            var workgroup = await _model.Workgroup
                .Where(w => w.workgroupId == workgroupId && w.WorkgroupUser.Any(wu => wu.userId == _currentUser))
                .Include(w => w.Client)
                .Include(w => w.WorkgroupUser.Select(wu => wu.User))
                .Include(w => w.WorkgroupOwner.Select(wo => wo.User))
                .Select(w => new WorkgroupDTO
                {
                    workgroupId = w.workgroupId,
                    name = w.name,
                    clientId = w.clientId,
                    clientName = w.Client.name,
                    clientPlanName = w.Client.planName,
                    archived = w.archived,
                    WorkgroupOwner = w.WorkgroupOwner.Select(wo => new WorkgroupOwnerDTO
                    {
                        workgroupId = w.workgroupId,
                        userId = wo.userId,
                        WorkgroupOwnerId = wo.workgroupOwnerId
                    }).ToList(),
                    WorkgroupUser = w.WorkgroupUser.Select(wu => new WorkgroupUserDTO
                    {
                        workgroupId = w.workgroupId,
                        userId = wu.userId,
                        workgroupUserId = wu.wuId
                    }).ToList()
                })
                .FirstOrDefaultAsync();

            return workgroup;
        }

        public async Task<bool> UpdateMeetingTranscriptionTask(MeetingTranscriptionTask transcriptionTask)
        {
            _model.MeetingTranscriptionTask.Update(transcriptionTask);
            var entries = await _model.SaveChangesAsync();

            return entries > 0;
        }

        public void SetContentPermissions(Content content, IEnumerable<int> users)
        {
            users = users.Distinct();

            content.ContentPermission = users.Select(u => new ContentPermission
            {
                contentId = content.contentId,
                userId = u,
                allowed = true,
                createDate = DateTime.UtcNow,
                createUser = _currentUser
            }).ToList();
        }

        public void SetContentOwners(Content content, IEnumerable<int> users)
        {
            users = users.Distinct();

            if (content.ContentOwner.Any())
            {
                foreach (var user in users)
                {
                    content.ContentOwner.Add(new ContentOwner
                    {
                        contentId = content.contentId,
                        userId = user,
                        createDate = DateTime.UtcNow,
                        createUser = _currentUser
                    });
                }

                return;
            }

            content.ContentOwner = users.Select(u => new ContentOwner
            {
                contentId = content.contentId,
                userId = u,
                createDate = DateTime.UtcNow,
                createUser = _currentUser
            }).ToList();
        }

        public void SetContentSubscribers(Content content, IEnumerable<int> users)
        {
            users = users.Distinct();

            if (content.ContentSubscriber.Any())
            {
                foreach (var user in users)
                {
                    content.ContentSubscriber.Add(new ContentSubscriber
                    {
                        contentId = content.contentId,
                        userId = user,
                        createDate = DateTime.UtcNow
                    });
                }

                return;
            }

            content.ContentSubscriber = users.Select(u => new ContentSubscriber
            {
                contentId = content.contentId,
                userId = u,
                createDate = DateTime.UtcNow
            }).ToList();
        }

        public async Task<Content> CreateContentAsync(Content content)
        {
            _model.Content.Add(content);

            // Database.Log is not supported in EF Core

            await _model.SaveChangesAsync();

            return content;
        }

        public async Task<bool> UpdateContentAsync(Content content)
        {
            var result = false;

            if (content != null)
            {
                _model.Content.Update(content);
                result = await _model.SaveChangesAsync() > 0;
            }

            return result;
        }

        public async Task<Content> GetParentContent(int parentContentId)
        {
            return await _model.Content.FindAsync(parentContentId);
        }

        public async Task<bool> CheckMeetingTaskAlreadyCreated(Guid taskId)
        {
            return await _model.MeetingTranscriptionTask.AnyAsync(tt => tt.taskId == taskId && tt.createdContentId.HasValue);
        }

        public async Task<ICollection<ContentPermission>> GetAllPermissionsForContents(IEnumerable<int> contentIds)
        {
            return await _model.ContentPermission
                .Where(cp => contentIds.Contains(cp.contentId))
                .ToListAsync();
        }

        public async Task<ICollection<ContentPermission>> GetAllPermissionsForContent(int contentId)
        {
            return await _model.ContentPermission.Where(cp => cp.contentId == contentId).ToListAsync();
        }

        public async Task<ICollection<Content>> GetAllChildContentsOfType(int parentContentId, string contentType)
        {

            return await _model.Content
                 .Where(c => c.parentContentId == parentContentId && c.type == contentType && c.deleted != true)
                 .ToListAsync();
        }

        public async Task<bool> UnlinkSuggestedTaskFromContent(int contentId)
        {
            var transcriptionTasks = await _model.MeetingTranscriptionTask
                .Where(tt => tt.createdContentId == contentId)
                .ToListAsync();

            transcriptionTasks.ForEach(tt => tt.createdContentId = null);

            var entries = await _model.SaveChangesAsync();
            return entries > 0;
        }

        public async Task<bool> IsMeetingOwner(int? parentContentId)
        {
            if (!parentContentId.HasValue) { return false; }

            var query = from c in _model.Content
                        join co in _model.ContentOwner on c.contentId equals co.contentId
                        where c.type == ContentTypes.Meeting && c.deleted != true && c.contentId == parentContentId && co.userId == _currentUser
                        select co;

            bool isOwner = await query.AnyAsync();
            return isOwner;
        }

        public async Task<ICollection<ContentOwner>> GetContentOwners(int contentId)
        {
            return await _model.ContentOwner.Where(co => co.contentId == contentId).ToListAsync();
        }

        public async Task<ICollection<ContentSubscriber>> GetContentSubscribers(int contentId)
        {
            return await _model.ContentSubscriber.Where(cs => cs.contentId == contentId).ToListAsync();
        }

        public async Task<bool> ShouldHideMeetingTranscription(int contentId)
        {
            return await (from c in _model.Content
                          join wk in _model.Workgroup on c.workgroupId equals wk.workgroupId
                          join cl in _model.Client on wk.clientId equals cl.clientId
                          where c.contentId == contentId
                          && c.type == ContentTypes.Meeting
                          && c.deleted != true
                          && wk.archived != true
                          && cl.blocked != true
                          && cl.deleted != true
                          && cl.atlasAIHideTranscription == true
                          select c.contentId).AnyAsync();
        }

        public async Task<bool> HasAnyChildren(int contentId)
        {
            using (var model = new AtlasModelCore())
            {
                string sql = $@"
                    SELECT
                        CAST(1 AS BIT) HasChild
                    FROM [Content]
                    WHERE parentContentId = @contentId AND ISNULL(deleted, 0) = 0";

                var result = await _model.Database.SqlQueryRaw<bool>(sql, new SqlParameter("@contentId", System.Data.SqlDbType.Int) { Value = contentId })
                    .AnyAsync();

                return result;
            }
        }
    }
}

