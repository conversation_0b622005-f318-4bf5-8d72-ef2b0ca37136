using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Atlas.Data.Entities;

namespace Atlas.Data.Repository
{
    public class UserRoleRepository
    {
        int _currentUser;
        public UserRoleRepository(int user)
        {
            _currentUser = user;
        }

        AtlasModelCore _md = new AtlasModelCore();

        public async Task<List<Role>> GetAvailableRolesForWorkgroup(int workgroupId, bool workgroupRoleOnly = false)
        {
            //filter client custom roles
            var select = (from r in _md.Role
                          select r);

            if (workgroupRoleOnly)
            {
                select = select.Where(o => o.isWorkgroupRole == true);
            }

            return await select.ToListAsync();
        }
        public async Task<List<Role>> GetCurrentRolesForUserInClient(int userId, int clientId)
        {
            var select = (from r in _md.Role
                          join ur in _md.UserRole on r.roleId equals ur.roleId
                          where ur.userId == userId
                          && ur.clientId == clientId
                          select r);


            select = select.Where(o => o.isWorkgroupRole == false);

            return await select.Distinct().ToListAsync();
        }

        public async Task<List<Role>> GetCurrentRolesForUser(int userId)
        {
            var select = (from r in _md.Role
                          join ur in _md.UserRole on r.roleId equals ur.roleId
                          where ur.userId == userId
                          select r);


            select = select.Where(o => o.isWorkgroupRole == false);

            return await select.Distinct().ToListAsync();
        }

        public List<Role> GetCurrentRolesForUserSynchronously(int userId)
        {
            var select = (from r in _md.Role
                          join ur in _md.UserRole on r.roleId equals ur.roleId
                          where ur.userId == userId
                          select r);


            select = select.Where(o => o.isWorkgroupRole == false);

            return select.Distinct().ToList();
        }

        [Obsolete]
        public async void GetCurrentRolesForUser(int workgroupId, int userId, bool workgroupRoleOnly = false)
        {
            //if (workgroupRoleOnly)
            //{
            //    var select = (from r in _md.Role
            //                  join wr in _md.WorkgroupUserRole on r.roleId equals wr.roleId
            //                  join wu in _md.WorkgroupUser on wr.wuId equals wu.wuId
            //                  where wu.workgroupId == workgroupId && wu.userId == userId
            //                  select r);


            //    select = select.Where(o => o.isWorkgroupRole == true);
            //    return await select.Distinct().ToListAsync();
            //}
            //else
            //{
            //    throw new NotImplementedException();
            //}
        }

        //public async Task<object> Save(int userId, int workgroupId, List<Role> selected_roles)
        //{
        //    var current_roles = await (from wr in _md.WorkgroupUserRole
        //                               join wu in _md.WorkgroupUser on wr.wuId equals wu.wuId
        //                               where wu.userId == userId && wu.workgroupId == workgroupId
        //                               select wr).Include(o => o.Role).ToListAsync();

        //    var new_roles = new List<Role>();
        //    var excluded_roles = new List<WorkgroupUserRole>();


        //    var wuId = current_roles.First().wuId;

        //    //items to add
        //    foreach (var item in selected_roles)
        //    {
        //        if (!current_roles.Select(o => o.roleId).Contains(item.roleId))
        //        {
        //            new_roles.Add(item);
        //        }
        //    }

        //    //items to exclude
        //    foreach (var item in current_roles)
        //    {
        //        if (!selected_roles.Select(o => o.roleId).Contains(item.roleId))
        //        {
        //            excluded_roles.Add(item);
        //        }
        //    }

        //    //_md.WorkgroupUserRole.AddRange(new_roles.Select(o => new WorkgroupUserRole()
        //    //{
        //    //    roleId = o.roleId,
        //    //    createDate = DateTime.UtcNow,
        //    //    wuId = wuId
        //    //}));


        //    //_md.WorkgroupUserRole.RemoveRange(excluded_roles);

        //    return await _md.SaveChangesAsync();

        //}

        //public bool CheckPermissionsForWorkgroup(string operationKey, int workgroupId)
        //{
        //    var select = (from c in _md.Content
        //                  join wu in _md.WorkgroupUser on c.workgroupId equals wu.workgroupId
        //                  join wur in _md.WorkgroupUserRole on wu.wuId equals wur.wuId
        //                  join r in _md.Role on wur.roleId equals r.roleId
        //                  join rop in _md.RoleOperation on r.roleId equals rop.roleId
        //                  join op in _md.Operation on rop.operationId equals op.operationId
        //                  where wu.userId == _currentUser && op.operationKey == operationKey && wu.workgroupId == workgroupId
        //                  select wu);

        //    return select.Any();
        //}

        public List<UserRole> GetUserRolesDetails(int userId)
        {
            var select = (from ur in _md.UserRole
                          where ur.userId == userId
                          select ur).Include(o => o.Role).Include(o => o.Client).OrderBy(o => o.clientId);

            return select.ToList();
        }

        public async Task<bool> DeleteRoleFromUser(int userId, int? clientId, string roleName)
        {
            var rolesToExclude = await (from ur in _md.UserRole
                                        join r in _md.Role on ur.roleId equals r.roleId
                                        where ur.userId == userId && r.name == roleName && ur.clientId == clientId
                                        select ur).ToListAsync();

            if (!rolesToExclude.Any())
            {
                return false; // Role not found
            }

            _md.UserRole.RemoveRange(rolesToExclude);

            var entries = await _md.SaveChangesAsync();
            return entries > 0;
        }

        public async Task<bool> AddRoleToUser(int userId, int clientId, string roleName)
        {
            var role = await _md.Role.Where(o => o.name == roleName).FirstOrDefaultAsync();

            if (role == null)
            {
                throw new Exception("Role not found");
            }

            var userRole = _md.UserRole.Add(new UserRole()
            {
                userId = userId,
                roleId = role.roleId,
                createUser = _currentUser,
                createDate = DateTime.UtcNow,
                clientId = clientId
            });

            //create a new record at UserRole
            var entries = await _md.SaveChangesAsync();
            return entries > 0;
        }
    }
}
