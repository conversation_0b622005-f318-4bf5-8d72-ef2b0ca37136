using Atlas.CrossCutting.AppEnums;
using Atlas.CrossCutting.DTO.Resolution;
using Atlas.CrossCutting.DTO.Workgroup;
using Atlas.Data.Entities;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace Atlas.Data.Repository
{
    public class ResolutionRepository
    {
        readonly private int _currentUser;
        readonly private AtlasModelCore _model;
        readonly private string[] _defaultStatuses = new string[] { "AWAITING_VOTES", "CANCELLED", "CLOSED" };

        public ResolutionRepository(int currentUserId)
        {
            _currentUser = currentUserId;
            _model = new AtlasModelCore();
        }

        public async Task<Content> CreateResolutionContentAsync(Content resolutionContent)
        {
            var entity = await _model.Content.AddAsync(resolutionContent);
            var entries = await _model.SaveChangesAsync();

            return entries > 0 ? entity.Entity : null;
        }

        public async Task<Content> GetSimpleParentContent(int parentContentId)
        {
            return await _model.Content.
           Where(c => c.contentId == parentContentId)
           .Include(c => c.ContentPermission)
           .Include(c => c.ContentOwner)
           .Include(c => c.ContentOwner.Select(o => o.User))
           .Include(c => c.ContentSubscriber)
           .Include(c => c.ContentSubscriber.Select(o => o.User))
           .FirstOrDefaultAsync();
        }

        public async Task<WorkgroupDTO> GetWorkgroup(int workgroupId)
        {
            var workgroup = await _model.Workgroup
                .Where(w => w.workgroupId == workgroupId && w.WorkgroupUser.Any(wu => wu.userId == _currentUser))
                .Include(w => w.Client)
                .Include(w => w.WorkgroupUser).ThenInclude(wu => wu.User)
                .Include(w => w.WorkgroupOwner).ThenInclude(wo => wo.User)
                .Select(w => new WorkgroupDTO
                {
                    workgroupId = w.workgroupId,
                    name = w.name,
                    clientId = w.clientId,
                    clientName = w.Client.name,
                    clientPlanName = w.Client.planName,
                    clientBlocked = w.Client.blocked,
                    clientDeleted = w.Client.deleted,
                    archived = w.archived,
                    WorkgroupOwner = w.WorkgroupOwner.Select(wo => new WorkgroupOwnerDTO
                    {
                        workgroupId = w.workgroupId,
                        userId = wo.userId,
                        WorkgroupOwnerId = wo.workgroupOwnerId
                    }).ToList(),
                    WorkgroupUser = w.WorkgroupUser.Select(wu => new WorkgroupUserDTO
                    {
                        workgroupId = w.workgroupId,
                        userId = wu.userId,
                        workgroupUserId = wu.wuId
                    }).ToList()
                })
                .FirstOrDefaultAsync();

            return workgroup;
        }

        public async Task<WorkgroupDTO> GetWorkgroupByContentId(int contentMeetingId)
        {
            var workgroupId = await _model.Content
                .Where(c => c.contentId == contentMeetingId)
                .Select(c => c.workgroupId)
                .FirstOrDefaultAsync();

            var result = await GetWorkgroup(workgroupId);
            return result;
        }

        public void SetContentOwners(Content resolution)
        {
            if (resolution.Parent_Content != null)
            {
                foreach (var permission in resolution.Parent_Content.ContentOwner)
                {
                    resolution.ContentOwner.Add(new ContentOwner()
                    {
                        createUser = _currentUser,
                        userId = permission.userId,
                        createDate = DateTime.UtcNow,
                        contentUuid = resolution.contentUuid
                    });
                }

                return;
            }

            resolution.ContentOwner.Add(new ContentOwner()
            {
                createUser = _currentUser,
                userId = _currentUser,
                createDate = DateTime.UtcNow
            });

            return;
        }

        public void SetContentPermissions(Content resolution, WorkgroupDTO workgroup)
        {
            if (resolution.Parent_Content != null)
            {
                foreach (var permission in resolution.Parent_Content.ContentPermission)
                {
                    resolution.ContentPermission.Add(new ContentPermission()
                    {
                        createUser = _currentUser,
                        userId = permission.userId,
                        allowed = true,
                        createDate = DateTime.UtcNow,
                        contentUuid = resolution.contentUuid
                    });
                }
            }
            else
            {
                bool found = false;

                var permissions = new List<ContentPermission>();
                var usersToPermit = workgroup.WorkgroupUser.Select(x => x.userId).ToList();

                foreach (var userId in usersToPermit)
                {
                    permissions.Add(new ContentPermission()
                    {
                        createUser = _currentUser,
                        userId = userId,
                        allowed = true,
                        createDate = DateTime.UtcNow
                    });

                    if (userId == _currentUser) { found = true; }
                }

                if (!found)
                {
                    permissions.Add(new ContentPermission()
                    {
                        createUser = _currentUser,
                        userId = _currentUser,
                        allowed = true,
                        createDate = DateTime.UtcNow
                    });
                }
                resolution.ContentPermission = permissions;
            }
        }

        public void SetContentSubscribers(Content resolution, int[] subscribers = null)
        {
            if (resolution.Parent_Content != null)
            {
                var filteredSubscribers = subscribers != null && subscribers.Any() ?
                    resolution.Parent_Content.ContentPermission.Where(cs => subscribers.Contains(cs.userId)).ToList() :
                    resolution.Parent_Content.ContentPermission.ToList();

                foreach (var permission in filteredSubscribers)
                {
                    resolution.ContentSubscriber.Add(new ContentSubscriber()
                    {
                        userId = permission.userId,
                        createDate = DateTime.UtcNow,
                        contentUuid = resolution.contentUuid
                    });
                }
            }
        }

        private string GetStatusFilter(string status, bool isCounterFilter = false)
        {
            string resultStatus = " AND C.[status] = @status_str ";

            bool getAllStatuses = string.IsNullOrWhiteSpace(status) || !_defaultStatuses.Contains(status);

            if (getAllStatuses)
            {
                resultStatus = " AND C.[status] IN ('AWAITING_VOTES', 'CANCELLED', 'CLOSED') ";

                if (isCounterFilter)
                {
                    resultStatus = " AND (C.[status] IN ('CANCELLED', 'CLOSED') OR (C.[status] = 'AWAITING_VOTES' AND CS.csubId IS NOT NULL AND V.pollId IS NULL)) ";
                }
            }
            else if (status == "AWAITING_VOTES")
            {
                resultStatus += " AND CS.csubId IS NOT NULL AND V.pollId IS NULL ";

                if (isCounterFilter)
                {
                    resultStatus = " AND (C.[status] = 'AWAITING_VOTES' AND CS.csubId IS NOT NULL AND V.pollId IS NULL) ";
                }
            }

            return resultStatus;
        }


        public async Task<List<ResolutionCountersDTO>> GetCounters(int currentUserId, string status, int[] workgroupList, DateTime? startDate, DateTime? endDate)
        {
            try
            {
                if (status == "AWAITING_MY_VOTE")
                {
                    status = "AWAITING_VOTES";
                }

                bool getAllStatuses = string.IsNullOrWhiteSpace(status) || !_defaultStatuses.Contains(status);

                List<SqlParameter> parameters = new List<SqlParameter>
                {
                    new SqlParameter("@currentUserId", currentUserId) { SqlDbType = SqlDbType.Int }
                };

                var statusFilter = GetStatusFilter(status, isCounterFilter: true);

                if (_defaultStatuses.Contains(status) && status != "AWAITING_VOTES")
                {
                    parameters.Add(new SqlParameter("@status_str", status) { SqlDbType = SqlDbType.NVarChar });
                }

                string workgroupListFilter = workgroupList.Any() ? $" AND W.workgroupId IN ({string.Join(",", workgroupList)}) " : "";

                string dateFilter = ValidateDateParam(startDate, endDate);

                if (startDate.HasValue)
                    parameters.Add(new SqlParameter("@start_ts", startDate.Value.ToUniversalTime()) { SqlDbType = SqlDbType.DateTime });

                if (endDate.HasValue)
                    parameters.Add(new SqlParameter("@end_ts", endDate.Value.ToUniversalTime()) { SqlDbType = SqlDbType.DateTime });

                var result = new List<ResolutionCountersDTO>();
                result.AddRange(new List<ResolutionCountersDTO>
                {
                    new ResolutionCountersDTO {Status = "AWAITING_VOTES", Total = 0},
                    new ResolutionCountersDTO {Status = "CANCELLED", Total = 0},
                    new ResolutionCountersDTO {Status = "CLOSED", Total = 0}
                });

                string baseQuery = $@"
                     WITH FilteredContent AS (
                        SELECT C.*
                        FROM Content C
                        inner join [ContentPermission] CP ON C.contentId = CP.contentId and CP.userId = @currentUserId
                        WHERE [type] = 'Poll' AND (deleted = 0 OR deleted IS NULL)
                    ),
                    FilteredPollVote AS (
                        SELECT PV.*
                        FROM PollVote PV
	                    inner join Poll P ON PV.pollId = P.pollId
	                    inner join Content C ON C.contentId = P.contentId
	                    inner join [ContentPermission] CP ON C.contentId = CP.contentId and CP.userId = @currentUserId
                        WHERE (C.deleted = 0 OR C.deleted IS NULL)
                    )
                    SELECT 
                        C.[status], 
                        COUNT(C.[status]) AS Total
                    FROM
                        Poll as P
                    JOIN [FilteredContent] C ON P.contentId = C.contentId
                    LEFT JOIN [FilteredContent] Parent ON C.parentContentId = P.contentId
                    JOIN [ContentPermission] CP ON C.contentId = CP.contentId AND CP.userId = @currentUserId
                    LEFT JOIN [ContentOwner] CO ON C.contentId = CO.contentId AND CO.userId = @currentUserId
                    LEFT JOIN [ContentSubscriber] CS ON C.contentId = CS.contentId AND CS.userId = @currentUserId
                    LEFT JOIN [FilteredPollVote] AS V ON P.pollId = V.pollId AND V.userId = @currentUserId
                    JOIN [Workgroup] W ON C.workgroupId = W.workgroupId
                    JOIN [Client] CL ON W.clientId = CL.clientId
                    WHERE
                        (C.deleted = 0 OR C.deleted IS NULL)
                        AND (Parent.deleted = 0 OR Parent.deleted IS NULL)
                        AND W.archived = 0
                        AND CL.blocked = 0
                        AND CL.deleted = 0
                        AND (P.[hidden] = 0 OR P.[hidden] IS NULL OR CO.contentOwnerId IS NOT NULL)
                        {statusFilter}
                        {dateFilter}
                        {workgroupListFilter}
                    GROUP BY 
                        C.[status]  ";

                // Stopwatch stopwatch = new Stopwatch();
                // stopwatch.Start();

                var queryResult = await _model.Database.SqlQueryRaw<ResolutionCountersDTO>(baseQuery, parameters.ToArray()).ToListAsync();

                //stopwatch.Stop();
                //long elapsedMillisecondsSqlQuery = stopwatch.ElapsedMilliseconds;

                //stopwatch.Restart();
                foreach (var queryStatus in _defaultStatuses)
                {
                    var resultItem = result.FirstOrDefault(c => c.Status == queryStatus);
                    var queryResultItem = queryResult.FirstOrDefault(c => c.Status == queryStatus);

                    if (resultItem != null && queryResultItem != null)
                    {
                        resultItem.Total = queryResultItem.Total;
                    }
                }

                //stopwatch.Stop();
                //long elapsedMillisecondsFillDTO = stopwatch.ElapsedMilliseconds;

                //Debug.WriteLine($"ResolutionRepository.GetCounts.SqlQuery: Elapsed time: {elapsedMillisecondsSqlQuery} milliseconds");
                //Debug.WriteLine($"ResolutionRepository.GetCounts.FillsDTO: Elapsed time: {elapsedMillisecondsFillDTO} milliseconds");

                return result;
            }
            catch (Exception)
            {
                return new List<ResolutionCountersDTO>() { };
            }
        }

        public async Task<ResolutionListingDTO> GetResolutions
            (int currentUserId, int page, int itemsPerPage, string status, int[] workgroupList, DateTime? startDate, DateTime? endDate)
        {
            try
            {
                // var stopWatchOverall = Stopwatch.StartNew();
                // var stopWatch1 = Stopwatch.StartNew();

                var subscriberJoin = status == "AWAITING_VOTES" ?
                    " LEFT JOIN [ContentSubscriber] CS ON C.contentId = CS.contentId AND CS.userId = @currentUserId " :
                    "";

                string workgroupListFilter = workgroupList.Any() ? $" AND W.workgroupId IN ({string.Join(",", workgroupList)}) " : "";

                string dateFilter = ValidateDateParam(startDate, endDate);

                string querySelect = @"
                    P.pollId as ResolutionId,
                    C.contentId as ContentId,
                    C.[status] as [Status],
                    C.[title] as Title,
                    P.dueDate as DueDate,
                    CAST(CASE WHEN V.voteId IS NULL THEN 0 ELSE 1 END AS BIT) AS VoteRegistered,
                    W.workgroupId as WorkgroupId,
                    W.name as WorkgroupName,
                    W.bulletColor as WorkgroupColor,
                    CL.name as ClientName";
                string queryOrderBy = "ORDER BY P.dueDate desc ";
                string queryPaging = "OFFSET ((@page - 1) * @pageSize) ROWS FETCH NEXT @pageSize ROWS ONLY";

                var statusFilter = GetStatusFilter(status);

                #region Build Sql Parameters
                var sqlParameters = new List<SqlParameter>()
                {
                    new SqlParameter("@currentUserId", currentUserId) { SqlDbType = SqlDbType.Int },
                };

                if (_defaultStatuses.Contains(status))
                {
                    sqlParameters.Add(new SqlParameter("@status_str", status) { SqlDbType = SqlDbType.NVarChar });
                }

                if (startDate.HasValue)
                    sqlParameters.Add(new SqlParameter("@start_ts", startDate.Value.ToUniversalTime()) { SqlDbType = SqlDbType.DateTime });

                if (endDate.HasValue)
                    sqlParameters.Add(new SqlParameter("@end_ts", endDate.Value.ToUniversalTime()) { SqlDbType = SqlDbType.DateTime });
                #endregion

                // var stopWatch2 = Stopwatch.StartNew();
                // Gets the overall number of resolutions
                int filteredCount = await GetResolutionFilteredCount(subscriberJoin, statusFilter, dateFilter, workgroupListFilter, sqlParameters);
                // stopWatch2.Stop();

                sqlParameters.Add(new SqlParameter("@page", page) { SqlDbType = SqlDbType.Int });
                sqlParameters.Add(new SqlParameter("@pageSize", itemsPerPage) { SqlDbType = SqlDbType.Int });

                // var stopWatch3 = Stopwatch.StartNew();
                var filteredList = await GetResolutionPagedList(querySelect, subscriberJoin, statusFilter, dateFilter, workgroupListFilter, sqlParameters, queryOrderBy, queryPaging);
                // stopWatch3.Stop();

                // var filteredResult = GetResolutionListing(baseQuery, currentUserId, page, itemsPerPage, status, startDate, endDate);
                // stopWatchOverall.Stop();

                // Prints all metrics collected
                //Debug.WriteLine($"ResolutionRepository.GetResolutions.InitValidation: Elapsed time: {stopWatch1.ElapsedMilliseconds} milliseconds");
                //Debug.WriteLine($"ResolutionRepository.GetResolutions.Count: Elapsed time: {stopWatch2.ElapsedMilliseconds} milliseconds");
                //Debug.WriteLine($"ResolutionRepository.GetResolutions.FilteredResults: Elapsed time: {stopWatch3.ElapsedMilliseconds} milliseconds");
                //Debug.WriteLine($"ResolutionRepository.GetResolutions.Overall: Elapsed time: {stopWatchOverall.ElapsedMilliseconds} milliseconds");

                return new ResolutionListingDTO
                {
                    TotalOfItems = filteredCount,
                    Listing = filteredList
                };
            }
            catch (Exception)
            {
                return new ResolutionListingDTO();
            }
        }

        private async Task<List<ResolutionListing>> GetResolutionPagedList(string querySelect, string subscriberJoin, string statusFilter, string dateFilter, string workgroupListFilter, List<SqlParameter> parameters, string queryOrderBy, string queryPaging)
        {
            string baseQuery = $@"
                WITH FilteredContent AS (
                    SELECT C.*
                    FROM Content C
	                inner join [ContentPermission] CP ON C.contentId = CP.contentId and CP.userId = @currentUserId
                    WHERE [type] = 'Poll' AND (deleted = 0 OR deleted IS NULL)
                ),
                FilteredPollVote AS (
                    SELECT PV.*
                    FROM PollVote PV
	                inner join Poll P ON PV.pollId = P.pollId
	                inner join Content C ON C.contentId = P.contentId
	                inner join [ContentPermission] CP ON C.contentId = CP.contentId and CP.userId = @currentUserId
                    WHERE (C.deleted = 0 OR C.deleted IS NULL)
                )

                SELECT
                    {querySelect}
                FROM
                    Poll as P
                JOIN [FilteredContent] C ON P.contentId = C.contentId
                LEFT JOIN [FilteredContent] Parent ON C.parentContentId = P.contentId
                JOIN [ContentPermission] CP ON C.contentId = CP.contentId and CP.userId = @currentUserId
                {subscriberJoin}
                LEFT JOIN [ContentOwner] CO ON C.contentId = CO.contentId AND CO.userId = @currentUserId
                LEFT JOIN [FilteredPollVote] AS V ON P.pollId = V.pollId AND V.userId = @currentUserId
                JOIN [Workgroup] W ON C.workgroupId = W.workgroupId
                JOIN [Client] CL ON W.clientId = CL.clientId

                WHERE
                    (C.deleted = 0 OR C.deleted IS NULL)
                    AND (Parent.deleted = 0 OR Parent.deleted IS NULL)
                    AND W.archived = 0
                    AND CL.blocked = 0
                    AND CL.deleted = 0
                    AND (P.[hidden] = 0 OR P.[hidden] IS NULL OR CO.contentOwnerId IS NOT NULL)
                    {statusFilter}
                    {dateFilter}
                    {workgroupListFilter}
                {queryOrderBy}
                {queryPaging}
                ";

            //var sqlParamsList = new List<SqlParameter>(parameters);
            //var sqlParamsArray = sqlParamsList.ToArray();

            // Create a new list of parameters for this query execution
            var queryParameters = parameters.Select(p => new SqlParameter(p.ParameterName, p.Value) { SqlDbType = p.SqlDbType }).ToArray();

            var list = await _model.Database.SqlQueryRaw<ResolutionListing>(baseQuery, queryParameters).ToListAsync();

            return list;
        }

        private async Task<int> GetResolutionFilteredCount(string subscriberJoin, string statusFilter, string dateFilter, string workgroupListFilter, List<SqlParameter> parameters)
        {
            string baseQuery = $@"
                WITH FilteredContent AS (
                    SELECT C.*
                    FROM Content C
	                inner join [ContentPermission] CP ON C.contentId = CP.contentId and CP.userId = @currentUserId
                    WHERE [type] = 'Poll' AND (deleted = 0 OR deleted IS NULL)
                ),
                FilteredPollVote AS (
                    SELECT PV.*
                    FROM PollVote PV
	                inner join Poll P ON PV.pollId = P.pollId
	                inner join Content C ON C.contentId = P.contentId
	                inner join [ContentPermission] CP ON C.contentId = CP.contentId and CP.userId = @currentUserId
                    WHERE (C.deleted = 0 OR C.deleted IS NULL)
                )
                SELECT
                    COUNT(distinct C.contentId) AS Total
                FROM
                    Poll as P
                JOIN [FilteredContent] C ON P.contentId = C.contentId
                LEFT JOIN [FilteredContent] Parent ON C.parentContentId = P.contentId
                JOIN [ContentPermission] CP ON C.contentId = CP.contentId and CP.userId = @currentUserId
                {subscriberJoin}
                LEFT JOIN [ContentOwner] CO ON C.contentId = CO.contentId AND CO.userId = @currentUserId
                LEFT JOIN [FilteredPollVote] AS V ON P.pollId = V.pollId AND V.userId = @currentUserId
                JOIN [Workgroup] W ON C.workgroupId = W.workgroupId
                JOIN [Client] CL ON W.clientId = CL.clientId

                WHERE
                    (C.deleted = 0 OR C.deleted IS NULL)
                    AND (Parent.deleted = 0 OR Parent.deleted IS NULL)
                    AND W.archived = 0
                    AND CL.blocked = 0
                    AND CL.deleted = 0
                    AND (P.[hidden] = 0 OR P.[hidden] IS NULL OR CO.contentOwnerId IS NOT NULL)
                    {statusFilter} 
                    {dateFilter}
                    {workgroupListFilter}
                ";

            var total = await _model.Database.SqlQueryRaw<int>(baseQuery, parameters.ToArray()).FirstOrDefaultAsync();

            return total;
        }

        private string ValidateDateParam(DateTime? startDate, DateTime? endDate)
        {
            var conditions = new List<string>();

            if (startDate.HasValue)
            {
                conditions.Add(" P.dueDate >= @start_ts ");
            }

            if (endDate.HasValue)
            {
                conditions.Add(" P.dueDate <= @end_ts ");
            }

            if (conditions.Any())
            {
                return " AND " + string.Join(" AND ", conditions);
            }
            else
            {
                return "";
            }
        }

        public async Task<List<PossibleVoterDTO>> GetPossibleVoters(int contentId)
        {
            return await (from cp in _model.ContentPermission
                          join c in _model.Content on cp.contentId equals c.contentId
                          join u in _model.User on cp.userId equals u.userId
                          where cp.contentId == contentId && c.deleted != true
                          select new PossibleVoterDTO
                          {
                              UserId = u.userId,
                              Name = u.name,
                              Email = u.email,
                              Deleted = u.deleted,
                              Blocked = u.blocked,
                              ProfilePic = u.profilePic
                          }).ToListAsync();
        }

        public async Task<PollVote> RegisterVotesAsync(PollVote pollVote)
        {
            using (var dbContext = new AtlasModelCore())
            {
                var entity = await dbContext.PollVote.AddAsync(pollVote);
                var entries = await dbContext.SaveChangesAsync();

                return entries > 0 ? entity.Entity : null;
            }
        }

        public async System.Threading.Tasks.Task VoteRemove(PollVote vote)
        {
            using var dbContext = new AtlasModelCore();
            dbContext.PollVote.Remove(vote);
            await dbContext.SaveChangesAsync();
        }

        public async Task<Content> GetResolutionWithRelationsAsync(Guid contentUuId)
        {
            var query = _model.Content
                        .Where(c => c.contentUuid == contentUuId &&
                                    c.type == ContentTypes.Poll &&
                                    c.deleted != true)
                        .Include(c => c.User_Create)
                        .Include(c => c.Workgroup)
                            .ThenInclude(w => w.WorkgroupOwner)
                                .ThenInclude(wo => wo.User)
                        .Include(c => c.Workgroup)
                            .ThenInclude(w => w.Client)
                        .Include(c => c.Poll)
                            .ThenInclude(p => p.Options)
                                .ThenInclude(o => o.Votes.OrderBy(v => v.User_Vote.name))
                                    .ThenInclude(v => v.User_Vote)
                        .Include(c => c.Poll)
                            .ThenInclude(p => p.Votes.OrderBy(v => v.User_Vote.name))
                                .ThenInclude(v => v.User_Vote)
                        .Include(c => c.ContentOwner.Where(co => co.User.deleted != true).OrderBy(co => co.User.name))
                            .ThenInclude(co => co.User)
                        .Include(c => c.ContentPermission.Where(cp => cp.User.deleted != true).OrderBy(cp => cp.User.name))
                            .ThenInclude(cp => cp.User)
                        .Include(c => c.ContentSubscriber.Where(cs => cs.User.deleted != true).OrderBy(cs => cs.User.name))
                            .ThenInclude(cs => cs.User)
                        .Include(c => c.ContentComment)
                            .ThenInclude(cc => cc.User)
                        .AsSplitQuery();

            var content = await query.FirstOrDefaultAsync();

            if (content == null)
                return null;

            if (content.Poll == null || !content.Poll.Any())
            {
                throw new InvalidOperationException($"Content {contentUuId} is marked as Poll but has no Poll data");
            }

            var poll = content.Poll.First();

            poll.hasVoted = (poll.Votes?.Any(v => v.userId == _currentUser) == true) ||
                            (poll.Options?.Any(o => o.Votes?.Any(v => v.userId == _currentUser) == true) == true);

            SetTotalVotes(poll);

            if (content.parentContentUuid.HasValue)
            {
                var parentMeeting = await _model.Content
                    .AsNoTracking()
                    .Where(c => c.contentUuid == content.parentContentUuid.Value)
                    .Include(c => c.Meeting)
                    .FirstOrDefaultAsync();

                if (parentMeeting != null)
                {
                    content.Parent_Content = parentMeeting;
                    content.parent_status = parentMeeting.status;

                    var meeting = parentMeeting.Meeting?.FirstOrDefault();
                    if (meeting != null)
                    {
                        content.parentTitle = meeting.title;
                    }
                    else
                    {
                        content.parentTitle = parentMeeting.title;
                    }

                    var pollAgenda = await _model.MeetingAgendaItem
                                            .AsNoTracking()
                                            .Where(mai => mai.pollContentUuid == poll.contentUuid).FirstOrDefaultAsync() ?? throw new InvalidOperationException("RESOLUTION_NOT_FOUND");

                    poll.time = pollAgenda.time;
                    poll.agendaItemContentUuId = pollAgenda.contentUuid;
                }
            }

            if (poll.Options == null)
            {
                poll.Options = new List<PollOption>();
            }

            if (!poll.voteDisclosureOnMinute.GetValueOrDefault(true)
                && !ContentStatuses.FinalResolutionStatus.Any(status => status == content.status))
            {
                AnonymizeVotes(poll);
            }

            return content;
        }

        /// <summary>
        /// Anonymizes votes in the specified poll by removing identifying information
        /// (userId and User_Vote) from all votes except those belonging to the current user.
        /// The current user's votes are preserved, while other users' votes are anonymized.
        /// </summary>
        /// <remarks>
        /// This method is used to protect the privacy of other users' votes when displaying poll results.
        /// Only the current user's votes remain identifiable; all others are set to userId = 0 and User_Vote = null.
        /// </remarks>
        private void AnonymizeVotes(Poll poll)
        {
            if (poll?.Options != null)
            {
                foreach (var option in poll.Options)
                {
                    if (option.Votes == null) continue;

                    var filteredVotes = new List<PollVote>();

                    foreach (var vote in option.Votes)
                    {
                        if (vote != null && vote.userId == _currentUser)
                        {
                            filteredVotes.Add(vote);
                        }
                    }

                    option.Votes = filteredVotes;
                }
            }

            if (poll.Votes != null)
            {
                var filteredPollVotes = new List<PollVote>();

                foreach (var vote in poll.Votes)
                {
                    if (vote != null && vote.userId == _currentUser)
                    {
                        filteredPollVotes.Add(vote);
                    }
                }

                poll.Votes = filteredPollVotes;
            }
        }

        public async System.Threading.Tasks.Task Unvote(PollVote vote)
        {
            using var dbContext = new AtlasModelCore();
            dbContext.Entry(vote).State = EntityState.Modified;
            await dbContext.SaveChangesAsync();
        }

        /// <summary>
        /// Creates both agenda content and resolution content within a database transaction
        /// ensuring atomic operations - either both succeed or both fail
        /// </summary>
        /// <param name="agendaContent">The agenda content to be created (can be null for non-parented resolutions)</param>
        /// <param name="resolutionContent">The resolution content to be created</param>
        /// <returns>A tuple containing the created agenda content and resolution content</returns>
        public async Task<(Content agendaContent, Content resolutionContent)> CreateResolutionWithAgendaAsync(
            Content agendaContent,
            Content resolutionContent)
        {
            using var transaction = await _model.Database.BeginTransactionAsync();

            try
            {
                Content savedAgendaContent = null;

                if (agendaContent != null)
                {
                    agendaContent.Parent_Content = null;
                    var agendaEntity = await _model.Content.AddAsync(agendaContent);
                    await _model.SaveChangesAsync();

                    savedAgendaContent = agendaEntity.Entity;

                    if (savedAgendaContent?.contentId == null)
                        throw new InvalidOperationException("Failed to create agenda content - no ID assigned");
                }

                resolutionContent.Parent_Content = null;
                var resolutionEntity = await _model.Content.AddAsync(resolutionContent);
                await _model.SaveChangesAsync();

                var savedResolutionContent = resolutionEntity.Entity;

                if (savedResolutionContent?.contentId == null)
                    throw new InvalidOperationException("Failed to create resolution content - no ID assigned");

                await transaction.CommitAsync();

                return (savedAgendaContent, savedResolutionContent);
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<Content> UpdateResolutionAsync(Content updatedContent)
        {
            using var transaction = await _model.Database.BeginTransactionAsync();

            try
            {
                _model.Entry(updatedContent).State = EntityState.Modified;

                if (updatedContent.Poll?.Any() == true)
                {
                    var poll = updatedContent.Poll.First();
                    _model.Entry(poll).State = EntityState.Modified;

                    if (poll.agendaItemContentUuId.HasValue && poll.time.HasValue)
                    {
                        var agendaItem = await _model.MeetingAgendaItem
                            .FirstOrDefaultAsync(mai => mai.contentUuid == poll.agendaItemContentUuId.Value);

                        if (agendaItem != null)
                        {
                            agendaItem.time = poll.time.Value;
                            _model.Entry(agendaItem).State = EntityState.Modified;
                        }
                        else
                        {
                            throw new InvalidOperationException($"MeetingAgendaItem with contentUuid {poll.agendaItemContentUuId.Value} not found");
                        }
                    }
                }

                await _model.SaveChangesAsync();
                await transaction.CommitAsync();

                return updatedContent;
            }
            catch (Exception)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        private static void SetTotalVotes(Poll poll)
        {
            if (poll.pollType == ContentStatuses.ResolutionTypes.CUSTOM)
            {
                poll.totalVotes = poll.Options?.Sum(o => o.Votes?.Where(v => v.deleted != true).Count() ?? 0) ?? 0;
                return;
            }

            poll.totalVotes = poll.Votes?.Count(v => v.deleted != true) ?? 0;
            return;
        }
    }
}
