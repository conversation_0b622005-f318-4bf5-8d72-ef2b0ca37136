using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Atlas.Data.Entities;
using Microsoft.Data.SqlClient;
using System.Security;
using Atlas.CrossCutting.AppEnums;
using Atlas.CrossCutting.DTO;
using Atlas.CrossCutting.DTO.AuditLog;

namespace Atlas.Data.Repository
{
    public class ContentActivityRepository
    {
        //public User _currentUser { get; set; }
        public int _currentUser { get; set; }

        private AtlasModelCore _md;
        private readonly User _user;

        //public static ConnectionMultiplexer connection = ConnectionMultiplexer.Connect("atlasv2prod.redis.cache.windows.net,abortConnect=false,ssl=true,password=...");

        /// <summary>
        /// Create an instance of the content repository with the current logged user, for permissions purposes.
        /// </summary>
        /// <param name="userId">Identity of the current logged user.</param>
        public ContentActivityRepository(int userId)
        {
            //_md.Configuration.ProxyCreationEnabled = false;
            //_md.Configuration.LazyLoadingEnabled = false;

            //var select = (from u in _md.User
            //              where u.userId == userId
            //              select u);
            //_currentUser = select.FirstOrDefault();

            //if (_currentUser == null)
            //{
            //    throw new ArgumentException("User not found/Usuário não encontrado");
            //}

            //if (_currentUser.blocked || _currentUser.deleted)
            //{
            //    throw new System.Security.SecurityException("Usuário não autorizado.");
            //}
            _currentUser = userId;
            this._md = new AtlasModelCore();
        }

        public ContentActivityRepository(int userId, User user) : this(userId)
        {
            this._user = user;
        }

        public List<ContentActivity> GetByContentId(int contentId)
        {
            return _md.ContentActivity.Include(o => o.User).Where(c => c.contentId == contentId).Include(c => c.Content).ToList();
        }

        public ContentActivity GetByContentActivityId(int activityId)
        {
            ContentActivity bbVersionActivity = _md.ContentActivity.FirstOrDefault(a => a.contentActivityId == activityId);
            return bbVersionActivity;
        }

        public List<ContentActivity> GetByContentIdWithChild(int contentId)
        {
            //var query = _md.ContentActivity
            //    .Where(c => 
            //            (c.contentId == contentId || c.Content.parentContentId == contentId) 
            //            && (c.Content.ContentPermission.Select(a => a.userId).Contains(_currentUser) || c.type == "PERMISSIONS_UPDATE")
            //    ).Include(c => c.Content).Include(c => c.User);


            var newQuery = (from ca in _md.ContentActivity
                            join c in _md.Content on ca.contentId equals c.contentId
                            join u in _md.User on ca.activityUser equals u.userId
                            join cp in _md.ContentPermission on new { ca.contentId, userId = _currentUser } equals new { cp.contentId, cp.userId }
                            where ca.contentId == contentId || c.parentContentId == contentId
                            select ca
                            ).Include(c => c.Content).Include(c => c.User);



            //order by after toList to save database resources
            return newQuery.ToList().OrderBy(o => o.contentActivityId).ToList();
        }

        public List<ContentActivity> GetByContentIdWithChildPermissions(int contentId)
        {
            var query = _md.ContentActivity
                .Where(c =>
                        (c.contentId == contentId || c.Content.parentContentId == contentId)
                        && c.Content.ContentPermission.Select(a => a.userId).Contains(_currentUser)
                        && c.Content.type != "Announcement"
                    )
                .Include(c => c.Content).Include(c => c.User).Include(c => c.Content.ContentPermission);

            return query.OrderBy(o => o.contentActivityId).ToList();
        }

        public async Task<List<ContentActivity>> Get(ContentRequestFilter filter, bool alwaysFilterMinDate = true)
        {
            var query = _md.Content
                    .Include(o => o.Poll)
                   .Where(o => o.ContentPermission
                            .Select(a => a.userId).Contains(_currentUser)
                            && !o.Workgroup.archived
                            && o.Workgroup.Client.deleted != true
                            && o.Workgroup.Client.blocked != true
                            && (o.type != ContentTypes.Poll || (o.type == ContentTypes.Poll && o.Poll.FirstOrDefault().hidden != true))
                            );

            //var query = from content in _md.Content
            //            join parent in _md.Content on content.parentContentId equals parent.contentId into defaultParent
            //            from parent in defaultParent.DefaultIfEmpty()
            //            where !(content.type == ContentTypes.Poll && parent.type == ContentTypes.Meeting && parent.status == "OPEN")
            //            select content;

            //query = query.Where(o => o.ContentPermission
            //                .Select(a => a.userId).Contains(_currentUser)
            //                && !o.Workgroup.archived
            //                && o.Workgroup.Client.deleted != true
            //                && o.Workgroup.Client.blocked != true);


            //workgroups
            if (filter.workgroups != null)
            {
                if (filter.workgroups.Length > 0)
                {
                    query = query.Where(c => filter.workgroups.Contains(c.workgroupId));
                }
            }

            //tipo
            if (!string.IsNullOrWhiteSpace(filter.type))
            {
                if (filter.type == Atlas.CrossCutting.AppEnums.ContentTypes.Meeting)
                {
                    query = query.Where(c => (c.type == filter.type && c.contentId == filter.contentId) || c.parentContentId == filter.contentId);
                }
                else
                {
                    query = query.Where(c => c.type == filter.type && (c.deleted ?? false) == false);
                }
            }
            else
            {
                query = query.Where(o => (o.deleted ?? false) == false);

            }

            if (filter.clientId.HasValue)
            {
                query = query.Where(c => c.Workgroup.clientId == filter.clientId);
            }

            IQueryable<ContentActivity> activityquery = query.SelectMany(o => o.ContentActivity)

                   .OrderByDescending(o => o.date)
                   .Include(o => o.User)
                   .Include(o => o.Content.Workgroup)
                   .Include(o => o.Content.Workgroup.Client)
                   .Include(o => o.Content);

            //usuarios da ação
            if (filter.actionUsers != null)
            {
                if (filter.actionUsers.Length > 0)
                {
                    activityquery = activityquery.Where(c => filter.actionUsers.Contains(c.activityUser.Value));
                }
            }

            //tipo de activity
            if (!string.IsNullOrWhiteSpace(filter.activityType))
            {
                activityquery = activityquery.Where(c => c.type == filter.activityType);
            }

            if (filter.createDateMin != null)
            {
                activityquery = activityquery.Where(o => o.date >= filter.createDateMin);
            }
            else if (alwaysFilterMinDate && filter.contentId == 0)
            {
                //se nao tem data minima definida, coloca até 30 dias
                var minDate = DateTime.UtcNow.AddDays(-30);
                activityquery = activityquery.Where(o => o.date > minDate);
                //activityquery = activityquery.Where(a => (a.hidden ?? false) == false);
            }

            if (filter.createDateMax != null)
            {
                activityquery = activityquery.Where(o => o.date <= filter.createDateMax);
            }

            //paginação
            if (filter.pageNumber.HasValue)
            {
                activityquery = activityquery.Skip(filter.pageNumber.Value * filter.pageSize.Value);
            }

            activityquery = activityquery.Take(filter.pageSize ?? 10);

            List<ContentActivity> activityList = await activityquery.ToListAsync();

            return activityList;
        }


        public async Task<ContentRequestFilterOptions> GetOptions(ContentRequestFilter filter)
        {

            ContentRequestFilterOptions options_result = new ContentRequestFilterOptions();

            var query = _md.Content.Where(o => o.ContentPermission.Select(a => a.userId).Contains(_currentUser) && (o.deleted ?? false) == false && !o.Workgroup.archived);


            //string types_query = @"select [type], count(distinct C.contentId) from ContentPermission CP
            //                        inner join Content C ON C.contentId = CP.contentId
            //                        where userId = @userId
            //                        group by [type]";



            //types
            string types_query_text = @"select [type] from ContentPermission CP
                                    inner join Content C ON C.contentId = CP.contentId
                                    where userId = @userId
                                    group by [type]";

            //var types_query = _md.Database.FromSqlRaw<string>(types_query_text, new SqlParameter("@userId", _currentUser));
            var types_query = _md.Set<string>().FromSqlRaw(types_query_text, new SqlParameter("@userId", _currentUser));

            options_result.contentTypes = await types_query.ToListAsync();


            //activityUsers
            //string users_query_text = @"select * 
            //                            from [User] U
            //                            INNER JOIN 
            //                            (
            //                            select CA.activityUser, count(distinct CA.contentId) contentCount from ContentPermission CP
            //                            inner join Content C ON C.contentId = CP.contentId
            //                            inner join ContentActivity CA ON CA.contentId = CP.contentId
            //                            where userId = 1
            //                            GROUP BY CA.activityUser
            //                            ) ACT
            //                            ON ACT.activityUser = U.userId";

            string users_query_text = @"select * 
                                        from [User] U
                                        INNER JOIN 
                                        (
                                        select CA.activityUser from ContentPermission CP
                                        inner join Content C ON C.contentId = CP.contentId
                                        inner join ContentActivity CA ON CA.contentId = CP.contentId
                                        where userId = @userId
                                        GROUP BY CA.activityUser
                                        ) ACT
                                        ON ACT.activityUser = U.userId
                                        order by U.name ";

            var users_query = _md.User.FromSqlRaw(users_query_text, new SqlParameter("@userId", _currentUser));

            options_result.activityUsers = await users_query.ToListAsync();


            //string wkg_query_text = @"select * 
            //                        from Workgroup W
            //                        INNER JOIN 
            //                        (
            //                        select workgroupId, count(*) contentCount from ContentPermission CP
            //                        inner join Content C ON C.contentId = CP.contentId
            //                        where userId = 1
            //                        GROUP BY workgroupId
            //                        ) C
            //                        ON C.workgroupId = W.workgroupId";

            //string wkg_query_text = @"select W.*, W.[private] as [_private], 'aaaa' as clientName 
            //                        from Workgroup W
            //                        INNER JOIN 
            //                        (
            //                        select workgroupId from ContentPermission CP
            //                        inner join Content C ON C.contentId = CP.contentId
            //                        where userId = @userId
            //                        GROUP BY workgroupId
            //                        ) C
            //                        ON C.workgroupId = W.workgroupId
            //                        order by W.name";


            string wkg_query_text = @"SELECT DISTINCT workgroupId AS Value from ContentPermission CP
                                    inner join Content C ON C.contentId = CP.contentId
                                    where userId = @userId
                                    GROUP BY workgroupId";

            //var wkg_query = _md.Workgroup.SqlQuery(wkg_query_text, new[] { new SqlParameter("@userId", _currentUser) });
            var wkg_query = await _md.Database.SqlQueryRaw<int>(wkg_query_text, new SqlParameter("@userId", _currentUser)).ToArrayAsync();

            options_result.workgroups = await _md.Workgroup.Include(o => o.Client).Where(o => wkg_query.Contains(o.workgroupId)).ToListAsync();

            return options_result;
        }

        public List<ContentActivity> GetFromUser(int user_id, int offset)
        {
            List<ContentActivity> result = new List<ContentActivity>();

            if (offset > 0)
            {
                // Searchs for ContentActivities limited by the offset
                var date = DateTime.Now.AddDays(offset * -1);
                result = _md.ContentActivity
                    .Where(ca => ca.activityUser == user_id && ca.date >= date)
                    .ToList();
            }
            else
            {
                // Searchs for the last ContentActivity
                var q = _md.ContentActivity.Where(ca => ca.activityUser == user_id).ToList().LastOrDefault();
                result.Add(q);
            }

            return result;
        }

        public async Task<List<ContentActivity>> FetchAll(ContentRequestFilter filter, List<UserRole> userRoles)
        {
            var clientId = 0;
            // User _u = _md.User.Where(usr => usr.userId == _currentUser).FirstOrDefault();

            //is clientId but not SuperAdmin
            if (userRoles.Select(o => o.Role.name).Contains("CLIENT_ADMIN") && !userRoles.Select(o => o.Role.name).Contains("SUPER_ADMIN") && !userRoles.Select(o => o.Role.name).Contains("SUPPORT_ADMIN"))
            {
                //if the filter has not clientId specified or is the same as the user
                if (filter.clientId == null || userRoles.Select(o => o.clientId).Contains(filter.clientId))
                {
                    clientId = filter.clientId ?? _user.clientId;
                }
                else
                {
                    throw new SecurityException("NOT_ADMIN_CLIENT");
                }
            }
            else if (!userRoles.Select(o => o.Role.name).Contains("CLIENT_ADMIN") && !userRoles.Select(o => o.Role.name).Contains("SUPER_ADMIN") && !userRoles.Select(o => o.Role.name).Contains("SUPPORT_ADMIN"))
            {
                throw new SecurityException("NOT_ADMIN");
            }
            //is not CLIENT_ADMIN but is SUPER_ADMIN
            else if (userRoles.Select(o => o.Role.name).Contains("SUPER_ADMIN") || userRoles.Select(o => o.Role.name).Contains("SUPPORT_ADMIN"))
            {
                if (filter.clientId.HasValue)
                {

                    clientId = filter.clientId.Value;
                }
                else
                {
                    clientId = _user.clientId;
                }
            }
            else
            {
                throw new SecurityException("ADMIN_UNKNOWN");
            }

            AtlasModelCore model = new AtlasModelCore();
            model.Database.SetCommandTimeout(0);
            model.ChangeTracker.AutoDetectChangesEnabled = false;

            IQueryable<ContentActivity> query = model.ContentActivity
                .AsNoTracking()
                .OrderByDescending(o => o.date)
                .Include(o => o.User)
                .Include(o => o.Content.Workgroup)
                .Include(o => o.Content.Workgroup.Client)
                .Include(o => o.Content);

            query = query.Where(c => c.Content.Workgroup.clientId == clientId);

            if (filter.contentId > 0)
                query = query.Where(c => c.contentId == filter.contentId);


            //filtra workgroup
            if (filter.workgroups != null)
            {
                if (filter.workgroups.Length > 0)
                {
                    query = query.Where(o => filter.workgroups.Contains((int)o.Content.workgroupId));
                }
            }

            //filtra tipos
            if (filter.types != null)
            {
                if (filter.types.Length > 0)
                {
                    query = query.Where(o => filter.types.Contains(o.type));
                }
            }

            if (filter.actionUsers != null)
            {
                if (filter.actionUsers.Length > 0)
                {
                    query = query.Where(o => filter.actionUsers.Contains((int)o.activityUser));
                }
            }

            if (filter.createDateMin != null)
            {
                query = query.Where(o => o.date >= filter.createDateMin);
            }
            else
            {
                //se nao tem data minima definida, coloca até 30 dias
                var minDate = DateTime.UtcNow.AddDays(-30);
                query = query.Where(o => o.date > minDate);
            }

            if (filter.createDateMax != null)
            {
                query = query.Where(o => o.date <= filter.createDateMax);
            }


            //paginação
            if (filter.pageNumber.HasValue && filter.pageNumber != 0)
            {
                if (filter.pageSize == null)
                {
                    filter.pageSize = 50;
                }
                //hotfix S-41 - Leo - deixar a paginaçao só na Service.. senao pula linha que nao pode
                //query = query.Skip(filter.pageNumber.Value * filter.pageSize.Value);
            }

            //workarround to export data and avoid rowcount. PageSize = ZERO
            return await query.ToListAsync();
        }

        public async Task<List<AuditLogItemView>> FetchAllAsync(ContentRequestFilter filter, List<UserRole> userRoles)
        {
            var clientId = 0;
            // User _u = _md.User.Where(usr => usr.userId == _currentUser).FirstOrDefault();

            //is clientId but not SuperAdmin
            #region Validations
            if (userRoles.Select(o => o.Role.name).Contains("CLIENT_ADMIN") && !userRoles.Select(o => o.Role.name).Contains("SUPER_ADMIN") && !userRoles.Select(o => o.Role.name).Contains("SUPPORT_ADMIN"))
            {
                //if the filter has not clientId specified or is the same as the user
                if (filter.clientId == null || userRoles.Select(o => o.clientId).Contains(filter.clientId))
                {
                    clientId = filter.clientId ?? _user.clientId;
                }
                else
                {
                    throw new SecurityException("NOT_ADMIN_CLIENT");
                }
            }
            else if (!userRoles.Select(o => o.Role.name).Contains("CLIENT_ADMIN") && !userRoles.Select(o => o.Role.name).Contains("SUPER_ADMIN") && !userRoles.Select(o => o.Role.name).Contains("SUPPORT_ADMIN"))
            {
                throw new SecurityException("NOT_ADMIN");
            }
            //is not CLIENT_ADMIN but is SUPER_ADMIN
            else if (userRoles.Select(o => o.Role.name).Contains("SUPER_ADMIN") || userRoles.Select(o => o.Role.name).Contains("SUPPORT_ADMIN"))
            {
                if (filter.clientId.HasValue)
                {

                    clientId = filter.clientId.Value;
                }
                else
                {
                    clientId = _user.clientId;
                }
            }
            else
            {
                throw new SecurityException("ADMIN_UNKNOWN");
            }
            #endregion

            AtlasModelCore model = new AtlasModelCore();
            model.Database.SetCommandTimeout(0);
            model.ChangeTracker.AutoDetectChangesEnabled = false;

            var query = from ca in model.ContentActivity
                        join content in model.Content on ca.contentId equals content.contentId
                        join workgroup in model.Workgroup on content.workgroupId equals workgroup.workgroupId
                        join user in model.User on ca.activityUser equals user.userId into caUsers
                        from user in caUsers.DefaultIfEmpty()
                        join client in model.Client on workgroup.clientId equals client.clientId
                        where client.clientId == clientId
                        orderby ca.date descending
                        select new AuditLogItemView
                        {
                            ContentActivityId = ca.contentActivityId,
                            Type = ca.type,
                            SubItemType = ca.subItemType,
                            SubItemId = ca.subItemId,
                            ContentData = ca.contentData,
                            ActivityUser = ca.activityUser,
                            UserName = user != null ? user.name : null,
                            ClientId = client.clientId,
                            Date = ca.date,
                            BulletColor = workgroup.bulletColor,
                            WorkgroupName = workgroup.name,
                            WorkgroupId = workgroup.workgroupId,
                            ContentId = ca.contentId,
                            ContentType = content.type,
                            IPAddress = ca.IPAddress,
                            SessionKey = ca.sessionKey,
                            Device = ca.device
                        };

            // query = query.Where(c => c.Content.Workgroup.clientId == clientId);

            if (filter.contentId > 0)
                query = query.Where(c => c.ContentId == filter.contentId);


            //filtra workgroup
            if (filter.workgroups != null)
            {
                if (filter.workgroups.Length > 0)
                {
                    query = query.Where(o => filter.workgroups.Contains((int)o.WorkgroupId));
                }
            }

            //filtra tipos
            if (filter.types != null)
            {
                if (filter.types.Length > 0)
                {
                    query = query.Where(o => filter.types.Contains(o.Type));
                }
            }

            if (filter.actionUsers != null)
            {
                if (filter.actionUsers.Length > 0)
                {
                    query = query.Where(o => filter.actionUsers.Contains((int)o.ActivityUser));
                }
            }

            if (filter.createDateMin != null)
            {
                query = query.Where(o => o.Date >= filter.createDateMin);
            }
            else
            {
                //se nao tem data minima definida, coloca até 30 dias
                var minDate = DateTime.UtcNow.AddDays(-30);
                query = query.Where(o => o.Date > minDate);
            }

            if (filter.createDateMax != null)
            {
                query = query.Where(o => o.Date <= filter.createDateMax);
            }


            //paginação
            if (filter.pageNumber.HasValue && filter.pageNumber != 0)
            {
                if (filter.pageSize == null)
                {
                    filter.pageSize = 50;
                }
                //hotfix S-41 - Leo - deixar a paginaçao só na Service.. senao pula linha que nao pode
                //query = query.Skip(filter.pageNumber.Value * filter.pageSize.Value);
            }

            //workarround to export data and avoid rowcount. PageSize = ZERO
            return await query.ToListAsync();
        }

        public async Task<IOrderedQueryable<AuditLogItemView>> GetQueryAsync(AuditLogFiltersDTO filter, int clientId)
        {
            var model = new AtlasModelCore();
            model.Database.SetCommandTimeout(0);
            model.ChangeTracker.AutoDetectChangesEnabled = false;

            var query = model.ContentActivity
                .AsNoTracking()
                .Join(model.Content, ca => ca.contentId, content => content.contentId, (ca, content) => new { ca, content })
                .Join(model.Workgroup, combined => combined.content.workgroupId, workgroup => workgroup.workgroupId, (combined, workgroup) => new { combined.ca, combined.content, workgroup })
                .Join(model.Client, combined => combined.workgroup.clientId, client => client.clientId, (combined, client) => new { combined.ca, combined.content, combined.workgroup, client })
                .GroupJoin(model.User, combined => combined.ca.activityUser, user => user.userId, (combined, users) => new { combined.ca, combined.content, combined.workgroup, combined.client, users })
                .SelectMany(combined => combined.users.DefaultIfEmpty(), (combined, user) => new AuditLogItemView
                {
                    ContentActivityId = combined.ca.contentActivityId,
                    Type = combined.ca.type,
                    SubItemType = combined.ca.subItemType,
                    SubItemId = combined.ca.subItemId,
                    ContentData = combined.ca.contentData,
                    ActivityUser = combined.ca.activityUser,
                    UserName = user.name,
                    ClientId = combined.client.clientId,
                    Date = combined.ca.date,
                    BulletColor = combined.workgroup.bulletColor,
                    WorkgroupName = combined.workgroup.name,
                    WorkgroupId = combined.workgroup.workgroupId,
                    ContentId = combined.ca.contentId,
                    ContentType = combined.content.type,
                    IPAddress = combined.ca.IPAddress,
                    SessionKey = combined.ca.sessionKey,
                    Device = combined.ca.device
                })
                .Where(item => item.ClientId == clientId)
                .OrderByDescending(item => item.Date);

            // Apply filters  
            if (filter.contentId > 0)
                query = (IOrderedQueryable<AuditLogItemView>)query.Where(item => item.ContentId == filter.contentId);

            if (filter.workgroups?.Length > 0)
                query = (IOrderedQueryable<AuditLogItemView>)query.Where(item => filter.workgroups.Contains(item.WorkgroupId));

            if (filter.types?.Length > 0)
                query = (IOrderedQueryable<AuditLogItemView>)query.Where(item => filter.types.Contains(item.Type));

            if (filter.actionUsers?.Length > 0)
                query = (IOrderedQueryable<AuditLogItemView>)query.Where(item => filter.actionUsers.Contains(item.ActivityUser ?? 0));

            if (filter.createDateMin.HasValue)
                query = (IOrderedQueryable<AuditLogItemView>)query.Where(item => item.Date >= filter.createDateMin.Value);
            else
                query = (IOrderedQueryable<AuditLogItemView>)query.Where(item => item.Date > DateTime.UtcNow.AddDays(-30));

            if (filter.createDateMax.HasValue)
                query = (IOrderedQueryable<AuditLogItemView>)query.Where(item => item.Date <= filter.createDateMax.Value);

            return query;

        }

        public async Task<List<AuditLogItemView>> GetPaginatedAsync(AuditLogFiltersDTO filter, int clientId)
        {
            var pageSize = filter.pageSize ?? 50;
            var pageNumber = filter.pageNumber ?? 1;

            var query = await GetQueryAsync(filter, clientId);

            // prepare for pagination
            query = (IOrderedQueryable<AuditLogItemView>)query.Take(pageNumber * pageSize);

            var result = await query.ToListAsync();
            return result;
        }

        public async Task<int> GetCountAsync(AuditLogFiltersDTO filter, int clientId)
        {
            var results = await GetQueryAsync(filter, clientId);
            return await results.CountAsync();
        }
    }
}
