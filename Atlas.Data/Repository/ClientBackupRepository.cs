using Atlas.Data.Entities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Atlas.Data.Repository
{
    public class ClientBackupRepository
    {
        public int _currentUser { get; private set; }
        public ClientBackupRepository(int currentUserId)
        {
            _currentUser = currentUserId;
        }

        public async Task<ClientBackupRequest> GetRequest(int clientId, int? bakReqId = null)
        {
            AtlasModelCore _md = new AtlasModelCore();
            ClientBackupRequest req = null;

            //is known, search by bakReqId
            if (bakReqId.HasValue)
            {
                req = _md.ClientBackupRequest.Where(o => (o.type == "CLIENT" || string.IsNullOrEmpty(o.type)) && o.clientId == clientId && o.bakReqId == bakReqId).FirstOrDefault();
            }
            else
            {
                req = _md.ClientBackupRequest.Where(o => (o.type == "CLIENT" || string.IsNullOrEmpty(o.type)) && o.clientId == clientId).OrderByDescending(o => o.bakReqId).FirstOrDefault();
            }

            //if found, load the results
            if (req != null)
            {
                req.ClientBackupResults = _md.ClientBackupResult.Where(o => o.bakReqId == req.bakReqId).ToList();
                req.User_Request = _md.User.Where(o => o.userId == req.requestUserId).FirstOrDefault();
            }

            return req;
        }

        public async Task<bool> HasAnyOpenRequest(int clientId, string type, int user, string filters)
        {
            using (AtlasModelCore _md = new AtlasModelCore())
            {
                type = type.ToUpper();

                string[] closedStatuses = new string[] { "AVAILABLE", "FINISHED", "CANCELLED", "ERROR" };

                var result = await _md.ClientBackupRequest
                    .Where(o => o.type == type
                        && o.clientId == clientId
                        && !closedStatuses.Contains(o.status)
                        && o.requestUserId == user
                        && o.requestFilters == filters)
                    .AnyAsync();

                return result;
            }
        }

        public async Task<ClientBackupRequest> AddRequest(int clientId, string filtersJson, string type)
        {
            AtlasModelCore _md = new AtlasModelCore();

            ClientBackupRequest backupRequest = new()
            {
                clientId = clientId,
                requestFilters = filtersJson,
                requestDate = DateTime.UtcNow,
                requestUserId = _currentUser,
                bakKey = Guid.NewGuid().ToString().Split('-')[0],
                processed = false,
                status = "CREATED",
                type = type
            };

            var added = _md.ClientBackupRequest.Add(backupRequest);

            await _md.SaveChangesAsync();
            return backupRequest;
        }

        public async Task<ClientBackupResult> ResultGet(int bakResId)
        {
            AtlasModelCore _md = new AtlasModelCore();

            return _md.ClientBackupResult
                .Include(o => o.ClientBackupRequest)
                .Include(o => o.ResultAttachment)
                .Where(o => o.bakResId == bakResId)
                .FirstOrDefault();
        }

        public async Task<bool> UpdateRequest(ClientBackupRequest req_mod)
        {
            AtlasModelCore _md = new AtlasModelCore();
            var req_ori = _md.ClientBackupRequest.Where(o => o.clientId == req_mod.clientId && o.bakReqId == req_mod.bakReqId).FirstOrDefault();

            if (req_mod.status != req_ori.status && !string.IsNullOrEmpty(req_mod.status))
            {
                req_ori.status = req_mod.status;
            }

            _md.ClientBackupRequest.Update(req_ori);
            return await _md.SaveChangesAsync() > 0;

        }

        public async Task<bool> UpdateResult(ClientBackupResult res_mod)
        {
            AtlasModelCore _md = new AtlasModelCore();
            _md.ClientBackupResult.Update(res_mod);
            return await _md.SaveChangesAsync() > 0;
        }



        private class ClientBackupFiltersViewModel
        {
            public DateTime? startDate { get; set; }
            public DateTime? endDate { get; set; }
            public int? workgroupId { get; set; }
        }
    }
}
