using Atlas.CrossCutting.DTO.ExternalDocumentRequest;
using Atlas.Data.Entities;
using Atlas.Data.Enums;
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading.Tasks;
using Atlas.CrossCutting.AppEnums;

namespace Atlas.Data.Repository
{
    public class ExternalUserRepository
    {
        readonly private int _currentUser;
        readonly private AtlasModelCore _model;

        public ExternalUserRepository()
        {
            _model = new AtlasModelCore();
        }

        public ExternalUserRepository(int currentUser)
        {
            _currentUser = currentUser;
            _model = new AtlasModelCore();
        }

        public async Task<ExternalUser> GetExternalUser(int externalUserId)
        {
            return await _model.ExternalUser
                .Include(e => e.ExternalDocumentRequest)
                    .ThenInclude(edr => edr.Content)
                .AsSplitQuery()
                .FirstOrDefaultAsync(e => e.ExternalUserId == externalUserId);
        }

        private async Task<List<ExternalUserDTO>> GetRecurringExternalUsers(int workgroupId)
        {
            var workgroupExternalUsers = await _model.Workgroup
                .AsNoTracking()
                .Join(_model.RecurringGuest.AsNoTracking(),
                    w => w.workgroupId,
                    rg => rg.workgroupId,
                    (w, rg) => new { Workgroup = w, RecurringGuest = rg })
                .Where(x => x.Workgroup.workgroupId == workgroupId &&
                           !x.Workgroup.archived &&
                           x.RecurringGuest.email != null &&
                           x.RecurringGuest.mobile != null)
                .Select(x => new ExternalUserDTO
                {
                    ExternalUserId = null,
                    ExternalDocumentRequestId = null,
                    ExternalName = x.RecurringGuest.name,
                    ExternalMail = x.RecurringGuest.email,
                    ExternalMobile = x.RecurringGuest.mobile,
                    WorkgroupId = x.Workgroup.workgroupId,
                    SentDate = null,
                    IsRecurring = true,
                    RecurringGuestId = x.RecurringGuest.recurringGuestId
                })
                .ToListAsync();

            return workgroupExternalUsers;
        }

        public async Task<RecurringGuest> GetExternalUserRecurringGuest(int contentId, string externalMail)
        {
            return await (from rg in _model.RecurringGuest
                          join w in _model.Workgroup on rg.workgroupId equals w.workgroupId
                          join c in _model.Content on w.workgroupId equals c.workgroupId
                          where c.contentId == contentId && c.deleted != true && rg.email == externalMail
                          select rg).FirstOrDefaultAsync();
        }

        public async Task<RecurringGuest> GetExternalUserRecurringGuest(Guid contentUuId, string externalMail)
        {
            return await _model.RecurringGuest
                .Join(_model.Workgroup, rg => rg.workgroupId, w => w.workgroupId, (rg, w) => new { RecurringGuest = rg, Workgroup = w })
                .Join(_model.Content, x => x.Workgroup.workgroupId, c => c.workgroupId, (x, c) => new { x.RecurringGuest, Content = c })
                .Where(x => x.Content.contentUuid == contentUuId && x.Content.deleted != true && x.RecurringGuest.email == externalMail)
                .Select(x => x.RecurringGuest)
                .FirstOrDefaultAsync();
        }

        public async Task<List<ExternalUserDTO>> GetExternalUsersByContentId(int contentId)
        {
            List<ExternalUserDTO> externalUsers = new List<ExternalUserDTO>();

            externalUsers = await (from eu in _model.ExternalUser
                                   join c in _model.Content on eu.ContentId equals c.contentId
                                   where eu.ContentId == contentId && c.deleted != true
                                   select new ExternalUserDTO
                                   {
                                       ExternalUserId = eu.ExternalUserId,
                                       ExternalDocumentRequestId = eu.ExternalDocumentRequestId,
                                       ExternalName = eu.ExternalName,
                                       ExternalMail = eu.ExternalMail,
                                       ExternalMobile = eu.ExternalMobile,
                                       WorkgroupId = c.workgroupId,
                                       SentDate = eu.SentDate
                                   }).ToListAsync();

            int workgroupId = externalUsers.FirstOrDefault(e => e.WorkgroupId.HasValue)?.WorkgroupId
                  ?? (await _model.Content.FirstOrDefaultAsync(c => c.contentId == contentId)).workgroupId;

            var recurringExternalUsers = await GetRecurringExternalUsers(workgroupId);

            if (recurringExternalUsers.Any())
            {
                foreach (var recurringUser in recurringExternalUsers)
                {
                    var existingUser = externalUsers.FirstOrDefault(e => e.ExternalMail == recurringUser.ExternalMail);

                    if (existingUser != null)
                    {
                        existingUser.RecurringGuestId = recurringUser.RecurringGuestId;
                        existingUser.IsRecurring = true;
                    }
                    else
                    {
                        externalUsers.Add(recurringUser);
                    }
                }
            }

            return externalUsers;
        }

        public async Task<List<ExternalUser>> GetExternalUsersByDocumentRequest(int externalDocumentRequest)
        {
            return await (from eu in _model.ExternalUser
                          join edr in _model.ExternalDocumentRequest on eu.ExternalDocumentRequestId equals edr.ExternalDocumentRequestId
                          where edr.ExternalDocumentRequestId == externalDocumentRequest
                          select eu).ToListAsync();
        }

        public async Task<ExternalUser> SaveExternalUser(ExternalUser externalUser)
        {
            _model.ExternalUser.Add(externalUser);

            await _model.SaveChangesAsync();

            return externalUser;
        }

        public async Task<string[]> GetWorkgroupUserEmails(int workgroupId)
        {
            var emails = await (from wu in _model.WorkgroupUser
                                where wu.workgroupId == workgroupId
                                select wu.User.email.ToLower())
                               .Concat(from rg in _model.RecurringGuest
                                       join w in _model.Workgroup on rg.workgroupId equals w.workgroupId
                                       where w.workgroupId == workgroupId
                                       select rg.email.ToLower())
                               .ToArrayAsync();

            return emails;
        }

        public async Task<bool> UpdateExternalUser(ExternalUser existingExternalUser)
        {
            _model.ExternalUser.Update(existingExternalUser);
            return await _model.SaveChangesAsync() > 0;
        }

        public async Task<bool> DeleteExternalUser(ExternalUser externalUser)
        {
            _model.ExternalUser.Remove(externalUser);
            return await _model.SaveChangesAsync() > 0;
        }

        public async Task<bool> HasAgendaOwnership(int contentId)
        {
            return await (from c in _model.Content
                          join co in _model.ContentOwner on c.contentId equals co.contentId
                          where c.contentId == contentId && c.deleted != true && co.userId == _currentUser
                          select c).AnyAsync();
        }

        public async Task<bool> HasAgendaOwnership(Guid contentUuId)
        {
            return await (from c in _model.Content
                          join co in _model.ContentOwner on c.contentId equals co.contentId
                          where c.contentUuid == contentUuId && c.deleted != true && co.userId == _currentUser
                          select c).AnyAsync();
        }

        public async Task<UserToken> GetExternalUser2FAToken(string hash)
        {
            string sqlQuery = @"
                SELECT TOP 1 *
                FROM UserToken
                WHERE ProtectedTicket = @p0 AND tokenType = @p1
                ORDER BY expireDate DESC";

            var parameters = new object[] { hash, TokenTypes.EXTERNAL_USER.ToString() };

            return await _model.UserToken.FromSqlRaw(sqlQuery, parameters).FirstOrDefaultAsync();
        }

        public async Task<bool> UpdateExternalUser2FAToken(UserToken twoFactorToken)
        {
            _model.UserToken.Update(twoFactorToken);
            return await _model.SaveChangesAsync() > 0;
        }

        public async Task<bool> UpdateExternalUser2FAToken(UserToken twoFactorToken, ExternalUser externalUser)
        {
            bool result;

            using (var transaction = await _model.Database.BeginTransactionAsync())
            {
                try
                {
                    _model.ExternalUser.Update(externalUser);
                    _model.UserToken.Update(twoFactorToken);

                    result = await _model.SaveChangesAsync() > 0;

                    await transaction.CommitAsync();
                }
                catch (Exception)
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }

            return result;
        }

        public async Task<bool> ExternalMailAlreadyUsed(ExternalUserDTO extUser, int contentId)
        {
            var usedEmails = await GetAllUsedEmailsForContent(contentId);
            return usedEmails.Contains(extUser.ExternalMail, StringComparer.OrdinalIgnoreCase);
        }

        public async Task<bool> ExternalMailAlreadyUsed(ExternalUserDTO extUser, Guid contentUuId)
        {
            var usedEmails = await GetAllUsedEmailsForContent(contentUuId);
            return usedEmails.Contains(extUser.ExternalMail, StringComparer.OrdinalIgnoreCase);
        }

        private async Task<HashSet<string>> GetAllUsedEmailsForContent(int contentId)
        {
            var content = await _model.Content
                .AsNoTracking()
                .Where(c => c.contentId == contentId && c.deleted != true)
                .Select(c => c.workgroupId)
                .FirstOrDefaultAsync();

            if (content == 0) return new HashSet<string>();

            var workgroupUserEmails = await _model.WorkgroupUser
                .AsNoTracking()
                .Where(wu => wu.workgroupId == content)
                .Join(_model.User.AsNoTracking(),
                    wu => wu.userId,
                    u => u.userId,
                    (wu, u) => u.email)
                .Where(email => !string.IsNullOrEmpty(email))
                .ToListAsync();

            var externalUserEmails = await _model.ExternalUser
                .AsNoTracking()
                .Where(eu => eu.ContentId == contentId)
                .Select(eu => eu.ExternalMail)
                .Where(email => !string.IsNullOrEmpty(email))
                .ToListAsync();

            var recurringGuestEmails = await _model.RecurringGuest
                .AsNoTracking()
                .Where(rg => rg.workgroupId == content &&
                             !string.IsNullOrEmpty(rg.email) &&
                             !string.IsNullOrEmpty(rg.mobile))
                .Select(rg => rg.email)
                .ToListAsync();

            var allEmails = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
            allEmails.UnionWith(workgroupUserEmails);
            allEmails.UnionWith(externalUserEmails);
            allEmails.UnionWith(recurringGuestEmails);

            return allEmails;
        }

        private async Task<HashSet<string>> GetAllUsedEmailsForContent(Guid contentUuId)
        {
            var content = await _model.Content
                .AsNoTracking()
                .Where(c => c.contentUuid == contentUuId && c.deleted != true)
                .Select(c => new { c.contentId, c.workgroupId })
                .FirstOrDefaultAsync();

            if (content == null) return new HashSet<string>();

            var workgroupUserEmails = await _model.WorkgroupUser
                .AsNoTracking()
                .Where(wu => wu.workgroupId == content.workgroupId)
                .Join(_model.User.AsNoTracking(),
                    wu => wu.userId,
                    u => u.userId,
                    (wu, u) => u.email)
                .Where(email => !string.IsNullOrEmpty(email))
                .ToListAsync();

            var externalUserEmails = await _model.ExternalUser
                .AsNoTracking()
                .Where(eu => eu.contentUuid == contentUuId)
                .Select(eu => eu.ExternalMail)
                .Where(email => !string.IsNullOrEmpty(email))
                .ToListAsync();

            var recurringGuestEmails = await _model.RecurringGuest
                .AsNoTracking()
                .Where(rg => rg.workgroupId == content.workgroupId &&
                             !string.IsNullOrEmpty(rg.email) &&
                             !string.IsNullOrEmpty(rg.mobile))
                .Select(rg => rg.email)
                .ToListAsync();

            var allEmails = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
            allEmails.UnionWith(workgroupUserEmails);
            allEmails.UnionWith(externalUserEmails);
            allEmails.UnionWith(recurringGuestEmails);

            return allEmails;
        }

        public async Task<List<ExternalUserDTO>> GetExternalUsersByContentUuId(Guid contentUuId)
        {
            var allResults = await _model.ExternalUser
                .AsNoTracking()
                .Join(_model.Content.AsNoTracking(),
                    eu => eu.ContentId,
                    c => c.contentId,
                    (eu, c) => new { ExternalUser = eu, Content = c })
                .Join(_model.ExternalDocumentRequest.AsNoTracking(),
                    eu => eu.ExternalUser.ExternalDocumentRequestId,
                    edr => edr.ExternalDocumentRequestId,
                    (eu, edr) => new { ExternalUser = eu.ExternalUser, Content = eu.Content, ExternalDocumentRequest = edr })
                .Where(x => x.ExternalUser.contentUuid == contentUuId && x.Content.deleted != true)
                .Select(x => new
                {
                    x.ExternalUser.ExternalUserId,
                    x.ExternalUser.ExternalDocumentRequestId,
                    x.ExternalUser.ExternalName,
                    x.ExternalUser.ExternalMail,
                    x.ExternalUser.ExternalMobile,
                    x.Content.workgroupId,
                    x.ExternalUser.SentDate,
                    x.ExternalDocumentRequest.Status
                })
                .ToListAsync();

            var baseQuery = allResults
                .GroupBy(x => x.ExternalMail)
                .Select(g => g.OrderByDescending(x => x.ExternalUserId).First())
                .ToList();

            if (!baseQuery.Any())
            {
                // If no external users found, still try to get workgroup for recurring guests
                var workgroupId = await _model.Content
                    .AsNoTracking()
                    .Where(c => c.contentUuid == contentUuId && c.deleted != true)
                    .Select(c => c.workgroupId)
                    .FirstOrDefaultAsync();

                if (workgroupId > 0)
                {
                    return await GetRecurringExternalUsers(workgroupId);
                }
                return new List<ExternalUserDTO>();
            }

            var workgroupIdFromUsers = baseQuery.First().workgroupId;

            // Get recurring guests for the workgroup
            var recurringGuests = await _model.RecurringGuest
                .AsNoTracking()
                .Where(rg => rg.workgroupId == workgroupIdFromUsers)
                .ToDictionaryAsync(rg => rg.email.ToLower(), rg => rg.recurringGuestId);

            var externalUsers = baseQuery.Select(x => new ExternalUserDTO
            {
                ExternalUserId = x.ExternalUserId,
                ExternalDocumentRequestId = x.ExternalDocumentRequestId,
                ExternalName = x.ExternalName,
                ExternalMail = x.ExternalMail,
                ExternalMobile = x.ExternalMobile,
                WorkgroupId = x.workgroupId,
                SentDate = x.SentDate,
                RequestStatus = MapRequestStatus(x.ExternalDocumentRequestId, x.SentDate),
                IsRecurring = recurringGuests.ContainsKey(x.ExternalMail.ToLower()),
                RecurringGuestId = recurringGuests.ContainsKey(x.ExternalMail.ToLower()) ? 
                    recurringGuests[x.ExternalMail.ToLower()] : (int?)null
            }).ToList();

            // Add recurring guests that are not already external users
            var recurringExternalUsers = await GetRecurringExternalUsers(workgroupIdFromUsers);
            
            if (recurringExternalUsers.Any())
            {
                foreach (var recurringUser in recurringExternalUsers)
                {
                    var existingUser = externalUsers.FirstOrDefault(e => 
                        string.Equals(e.ExternalMail, recurringUser.ExternalMail, StringComparison.OrdinalIgnoreCase));

                    if (existingUser == null)
                    {
                        externalUsers.Add(recurringUser);
                    }
                }
            }

            return externalUsers;
        }

        private static ExternalUserRequestStatusEnum MapRequestStatus(int? externalDocumentRequestId, DateTime? sentDate)
        {
            if (!externalDocumentRequestId.HasValue)
                return ExternalUserRequestStatusEnum.NotRequested;

            if (sentDate.HasValue)
                return ExternalUserRequestStatusEnum.Completed;
            else
                return ExternalUserRequestStatusEnum.Pending;
        }
    }
}
