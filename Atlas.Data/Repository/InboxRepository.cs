using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Atlas.CrossCutting.AppEnums;
using Atlas.CrossCutting.Helpers;
using Atlas.Data.Dtos;
using Atlas.Data.Entities;
using Microsoft.EntityFrameworkCore;

namespace Atlas.Data.Repository;

public sealed class InboxRepository
{
    private readonly AtlasModelCore _md = new AtlasModelCore();
    private readonly int _currentUser;

    public InboxRepository(AuthUtil auth)
    {
        _currentUser = auth.UserId;
    }

    // Retorna buckets “brutos” (antes de aplicar os filtros de seção + justClosed)
    public async Task<(List<InboxOutboxDTO> tasks,
                       List<InboxOutboxDTO> polls,
                       List<InboxOutboxDTO> forms,
                       List<InboxOutboxDTO> esigns)>
        GetRawInboxBucketsAsync(ContentRequestFilter filters, int userId)
    {
        var bringClosed = filters.multiStatus.Contains("CLOSED");

        var tasks = await QueryTasksAsync(filters, userId);
        var polls = await QueryPollsAsync(filters, userId);
        var forms = await QueryFormsAsync(filters, userId);
        var esigns = await QueryESignsAsync(filters, userId);
        return (tasks, polls, forms, esigns);
    }

    public async Task<List<InboxOutboxDTO>> FilterAndOrderAsync(
        ContentRequestFilter filters,
        List<InboxOutboxDTO> tasks,
        List<InboxOutboxDTO> polls,
        List<InboxOutboxDTO> esigns)
    {
        var justClosed = filters.multiStatus != null && filters.multiStatus.Contains("CLOSED", StringComparer.OrdinalIgnoreCase);

        var filteredTasks = new List<InboxOutboxDTO>();
        var filteredPolls = new List<InboxOutboxDTO>();
        var filteredESigns = new List<InboxOutboxDTO>();

        bool wantsInbox = filters.inboxParameter.Contains("inbox");
        bool wantsOutbox = filters.inboxParameter.Contains("outbox");
        bool wantsOthers = filters.inboxParameter.Contains("others");
        bool wantsSignatures = filters.inboxParameter.Contains("signatures");

        if (wantsInbox)
        {
            filteredTasks.AddRange(tasks.Where(t => justClosed ? t.isInboxClosed : t.isInboxOpen));
            filteredPolls.AddRange(polls.Where(p => justClosed ? p.isInboxClosed : p.isInboxOpen));
            filteredESigns.AddRange(esigns.Where(e => justClosed ? e.isInboxClosed : e.isInboxOpen));
        }
        if (wantsOutbox)
        {
            filteredTasks.AddRange(tasks.Where(t => justClosed ? t.isOutboxClosed : t.isOutboxOpen));
            filteredPolls.AddRange(polls.Where(p => justClosed ? p.isOutboxClosed : p.isOutboxOpen));
            filteredESigns.AddRange(esigns.Where(e => justClosed ? e.isOutboxClosed : e.isOutboxOpen));
        }
        if (wantsOthers)
        {
            filteredTasks.AddRange(tasks.Where(t => justClosed ? t.isOtherClosed : t.isOthersOpen));
            filteredPolls.AddRange(polls.Where(p => justClosed ? p.isOtherClosed : p.isOthersOpen));
        }

        var result = new List<InboxOutboxDTO>();
        result.AddRange(filteredTasks);
        result.AddRange(filteredPolls);
        result.AddRange(filteredESigns);

        if (wantsInbox && !justClosed)
        {
            var pending = await GetFilteredPendingAccessAsync();
            if (pending.Count > 0)
                result.InsertRange(0, pending);
        }

        result = (justClosed
                  ? result.OrderByDescending(o => o.dueDate).ToList()
                  : result.OrderBy(o => o.dueDate).ToList());

        return result;
    }

    private async Task<List<InboxOutboxDTO>> QueryTasksAsync(ContentRequestFilter f, int userId)
    {
        var baseQ = _md.Content
            .AsNoTracking()
            .Where(c => c.type == ContentTypes.Task && c.deleted != true)

            .Where(c =>
                   c.createUser == userId
                || c.assignedUser == userId
                || c.reviewerUser == userId
                || _md.WorkgroupUser.Any(wu => wu.workgroupId == c.workgroupId && wu.userId == userId)
            );

        if (f.workgroups != null && f.workgroups.Length > 0)
            baseQ = baseQ.Where(c => f.workgroups.Contains(c.workgroupId));

        if (f.multiStatus != null && f.multiStatus.Length > 0)
            baseQ = baseQ.Where(c => f.multiStatus.Contains(c.status));

        var rows = await baseQ
            .Select(c => new
            {
                c.contentId,
                c.title,
                c.status,
                c.workgroupId,
                c.parentContentId,
                createUser = (int?)c.createUser,
                assignedUser = (int?)c.assignedUser,
                reviewerUser = (int?)c.reviewerUser,
                dueDate = c.Task.Select(t => (DateTime?)t.dueDate).FirstOrDefault()
            })
            .ToListAsync();

        if (rows.Count == 0) return new List<InboxOutboxDTO>();

        // 2) Pré-carrega dicionários (NULL-safe)
        var contentIds = rows.Select(r => r.contentId).ToList();
        var workgroupIds = rows.Select(r => r.workgroupId).Distinct().ToList();

        var userIds = rows
            .SelectMany(r => new[] { r.createUser, r.assignedUser, r.reviewerUser })
            .Where(id => id.HasValue).Select(id => id!.Value)
            .Distinct().ToList();

        // Workgroups (nome, cor, cliente)
        var wgInfo = await _md.Workgroup.AsNoTracking()
            .Where(w => workgroupIds.Contains(w.workgroupId))
            .Select(w => new
            {
                w.workgroupId,
                workgroupName = w.name,
                workgroupColor = w.bulletColor,
                clientName = w.Client.name
            })
            .ToListAsync();
        var wgById = wgInfo.ToDictionary(x => x.workgroupId, x => x);

        // Users (nome, pic, blocked, deleted)
        var users = await _md.User.AsNoTracking()
            .Where(u => userIds.Contains(u.userId))
            .Select(u => new
            {
                u.userId,
                u.name,
                u.profilePic,
                u.blocked,
                u.deleted
            })
            .ToListAsync();
        var userById = users.ToDictionary(x => x.userId, x => x);

        // Checklist (conta total/completos por contentId)
        var checklist = await _md.TaskCheckListItem.AsNoTracking()
            .Where(i => contentIds.Contains(i.contentId))
            .GroupBy(i => i.contentId)
            .Select(g => new
            {
                contentId = g.Key,
                total = g.Count(),
                done = g.Count(i => i.finished)
            })
            .ToListAsync();
        var checklistByCid = checklist.ToDictionary(x => x.contentId, x => x);

        // 3) Monta DTO (NULL-safe, sem navegação)
        var items = rows.Select(r =>
        {
            // Workgroup info
            wgById.TryGetValue(r.workgroupId, out var wg);

            // Users
            var create = (r.createUser.HasValue && userById.TryGetValue(r.createUser.Value, out var cu)) ? cu : null;
            var assign = (r.assignedUser.HasValue && userById.TryGetValue(r.assignedUser.Value, out var au)) ? au : null;
            var review = (r.reviewerUser.HasValue && userById.TryGetValue(r.reviewerUser.Value, out var ru)) ? ru : null;

            // Flags
            var isAssigned = r.assignedUser == userId;
            var isCreator = r.createUser == userId;
            var isClosed = string.Equals(r.status, "CLOSED", StringComparison.OrdinalIgnoreCase);
            var isOpen = !isClosed;

            // Checklist %
            int total = 0, done = 0;
            if (checklistByCid.TryGetValue(r.contentId, out var ck))
            {
                total = ck.total;
                done = ck.done;
            }
            double? overall = (total > 0) ? Math.Round((double)done / total, 2) : (double?)null;

            return new InboxOutboxDTO
            {
                type = "Task",
                contentId = r.contentId,
                title = r.title,
                clientName = wg?.clientName,
                workgroupId = r.workgroupId,
                workgroupName = wg?.workgroupName,
                workgroupColor = wg?.workgroupColor,

                createUserName = create?.name,
                assignedUserName = assign?.name,
                assignedUserPic = assign?.profilePic,
                assignedUser = r.assignedUser ?? 0,
                isAssignedUserBlocked = assign?.blocked ?? false,
                reviewerUserIsDeleted = review?.deleted == true,
                assignedUserIsDeleted = assign?.deleted == true,

                status = r.status,
                dueDate = r.dueDate,

                isInboxOpen = isAssigned && isOpen,
                isInboxClosed = isAssigned && isClosed,
                isOutboxOpen = isCreator && isOpen,
                isOutboxClosed = isCreator && isClosed,
                isOthersOpen = !isAssigned && !isCreator && isOpen,
                isOtherClosed = !isAssigned && !isCreator && isClosed,

                isForReview = (r.reviewerUser == userId) && isOpen,

                checklistOverallFinished = overall
            };
        }).ToList();

        return items;
    }

    private async Task<List<InboxOutboxDTO>> QueryPollsAsync(ContentRequestFilter f, int userId)
    {
        var q = _md.Content
            .AsNoTracking()
            .Where(c => c.type == ContentTypes.Poll && c.deleted != true)

            .Where(c =>
                   c.createUser == userId
                || _md.WorkgroupUser.Any(wu => wu.workgroupId == c.workgroupId && wu.userId == userId)
            )

            .Select(c => new
            {
                c.contentId,
                c.title,
                c.status,
                c.createUser,
                c.parentContentId,
                workgroupId = c.workgroupId,
                workgroupName = c.Workgroup.name,
                workgroupColor = c.Workgroup.bulletColor,
                clientName = c.Workgroup.Client.name,
                createUserName = c.User_Create.name,
                dueDate = c.Poll.Select(p => (DateTime?)p.dueDate).FirstOrDefault(),
                hasVoted = c.Poll.SelectMany(p => p.Votes).Any(v => v.userId == userId)
            });

        if (f.workgroups != null && f.workgroups.Length > 0)
            q = q.Where(x => f.workgroups.Contains(x.workgroupId));

        if (f.multiStatus != null && f.multiStatus.Length > 0)
            q = q.Where(x => f.multiStatus.Contains(x.status));

        var rows = await q.ToListAsync();

        var items = rows.Select(r =>
        {
            var isCreator = r.createUser == userId;
            var isClosed = r.status is "CLOSED" or "CANCELLED";
            var isOpen = !isClosed;

            var inboxOpen = isOpen && !r.hasVoted && !isCreator;
            var inboxClosed = isClosed || r.hasVoted;

            return new InboxOutboxDTO
            {
                type = "Poll",
                contentId = r.contentId,
                title = r.title,
                clientName = r.clientName,
                workgroupId = r.workgroupId,
                workgroupName = r.workgroupName,
                workgroupColor = r.workgroupColor,

                createUserName = r.createUserName,

                status = r.status,
                dueDate = r.dueDate,

                isInboxOpen = inboxOpen,
                isInboxClosed = inboxClosed,

                isOutboxOpen = isCreator && isOpen,
                isOutboxClosed = isCreator && isClosed,

                isOthersOpen = !isCreator && isOpen && !inboxOpen,
                isOtherClosed = !isCreator && isClosed
            };
        }).ToList();

        return items;
    }

    private async Task<List<InboxOutboxDTO>> QueryFormsAsync(ContentRequestFilter f, int userId)
    {
        var q = _md.Content
            .AsNoTracking()
            .Where(c => c.type == ContentTypes.Form && c.deleted != true)

            .Where(c =>
                   c.createUser == userId
                || _md.WorkgroupUser.Any(wu => wu.workgroupId == c.workgroupId && wu.userId == userId)
            )

            .Select(c => new
            {
                c.contentId,
                c.title,
                c.status,
                c.createUser,
                c.parentContentId,
                workgroupId = c.workgroupId,
                workgroupName = c.Workgroup.name,
                workgroupColor = c.Workgroup.bulletColor,
                clientName = c.Workgroup.Client.name,
                createUserName = c.User_Create.name,

                dueDate = c.Forms.Select(fm => (DateTime?)fm.expirationDate).FirstOrDefault(),

                answered = _md.FormRespondents.Any(fr => fr.contentId == c.contentId && fr.userId == userId && fr.status == "CLOSED"),
                totalAnswers = _md.FormRespondents.Count(fr => fr.contentId == c.contentId && fr.status == "CLOSED")
            });

        if (f.workgroups != null && f.workgroups.Length > 0)
            q = q.Where(x => f.workgroups.Contains(x.workgroupId));

        if (f.multiStatus != null && f.multiStatus.Length > 0)
            q = q.Where(x => f.multiStatus.Contains(x.status));

        var rows = await q.ToListAsync();

        var items = rows.Select(r =>
        {
            var isCreator = r.createUser == userId;
            var isClosed = string.Equals(r.status, "CLOSED", StringComparison.OrdinalIgnoreCase);
            var isOpen = !isClosed;

            // InboxOpen: form aberto e não respondido e não sou o criador
            var inboxOpen = isOpen && !r.answered && !isCreator;
            // InboxClosed: CLOSED ou já respondeu
            var inboxClosed = isClosed || r.answered;

            return new InboxOutboxDTO
            {
                type = "Form",
                contentId = r.contentId,
                title = r.title,
                clientName = r.clientName,
                workgroupId = r.workgroupId,
                workgroupName = r.workgroupName,
                workgroupColor = r.workgroupColor,

                createUserName = r.createUserName,

                status = r.status,
                dueDate = r.dueDate,

                isInboxOpen = inboxOpen,
                isInboxClosed = inboxClosed,

                isOutboxOpen = isCreator && isOpen,
                isOutboxClosed = isCreator && isClosed,

                isOthersOpen = !isCreator && isOpen && !inboxOpen,
                isOtherClosed = !isCreator && isClosed
            };
        }).ToList();

        return items;
    }

    private async Task<List<InboxOutboxDTO>> QueryESignsAsync(ContentRequestFilter f, int userId)
    {
        var csrBase =
            from csr in _md.ContentSignatureRequest.AsNoTracking()
            join c in _md.Content.AsNoTracking() on csr.contentId equals c.contentId
            where c.Workgroup.archived == false

            where csr.requesterUserId == userId
               || _md.ContentSigner.Any(s => s.contentSignatureRequestId == csr.contentSignatureRequestId && s.userId == userId)

            select new
            {
                csr.contentSignatureRequestId,
                csr.contentId,
                csr.status,
                csr.requestDate,
                csr.provider,
                csr.contentAttachmentId,
                csr.requesterUserId,

                c.title,
                statusContent = c.status,
                c.workgroupId,
                workgroupName = c.Workgroup.name,
                workgroupColor = c.Workgroup.bulletColor,
                clientName = c.Workgroup.Client.name
            };

        if (f.workgroups != null && f.workgroups.Length > 0)
        {
            var wgs = f.workgroups;
            csrBase = csrBase.Where(x => wgs.Contains(x.workgroupId));
        }

        var csrRows = await csrBase.ToListAsync();
    if (csrRows.Count == 0) return new List<InboxOutboxDTO>();

    // Requesters (nomes)
    var requesterIds = csrRows.Select(x => x.requesterUserId).Distinct().ToList();
    var requesterNames = await _md.User.AsNoTracking()
        .Where(u => requesterIds.Contains(u.userId))
        .Select(u => new { u.userId, u.name })
        .ToListAsync();
    var requesterById = requesterNames.ToDictionary(x => x.userId, x => x.name);

    // Sou signer? (pega meus ContentSigner para essas CSRs)
    var csrIds = csrRows.Select(x => x.contentSignatureRequestId).Distinct().ToList();
    var mySigners = await _md.ContentSigner.AsNoTracking()
        .Where(s => csrIds.Contains(s.contentSignatureRequestId) && s.userId == userId)
        .Select(s => new { s.contentSignatureRequestId, s.contentSignerId })
        .ToListAsync();
    var mySignerByCsr = mySigners
        .GroupBy(s => s.contentSignatureRequestId)
        .ToDictionary(g => g.Key, g => g.Select(x => x.contentSignerId).ToList());

    // Assinaturas (para *meus* signers)
    var mySignerIds = mySigners.Select(s => s.contentSignerId).Distinct().ToList();
    var mySigs = await _md.ContentSignature.AsNoTracking()
        .Where(sig => mySignerIds.Contains(sig.contentSignerId))
        .Select(sig => new { sig.contentSignerId, sig.signDate, sig.rejected })
        .ToListAsync();
    var sigsBySigner = mySigs.GroupBy(s => s.contentSignerId)
        .ToDictionary(g => g.Key, g => g.ToList());

    var items = new List<InboxOutboxDTO>();
    foreach (var r in csrRows)
    {
        var requesterName = requesterById.TryGetValue(r.requesterUserId, out var nm) ? nm : null;
        var isCancelledByParent = string.Equals(r.statusContent, "CANCELLED", StringComparison.OrdinalIgnoreCase);

        // Se eu for signer desta CSR:
        bool isSigner = mySignerByCsr.TryGetValue(r.contentSignatureRequestId, out var signerIds) && signerIds.Count > 0;
        bool hasSigned = false;
        bool hasRejected = false;

        if (isSigner)
        {
            foreach (var signerId in signerIds)
            {
                if (!sigsBySigner.TryGetValue(signerId, out var sigs)) continue;

                // hasRejected: qualquer sig com rejected == true
                if (sigs.Any(s => s.rejected == true))
                {
                    hasRejected = true;
                    break;
                }

                // hasSigned: qualquer sig com signDate preenchida e não rejeitada
                if (sigs.Any(s => s.signDate != default && (s.rejected == null || s.rejected == false)))
                {
                    hasSigned = true;
                }
            }
        }

        bool isRequester = r.requesterUserId == userId;
        var csrStatus = r.status; // status da CSR

        // Bandeiras iguais ao v2:
        // Inbox Open: sou signer, CSR OPEN, ainda não assinei nem rejeitei
        bool isInboxOpen   = isSigner && csrStatus == "OPEN" && !hasSigned && !hasRejected;
        // Inbox Closed: assinei/rejeitei, OU CSR não está OPEN (CLOSED/EXPIRED/REJECTED) OU parent cancelado
        bool isInboxClosed = (!isInboxOpen) && (hasSigned || hasRejected || csrStatus != "OPEN" || isCancelledByParent);

        // Outbox Open: sou requester e CSR OPEN
        bool isOutboxOpen  = isRequester && csrStatus == "OPEN";
        // Outbox Closed: requester e CSR não-OPEN ou parent cancelado
        bool isOutboxClosed= isRequester && (csrStatus != "OPEN" || isCancelledByParent);

        items.Add(new InboxOutboxDTO
        {
            type = "ESign",
            contentId = r.contentId,
            title = r.title,
            clientName = r.clientName,
            workgroupId = r.workgroupId,
            workgroupName = r.workgroupName,
            workgroupColor = r.workgroupColor,

            createUserName = requesterName,

            status = csrStatus,
            requestDate = r.requestDate,

            isInboxOpen = isInboxOpen,
            isInboxClosed = isInboxClosed,
            isOutboxOpen = isOutboxOpen,
            isOutboxClosed = isOutboxClosed,

            // others não se aplica para ESign (igual v2)
            isOthersOpen = false,
            isOtherClosed = false,

            // extras
            isSigner = isSigner,
            hasSigned = hasSigned,
            hasRejected = hasRejected,
            isEsignRequester = isRequester,

            signatureType = "ELECTRONIC",
            signatureProvider = r.provider,
            contentAttachmentId = r.contentAttachmentId,
            requesterUserName = requesterName,

            guid = null
        });
    }

    // Filtro de status dentro do "signatures" (igual v2)
    if (f.inboxParameter.Contains("signatures") && f.multiStatus != null && f.multiStatus.Length > 0)
    {
        var ms = f.multiStatus;
        if (ms.Contains("CLOSED")) items = items.Where(i => i.status == "CLOSED").ToList();
        if (ms.Contains("OPEN"))   items = items.Where(i => i.status == "OPEN").ToList();
    }

    return items;
}

    private async Task<List<InboxOutboxDTO>> GetFilteredPendingAccessAsync()
    {
        var pending = await _md.Activity
            .AsNoTracking()
            .Where(a => a.activityType == "PENDING_ACCESS"
                        && a.activityUserId == _currentUser
                        && a.processed == false)
            .OrderByDescending(a => a.date)
            .Select(a => new InboxOutboxDTO
            {
                type = "Access",
                title = "Acesso pendente",
                pendingAccessUserId = _currentUser,
                pendingAccessDate = a.date,
                isInboxOpen = true
            })
            .ToListAsync();

        return pending;
    }
}
