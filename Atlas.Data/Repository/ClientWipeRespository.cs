using Atlas.Data.Entities;
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading.Tasks;

namespace Atlas.Data.Repository
{
    public class ClientWipeRespository
    {
        private int _currentUser { get; }
        public ClientWipeRespository(int currentUser)
        {
            this._currentUser = currentUser;
        }
        public ClientWipeRequest AddRequest(int clientId, UserToken userToken)
        {
            using (AtlasModelCore model = new AtlasModelCore())
            {
                var request = new ClientWipeRequest()
                {
                    requestUserId = _currentUser,
                    requestDate = DateTime.UtcNow,
                    status = "CREATED",
                    processed = false,
                    retries = 0,
                    clientId = clientId,
                };
                request.WipeRequestTokens.Add(new WipeRequestToken()
                {
                    clientWipeRequestId = request.clientWipeRequestId,
                    lastUpdate = DateTime.UtcNow,
                    userTokenId = userToken.tokenId
                });

                model.ClientWipeRequest.Add(request);
                int entries = model.SaveChanges();

                return entries > 0 ? request : null;
            }
        }

        public async System.Threading.Tasks.Task UpdateStatus(int opReqId, string status, string errorDetails = null)
        {
            AtlasModelCore _md = new AtlasModelCore();

            var opReq = await _md.ClientOperationRequest.Where(o => o.opReqId == opReqId).Include(o => o.User_Request).FirstAsync();

            opReq.status = status;

            if (status == "RUNNING")
            {
                opReq.processBeginDate = DateTime.UtcNow;
            }

            if (status == "FINISHED")
            {
                opReq.processed = true;
                opReq.processEndDate = DateTime.UtcNow;
            }

            if (status == "ERROR")
            {
                opReq.lastError = errorDetails;
            }

            _md.ClientOperationRequest.Update(opReq);
            await _md.SaveChangesAsync();
        }

        public async Task<List<ClientOperationRequest>> GetAll(int clientId, string requestType)
        {
            AtlasModelCore _md = new AtlasModelCore();
            return await _md.ClientOperationRequest.Where(o => o.clientId == clientId && o.requestType == requestType).Include(o => o.User_Request).OrderByDescending(o => o.requestDate).ToListAsync();
        }
        public async Task<ClientOperationRequest> Get(int opReqId)
        {
            AtlasModelCore _md = new AtlasModelCore();
            return await _md.ClientOperationRequest.Where(o => o.opReqId == opReqId).Include(o => o.User_Request).FirstAsync();
        }
    }
}
