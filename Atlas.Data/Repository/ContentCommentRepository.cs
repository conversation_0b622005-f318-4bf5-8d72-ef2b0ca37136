using System;
using System.Linq;
using Atlas.Data.Entities;

namespace Atlas.Data.Repository
{
    public class ContentCommentRepository
    {
        //ContentTypeEnum _type;
        //Entities.Content _content;
        //object _innerContent;

        User _currentUser;
        Content _content;
        int _content_id;

        AtlasModelCore _md = new AtlasModelCore();

        /// <summary>
        /// Create an instance of the content repository with the current logged user, for permissions purposes.
        /// </summary>
        /// <param name="userId">Identity of the current logged user.</param>
        public ContentCommentRepository(int userId, int content_id)
        {
            _content_id = content_id;

            //_md.Configuration.ProxyCreationEnabled = false;
            //_md.Configuration.LazyLoadingEnabled = false;

            //var select = (from u in _md.User
            //              where u.userId == userId
            //              select u);
            //_currentUser = select.FirstOrDefault();

            //var cnt = (from c in _md.Content
            //              where c.contentId == content_id
            //              select c);
            //_content = cnt.FirstOrDefault();

            //if (_currentUser == null)
            //{
            //    throw new ArgumentException("User not found/Usuário não encontrado");
            //}

            //if (_currentUser.blocked || _currentUser.deleted)
            //{
            //    throw new System.Security.SecurityException("Usuário não autorizado.");
            //}

            //if (_content == null)
            //{
            //    throw new ArgumentException("Content not found/Conteudo não encontrado");
            //}

            //ContentRepository _repo = new ContentRepository(userId);
            //_repo.CheckPermissions(content_id);
        }
      
       
        public bool CheckPermissions(int contentId)
        {
            var select = (from c in _md.Content
                          join p in _md.ContentPermission on c.contentId equals p.contentId
                          where p.userId == _currentUser.userId && p.contentId == contentId && (c.deleted ?? false) == false
                          select c);

            return select.Any();
        }

        public bool Delete(int content_id)
        {
            var item = _md.ContentComment.FirstOrDefault(c => c.contentId == content_id);
            //todo: passar pra logical delete
            //item.deleted = true;
            _md.ContentComment.Remove(item);
            return _md.SaveChanges() > 0;
        }

        
        public int Add(string text)
        {
            ContentComment c = new ContentComment();
            c.date = DateTime.UtcNow;
            c.contentId = this._content_id;
            c.text = text;
            var added = _md.ContentComment.Add(c);
            _md.SaveChanges();

            return added.Entity.contentId;
        }
        public int Update(int comment_id, string text)
        {
            var comment = _md.ContentComment.FirstOrDefault(c => c.contentId == comment_id);
            if (comment != null)
            {
                comment.text = text;
                _md.ContentComment.Update(comment);
                return _md.SaveChanges();
            }
            throw new NotImplementedException();
        }
     
    }
}
