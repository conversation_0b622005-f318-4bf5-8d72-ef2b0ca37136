using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Atlas.Data.Entities;
using Microsoft.EntityFrameworkCore;

namespace Atlas.Data.Repository
{
    public class WorkgroupOwnerRepository
    {
        int _workgroup_id;
        private User currentUser { get; set; }

        AtlasModelCore _md = new AtlasModelCore();

        public WorkgroupOwnerRepository(int currentUserId, int workgroup_id)
        {
            _workgroup_id = workgroup_id;

            var select = (from u in _md.User
                          where u.userId == currentUserId
                          select u);
            currentUser = select.FirstOrDefault();

            if (currentUser == null)
            {
                throw new ArgumentException("User not found/Usuário não encontrado");
            }

            if (currentUser.blocked)
            {
                throw new System.Security.SecurityException("User blocked or deleted.");
            }
        }

        public WorkgroupOwnerRepository(int currentUserId)
        {
            var select = (from u in _md.User
                          where u.userId == currentUserId
                          select u);
            currentUser = select.FirstOrDefault();

            if (currentUser == null)
            {
                throw new ArgumentException("User not found/Usuário não encontrado");
            }

            if (currentUser.blocked)
            {
                throw new System.Security.SecurityException("User blocked or deleted.");
            }
        }

        public List<WorkgroupOwner> GetCurrentOwners()
        {
            var result = _md.WorkgroupOwner.Include(u => u.User).Where(o => o.workgroupId == _workgroup_id).ToList();
            return result;
        }

        public bool IsOwnerAnyWorkgroup()
        {
            return _md.WorkgroupOwner.Any(o => o.userId == currentUser.userId);
        }

        public bool Add(int userId, out int added_item)
        {
            //checa se o usuário ATUAL é owner
            if (_md.WorkgroupOwner.Where(o => o.workgroupId == _workgroup_id && o.userId == currentUser.userId).Count() == 0)
            {
                added_item = 0;
                return false;
            }

            //checa se ja nao existe
            if (_md.WorkgroupOwner.Where(o => o.workgroupId == _workgroup_id && o.userId == userId).Count() > 0)
            {
                added_item = 0;
                return false;
            }

            //cria objeto
            WorkgroupOwner wo = new WorkgroupOwner();
            wo.userId = userId;
            wo.workgroupId = _workgroup_id;
            wo.createUser = currentUser.userId;
            wo.createDate = DateTime.UtcNow;
            // Add the new entity
            var result = _md.WorkgroupOwner.Add(wo);

            // Save changes to persist the entity and retrieve the generated ID
            if (_md.SaveChanges() > 0)
            {
                added_item = result.Entity.workgroupOwnerId; // The ID is populated after SaveChanges
                return true;
            }
            else
            {
                added_item = 0;
                return false;
            }
        }

        public async Task<bool> Delete(int userId)
        {
            //checa se o usuário ATUAL é owner
            if (_md.WorkgroupOwner.Where(o => o.workgroupId == _workgroup_id && o.userId == currentUser.userId).Count() == 0)
            {
                return false;
            }
            var result = _md.WorkgroupOwner.Where(o => o.workgroupId == _workgroup_id && o.userId == userId).FirstOrDefault();

            if(result == null)
            {
                return false;
            }

            _md.WorkgroupOwner.Remove(result);

            if (_md.SaveChanges() > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
            }

        public int DeleteWorkgroupOwnerFromAll(int userId)
        {
            var list = _md.WorkgroupOwner.Where(co => co.userId == userId).ToList();
            _md.WorkgroupOwner.RemoveRange(list);

            return _md.SaveChanges();
        }

        /// <summary>Sets the owners and it's called on Owner Management dialog</summary>
        /// <param name="workgroup_id">The content identifier.</param>
        /// <param name="list_add">The list to add.</param>
        /// <param name="list_remove">The list to remove.</param>
        /// <returns>Database commit result</returns>
        public int Set_Owners(int workgroup_id, int createUser, List<WorkgroupOwner> list_add, List<WorkgroupOwner> list_remove)
        {
            foreach (var item in list_add)
            {
                _md.WorkgroupOwner.Add(new WorkgroupOwner
                {
                    userId = item.userId,
                    workgroupId = workgroup_id,
                    createDate = DateTime.UtcNow,
                    createUser = createUser
                });


            }

            foreach (var item in list_remove)
            {
                var remove_item = _md.WorkgroupOwner.Where(o => o.userId == item.userId && o.workgroupId == workgroup_id).First();
                _md.WorkgroupOwner.Remove(remove_item);
            }

            return _md.SaveChanges();
        }

    }
}
