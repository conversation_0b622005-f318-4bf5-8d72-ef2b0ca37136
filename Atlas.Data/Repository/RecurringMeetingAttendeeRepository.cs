using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Atlas.Data.Abstract;
using Atlas.Data.Entities;
// using System.Data.Entity;
using Microsoft.EntityFrameworkCore;

namespace Atlas.Data.Repository
{
    public class RecurringMeetingAttendeeRepository : EfRepositoryBase<RecurringMeetingAttendee, AtlasModelCore>
    {
        public RecurringMeetingAttendeeRepository(AtlasModelCore context) : base(context)
        {
        }

        /// <summary>
        /// Obtém todos os participantes de uma reunião recorrente
        /// </summary>
        /// <param name="recurringMeetingId">ID da reunião recorrente</param>
        /// <returns>Lista de participantes com dados do usuário</returns>
        public async Task<List<RecurringMeetingAttendee>> GetAttendeesByRecurringMeetingAsync(Guid recurringMeetingId)
        {
            return await dbSet
                .Include(x => x.User)
                .Where(x => x.recurringMeetingId == recurringMeetingId)
                .OrderBy(x => x.User.name)
                .ToListAsync();
        }

        /// <summary>
        /// Obtém todos os participantes de uma reunião recorrente (somente userIds)
        /// </summary>
        /// <param name="recurringMeetingId">ID da reunião recorrente</param>
        /// <returns>Lista de userIds</returns>
        public async Task<List<int>> GetAttendeeUserIdsByRecurringMeetingAsync(Guid recurringMeetingId)
        {
            return await dbSet
                .Where(x => x.recurringMeetingId == recurringMeetingId)
                .Select(x => x.userId)
                .ToListAsync();
        }

        /// <summary>
        /// Verifica se um usuário é participante de uma reunião recorrente
        /// </summary>
        /// <param name="recurringMeetingId">ID da reunião recorrente</param>
        /// <param name="userId">ID do usuário</param>
        /// <returns>True se for participante</returns>
        public async Task<bool> IsUserAttendeeAsync(Guid recurringMeetingId, int userId)
        {
            return await dbSet
                .AnyAsync(x => x.recurringMeetingId == recurringMeetingId && x.userId == userId);
        }

        /// <summary>
        /// Adiciona um participante à reunião recorrente
        /// </summary>
        /// <param name="recurringMeetingId">ID da reunião recorrente</param>
        /// <param name="userId">ID do usuário</param>
        /// <returns>ID do registro criado</returns>
        public async Task<Guid> AddAttendeeAsync(Guid recurringMeetingId, int userId)
        {
            // Verifica se já existe
            var exists = await IsUserAttendeeAsync(recurringMeetingId, userId);
            if (exists)
            {
                throw new InvalidOperationException($"User {userId} is already an attendee of recurring meeting {recurringMeetingId}");
            }

            var attendee = new RecurringMeetingAttendee
            {
                recurringMeetingAttendeeId = Guid.NewGuid(),
                recurringMeetingId = recurringMeetingId,
                userId = userId,
                createdOn = DateTime.UtcNow
            };

            dbSet.Add(attendee);
            return attendee.recurringMeetingAttendeeId;
        }

        /// <summary>
        /// Adiciona múltiplos participantes à reunião recorrente
        /// </summary>
        /// <param name="recurringMeetingId">ID da reunião recorrente</param>
        /// <param name="userIds">Lista de IDs dos usuários</param>
        /// <returns>Lista de IDs dos registros criados</returns>
        public async Task<List<Guid>> AddMultipleAttendeesAsync(Guid recurringMeetingId, List<int> userIds)
        {
            if (userIds == null || !userIds.Any())
                return new List<Guid>();

            // Remove duplicatas
            userIds = userIds.Distinct().ToList();

            // Verifica quais usuários já são participantes
            var existingUserIds = await GetAttendeeUserIdsByRecurringMeetingAsync(recurringMeetingId);
            var newUserIds = userIds.Where(id => !existingUserIds.Contains(id)).ToList();

            var createdIds = new List<Guid>();
            foreach (var userId in newUserIds)
            {
                var attendee = new RecurringMeetingAttendee
                {
                    recurringMeetingAttendeeId = Guid.NewGuid(),
                    recurringMeetingId = recurringMeetingId,
                    userId = userId,
                    createdOn = DateTime.UtcNow
                };

                dbSet.Add(attendee);
                createdIds.Add(attendee.recurringMeetingAttendeeId);
            }

            return createdIds;
        }

        /// <summary>
        /// Remove um participante da reunião recorrente
        /// </summary>
        /// <param name="recurringMeetingId">ID da reunião recorrente</param>
        /// <param name="userId">ID do usuário</param>
        /// <returns>True se removido com sucesso</returns>
        public async Task<bool> RemoveAttendeeAsync(Guid recurringMeetingId, int userId)
        {
            var attendee = await dbSet
                .FirstOrDefaultAsync(x => x.recurringMeetingId == recurringMeetingId && x.userId == userId);

            if (attendee == null)
                return false;

            dbSet.Remove(attendee);
            return true;
        }

        /// <summary>
        /// Remove múltiplos participantes da reunião recorrente
        /// </summary>
        /// <param name="recurringMeetingId">ID da reunião recorrente</param>
        /// <param name="userIds">Lista de IDs dos usuários</param>
        /// <returns>Número de participantes removidos</returns>
        public async Task<int> RemoveMultipleAttendeesAsync(Guid recurringMeetingId, List<int> userIds)
        {
            if (userIds == null || !userIds.Any())
                return 0;

            var attendeesToRemove = await dbSet
                .Where(x => x.recurringMeetingId == recurringMeetingId && userIds.Contains(x.userId))
                .ToListAsync();

            foreach (var attendee in attendeesToRemove)
            {
                dbSet.Remove(attendee);
            }

            return attendeesToRemove.Count;
        }

        /// <summary>
        /// Substitui todos os participantes de uma reunião recorrente
        /// </summary>
        /// <param name="recurringMeetingId">ID da reunião recorrente</param>
        /// <param name="newUserIds">Nova lista de IDs dos usuários</param>
        /// <returns>Resultado da operação com estatísticas</returns>
        public async Task<AttendeeUpdateResult> ReplaceAllAttendeesAsync(Guid recurringMeetingId, List<int> newUserIds)
        {
            if (newUserIds == null)
                newUserIds = new List<int>();

            // Remove duplicatas
            newUserIds = newUserIds.Distinct().ToList();

            // Obtém participantes atuais
            var currentUserIds = await GetAttendeeUserIdsByRecurringMeetingAsync(recurringMeetingId);

            // Calcula diferenças
            var usersToAdd = newUserIds.Where(id => !currentUserIds.Contains(id)).ToList();
            var usersToRemove = currentUserIds.Where(id => !newUserIds.Contains(id)).ToList();

            var result = new AttendeeUpdateResult
            {
                AddedCount = 0,
                RemovedCount = 0,
                AddedUserIds = usersToAdd,
                RemovedUserIds = usersToRemove
            };

            // Adiciona novos participantes
            if (usersToAdd.Any())
            {
                var addedIds = await AddMultipleAttendeesAsync(recurringMeetingId, usersToAdd);
                result.AddedCount = addedIds.Count;
            }

            // Remove participantes
            if (usersToRemove.Any())
            {
                result.RemovedCount = await RemoveMultipleAttendeesAsync(recurringMeetingId, usersToRemove);
            }

            return result;
        }

        /// <summary>
        /// Remove todos os participantes de uma reunião recorrente
        /// </summary>
        /// <param name="recurringMeetingId">ID da reunião recorrente</param>
        /// <returns>Número de participantes removidos</returns>
        public async Task<int> RemoveAllAttendeesAsync(Guid recurringMeetingId)
        {
            var attendeesToRemove = await dbSet
                .Where(x => x.recurringMeetingId == recurringMeetingId)
                .ToListAsync();

            foreach (var attendee in attendeesToRemove)
            {
                dbSet.Remove(attendee);
            }

            return attendeesToRemove.Count;
        }

        /// <summary>
        /// Obtém todas as reuniões recorrentes em que um usuário participa
        /// </summary>
        /// <param name="userId">ID do usuário</param>
        /// <returns>Lista de IDs das reuniões recorrentes</returns>
        public async Task<List<Guid>> GetRecurringMeetingsByUserAsync(int userId)
        {
            return await dbSet
                .Where(x => x.userId == userId)
                .Select(x => x.recurringMeetingId)
                .ToListAsync();
        }

        #region Backward Compatibility & Hybrid Methods

        /// <summary>
        /// Checks if a recurring meeting has attendees defined at series level (new model)
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <returns>True if series has attendees in new model</returns>
        public async Task<bool> HasSeriesAttendeesAsync(Guid recurringMeetingId)
        {
            return await dbSet
                .AnyAsync(x => x.recurringMeetingId == recurringMeetingId);
        }

        /// <summary>
        /// Gets attendees from series level only (new model)
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <returns>List of user IDs from series</returns>
        public async Task<List<int>> GetSeriesAttendeeIdsOnlyAsync(Guid recurringMeetingId)
        {
            return await dbSet
                .Where(x => x.recurringMeetingId == recurringMeetingId)
                .Select(x => x.userId)
                .ToListAsync();
        }

        /// <summary>
        /// Gets attendees from occurrences only (legacy model)
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <returns>List of unique user IDs from all occurrences</returns>
        public async Task<List<int>> GetOccurrenceAttendeeIdsOnlyAsync(Guid recurringMeetingId)
        {
            using (var context = new AtlasModelCore())
            {
                var attendeeIds = await context.ContentSubscriber
                    .Where(cs => cs.Content.recurringMeetingId == recurringMeetingId
                              && cs.Content.type == "Meeting"
                              && cs.Content.deleted != true)
                    .Select(cs => cs.userId)
                    .Distinct()
                    .ToListAsync();

                return attendeeIds;
            }
        }

        /// <summary>
        /// Gets combined attendees from both series and occurrences (hybrid approach for backward compatibility)
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <returns>Combined list of unique user IDs</returns>
        public async Task<List<int>> GetCombinedAttendeeIdsAsync(Guid recurringMeetingId)
        {
            var seriesAttendees = await GetSeriesAttendeeIdsOnlyAsync(recurringMeetingId);
            var occurrenceAttendees = await GetOccurrenceAttendeeIdsOnlyAsync(recurringMeetingId);

            // Combine and remove duplicates
            var allAttendees = seriesAttendees.Union(occurrenceAttendees).ToList();
            return allAttendees;
        }

        /// <summary>
        /// Gets attendees with full User objects using hybrid approach
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <returns>List of User objects</returns>
        public async Task<List<User>> GetCombinedAttendeesAsync(Guid recurringMeetingId)
        {
            var userIds = await GetVersionBasedAttendeeIdsAsync(recurringMeetingId);

            if (!userIds.Any())
                return new List<User>();

            using (var context = new AtlasModelCore())
            {
                var users = await context.User
                    .Where(u => userIds.Contains(u.userId) && !u.deleted && !u.blocked)
                    .OrderBy(u => u.name)
                    .ToListAsync();

                return users;
            }
        }

        /// <summary>
        /// Determines which model is being used for a recurring meeting
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <returns>Model type being used</returns>
        public async Task<RecurringMeetingAttendeeModel> GetAttendeeModelTypeAsync(Guid recurringMeetingId)
        {
            var hasSeriesAttendees = await HasSeriesAttendeesAsync(recurringMeetingId);
            var occurrenceAttendees = await GetOccurrenceAttendeeIdsOnlyAsync(recurringMeetingId);

            if (hasSeriesAttendees && occurrenceAttendees.Any())
                return RecurringMeetingAttendeeModel.Hybrid;
            else if (hasSeriesAttendees)
                return RecurringMeetingAttendeeModel.Series;
            else if (occurrenceAttendees.Any())
                return RecurringMeetingAttendeeModel.Legacy;
            else
                return RecurringMeetingAttendeeModel.None;
        }

        /// <summary>
        /// Gets attendance statistics for a recurring meeting
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <returns>Attendance statistics</returns>
        public async Task<AttendanceStatistics> GetAttendanceStatisticsAsync(Guid recurringMeetingId)
        {
            var seriesCount = await dbSet
                .CountAsync(x => x.recurringMeetingId == recurringMeetingId);

            var occurrenceCount = await GetOccurrenceAttendeeIdsOnlyAsync(recurringMeetingId);
            var combinedCount = await GetVersionBasedAttendeeIdsAsync(recurringMeetingId);

            return new AttendanceStatistics
            {
                SeriesAttendeesCount = seriesCount,
                OccurrenceAttendeesCount = occurrenceCount.Count,
                TotalUniqueAttendeesCount = combinedCount.Count,
                ModelType = await GetVersionBasedModelTypeAsync(recurringMeetingId)
            };
        }

        #endregion

        #region Version-Based Attendee Management (Safe & Explicit)

        /// <summary>
        /// Gets attendees based on the recurring meeting's version (safe approach)
        /// V1 = From occurrences, V2+ = From series with fallback to occurrences
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <returns>List of user IDs based on version</returns>
        public async Task<List<int>> GetVersionBasedAttendeeIdsAsync(Guid recurringMeetingId)
        {
            using (var context = new AtlasModelCore())
            {
                var recurringMeeting = await context.RecurringMeeting
                    .FirstOrDefaultAsync(rm => rm.recurringMeetingId == recurringMeetingId);

                if (recurringMeeting == null)
                    return new List<int>();

                if (recurringMeeting.IsSeriesAttendeesVersion) // V2+
                {
                    // Primary: Get from series
                    var seriesAttendees = await GetSeriesAttendeeIdsOnlyAsync(recurringMeetingId);

                    // Fallback: If series is empty, get from occurrences
                    if (!seriesAttendees.Any())
                    {
                        return await GetOccurrenceAttendeeIdsOnlyAsync(recurringMeetingId);
                    }

                    return seriesAttendees;
                }
                else // V1 (Legacy)
                {
                    // Primary: Get from occurrences
                    var occurrenceAttendees = await GetOccurrenceAttendeeIdsOnlyAsync(recurringMeetingId);

                    // Additional: Include any series attendees that might exist
                    var seriesAttendees = await GetSeriesAttendeeIdsOnlyAsync(recurringMeetingId);

                    // Combine and remove duplicates
                    return occurrenceAttendees.Union(seriesAttendees).ToList();
                }
            }
        }

        /// <summary>
        /// Gets attendees with full User objects based on version
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <returns>List of User objects</returns>
        public async Task<List<User>> GetVersionBasedAttendeesAsync(Guid recurringMeetingId)
        {
            var userIds = await GetVersionBasedAttendeeIdsAsync(recurringMeetingId);

            if (!userIds.Any())
                return new List<User>();

            using (var context = new AtlasModelCore())
            {
                var users = await context.User
                    .Where(u => userIds.Contains(u.userId) && !u.deleted && !u.blocked)
                    .OrderBy(u => u.name)
                    .ToListAsync();

                return users;
            }
        }

        /// <summary>
        /// Determines attendee model based on explicit version (safe and reliable)
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <returns>Model type based on version</returns>
        public async Task<RecurringMeetingAttendeeModel> GetVersionBasedModelTypeAsync(Guid recurringMeetingId)
        {
            using (var context = new AtlasModelCore())
            {
                var recurringMeeting = await context.RecurringMeeting
                    .FirstOrDefaultAsync(rm => rm.recurringMeetingId == recurringMeetingId);

                if (recurringMeeting == null)
                    return RecurringMeetingAttendeeModel.None;

                if (recurringMeeting.IsSeriesAttendeesVersion) // V2+
                {
                    // Check if series has any attendees
                    var hasSeriesAttendees = await HasSeriesAttendeesAsync(recurringMeetingId);
                    return hasSeriesAttendees ? RecurringMeetingAttendeeModel.Series : RecurringMeetingAttendeeModel.None;
                }
                else // V1 (Legacy)
                {
                    // Check if occurrences have attendees
                    var occurrenceAttendees = await GetOccurrenceAttendeeIdsOnlyAsync(recurringMeetingId);
                    return occurrenceAttendees.Any() ? RecurringMeetingAttendeeModel.Legacy : RecurringMeetingAttendeeModel.None;
                }
            }
        }

        /// <summary>
        /// Gets comprehensive attendance statistics based on version
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <returns>Version-aware attendance statistics</returns>
        public async Task<VersionBasedAttendanceStatistics> GetVersionBasedAttendanceStatisticsAsync(Guid recurringMeetingId)
        {
            using (var context = new AtlasModelCore())
            {
                var recurringMeeting = await context.RecurringMeeting
                    .FirstOrDefaultAsync(rm => rm.recurringMeetingId == recurringMeetingId);

                var seriesCount = await dbSet.CountAsync(x => x.recurringMeetingId == recurringMeetingId);
                var occurrenceAttendees = await GetOccurrenceAttendeeIdsOnlyAsync(recurringMeetingId);
                var versionBasedAttendees = await GetVersionBasedAttendeeIdsAsync(recurringMeetingId);

                return new VersionBasedAttendanceStatistics
                {
                    AttendeeVersion = recurringMeeting?.attendeeVersion ?? 1,
                    SeriesAttendeesCount = seriesCount,
                    OccurrenceAttendeesCount = occurrenceAttendees.Count,
                    EffectiveAttendeesCount = versionBasedAttendees.Count,
                    ModelType = await GetVersionBasedModelTypeAsync(recurringMeetingId),
                    IsSeriesVersion = recurringMeeting?.IsSeriesAttendeesVersion ?? false,
                    IsLegacyVersion = recurringMeeting?.IsLegacyAttendeesVersion ?? true
                };
            }
        }

        /// <summary>
        /// Upgrades a recurring meeting from V1 to V2 (legacy to series)
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <returns>Upgrade result</returns>
        public async Task<VersionUpgradeResult> UpgradeToSeriesVersionAsync(Guid recurringMeetingId)
        {
            var result = new VersionUpgradeResult();

            using (var context = new AtlasModelCore())
            {
                var recurringMeeting = await context.RecurringMeeting
                    .FirstOrDefaultAsync(rm => rm.recurringMeetingId == recurringMeetingId);

                if (recurringMeeting == null)
                {
                    result.Success = false;
                    result.Message = "Recurring meeting not found";
                    return result;
                }

                if (recurringMeeting.IsSeriesAttendeesVersion)
                {
                    result.Success = true;
                    result.Message = "Already using series version";
                    return result;
                }

                try
                {
                    // Get attendees from occurrences
                    var occurrenceAttendees = await GetOccurrenceAttendeeIdsOnlyAsync(recurringMeetingId);
                    result.AttendeesFoundInOccurrences = occurrenceAttendees.Count;

                    // Migrate to series if there are attendees
                    if (occurrenceAttendees.Any())
                    {
                        var migratedIds = await AddMultipleAttendeesAsync(recurringMeetingId, occurrenceAttendees);
                        result.AttendeesMigratedToSeries = migratedIds.Count;
                    }

                    // Upgrade version
                    recurringMeeting.attendeeVersion = 2;
                    context.Entry(recurringMeeting).Property(x => x.attendeeVersion).IsModified = true;
                    await context.SaveChangesAsync();

                    result.Success = true;
                    result.UpgradedFromVersion = 1;
                    result.UpgradedToVersion = 2;
                    result.Message = $"Upgraded to V2. Migrated {result.AttendeesMigratedToSeries} attendees from occurrences to series.";
                }
                catch (Exception ex)
                {
                    result.Success = false;
                    result.Message = $"Upgrade failed: {ex.Message}";
                }

                return result;
            }
        }

        #endregion

        #region ETAPA 4.2: Performance-Optimized Methods for WorkgroupService Integration

        /// <summary>
        /// ETAPA 4.2 - SIMPLIFIED: Checks attendee model type efficiently for WorkgroupService integration
        /// This method provides optimized model type detection without checking occurrences for performance
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <returns>Model type based on version only (simplified)</returns>
        public async Task<RecurringMeetingAttendeeModel> GetSimplifiedVersionBasedModelTypeAsync(Guid recurringMeetingId)
        {
            // Use existing context with FindAsync for better performance (primary key lookup)
            var recurringMeeting = await _context.RecurringMeeting.FindAsync(recurringMeetingId);

            if (recurringMeeting == null)
                return RecurringMeetingAttendeeModel.None;

            if (recurringMeeting.IsSeriesAttendeesVersion) // V2+
            {
                // For performance, only check if series has any attendees (no occurrence check)
                var hasSeriesAttendees = await dbSet
                    .AnyAsync(x => x.recurringMeetingId == recurringMeetingId);

                return hasSeriesAttendees ? RecurringMeetingAttendeeModel.Series : RecurringMeetingAttendeeModel.None;
            }
            else // V1 (Legacy)
            {
                // For simplified performance, assume Legacy model without checking occurrences
                // This avoids expensive ContentSubscriber queries in WorkgroupService scenarios
                return RecurringMeetingAttendeeModel.Legacy;
            }
        }

        #endregion

        #region Simplified Operations for Performance (ETAPA 4)

        /// <summary>
        /// ETAPA 4 - SIMPLIFIED: Adds attendees without validations for performance
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <param name="userIds">List of user IDs</param>
        /// <returns>Number of attendees added</returns>
        public async Task<int> AddAttendeesSimplifiedAsync(Guid recurringMeetingId, List<int> userIds)
        {
            if (userIds == null || !userIds.Any())
                return 0;

            // Remove duplicates
            userIds = userIds.Distinct().ToList();

            // Get existing attendees to avoid duplicates
            var existingUserIds = await dbSet
                .Where(x => x.recurringMeetingId == recurringMeetingId)
                .Select(x => x.userId)
                .ToListAsync();

            var newUserIds = userIds.Where(id => !existingUserIds.Contains(id)).ToList();

            if (!newUserIds.Any())
                return 0;

            // Add new attendees without validation
            var attendeesToAdd = newUserIds.Select(userId => new RecurringMeetingAttendee
            {
                // recurringMeetingAttendeeId = Guid.NewGuid(),
                recurringMeetingId = recurringMeetingId,
                userId = userId,
                // createdOn = DateTime.UtcNow
            }).ToList();

            dbSet.AddRange(attendeesToAdd);
            return attendeesToAdd.Count;
        }

        /// <summary>
        /// ETAPA 4 - SIMPLIFIED: Updates attendees without validations for performance
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <param name="newUserIds">New list of user IDs</param>
        /// <returns>Simple update result</returns>
        public async Task<SimpleAttendeeUpdateResult> UpdateAttendeesSimplifiedAsync(Guid recurringMeetingId, List<int> newUserIds)
        {
            if (newUserIds == null)
                newUserIds = new List<int>();

            // Remove duplicates
            newUserIds = newUserIds.Distinct().ToList();

            // Get current attendees
            var currentUserIds = await dbSet
                .Where(x => x.recurringMeetingId == recurringMeetingId)
                .Select(x => x.userId)
                .ToListAsync();

            // Calculate differences
            var usersToAdd = newUserIds.Where(id => !currentUserIds.Contains(id)).ToList();
            var usersToRemove = currentUserIds.Where(id => !newUserIds.Contains(id)).ToList();

            var result = new SimpleAttendeeUpdateResult
            {
                AddedCount = 0,
                RemovedCount = 0
            };

            // Remove attendees
            if (usersToRemove.Any())
            {
                var attendeesToRemove = await dbSet
                    .Where(x => x.recurringMeetingId == recurringMeetingId && usersToRemove.Contains(x.userId))
                    .ToListAsync();

                dbSet.RemoveRange(attendeesToRemove);
                result.RemovedCount = attendeesToRemove.Count;
            }

            // Add new attendees
            if (usersToAdd.Any())
            {
                var attendeesToAdd = usersToAdd.Select(userId => new RecurringMeetingAttendee
                {
                    recurringMeetingAttendeeId = Guid.NewGuid(),
                    recurringMeetingId = recurringMeetingId,
                    userId = userId,
                    createdOn = DateTime.UtcNow
                }).ToList();

                dbSet.AddRange(attendeesToAdd);
                result.AddedCount = attendeesToAdd.Count;
            }

            return result;
        }

        /// <summary>
        /// ETAPA 4 - SIMPLIFIED: Migrates attendees from original to new recurring meeting without validations
        /// </summary>
        /// <param name="originalRecurringMeetingId">Original recurring meeting ID</param>
        /// <param name="newRecurringMeetingId">New recurring meeting ID</param>
        /// <param name="attendeeUserIds">Attendee user IDs to migrate (if empty, copies from original)</param>
        /// <returns>Number of attendees migrated</returns>
        public async Task<int> MigrateAttendeesSimplifiedAsync(Guid originalRecurringMeetingId, Guid newRecurringMeetingId, List<int> attendeeUserIds = null)
        {
            // If no specific attendees provided, get from original
            if (attendeeUserIds == null || !attendeeUserIds.Any())
            {
                attendeeUserIds = await dbSet
                    .Where(x => x.recurringMeetingId == originalRecurringMeetingId)
                    .Select(x => x.userId)
                    .ToListAsync();
            }

            if (!attendeeUserIds.Any())
                return 0;

            // Add attendees to new recurring meeting without validation
            var attendeesToAdd = attendeeUserIds.Distinct().Select(userId => new RecurringMeetingAttendee
            {
                recurringMeetingAttendeeId = Guid.NewGuid(),
                recurringMeetingId = newRecurringMeetingId,
                userId = userId,
                createdOn = DateTime.UtcNow
            }).ToList();

            dbSet.AddRange(attendeesToAdd);
            return attendeesToAdd.Count;
        }

        /// <summary>
        /// ETAPA 4 - SIMPLIFIED: Upgrades to V2 and optionally adds attendees
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <param name="attendeeUserIds">Optional attendee user IDs to add during upgrade</param>
        /// <returns>True if upgraded successfully</returns>
        public async Task<bool> UpgradeToV2SimplifiedAsync(Guid recurringMeetingId, List<int> attendeeUserIds = null)
        {
            using (var context = new AtlasModelCore())
            {
                var recurringMeeting = await context.RecurringMeeting
                    .FirstOrDefaultAsync(rm => rm.recurringMeetingId == recurringMeetingId);

                if (recurringMeeting == null || recurringMeeting.attendeeVersion >= 2)
                    return false;

                // Upgrade version
                recurringMeeting.attendeeVersion = 2;
                await context.SaveChangesAsync();
            }

            if (attendeeUserIds != null && attendeeUserIds.Any())
            {
                var addedCount = await AddAttendeesSimplifiedAsync(recurringMeetingId, attendeeUserIds);
                await CommitAsync();
            }

            return true;
        }

        #endregion
    }

    #region Helper Classes

    /// <summary>
    /// Result of an attendee update operation with statistics
    /// </summary>
    public class AttendeeUpdateResult
    {
        public int AddedCount { get; set; }
        public int RemovedCount { get; set; }
        public List<int> AddedUserIds { get; set; } = new List<int>();
        public List<int> RemovedUserIds { get; set; } = new List<int>();
    }

    /// <summary>
    /// Result of a simplified attendee update operation
    /// </summary>
    public class SimpleAttendeeUpdateResult
    {
        public int AddedCount { get; set; }
        public int RemovedCount { get; set; }
    }

    /// <summary>
    /// Enum representing different attendee models
    /// </summary>
    public enum RecurringMeetingAttendeeModel
    {
        None,       // No attendees defined
        Legacy,     // Only occurrence-based attendees (old model)
        Series,     // Only series-based attendees (new model)
        Hybrid      // Both series and occurrence attendees (deprecated)
    }

    /// <summary>
    /// Statistics about attendees in a recurring meeting (legacy format)
    /// </summary>
    [Obsolete("Use VersionBasedAttendanceStatistics for version-aware statistics")]
    public class AttendanceStatistics
    {
        public int SeriesAttendeesCount { get; set; }
        public int OccurrenceAttendeesCount { get; set; }
        public int TotalUniqueAttendeesCount { get; set; }
        public RecurringMeetingAttendeeModel ModelType { get; set; }
    }

    /// <summary>
    /// Version-aware statistics about attendees in a recurring meeting
    /// </summary>
    public class VersionBasedAttendanceStatistics
    {
        public int AttendeeVersion { get; set; }
        public int SeriesAttendeesCount { get; set; }
        public int OccurrenceAttendeesCount { get; set; }
        public int EffectiveAttendeesCount { get; set; }
        public RecurringMeetingAttendeeModel ModelType { get; set; }
        public bool IsSeriesVersion { get; set; }
        public bool IsLegacyVersion { get; set; }
    }

    /// <summary>
    /// Result of a version upgrade operation
    /// </summary>
    public class VersionUpgradeResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public int UpgradedFromVersion { get; set; }
        public int UpgradedToVersion { get; set; }
        public int AttendeesFoundInOccurrences { get; set; }
        public int AttendeesMigratedToSeries { get; set; }
    }

    #endregion
}