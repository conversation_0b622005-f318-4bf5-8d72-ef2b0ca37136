using Atlas.CrossCutting.AppEnums;
using Atlas.CrossCutting.DTO;
using Atlas.CrossCutting.DTO.Workgroup;
using Atlas.CrossCutting.Models.Responses;
using Atlas.Data.Entities;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using SharpRaven;
using SharpRaven.Data;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Security;
using System.Text;
using System.Threading.Tasks;
using static Atlas.CrossCutting.AppEnums.ContentStatuses;

namespace Atlas.Data.Repository
{
    public class WorkgroupRepository
    {
        private int _currentUser;
        static readonly string _ENVIRONMENT = ConfigurationManager.AppSettings["ENVIRONMENT"];
        private AtlasModelCore _md = new AtlasModelCore();
        private IDbContextTransaction _transaction;
        private const string workgroupDefaultType = "DEFAULT";

        public WorkgroupRepository(int user)
        {
            _currentUser = user;
        }

        public WorkgroupRepository(int user, AtlasModelCore _mdPar, IDbContextTransaction _transaction)
        {
            _md = _mdPar;
            this._transaction = _transaction;
            _currentUser = user;
        }

        public List<User> GetAllUsersFromWorkgroupWithinOperation(int workgroupId)
        {
            var select = (from u in _md.User
                          join w in _md.WorkgroupUser on u.userId equals w.userId
                          where w.workgroupId == workgroupId
                          select u);

            return select.Include(o => o.Client)
                            .Distinct()
                            .ToList();
        }

        //Ambiguous with GetPossibleUsersForWorkgroup
        //Should be reviewed
        public async Task<List<User>> GetPossibleUsersForClient(int id)
        {
            var select = (_md.WorkgroupUser
                .Where(o => o.Workgroup.clientId == id)
                .Include(a => a.User)
                .Select(a => a.User)).Distinct();

            if (select.Count() > 0)
            {
                var x = await select
                    .Include(o => o.Client)
                    .OrderBy(o => o.name)
                    .ToListAsync();

                return x;
            }

            return await System.Threading.Tasks.Task.Run(() =>
            {
                return new UserRepository().GetAdministrators(id);
            });
        }

        public async Task<List<User>> GetPossibleUsersForWorkgroup(int workgroupId)
        {
            var workgroup = _md.Workgroup.Find(workgroupId);



            var select = (from wu in _md.WorkgroupUser
                          join w in _md.Workgroup on wu.workgroupId equals w.workgroupId
                          where w.clientId == workgroup.clientId
                          select wu.User
                          ).Distinct()
                        .Include(o => o.Client)
                        .OrderBy(o => o.name)
                        .ToListAsync();

            return await select;
        }

        public async Task<List<User>> GetAllUsersFromClient()
        {
            var user = _md.User.Find(_currentUser);

            var select = (from u in _md.User
                          join wu in _md.WorkgroupUser on u.userId equals wu.userId
                          join w in _md.Workgroup on wu.workgroupId equals w.workgroupId
                          where w.clientId == user.clientId
                          orderby u.name
                          select u);

            return await select
                            .Include(o => o.Client)
                            .OrderBy(u => u.name)
                            .ToListAsync();
        }

        public async Task<int> Create(Workgroup wk)
        {
            _md.Workgroup.Add(wk);

            await _md.SaveChangesAsync();

            return wk.workgroupId;
        }

        public async Task<WorkgroupTaskList> CreateAsync(WorkgroupTaskList taskList)
        {
            var workgroupTaskList = new WorkgroupTaskList()
            {
                name = taskList.name,
                description = taskList.description,
                itemOrder = taskList.itemOrder,
                workgroupId = taskList.workgroupId,
                bulletColor = taskList.bulletColor
            };

            _md.WorkgroupTaskList.Add(workgroupTaskList);

            if (await _md.SaveChangesAsync() > 0)
            {
                return workgroupTaskList;
            }

            return null;
        }

        public async Task<int> Create(Workgroup wk, List<WorkgroupUser> users)
        {
            using (var dbContextTx = _md.Database.BeginTransaction())
            {
                try
                {
                    _md.Workgroup.Add(wk);
                    _md.SaveChanges();
                    foreach (var usr in users)
                    {
                        usr.workgroupId = wk.workgroupId;
                    }

                    _md.WorkgroupUser.AddRange(users);
                    _md.SaveChanges();

                    //Check if user has role. If not create it

                    foreach (WorkgroupUser wusr in users)
                    {
                        List<UserRole> hasRole = (from ur in _md.UserRole
                                                  where ur.userId == wusr.userId && ur.clientId == wk.clientId
                                                  select ur).ToList();

                        if (!hasRole.Any())
                        {
                            _md.UserRole.Add(new UserRole
                            {
                                userId = wusr.userId,
                                clientId = (from w in _md.Workgroup
                                            where w.workgroupId == wusr.workgroupId
                                            select w.clientId).FirstOrDefault(),
                                roleId = 1002,
                                createUser = this._currentUser,
                                createDate = DateTime.UtcNow
                            });

                            _md.SaveChanges();
                        }
                    }

                    _md.SaveChanges();
                    dbContextTx.Commit();
                }
                catch (DbUpdateException)
                {
                    dbContextTx.Rollback();
                    throw new Exception("ADD_USER_FAILED");
                }
                catch
                {
                    dbContextTx.Rollback();
                }

            }
            return wk.workgroupId;
        }

        public List<User> GetAllUsersFromWorkgroup(int workgroupId)
        {
            var select = (from u in _md.User
                          join w in _md.WorkgroupUser on u.userId equals w.userId
                          where w.workgroupId == workgroupId
                          select u)
                            .Include(o => o.Client)
                            .Include(o => o.UserRole)
                            .ToList();

            return select;
            // tipos de conteúdo
            //.Include(o => o.Company)
        }

        public List<Workgroup> GetWorkgroupsForUser(string type = "", bool withRootType = false, bool fetchArchived = false, DateTime? startDate = null, DateTime? endDate = null)
        {
            var query = from u in _md.User
                        join wu in _md.WorkgroupUser on u.userId equals wu.userId
                        join w in _md.Workgroup on wu.workgroupId equals w.workgroupId
                        join cl in _md.Client on w.clientId equals cl.clientId
                        where wu.userId == _currentUser && w.archived == fetchArchived && (withRootType || !w.type.Equals("ROOT"))
                        && !cl.deleted && !cl.blocked
                        select w;


            if (!string.IsNullOrEmpty(type))
            {
                query = query.Where(p => p.type == type);
            }

            if (startDate.HasValue || endDate.HasValue)
            {
                query = query.Where(w =>
                    _md.Content.Any(c =>
                        c.workgroupId == w.workgroupId &&
                        !c.deleted.HasValue || c.deleted == false &&
                        (!startDate.HasValue || c.createDate >= startDate.Value) &&
                        (!endDate.HasValue || c.createDate <= endDate.Value)
                    ));
            }

            return query
                        .AsNoTracking()
                        .Include(o => o.Client)
                        .Include(o => o.WorkgroupOwner)
                        .Include(o => o.WorkgroupUser)
                        .ThenInclude(a => a.User)
                        .Distinct()
                        .ToList();
        }

        public async Task<List<WorkgroupDTO>> GetWorkgroupsDTOForUserAsync(
            int[] clientIds,
            int[] workgroupIds,
            string searchTerm,
            string type = workgroupDefaultType,
            bool withRootType = false,
            bool fetchArchived = false,
            bool isAdminView = false,
            int? ownerUserId = null)
        {

            if (workgroupIds == null || workgroupIds.Length == 0)
            {
                if (isAdminView && clientIds != null && clientIds.Length > 0)
                {
                    workgroupIds = await GetWokgroupIdsForCurrentAdminAsync(clientIds[0]);
                }
                else
                {
                    workgroupIds = await GetWorkgroupIdsForCurrentUserAsync(clientIds, type, withRootType, fetchArchived, ownerUserId);
                }
            }
            else if (ownerUserId.HasValue)
            {
                workgroupIds = await _md.WorkgroupOwner
                    .Where(wo => workgroupIds.Contains(wo.workgroupId) && wo.userId == ownerUserId.Value)
                    .Select(wo => wo.workgroupId)
                    .ToArrayAsync();
            }

            if (workgroupIds.Length == 0)
            {
                return new List<WorkgroupDTO>();
            }

            var workgroupList = await FetchWorkgroupsDTOByIdsAsync(workgroupIds, type, searchTerm);

            if (workgroupList.Count > 0 && !isAdminView)
            {
                await PopulateNextMeetingDatesAsync(workgroupList);
            }

            return workgroupList;
        }

        private async Task<int[]> GetWokgroupIdsForCurrentAdminAsync(int clientId)
        {
            return await _md.Workgroup.Where(Workgroup => Workgroup.clientId == clientId
            && !Workgroup.type.Equals("ROOT")).Select(wk => wk.workgroupId).ToArrayAsync();
        }

        private async Task<int[]> GetWorkgroupIdsForCurrentUserAsync(
            int[] clientIds,
            string type = workgroupDefaultType,
            bool withRootType = false,
            bool fetchArchived = false,
            int? ownerUserId = null)
        {
            var query = _md.Workgroup.Where(w =>
                w.WorkgroupUser.Any(wu => wu.userId == _currentUser)
                && w.archived == fetchArchived
                && (withRootType || !w.type.Equals("ROOT"))
                && !w.Client.deleted
                && !w.Client.blocked
                && (string.IsNullOrEmpty(type) ? w.type == workgroupDefaultType : w.type == type)
            );

            if (ownerUserId.HasValue)
            {
                query = query.Where(w => w.WorkgroupOwner.Any(wo => wo.userId == ownerUserId.Value));
            }

            if (clientIds != null && clientIds.Length > 0)
            {
                query = query.Where(x => clientIds.Contains(x.Client.clientId));
            }

            return await query
                .Select(x => x.workgroupId)
                .Distinct()
                .ToArrayAsync();
        }

        private async Task<List<WorkgroupDTO>> FetchWorkgroupsDTOByIdsAsync(
            int[] workgroupIds,
            string type,
            string searchTerm)
        {
            var query = _md.Workgroup
                   .AsNoTracking()
                   .Where(workgroup => workgroupIds.Contains(workgroup.workgroupId) && workgroup.type == type);

            var queryResult = await query.Select(w => new WorkgroupDTO
            {
                workgroupId = w.workgroupId,
                name = w.name,
                clientId = w.clientId,
                clientName = w.Client.name,
                clientPlanName = w.Client.planName,
                clientDeleted = w.Client.deleted,
                clientBlocked = w.Client.blocked,
                archived = w.archived,
                workgroupColor = w.bulletColor ?? string.Empty,
                participantsCount = w.WorkgroupUser.Count,
                nextMeeting = w.next_meeting,
                type = w.type,
                WorkgroupUser = w.WorkgroupUser.Select(wu => new WorkgroupUserDTO
                {
                    userId = wu.userId,
                    profilePicture = wu.User.profilePic,
                    name = wu.User.name
                }).OrderBy(u => u.name).ToList().ToList()
            }).ToListAsync();

            if (!string.IsNullOrEmpty(searchTerm))
            {
                queryResult = queryResult.Where(workgroup =>
                    workgroup.name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)
                    ||
                    workgroup.clientName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)
                ).ToList();
            }

            return queryResult;
        }

        private async System.Threading.Tasks.Task PopulateNextMeetingDatesAsync(List<WorkgroupDTO> workgroupList)
        {
            var now = DateTime.UtcNow;

            var workgroupIds = workgroupList.Select(x => x.workgroupId).ToArray();

            var nextMeetings = await _md.Meeting.Where(meeting =>
                workgroupIds.Contains(meeting.Content.workgroupId) &&
                meeting.Content.type == "Meeting" &&
                meeting.Content.deleted != true &&
                meeting.date > now)
            .GroupBy(meeting => meeting.Content.workgroupId)
            .Select(group => new
            {
                WorkgroupId = group.Key,
                NextMeetingDate = group.Min(meeting => meeting.date)
            }
            ).ToListAsync();

            foreach (var workgroup in workgroupList)
            {
                var nextMeeting = nextMeetings.FirstOrDefault(x => x.WorkgroupId == workgroup.workgroupId);
                workgroup.nextMeeting = nextMeeting?.NextMeetingDate;
            }
        }

        public async Task<int[]> GetWorkgroupsIdsForUserAsync(string type = "", bool withRootType = false)
        {
            var workgroups = (from u in _md.User
                              join wu in _md.WorkgroupUser on u.userId equals wu.userId
                              join w in _md.Workgroup on wu.workgroupId equals w.workgroupId
                              join cl in _md.Client on w.clientId equals cl.clientId
                              where wu.userId == _currentUser && (withRootType || !w.type.Equals("ROOT"))
                              && !cl.deleted && !cl.blocked
                              select w);


            if (type != "")
            {
                workgroups = workgroups.Where(p => p.type == type);
            }

            return await workgroups
                .Select(wks => wks.workgroupId)
                .Distinct()
                .ToArrayAsync();
        }

        public int[] GetWorkgroupsIdsForUser(string planName, string type = "", bool withRootType = false)
        {
            var workgroups = (
                from u in _md.User
                join wu in _md.WorkgroupUser on u.userId equals wu.userId
                join w in _md.Workgroup on wu.workgroupId equals w.workgroupId
                join cl in _md.Client on w.clientId equals cl.clientId
                where wu.userId == _currentUser && (withRootType || !w.type.Equals("ROOT")) &&
                    !cl.deleted && !cl.blocked && cl.planName.Equals(planName)
                select w);

            if (type != "")
            {
                workgroups = workgroups.Where(p => p.type == type);
            }

            return workgroups
                .Select(wks => wks.workgroupId)
                .Distinct()
                .ToArray();
        }

        public async Task<IEnumerable<Workgroup>> GetWorkgroupsByClientAsync(int clientId, string type = "", bool hideArchived = true)
        {
            var query = (from w in _md.Workgroup
                         join wu in _md.WorkgroupUser on w.workgroupId equals wu.workgroupId
                         join cl in _md.Client on w.clientId equals cl.clientId
                         where wu.userId == _currentUser && !w.type.Equals("ROOT") && cl.deleted != true && cl.blocked != true && w.clientId == clientId
                         select w);

            if (hideArchived)
            {
                query = query.Where(o => !o.archived);
            }

            if (type != "")
            {
                query = query.Where(p => p.type == type);
            }

            return await query.Include(w => w.Client).Include(w => w.WorkgroupOwner).ToArrayAsync();

        }

        public async Task<Workgroup> GetWorkgroupRootByClientAsync(int clientId)
        {
            var select = (from w in _md.Workgroup
                          where w.clientId.Equals(clientId) && w.type.Equals("ROOT")
                          join wu in _md.WorkgroupUser on _currentUser equals wu.userId
                          where wu.workgroupId == w.workgroupId
                          select w);

            var workgroup = await select.Include(p => p.Client).FirstOrDefaultAsync();

            return workgroup;
        }

        public async Task<Workgroup> GetWorkgroupRootWithMembersByClientAsync(int clientId)
        {
            return await _md.Workgroup.Where(x =>
                x.clientId == clientId
                && x.type == "ROOT"
                && x.WorkgroupUser.Any(wu => wu.userId == _currentUser)
            ).Include(w => w.WorkgroupUser)
            .FirstOrDefaultAsync();
        }

        public async Task<Workgroup> Get(int workgroupId)
        {
            var workgroup = await _md.Workgroup
                .Where(w => w.workgroupId == workgroupId)
                .Where(w => w.WorkgroupUser.Any(wu => wu.userId == _currentUser))
                .Include(w => w.Client)
                .FirstOrDefaultAsync();

            if (workgroup == null)
                return null;

            //S-47 - Client deleted or blocked cant have content accessed
            if (workgroup.Client.deleted || workgroup.Client.blocked)
                throw new SecurityException("Client deleted/blocked");

            await _md.Entry(workgroup)
                .Collection(w => w.WorkgroupUser)
                .Query()
                .Include(wu => wu.User)
                .LoadAsync();

            await _md.Entry(workgroup)
                .Collection(w => w.WorkgroupOwner)
                .Query()
                .Include(wo => wo.User)
                .LoadAsync();

            await _md.Entry(workgroup)
                .Collection(w => w.WorkgroupTaskList)
                .LoadAsync();

            await _md.Entry(workgroup.Client)
                .Collection(c => c.ClientDomainPermission)
                .LoadAsync();

            return workgroup;
        }

        public async Task<Workgroup> GetWorkgroupAsync(int workgroupId)
        {
            var select = (from u in _md.User
                          join wu in _md.WorkgroupUser on u.userId equals wu.userId
                          join w in _md.Workgroup on wu.workgroupId equals w.workgroupId
                          join cl in _md.Client on w.clientId equals cl.clientId
                          where wu.userId == _currentUser && w.workgroupId == workgroupId
                          && !cl.deleted && !cl.blocked
                          select w);


            return await select.Include(w => w.WorkgroupOwner)
                               .Include(w => w.Client)
                               .FirstOrDefaultAsync();
        }

        public WorkgroupTaskList GetTaskListById(int id)
        {
            return _md.WorkgroupTaskList.FirstOrDefault(t => t.workgroupTaskListId == id);
        }

        public async Task<WorkgroupTaskList> GetTaskListByIdAsync(int id)
        {
            return await _md.WorkgroupTaskList.FindAsync(id);
        }

        /// <summary>
        /// Optimized method to check if a workgroup task list (column) is empty of active tasks.
        /// This version uses navigation properties to let EF Core optimize the query.
        /// PERFORMANCE: Uses a single optimized query with navigation properties instead of manual JOINs.
        /// SECURITY: Uses parameterized queries to prevent SQL injection attacks.
        /// </summary>
        /// <param name="workgroupTaskListId">The unique identifier of the workgroup task list to check.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains true if the task list has no active tasks, false otherwise.</returns>
        /// <remarks>
        /// Performance optimizations:
        /// 1. Uses navigation properties - EF Core generates optimal SQL with proper JOINs
        /// 2. Uses EXISTS pattern with AnyAsync() for early termination when tasks are found
        /// 3. Direct comparison without nullable checks for better performance
        /// 4. Filters by Content.deleted to exclude soft-deleted tasks
        /// Security optimizations:
        /// 5. Uses parameterized queries to prevent SQL injection attacks
        /// 6. Validates input parameters before executing queries
        /// Best practices:
        /// 7. Only considers tasks that are not meeting-related (implicit via workgroupTaskListId)
        /// 8. Excludes tasks where Content.type != 'Task' for data integrity
        /// 
        /// Recommended database indexes for optimal performance:
        /// - CREATE NONCLUSTERED INDEX IX_Task_WorkgroupTaskListId ON Task(workgroupTaskListId) WHERE workgroupTaskListId IS NOT NULL;
        /// - CREATE NONCLUSTERED INDEX IX_Content_Type_Deleted ON Content(contentUuid, type, deleted);
        /// </remarks>
        /// <exception cref="ArgumentException">Thrown when workgroupTaskListId is less than or equal to zero.</exception>
        public async Task<bool> WorkgroupTaskListIsEmpty(int workgroupTaskListId)
        {
            // Input validation for security
            if (workgroupTaskListId <= 0)
                throw new ArgumentException("Invalid workgroupTaskListId", nameof(workgroupTaskListId));

            // PERFORMANCE: Direct query with navigation properties - let EF Core optimize the SQL
            // EF Core will generate an efficient EXISTS query with proper JOIN
            var hasActiveTasks = await _md.Task
                .AnyAsync(task => task.workgroupTaskListId.HasValue &&
                                 task.workgroupTaskListId.Value == workgroupTaskListId && // Check HasValue and value equality
                                 task.Content.type == "Task" &&                     // Navigation property access
                                 task.Content.deleted != true);                     // Check non-deleted

            return !hasActiveTasks;
        }

        public async Task<int> SetWorkgroupUsersAsync(IEnumerable<WorkgroupUser> users)
        {
            try
            {
                AtlasModelCore _md = new AtlasModelCore();
                _md.WorkgroupUser.AddRange(users);
                return await _md.SaveChangesAsync();
            }
            catch (DbUpdateException)
            {
                throw new Exception("ADD_USER_FAILED");
            }
        }

        public async Task<int> ReorderTaskLists(IEnumerable<WorkgroupTaskList> taskLists)
        {
            using (var dbContext = new AtlasModelCore())
            {
                foreach (var column in taskLists)
                {
                    var entity = dbContext.WorkgroupTaskList.FirstOrDefault(w => w.workgroupTaskListId == column.workgroupTaskListId);
                    entity.itemOrder = column.itemOrder;
                }

                return await dbContext.SaveChangesAsync();
            }
        }

        /// <summary>
        /// Optimized bulk reordering of task lists using DTOs and parameterized queries.
        /// This version uses DTOs instead of entities and performs bulk operations for better performance.
        /// SECURITY: Uses parameterized queries to prevent SQL injection attacks.
        /// </summary>
        /// <param name="taskListReorders">The collection of task list reorder DTOs with their new order positions.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the number of affected records.</returns>
        /// <exception cref="ArgumentNullException">Thrown when taskListReorders is null.</exception>
        /// <exception cref="ArgumentException">Thrown when taskListReorders is empty.</exception>
        /// <remarks>
        /// Performance optimizations:
        /// 1. Uses DTOs instead of full entities (lighter objects)
        /// 2. Single database transaction for all operations
        /// 3. Bulk update using SQL IN clause with parameterized queries
        /// 4. No entity materialization - direct SQL operations
        /// Security optimizations:
        /// 5. Uses parameterized queries to prevent SQL injection attacks
        /// 6. Input validation to prevent malicious data
        /// </remarks>
        public async Task<int> ReorderTaskListsOptimized(List<WorkgroupTaskListReorderDto> taskListReorders)
        {
            // Input validation for security
            if (taskListReorders == null)
                throw new ArgumentNullException(nameof(taskListReorders));

            if (taskListReorders.Count == 0)
                throw new ArgumentException("At least one task list must be provided for reordering", nameof(taskListReorders));

            using var transaction = await _md.Database.BeginTransactionAsync();
            try
            {
                int totalUpdated = 0;

                // PERFORMANCE: Bulk update using parameterized queries
                foreach (var reorder in taskListReorders)
                {
                    // SECURITY: Use parameterized queries to prevent SQL injection
                    var rowsAffected = await _md.Database.ExecuteSqlRawAsync(
                        "UPDATE WorkgroupTaskList SET itemOrder = @itemOrder WHERE workgroupTaskListId = @taskListId",
                        new SqlParameter("@itemOrder", System.Data.SqlDbType.SmallInt) { Value = reorder.itemOrder },
                        new SqlParameter("@taskListId", System.Data.SqlDbType.Int) { Value = reorder.workgroupTaskListId }
                    );

                    totalUpdated += rowsAffected;
                }

                await transaction.CommitAsync();
                return totalUpdated;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// Optimized bulk reordering and moving of tasks using direct SQL updates.
        /// This version uses a single UPDATE statement with CASE expressions for maximum performance.
        /// SECURITY: Uses parameterized queries to prevent SQL injection attacks.
        /// </summary>
        /// <param name="taskReorders">The collection of task reorder DTOs with their new positions and columns.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the number of affected records.</returns>
        /// <exception cref="ArgumentNullException">Thrown when taskReorders is null.</exception>
        /// <exception cref="ArgumentException">Thrown when taskReorders is empty.</exception>
        public async Task<int> ReorderTasksOptimized(List<TaskReorderDto> taskReorders)
        {
            if (taskReorders == null)
                throw new ArgumentNullException(nameof(taskReorders));

            if (taskReorders.Count == 0)
                throw new ArgumentException("At least one task must be provided for reordering", nameof(taskReorders));

            using var transaction = await _md.Database.BeginTransactionAsync();
            try
            {
                // Build CASE expressions for workgroupTaskListId and taskListOrder
                var taskIds = new List<int>();
                var workgroupCases = new List<string>();
                var orderCases = new List<string>();
                var parameters = new List<object>();
                var paramIndex = 0;

                foreach (var reorder in taskReorders)
                {
                    taskIds.Add(reorder.taskId);

                    if (reorder.workgroupTaskListId.HasValue)
                    {
                        workgroupCases.Add($"WHEN taskId = {{{paramIndex}}} THEN {{{paramIndex + 1}}}");
                        parameters.Add(reorder.taskId);
                        parameters.Add(reorder.workgroupTaskListId.Value);
                        paramIndex += 2;
                    }

                    if (reorder.taskListOrder.HasValue)
                    {
                        orderCases.Add($"WHEN taskId = {{{paramIndex}}} THEN {{{paramIndex + 1}}}");
                        parameters.Add(reorder.taskId);
                        parameters.Add(reorder.taskListOrder.Value);
                        paramIndex += 2;
                    }
                }

                // Build the UPDATE statement
                var updateSql = new StringBuilder("UPDATE Task SET ");
                var updates = new List<string>();

                if (workgroupCases.Any())
                {
                    updates.Add($"workgroupTaskListId = CASE {string.Join(" ", workgroupCases)} ELSE workgroupTaskListId END");
                }

                if (orderCases.Any())
                {
                    updates.Add($"taskListOrder = CASE {string.Join(" ", orderCases)} ELSE taskListOrder END");
                }

                updateSql.Append(string.Join(", ", updates));

                // Add WHERE clause with task IDs
                var taskIdParams = string.Join(", ", Enumerable.Range(paramIndex, taskIds.Count).Select(i => $"{{{i}}}"));
                updateSql.Append($" WHERE taskId IN ({taskIdParams})");
                parameters.AddRange(taskIds.Cast<object>());

                var rowsAffected = await _md.Database.ExecuteSqlRawAsync(updateSql.ToString(), parameters.ToArray());

                await transaction.CommitAsync();
                return rowsAffected;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<int> ReorderOrMoveTasks(List<Entities.Task> tasks)
        {
            using (var dbContext = new AtlasModelCore())
            {
                foreach (var card in tasks)
                {
                    var entity = dbContext.Task.FirstOrDefault(t => t.taskId == card.taskId);
                    entity.workgroupTaskListId = card.workgroupTaskListId;
                    entity.taskListOrder = card.taskListOrder;
                }

                return await dbContext.SaveChangesAsync();
            }
        }

        public void UpdateWorkgroupUsers(Workgroup workgroup, List<WorkgroupUser> users_add, List<WorkgroupUser> users_delete)
        {
            using (IDbContextTransaction dbContextTx = _md.Database.BeginTransaction())
            {
                try
                {
                    foreach (WorkgroupUser wUser in users_delete)
                    {
                        int userToBeDeleted = wUser.userId;
                        int currentWorkgroup = wUser.workgroupId;

                        //Step 1 - Remove all permissions
                        _md.ContentPermission.RemoveRange(from cp in _md.ContentPermission
                                                          join c in _md.Content on cp.contentId equals c.contentId
                                                          join w in _md.Workgroup on c.workgroupId equals w.workgroupId
                                                          where w.workgroupId == currentWorkgroup
                                                          && cp.userId == userToBeDeleted
                                                          && c.type != "Note"
                                                          select cp
                                                          );
                        _md.SaveChanges();

                        //Step 2 - Remove all ownership
                        _md.ContentOwner.RemoveRange(from co in _md.ContentOwner
                                                     join c in _md.Content on co.contentId equals c.contentId
                                                     join w in _md.Workgroup on c.workgroupId equals w.workgroupId
                                                     where w.workgroupId == currentWorkgroup
                                                     && co.userId == userToBeDeleted
                                                     && c.type != "Note"
                                                     select co
                                                     );
                        _md.SaveChanges();

                        //Step 3 - Remove all WorkgroupUser
                        _md.WorkgroupUser.RemoveRange(from wu in _md.WorkgroupUser
                                                      join w in _md.Workgroup on wu.workgroupId equals w.workgroupId
                                                      where w.workgroupId == currentWorkgroup
                                                      && wu.userId == userToBeDeleted
                                                      select wu
                                                      );
                        _md.SaveChanges();
                    }
                    dbContextTx.Commit();
                }
                catch (Exception ex)
                {
                    dbContextTx.Rollback();

                    RavenClient ravenClient = new RavenClient("https://6aa2f8bcac3e41e198dc414d24d19e45:<EMAIL>/162539");
                    SentryEvent sentryEvent = new SentryEvent(new SentryMessage("Remove User From Workgroup - Rollback Operation"));
                    ravenClient.Environment = _ENVIRONMENT;
                    sentryEvent.Extra = new
                    {
                        ex
                    };
                    ravenClient.Capture(sentryEvent);

                }

            }

            try
            {
                _md.WorkgroupUser.AddRange(users_add);
                _md.SaveChanges();
            }
            catch (DbUpdateException)
            {
                throw new Exception("ADD_USER_FAILED");
            }


            //Check if user has role. If not create it

            foreach (WorkgroupUser wusr in users_add)
            {
                List<UserRole> hasRole = (from ur in _md.UserRole
                                          where ur.userId == wusr.userId && ur.clientId == workgroup.clientId
                                          select ur).ToList();

                if (hasRole.Count() == 0)
                {
                    _md.UserRole.Add(new UserRole
                    {
                        userId = wusr.userId,
                        clientId = (from w in _md.Workgroup
                                    where w.workgroupId == wusr.workgroupId
                                    select w.clientId).FirstOrDefault(),
                        roleId = 1002,
                        createUser = this._currentUser,
                        createDate = DateTime.UtcNow
                    });

                    _md.SaveChanges();
                }
            }


            foreach (var item in users_add)
            {
                this.SetUserPermissions(item.workgroupId, item.userId);
                this.SetAddedUserSubscribers(item.workgroupId, item.userId);
            }

            foreach (var item in users_delete)
            {
                this.SetUserPermissions(item.workgroupId, item.userId, true);
            }


        }

        public bool IsOwnerWorkgroupRoot(int clientId)
        {
            return (from wo in _md.WorkgroupOwner
                    join w in _md.Workgroup on wo.workgroupId equals w.workgroupId
                    where wo.userId == _currentUser && w.clientId == clientId
                    select wo).Any();
        }

        //The following method is used to add content permissions to a recently added workgroup user
        //It uses a stored procedure to do so, which as things stands right now, might not be the best solution
        //For instance, if we have a shitload of contents on said workgroup, it might need some time to complete
        //But for now, we will need it in order to avoid errors with the permission granting
        public void SetUserPermissions(int workgroupId, int userId, bool onlyEnforceCurrentRule = false)
        {
            AtlasModelCore _md = new AtlasModelCore();

            // applyRule3 = 1 quando queremos aplicar a regra (usuario adicionado ao workgroup)
            // applyRule3 = 0 quando estamos apenas reforcando/removendo (ex: usuario removido)
            var applyRule3 = onlyEnforceCurrentRule ? 0 : 1;

            _md.Database.ExecuteSqlRaw(
                "sp_ContentPermission_EnforceCurrentRule @workgroupId, @userId, @createUserId, @applyRule3",
                new SqlParameter("@workgroupId", workgroupId),
                new SqlParameter("@userId", userId),
                new SqlParameter("@createUserId", _currentUser),
                new SqlParameter("@applyRule3", applyRule3));

            if (!onlyEnforceCurrentRule)
            {
                var knowledgeDirectoryUuids = _md.Content
                    .Where(c => c.workgroupId == workgroupId
                             && c.type == "KnowledgeDirectory"
                             && !c.ContentPermission.Any(cp => cp.userId == userId))
                    .Select(c => new { c.contentId, c.contentUuid })
                    .ToList();

                if (knowledgeDirectoryUuids.Any())
                {
                    _md.Database.ExecuteSqlRaw(@"
                INSERT INTO ContentPermission (contentId, userId, allowed, createDate, createUser, contentUuid)
                SELECT c.contentId, @userId, 1, GETDATE(), @currentUser, c.contentUuid 
                FROM Content c
                LEFT JOIN ContentPermission cp ON c.contentId = cp.contentId AND cp.userId = @userId
                WHERE c.workgroupId = @workgroupId 
                AND c.Type = 'KnowledgeDirectory'
                AND cp.contentId IS NULL",
                        new SqlParameter("@userId", userId),
                        new SqlParameter("@currentUser", _currentUser),
                        new SqlParameter("@workgroupId", workgroupId)
                    );
                }
            }
        }

        public void SetAddedUserSubscribers(int workgroupId, int userId)
        {
            AtlasModelCore _md = new AtlasModelCore();

            //create subscriber in for user for the specified contents

            var kbArticle = _md.Content.Where(o =>
                                    o.type == ContentTypes.KnowledgeArticle  //is knowledge base
                                && o.workgroupId == workgroupId //current workgroup
                                && o.ContentPermission.Any(a => a.userId == userId)  //have permission
                                && !o.ContentSubscriber.Any(a => a.userId == userId) //dont have subscriber entry
                            ).Select(o => o.contentId).ToList();

            foreach (var item in kbArticle)
            {
                _md.ContentSubscriber.Add(new Entities.ContentSubscriber
                {
                    userId = userId,
                    contentId = item,
                    createDate = DateTime.UtcNow
                });
            }
            _md.SaveChanges();
        }

        public async Task<int> SetArchived(int id, bool value)
        {
            var wk = _md.Workgroup.FirstOrDefault(w => w.workgroupId == id);
            wk.archived = value;

            _md.Workgroup.Update(wk);
            return await _md.SaveChangesAsync();
        }

        public async Task<int> Update(int id, Workgroup update_wk)
        {
            var wk = _md.Workgroup.FirstOrDefault(w => w.workgroupId == id);
            if (!string.IsNullOrEmpty(update_wk.name))
            {
                wk.name = update_wk.name;
            }
            if (!string.IsNullOrEmpty(update_wk.bulletColor))
            {
                wk.bulletColor = update_wk.bulletColor;
            }

            wk.description = update_wk.description;

            _md.Workgroup.Update(wk);
            return await _md.SaveChangesAsync();
        }

        public async Task<int> Update(WorkgroupTaskList orig, WorkgroupTaskList mod)
        {
            if (!string.IsNullOrEmpty(mod.name))
            {
                orig.name = mod.name;
            }
            if (!string.IsNullOrEmpty(mod.description))
            {
                orig.description = mod.description;
            }

            if (!string.IsNullOrEmpty(mod.bulletColor))
            {
                orig.bulletColor = mod.bulletColor;
            }

            orig.itemOrder = mod.itemOrder;

            _md.WorkgroupTaskList.Update(orig);
            return await _md.SaveChangesAsync();
        }

        public async Task<int> UpdateOwners(int workgroupId, int createdUser, List<int> listAdd, List<int> listRemove)
        {
            var usersToAdd = listAdd.Select(userId => new WorkgroupOwner
            {
                userId = userId,
                workgroupId = workgroupId,
                createDate = DateTime.UtcNow,
                createUser = createdUser
            });

            await _md.WorkgroupOwner.AddRangeAsync(usersToAdd);

            var usersToRemove = await _md.WorkgroupOwner
                .Where(x => listRemove.Contains(x.userId) &&
                            x.workgroupId == workgroupId)
                .ToListAsync();

            _md.WorkgroupOwner.RemoveRange(usersToRemove);

            return await _md.SaveChangesAsync();
        }

        public async Task<int> DeleteTaskList(int taskListId)
        {
            var taskList = this.GetTaskListById(taskListId);
            taskList.deleted = true;

            _md.WorkgroupTaskList.Update(taskList);

            var result = _md.Task
                .Include(o => o.Content)
                .Where(o => o.workgroupTaskListId == taskListId && o.meetingId == null && o.Content.type == "Task")
                .Select(o => o.Content)
                .ToList();

            foreach (var item in result)
            {
                item.deleted = true;
                _md.Content.Update(item);
            }

            return await _md.SaveChangesAsync();
        }

        /// <summary>
        /// Optimized version that deletes a task list using the WorkgroupTaskList entity as parameter.
        /// This version avoids an extra database query to retrieve the entity and performs bulk operations for better performance.
        /// SECURITY: Uses parameterized queries to prevent SQL injection attacks.
        /// </summary>
        /// <param name="taskList">The WorkgroupTaskList entity to delete</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the number of affected records.</returns>
        /// <remarks>
        /// Performance optimizations:
        /// 1. No additional query to retrieve the task list entity (already provided as parameter)
        /// 2. Uses ExecuteSqlRaw with parameterized queries for bulk update operations
        /// 3. Single transaction for all operations
        /// 4. Reduced memory usage by avoiding entity materialization for updates
        /// Security optimizations:
        /// 5. Uses SqlParameter objects to prevent SQL injection attacks
        /// 6. Validates input parameters before executing SQL commands
        /// Best practices:
        /// 7. Creates separate SqlParameter instances to avoid state conflicts
        /// </remarks>
        public async Task<int> DeleteTaskListOptimized(WorkgroupTaskList taskList)
        {
            // Input validation for security
            if (taskList == null)
                throw new ArgumentNullException(nameof(taskList));

            if (taskList.workgroupTaskListId <= 0)
                throw new ArgumentException("Invalid workgroupTaskListId", nameof(taskList));

            using var transaction = await _md.Database.BeginTransactionAsync();
            try
            {
                // BEST PRACTICE: Create separate SqlParameter instances for each query
                // This prevents state conflicts and ensures thread safety

                // Mark the task list as deleted using parameterized query
                var taskListUpdated = await _md.Database.ExecuteSqlRawAsync(
                    "UPDATE WorkgroupTaskList SET deleted = 1 WHERE workgroupTaskListId = @workgroupTaskListId",
                    new SqlParameter("@workgroupTaskListId", System.Data.SqlDbType.Int)
                    {
                        Value = taskList.workgroupTaskListId
                    });

                // Mark all associated tasks as deleted using parameterized query
                // NOTE: Using a different parameter name and new instance to avoid conflicts
                var tasksUpdated = await _md.Database.ExecuteSqlRawAsync(@"
                    UPDATE Content 
                    SET deleted = 1 
                    WHERE contentId IN (
                        SELECT c.contentId 
                        FROM Content c
                        INNER JOIN Task t ON c.contentId = t.contentId
                        WHERE t.workgroupTaskListId = @taskListId
                        AND t.meetingId IS NULL 
                        AND c.type = 'Task'
                        AND c.deleted != 1
                    )",
                    new SqlParameter("@taskListId", System.Data.SqlDbType.Int)
                    {
                        Value = taskList.workgroupTaskListId
                    });

                await transaction.CommitAsync();

                // Return the total number of affected records (task list + tasks)
                return taskListUpdated + tasksUpdated;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<bool> IsUserInWorkgroup(int userId, int workgroupId)
        {
            return await _md.WorkgroupUser.Where(o => o.userId == userId && o.workgroupId == workgroupId).AnyAsync();
        }

        public async Task<int> Copy(int id, int[] contentsCopy, string name, string color, string description)
        {
            List<Content> existing_contents = new List<Content>();
            List<Content> new_contents = new List<Content>();

            var oriWorkgropu = await this.Get(id);

            if (contentsCopy == null)
            {
                contentsCopy = new int[0];
            }
            if (contentsCopy.Length == 0)
            {
                // EF Core doesn't support Database.Log, removed
                existing_contents = (await _md.Task
                                        .Include(o => o.Content)
                                        .Include(o => o.Content.ContentPermission)
                                        .Include(o => o.Content.ContentSubscriber)
                                        .Where(o => o.Content.workgroupId == id && o.Content.deleted == false && o.Content.type == "Task").ToListAsync()).Select(o => o.Content).ToList();
            }
            else
            {
                existing_contents = (await _md.Task
                                        .Include(o => o.Content)
                                        .Include(o => o.Content.ContentPermission)
                                        .Include(o => o.Content.ContentSubscriber)
                                        .Where(o => contentsCopy.Contains(o.contentId) && o.Content.workgroupId == id && o.Content.deleted == false && o.Content.type == "Task").ToListAsync()).Select(o => o.Content).ToList();

            }

            //new workgroup
            var inserted = new Workgroup()
            {
                name = name,
                bulletColor = color,
                clientId = oriWorkgropu.clientId,
                archived = false,
                type = oriWorkgropu.type,
                description = oriWorkgropu.description
            };

            _md.Workgroup.Add(inserted);

            await _md.SaveChangesAsync();


            //workgroup users
            _md.WorkgroupUser.AddRange(oriWorkgropu.WorkgroupUser.Select(o => new WorkgroupUser()
            {
                workgroupId = inserted.workgroupId,
                userId = o.userId,
                //WorkgroupUserRole = o.WorkgroupUserRole.Select(a => new WorkgroupUserRole() { roleId = a.roleId, createDate = DateTime.UtcNow }).ToList() 
            })
                );

            await _md.SaveChangesAsync();

            //workgroup owners
            _md.WorkgroupOwner.AddRange(oriWorkgropu.WorkgroupOwner.Select(o => new WorkgroupOwner() { workgroupId = inserted.workgroupId, userId = o.userId, createDate = DateTime.UtcNow, createUser = _currentUser }));
            await _md.SaveChangesAsync();


            Dictionary<int, int> columnsDictionary = new Dictionary<int, int>();

            foreach (var column in oriWorkgropu.WorkgroupTaskList)
            {

                var cloned = new WorkgroupTaskList()
                {
                    name = column.name,
                    description = column.description,
                    itemOrder = column.itemOrder,
                    deleted = column.deleted,
                    workgroupId = inserted.workgroupId
                };

                if (_md.SaveChanges() > 0)
                {
                    columnsDictionary.Add(column.workgroupTaskListId, cloned.workgroupTaskListId);
                }
            }

            //create new
            foreach (var item in existing_contents)
            {
                var item_task = item.Task.First();
                var new_content = new Content()
                {
                    type = item.type,
                    assignedUser = item.assignedUser,
                    createDate = DateTime.UtcNow,
                    workgroupId = inserted.workgroupId,
                    contentKey = Guid.NewGuid(),
                    createUser = _currentUser,
                    status = "OPEN",
                    deleted = false,
                    tags = item.tags,
                    title = item.title

                };

                bool result = columnsDictionary.TryGetValue(item_task.workgroupTaskListId.Value, out int columnValue);

                new_content.Task.Add(new Entities.Task()
                {
                    description = item_task.description,
                    question = item_task.question,
                    dueDate = item_task.dueDate,
                    startDate = item_task.startDate,
                    taskListOrder = item_task.taskListOrder,
                    workgroupTaskListId = result ? columnValue : null
                });

                foreach (var perm in item.ContentPermission)
                {
                    new_content.ContentPermission.Add(new ContentPermission() { userId = perm.userId, createDate = DateTime.UtcNow, createUser = _currentUser, allowed = true });
                }

                foreach (var subs in item.ContentSubscriber)
                {
                    new_content.ContentSubscriber.Add(new ContentSubscriber() { userId = subs.userId, createDate = DateTime.UtcNow });
                }

                _md.Content.Add(new_content);
            }

            await _md.SaveChangesAsync();

            return inserted.workgroupId;
        }

        public List<Content> GetContentsByTypeFromWorkgroup(string type, int workgroupId)
        {
            if (!_md.WorkgroupUser.Where(wu => wu.userId == _currentUser && wu.workgroupId == workgroupId).Any())
            {
                throw new SecurityException("NOT ALLOWED.");
            }
            return _md.Content.Where(c => c.type == type && c.workgroupId == workgroupId).Include(c => c.Meeting).ToList();
        }

        public List<Content> GetWorkgroupContents(int workgroupId)
        {
            var contentsWorkgroup = _md.Content.Where(c => c.workgroupId == workgroupId).Include(o => o.ContentAttachment).ToList();

            return contentsWorkgroup;
        }

        public Content GetLastMeeting(int workgroupId)
        {
            if (!_md.WorkgroupUser.Where(wu => wu.userId == _currentUser && wu.workgroupId == workgroupId).Any())
            {
                throw new SecurityException("NOT ALLOWED.");
            }

            var currentWorkgroup = _md.Workgroup.Include(o => o.Client).Where(o => o.workgroupId == workgroupId).First();

            //S-47 - Client deleted or blocked cant have content accessed
            if (currentWorkgroup.Client.deleted || currentWorkgroup.Client.blocked)
            {
                //throw exception to interrrupt the process
                throw new SecurityException("Client deleted/blocked");
            }

            var lastMeeting = _md.Content
                            .Where(c => c.ContentPermission
                                .Select(o => o.userId)
                                .Contains(_currentUser) && c.type == "Meeting" && c.workgroupId == workgroupId)
                            .Include(c => c.Meeting)
                            .OrderByDescending(o => o.contentId)
                            .FirstOrDefault();

            var currentWorkgroupUsers = _md.WorkgroupUser.Where(o => o.workgroupId == workgroupId).ToList();

            if (lastMeeting != null)
            {
                //obtaining list of users with permissions in the last meeting
                var lastMeetingPermissions = _md.ContentPermission.Where(o => o.contentId == lastMeeting.contentId).Select(o => o.userId).ToList();

                //filtering the Subscribers to allow only who have ContentPermission
                lastMeeting.ContentSubscriber =
                    _md.ContentSubscriber
                        .Where(o => o.contentId == lastMeeting.contentId && lastMeetingPermissions.Contains(o.userId) &&
                        currentWorkgroupUsers.Select(a => a.userId).Contains(o.userId)).ToList();

            }

            return lastMeeting;
        }

        public Content GetLastPoll(int workgroupId, string type)
        {
            if (!_md.WorkgroupUser.Where(wu => wu.userId == _currentUser && wu.workgroupId == workgroupId).Any())
            {
                throw new SecurityException("NOT ALLOWED.");
            }

            var currentWorkgroup = _md.Workgroup.Include(o => o.Client).Where(o => o.workgroupId == workgroupId).First();

            //S-47 - Client deleted or blocked cant have content accessed
            if (currentWorkgroup.Client.deleted || currentWorkgroup.Client.blocked)
            {
                //throw exception to interrrupt the process
                throw new SecurityException("Client deleted/blocked");
            }

            Content lastPoll = _md.Content
                .Where(c => c.ContentPermission
                    .Select(o => o.userId)
                    .Contains(_currentUser) && c.type == "Poll" && c.workgroupId == workgroupId)
                .Include(c => c.Poll)
                .Include(c => c.ContentPermission).ThenInclude(cs => cs.User)
                .Include(c => c.ContentSubscriber)
                .Where(c => c.Poll.FirstOrDefault().pollType == type)
                .OrderByDescending(o => o.contentId)
                .FirstOrDefault();

            return lastPoll;


        }

        public List<WorkgroupUser> GetWorkgroupUsers(int workgroupId)
        {
            return _md.WorkgroupUser.Where(w => w.workgroupId == workgroupId).ToList();
        }

        public List<Workgroup> GetClientProjects(int clientId)
        {
            List<Workgroup> projectsList = _md.Workgroup.Where(wk => wk.clientId == clientId && !wk.archived && wk.type == "PROJECT").ToList();

            return projectsList;
        }
        public int? GetFirstAuthorizedUser(int workgroupId)
        {
            int? userId = null;
            AtlasModelCore atlasmodel = new AtlasModelCore();
            var owner = atlasmodel.WorkgroupOwner.Where(o => o.workgroupId == workgroupId).OrderBy(o => o.createDate).FirstOrDefault();

            if (owner != null)
            {
                userId = owner.userId;
            }
            else
            {
                var perm = atlasmodel.WorkgroupUser.Where(o => o.workgroupId == workgroupId).OrderBy(o => o.createDate).FirstOrDefault();
                userId = perm.userId;
            }

            return userId;
        }

        public async Task<IEnumerable<Content>> GetFutureRecurrenceMeetings(int workgroupId)
        {
            var contents = await (
                from c in _md.Content
                join m in _md.Meeting on c.contentId equals m.contentId
                where c.recurringMeetingId.HasValue &&
                    c.status != ContentMeetingStatus.CANCELLED &&
                    c.type == "Meeting" && c.workgroupId == workgroupId &&
                    c.deleted != true && m.date > DateTime.UtcNow
                select c
                )
                .ToListAsync();

            var grouped = contents
                .GroupBy(c => c.recurringMeetingId, (key, g) => g.OrderBy(e => e.icsRecurrenceId)
                .First());

            return grouped;
        }

        public async Task<InsuranceDocument[]> GetWorkgroupInsurances(int[] workgroupIds)
        {
            return await _md.InsuranceDocument
                .Where(insDoc => insDoc.type == "POLICY" && insDoc.deleted != true)
                .Include(insDoc => insDoc.Workgroups)
                .Where(insDoc => insDoc.Workgroups.Any(wks => workgroupIds.Contains(wks.workgroupId)) && insDoc.dateEnd > DateTime.UtcNow)
                .ToArrayAsync();
        }

        public async Task<bool> CheckPermnissionsForWorkgroupAsync(int workgroupId)
        {
            var hasPermissions = await _md.WorkgroupUser
                .AnyAsync(wu => wu.userId == _currentUser && wu.workgroupId == workgroupId &&
                                !wu.Workgroup.Client.deleted && !wu.Workgroup.Client.blocked);

            return hasPermissions;
        }

        public async Task<bool> IsWorkgroupOwner(int workgroupId)
        {
            return await _md.WorkgroupOwner
                .AnyAsync(wo => wo.userId == _currentUser && wo.workgroupId == workgroupId);
        }

        public async Task<bool> HasClientMismatching(int workgroupId, int clientId)
        {
            return !await _md.Workgroup
               .AnyAsync(wo => wo.workgroupId == workgroupId && wo.clientId == clientId);
        }

        /// <summary>
        /// Retrieves a paginated list of users associated with the specified client via workgroups.
        /// If no workgroup users are found, it falls back to retrieving client administrators.
        /// </summary>
        /// <param name="clientId">The client identifier.</param>
        /// <param name="pageNumber">The page number (starting at 1).</param>
        /// <param name="pageSize">The number of records per page.</param>
        /// <returns>A paginated list of users.</returns>
        public async Task<PagedResult<User>> RetrieveAssociatedUsersForClientAsync(int clientId, int pageNumber, int pageSize, string? searchTerm)
        {
            var result = new PagedResult<User>
            {
                CurrentPage = pageNumber,
                PageSize = pageSize
            };

            // Build a lean query for users linked through workgroups (read-only), selecting only distinct users.
            var associatedUsersQuery = _md.WorkgroupUser
                .AsNoTracking()
                .Where(o => o.Workgroup.clientId == clientId)
                .Select(o => o.User)
                .Distinct();

            // Apply search filtering if searchTerm is provided
            associatedUsersQuery = ApplySearchTermFilter(associatedUsersQuery, searchTerm);

            // Execute a count query to get the total number of matching users.
            int totalCount = await associatedUsersQuery.CountAsync();

            if (totalCount > 0)
            {
                result.TotalItems = totalCount;
                result.Items = await associatedUsersQuery
                    .Include(u => u.Client) // Include additional navigation properties as needed.
                    .OrderBy(u => u.name)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();
                return result;
            }

            // If no workgroup users exist, fall back to retrieving client administrators.
            var administrators = new UserRepository().GetAdministrators(clientId);

            // Apply search filtering on the fallback in-memory collection.
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                administrators = administrators.Where(u =>
                    u.name.Contains(searchTerm) || u.email.Contains(searchTerm)).ToList();
            }

            result.TotalItems = administrators.Count;
            result.Items = administrators
                .OrderBy(u => u.name)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToList();
            return result;
        }

        public async Task<PagedResult<UserListInfoDto>> GetClientUsersInfoAsync(int clientId, string? searchTerm, int pageNumber, int pageSize)
        {
            var userRepository = new UserRepository();

            return await userRepository.ListUserInfoAsync(clientId, searchTerm, pageSize, pageNumber);
        }

        public async Task<PagedResult<UserListInfoDto>> GetWorkgroupUsersInfoAsync(int clientId, int workgroupId, string? searchTerm, int pageNumber, int pageSize)
        {
            var userRepository = new UserRepository();

            return await userRepository.GetWorkgroupUsersInfoAsync(clientId, workgroupId, searchTerm, pageSize, pageNumber);
        }

        /// <summary>
        /// If no workgroup users are found, it falls back to retrieving client administrators.
        /// </summary>
        /// <param name="clientId">The client identifier.</param>
        public async Task<List<User>> RetrieveAllAssociatedUsersForClientAsync(int clientId, string? searchTerm)
        {
            // Build the query for users linked through workgroups (read-only)
            var associatedUsersQuery = _md.WorkgroupUser
                .AsNoTracking()
                .Where(o => o.Workgroup.clientId == clientId)
                .Include(o => o.User)
                .Select(o => o.User)
                .Distinct();

            // Apply search filtering if searchTerm is provided.
            associatedUsersQuery = ApplySearchTermFilter(associatedUsersQuery, searchTerm);

            // Check if any associated users exist
            if (await associatedUsersQuery.AnyAsync())
            {
                return await associatedUsersQuery
                    .Include(u => u.Client)
                    .OrderBy(u => u.name)
                    .ToListAsync();
            }

            // If no workgroup users exist, fall back to retrieving client administrators.
            var userRepo = new UserRepository();
            var administrators = userRepo.GetAdministrators(clientId);

            // Apply search filtering on the fallback in-memory collection.
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                administrators = administrators.Where(u =>
                    u.name.Contains(searchTerm) || u.email.Contains(searchTerm)).ToList();
            }

            return administrators;
        }

        private static IQueryable<User> ApplySearchTermFilter(IQueryable<User> query, string? searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return query;

            return query.Where(u =>
                u.name.Contains(searchTerm) || u.email.Contains(searchTerm));
        }

        public async Task<(int activeCount, int archivedCount)> GetWorkgroupsCountAsync(
            int[] clientIds,
            int[] workgroupIds,
            string type = workgroupDefaultType,
            bool withRootType = false,
            bool isAdminView = false,
            int? ownerUserId = null)
        {
            if (isAdminView)
            {
                return (
                    activeCount: 0,
                    archivedCount: 0
                );
            }

            var query = _md.Workgroup.Where(workgroup =>
                workgroup.WorkgroupUser.Any(wu => wu.userId == _currentUser)
                && (withRootType || !workgroup.type.Equals("ROOT"))
                && !workgroup.Client.deleted
                && !workgroup.Client.blocked
                && (string.IsNullOrEmpty(type) || workgroup.type == type)
            );

            if (ownerUserId.HasValue)
            {
                query = query.Where(workgroup =>
                    workgroup.WorkgroupOwner.Any(wo => wo.userId == ownerUserId.Value));
            }

            if (clientIds != null && clientIds.Length > 0)
            {
                query = query.Where(wg => clientIds.Contains(wg.clientId));
            }

            if (workgroupIds != null && workgroupIds.Length > 0)
            {
                query = query.Where(wg => workgroupIds.Contains(wg.workgroupId))
                    .Where(wg => !wg.Client.deleted && !wg.Client.blocked);
            }

            return (
                activeCount: await query.CountAsync(wg => !wg.archived),
                archivedCount: await query.CountAsync(wg => wg.archived)
            );
        }

        public async Task<IEnumerable<string>> GetAadValidDomains(int clientId)
        {
            try
            {
                var client = await _md.Client
                    .AsNoTracking()
                    .Where(c => c.clientId == clientId)
                    .FirstOrDefaultAsync();

                if (client == null || client.useFederatedADConnect != true)
                {
                    return new List<string>();
                }

                var domains = await _md.ClientDomainPermission
                    .AsNoTracking()
                    .Where(cd => cd.clientId == clientId && cd.blocked != true)
                    .Select(cd => cd.domain)
                    .ToListAsync();

                return domains;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Error retrieving AAD domains", ex);
            }
        }

        /// <summary>
        /// Retrieves all non-deleted task lists for a specified workgroup, ordered by their item order.
        /// </summary>
        /// <param name="workgroupId">The unique identifier of the workgroup to retrieve task lists for.</param>
        /// <returns>
        /// A task that represents the asynchronous operation. The task result contains a list of 
        /// <see cref="WorkgroupTaskListDTO"/> objects representing the task lists for the specified workgroup.
        /// Returns an empty list if no task lists are found.
        /// </returns>
        /// <remarks>
        /// This method only returns task lists that are not marked as deleted (deleted = false).
        /// The results are ordered by the itemOrder property in ascending order.
        /// </remarks>
        /// <exception cref="ArgumentException">Thrown when workgroupId is less than or equal to zero.</exception>
        /// <exception cref="InvalidOperationException">Thrown when there are issues with database connectivity or query execution.</exception>
        public async Task<List<WorkgroupTaskListDTO>> GetTaskLists(int workgroupId)
        {
            return await _md.WorkgroupTaskList
                .Where(wtl => wtl.workgroupId == workgroupId && !wtl.deleted)
                .OrderBy(wtl => wtl.itemOrder)
                .Select(wtl => new WorkgroupTaskListDTO
                {
                    workgroupTaskListId = wtl.workgroupTaskListId,
                    name = wtl.name,
                    description = wtl.description,
                    itemOrder = wtl.itemOrder,
                    deleted = wtl.deleted,
                    workgroupId = wtl.workgroupId,
                    bulletColor = wtl.bulletColor
                })
                .ToListAsync();
        }

        public async Task<List<WorkgroupDTO>> GetSimpleWorkgroupsByClientIdAsync(int clientId)
        {
            var workgroups = await _md.Workgroup
                .AsNoTracking()
                .Where(w => w.clientId == clientId && w.type != "ROOT")
                .Select(w => new WorkgroupDTO
                {
                    workgroupId = w.workgroupId,
                    name = w.name,
                    clientId = w.clientId,
                    archived = w.archived,
                    workgroupColor = w.bulletColor ?? string.Empty,
                    type = w.type
                })
                .ToListAsync();

            return workgroups;
        }

        public async Task<List<WorkgroupRecycleBinDTO>> RootWorkgroupsWhereUserIsParticipant(int clientId, int userId)
        {
            // Workgroup Root if the user is a client admin
            string sql = @"
                        SELECT w.workgroupId, w.name, w.type
                        FROM [User] u 
                        INNER JOIN WorkgroupUser wu ON (u.userId = wu.userId)
                        INNER JOIN Workgroup w ON (wu.workgroupId = w.workgroupId)
                        INNER JOIN Client cl ON (w.clientId = cl.clientId)
                        WHERE w.clientId = @clientId AND w.type = 'ROOT' AND w.archived = 0 AND wu.userId = @userId";

            var workgroups = _md.Database.SqlQueryRaw<WorkgroupRecycleBinDTO>(sql,
                new SqlParameter("@clientId", clientId),
                new SqlParameter("@userId", userId)).ToList();

            return workgroups;
        }

        public async Task<List<WorkgroupRecycleBinDTO>> NotRootWorkgroupsWhereUserIsOwner(int clientId, int userId)
        {
            // Boards/projects that current user owns
            string sql = @"
                        SELECT w.workgroupId, w.name, w.type
                        FROM [User] u 
                        INNER JOIN WorkgroupUser wu ON (u.userId = wu.userId)
                        INNER JOIN Workgroup w ON (wu.workgroupId = w.workgroupId)
                        INNER JOIN Client cl ON (w.clientId = cl.clientId)
                        WHERE w.clientId = @clientId AND w.type <> 'ROOT' AND w.archived = 0 AND wu.userId = @userId
                        UNION ALL 
                        SELECT w.workgroupId, w.name, w.type
                        FROM [User] u 
                        INNER JOIN WorkgroupOwner wo ON (u.userId = wo.userId)
                        INNER JOIN Workgroup w ON (wo.workgroupId = w.workgroupId)
                        INNER JOIN Client cl ON (w.clientId = cl.clientId)
                        WHERE w.clientId = @clientId AND w.type <> 'ROOT' AND wo.userId = @userId AND w.archived = 0";

            var workgroups = _md.Database.SqlQueryRaw<WorkgroupRecycleBinDTO>(sql,
                new SqlParameter("@clientId", clientId),
                new SqlParameter("@userId", userId)).ToList();

            return workgroups;
        }
    }
}
