using Atlas.CrossCutting.DTO.Note;
using LiteDB;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Atlas.Data.Repository
{
    public sealed class NoteRepository : ContentRepository
    {
        public NoteRepository(int userId) : base(userId)
        {
        }

        public async Task<(List<NoteListItemDTO> Items, int Total)> GetNoteListAsync(
            int currentUserId,
            ContentRequestFilter filters,
            CancellationToken ct = default)
        {
            var hasWorkgroupFilter = filters.workgroups != null && filters.workgroups.Length > 0;

            var sql = @" SELECT N.noteId AS NoteId,
                                C.contentId AS ContentId,
                                C.contentUuid AS ContentUuid,
                                C.workgroupId AS WorkgroupId,
                                C.rlsClientId AS ClientId,
                                C.title AS Title,
                                N.[text] AS TextPreview,
                                ISNULL(N.lastUpdate, C.lastUpdate) AS LastUpdate,
                                ISNULL(C.[status], N'') AS [Status],
                                COUNT(*) OVER() AS TotalCount
                           FROM [Content] C
                                JOIN [Note] N ON N.contentId = C.contentId
                                JOIN [ContentPermission] CP ON CP.contentId = C.contentId AND CP.userId = @userId
                          WHERE ISNULL(C.deleted,0) <> 1
                            AND C.[type] = N'Note'";

            if (hasWorkgroupFilter)
            {
                sql += " AND C.workgroupId = @workgroupId";
            }

            if (filters.orderBy)
                sql += " ORDER BY N.lastUpdate desc ";
            else
            {
                sql += " ORDER BY N.lastUpdate asc ";
            }

            var pageNumber = filters.pageNumber > 0 ? filters.pageNumber : 1;
            var pageSize = (filters.pageSize > 0 && filters.pageSize <= 200) ? filters.pageSize : 20;
            var offset = (pageNumber - 1) * pageSize;

            if (filters.pageNumber.HasValue && filters.pageSize.HasValue)
            {
                pageSize = filters.pageSize ?? 10;
                pageNumber = filters.pageNumber ?? 1;
                offset = (pageNumber - 1) * pageSize;

                sql += $" OFFSET {offset} ROWS FETCH NEXT {pageSize} ROWS ONLY ";
            }

            var parameters = new List<SqlParameter>
            {
                new SqlParameter("@userId", SqlDbType.Int) { Value = currentUserId }
            };

            if (hasWorkgroupFilter)
            {
                parameters.Add(new SqlParameter("@workgroupId", SqlDbType.Int)
                {
                    Value = filters.workgroups[0]
                });
            }

            var rows = await _md.Database
                .SqlQueryRaw<NoteListItemDTO>(sql, parameters.ToArray())
                .ToListAsync(ct);

            var total = rows.FirstOrDefault()?.TotalCount ?? 0;

            return (rows, total);
        }

        public async Task<NoteDetailDTO> GetNoteByContentUuidAsync(
            int currentUserId,
            Guid contentUuid,
            CancellationToken ct = default)
        {
            const string sql = @" SELECT N.noteId AS NoteId,
                                         C.contentId AS ContentId,
                                         C.contentUuid AS ContentUuid,
                                         C.title AS Title,
                                         N.[text] AS Text,
                                         ISNULL(N.lastUpdate, C.lastUpdate) AS LastUpdate,
                                         ISNULL(C.[status], N'') AS [Status]
                                    FROM [Content] C
                                         JOIN [Note] N ON N.contentId = C.contentId
                                         JOIN [ContentPermission] CP ON CP.contentId = C.contentId AND CP.userId = @userId
                                         JOIN [WorkgroupUser] WU ON WU.workgroupId = C.workgroupId AND WU.userId = @userId
                                         JOIN [Workgroup] W ON W.workgroupId = C.workgroupId
                                         JOIN [Client] CL ON CL.clientId = W.clientId AND CL.deleted = 0 AND CL.blocked = 0
                                   WHERE ISNULL(C.deleted,0) <> 1
                                     AND C.[type] = N'Note'
                                     AND C.contentUuid = @contentUuid";


            var pUser = new SqlParameter("@userId", SqlDbType.Int) { Value = currentUserId };
            var pUuid = new SqlParameter("@contentUuid", SqlDbType.UniqueIdentifier) { Value = contentUuid };

            var dto = await _md.Database.SqlQueryRaw<NoteDetailDTO>(sql, pUser, pUuid)
                                        .FirstOrDefaultAsync(ct);

            return dto;
        }
    }
}
