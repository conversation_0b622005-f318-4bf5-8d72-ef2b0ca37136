using Atlas.CrossCutting.AppEnums;
using Atlas.CrossCutting.DTO.AtlasInsights;
using Atlas.Data.Entities;
using Atlas.Data.Entities.AtlasInsights;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Atlas.Data.Repository
{
    public class InsightsRepository
    {
        // ! DO NOT instantiate the model (db context) directly in the constructor - this repository is long lived throughout the az functions
        // ! the model should be instantiated in the methods where it is needed with 'using' to correctly dispose it

        private static class MaterialViewLogsActivities
        {
            public const string PollReportView = "POLL_REPORT_VIEW";
            public const string AttachmentView = "ATTACHMENT_VIEW";
        }

        public class MeetingMinuteSimple
        {
            public int MinuteId { get; set; }
            public int MeetingId { get; set; }
            public int ContentId { get; set; }
            public bool? Published { get; set; }
            public string MinuteType { get; set; }
        }

        public InsightsRepository() { }

        public async Task<List<Workgroup>> GetWorkgroupId(int[] workgroupIdList)
        {
            using (var db = new AtlasModelCore())
            {
                var result = await db.Workgroup
                    .Where(x => workgroupIdList.Contains(x.workgroupId))
                    .ToListAsync();

                return result;
            }
        }

        public async Task<int[]> GetEnterpriseWorkgroupsByUserId(int userId)
        {
            using (var db = new AtlasModelCore())
            {
                return await db.WorkgroupUser
                    .Where(wu => wu.userId == userId)
                    .Join(db.Workgroup, wu => wu.workgroupId, wg => wg.workgroupId, (wu, wg) => new { wu, wg })
                    .Join(db.Client, temp => temp.wg.clientId, c => c.clientId, (temp, c) => new { temp.wg, c })
                    .Where(x => x.c.planName == "ENTERPRISE"
                        && !x.wg.archived)
                    .Select(x => x.wg.workgroupId)
                    .Distinct()
                    .ToArrayAsync();
            }
        }

        // Counters

        public async Task<InsightsCounterDTO> GetCountersNew(IEnumerable<int> workgroupIdList, DateTime startDate, DateTime endDate)
        {
            var sqlQuery = $@"
                WITH MeetingsData AS (
                    SELECT
                        COUNT(insightsMeetingId) AS totalMeetings,
                        COALESCE(SUM(totalDurationMinutes), 0) AS totalDurationMinutes,
                        COALESCE(SUM(totalAgendas), 0) AS totalAgendas,
                        COALESCE(SUM(totalBlueBookPages), 0) AS totalBlueBookPages
                    FROM [InsightsMeetingV2] WITH(NOLOCK)
                    WHERE workgroupId IN ({string.Join(",", workgroupIdList)})
                    AND meetingDate BETWEEN @startDate AND @endDate
                ),
                ParticipantData AS (
                    SELECT COUNT(DISTINCT userId) as TotalParticipants FROM Content c WITH(NOLOCK)
                    INNER JOIN Meeting m WITH(NOLOCK) ON c.contentId = m.contentId
                    INNER JOIN ContentSubscriber cs WITH(NOLOCK) ON cs.contentId = m.contentId
                    WHERE (c.deleted IS NULL OR c.deleted = 0)
                    AND  workgroupId IN ({string.Join(",", workgroupIdList)})
                    AND m.[date] BETWEEN @startDate AND @endDate
                )
                SELECT 
                    m.totalMeetings,
                    m.totalDurationMinutes,
                    p.TotalParticipants,
                    m.totalAgendas,
                    m.totalBlueBookPages
                FROM MeetingsData m
                CROSS JOIN ParticipantData p
                ";

            var parameters = new SqlParameter[]
            {
                new SqlParameter("@startDate", startDate) { DbType = System.Data.DbType.DateTime },
                new SqlParameter("@endDate", endDate) { DbType = System.Data.DbType.DateTime }
            };

            using (var db = new AtlasModelCore())
            {
                db.Database.SetCommandTimeout(6000);
                var result = await db.Database.SqlQueryRaw<InsightsCounterDTO>(sqlQuery, parameters).FirstOrDefaultAsync();
                return result;
            }
        }

        public async Task<int> GetResolutionCounter(IEnumerable<int> workgroupIdList, DateTime startDate, DateTime endDate)
        {
            var sqlQuery = $@"
                SELECT
                    COUNT(insightsResolutionId) AS totalResolutions
                FROM [InsightsResolutionV2]
                WHERE workgroupId IN ({string.Join(",", workgroupIdList)})
                    AND resolutionDate BETWEEN @startDate AND @endDate";

            var parameters = new SqlParameter[]
            {
                new SqlParameter("@startDate", startDate) { DbType = System.Data.DbType.DateTime },
                new SqlParameter("@endDate", endDate) { DbType = System.Data.DbType.DateTime }
            };

            using (var db = new AtlasModelCore())
            {
                db.Database.SetCommandTimeout(6000);
                var counter = await db.Database.SqlQueryRaw<int>(sqlQuery, parameters).FirstOrDefaultAsync();
                return counter;
            }
        }

        // Reports dashboard

        public async Task<List<ReportORM>> GetReportsNew(IEnumerable<int> workgroupIdList, DateTime startDate, DateTime endDate)
        {
            var sqlQuery = $@"
                SELECT
                    FORMAT(m.MeetingDate, 'MM/yyyy') AS [Period],
                    COUNT(m.ContentId) AS Meetings,
                    SUM(m.TotalAgendas) AS Agendas,
                    SUM(m.TotalDurationMinutes) AS TotalMinutes,
                    SUM(CAST(m.hasPublishedMinute AS INT)) AS MinutesPublished,
                    SUM(CAST(m.hasPendingSignature AS INT)) AS MinutesWaitingSignature,
                    SUM(CAST(m.hasSignedMinute AS INT)) AS MinutesSigned
                FROM [InsightsMeetingV2] m
                WHERE m.WorkgroupId IN ({string.Join(",", workgroupIdList)})
                AND m.MeetingDate BETWEEN @startDate AND @endDate
                GROUP BY FORMAT(m.MeetingDate, 'MM/yyyy')
                ORDER BY MIN(m.MeetingDate) ASC ";

            var parameters = new SqlParameter[]
            {
                new SqlParameter("@startDate", startDate) { DbType = System.Data.DbType.DateTime },
                new SqlParameter("@endDate", endDate) { DbType = System.Data.DbType.DateTime }
            };

            using (var db = new AtlasModelCore())
            {
                db.Database.SetCommandTimeout(6000);
                var queryResult = await db.Database.SqlQueryRaw<ReportORM>(sqlQuery, parameters).ToListAsync();
                return queryResult;
            }
        }

        public async Task<List<PeriodTotalDTO>> GetReportResolutions(IEnumerable<int> workgroupIdList, DateTime startDate, DateTime endDate)
        {
            var sql = $@"
                SELECT
                    CONCAT(RIGHT(CONCAT('0', MONTH(resolutionDate)), 2), '/', YEAR(resolutionDate)) AS Period,
                    COUNT(insightsResolutionId) AS Total
                FROM [InsightsResolutionV2]
                WHERE workgroupId IN ({string.Join(",", workgroupIdList)})
                AND resolutionDate BETWEEN @startDate AND @endDate
                GROUP BY YEAR(resolutionDate), MONTH(resolutionDate)
                ORDER BY YEAR(resolutionDate), MONTH(resolutionDate)";

            var parameters = new SqlParameter[]
            {
                new SqlParameter("@startDate", startDate) { DbType = System.Data.DbType.DateTime },
                new SqlParameter("@endDate", endDate) { DbType = System.Data.DbType.DateTime }
            };

            using (var db = new AtlasModelCore())
            {
                db.Database.SetCommandTimeout(6000);
                var result = await db.Database.SqlQueryRaw<PeriodTotalDTO>(sql, parameters).ToListAsync();
                return result;
            }
        }

        public async Task<List<PeriodTotalDTO>> GetReportCommentsForGraph(IEnumerable<int> workgroupIdList, DateTime startDate, DateTime endDate)
        {
            var sql = $@"
                WITH CommentsWithDates AS (
                    SELECT
                        YEAR(m.meetingDate) AS [year],
                        MONTH(m.meetingDate) AS [month],
                        SUM(c.totalComments) AS totalComments
                    FROM [InsightsCommentsV2] c
                    JOIN [InsightsMeetingV2] m ON c.insightsMeetingId = m.insightsMeetingId
                    JOIN Content Ct ON Ct.contentId = c.parentContentId
                    WHERE c.workgroupId IN ({string.Join(",", workgroupIdList)})
                    AND c.totalComments > 0
                    AND m.meetingDate BETWEEN @startDate AND @endDate
                    AND (ct.deleted IS NULL OR ct.deleted = 0)
                    GROUP BY YEAR(m.meetingDate), MONTH(m.meetingDate)

                    UNION ALL

                    SELECT
                        YEAR(r.resolutionDate) AS [year],
                        MONTH(r.resolutionDate) AS [month],
                        SUM(c.totalComments) AS totalComments
                    FROM [InsightsCommentsV2] c
                    JOIN [InsightsResolutionV2] r ON c.parentContentId = r.contentId
                    JOIN Content Ct ON Ct.contentId = c.parentContentId
                    WHERE c.insightsMeetingId IS NULL
                    AND c.totalComments > 0
                    AND c.workgroupId IN ({string.Join(",", workgroupIdList)})
                    AND r.resolutionDate BETWEEN @startDate AND @endDate
                    AND (ct.deleted IS NULL OR ct.deleted = 0)
                    GROUP BY YEAR(r.resolutionDate), MONTH(r.resolutionDate)
                )
                SELECT
                    CONCAT(RIGHT(CONCAT('0', [month]), 2), '/', [year]) AS Period,
                    SUM(totalComments) AS Total
                FROM CommentsWithDates
                GROUP BY [year], [month]
                ORDER BY [year], [month]";

            var parameters = new SqlParameter[]
            {
                new SqlParameter("@startDate", startDate) { DbType = System.Data.DbType.DateTime },
                new SqlParameter("@endDate", endDate) { DbType = System.Data.DbType.DateTime }
            };

            using (var db = new AtlasModelCore())
            {
                db.Database.SetCommandTimeout(6000);
                var result = await db.Database.SqlQueryRaw<PeriodTotalDTO>(sql, parameters).ToListAsync();
                return result;
            }
        }

        public async Task<List<ReportORM>> GetReportBlueBookViews(IEnumerable<int> workgroupIdList, DateTime startDate, DateTime endDate)
        {
            var sqlQuery = $@"
                SELECT
                    FORMAT(m.MeetingDate, 'MM/yyyy') AS [Period],
                    SUM(bbv.totalViews) AS AllBbViews,
                    COUNT(DISTINCT (CAST(bbv.userId AS BIGINT) << 32 | CAST(bbv.contentId AS BIGINT))) AS SingleBbViews
                FROM [InsightsBlueBookViewsV2] bbv
                JOIN [InsightsMeetingV2] m ON bbv.insightsMeetingId = m.insightsMeetingId
                WHERE m.WorkgroupId IN ({string.Join(",", workgroupIdList)})
                AND m.MeetingDate BETWEEN @startDate AND @endDate
                GROUP BY FORMAT(m.MeetingDate, 'MM/yyyy')
                ORDER BY MIN(m.MeetingDate) ASC ";

            var parameters = new SqlParameter[]
            {
                new SqlParameter("@startDate", startDate) { DbType = System.Data.DbType.DateTime },
                new SqlParameter("@endDate", endDate) { DbType = System.Data.DbType.DateTime }
            };

            using (var db = new AtlasModelCore())
            {
                db.Database.SetCommandTimeout(6000);
                var queryResult = await db.Database.SqlQueryRaw<ReportORM>(sqlQuery, parameters).ToListAsync();
                return queryResult;
            }
        }

        public async Task<List<ReportORM>> GetReportMaterialViews(IEnumerable<int> workgroupIdList, DateTime startDate, DateTime endDate)
        {
            var sqlQuery = $@"
                WITH ViewLogs AS (
                    -- Views linked to meeting agendas
                    SELECT
                        YEAR(m.MeetingDate) AS [Year],
                        MONTH(m.MeetingDate) AS [Month],
                        SUM(mv.totalViews) AS AllMaterialViews,
                        COUNT(DISTINCT (CAST(mv.userId AS BIGINT) << 32 | CAST(mv.attachmentId AS BIGINT))) AS SingleMaterialViews
                    FROM [InsightsMaterialViewLogsV2] mv
                    LEFT JOIN [InsightsResolutionV2] r ON mv.parentContentId = r.contentId
                    JOIN [InsightsMeetingV2] m ON mv.insightsMeetingId = m.insightsMeetingId
                    WHERE m.WorkgroupId IN ({string.Join(",", workgroupIdList)})
                        AND r.contentId IS NULL
                        AND mv.deleted != 1
                        AND m.meetingDate BETWEEN @startDate AND @endDate
                    GROUP BY YEAR(m.MeetingDate), MONTH(m.MeetingDate)

                    UNION ALL

                    -- Views linked to resolutions
                    SELECT
                        YEAR(r.resolutionDate) AS [Year],
                        MONTH(r.resolutionDate) AS [Month],
                        SUM(mv.totalViews) AS AllMaterialViews,
                        COUNT(DISTINCT (CAST(mv.userId AS BIGINT) << 32 | CAST(mv.attachmentId AS BIGINT))) AS SingleMaterialViews
                    FROM [InsightsMaterialViewLogsV2] mv
                    JOIN [InsightsResolutionV2] r ON mv.parentContentId = r.contentId
                    WHERE mv.WorkgroupId IN ({string.Join(",", workgroupIdList)})
                        AND mv.deleted != 1
                        AND r.resolutionDate BETWEEN @startDate AND @endDate
                    GROUP BY YEAR(r.resolutionDate), MONTH(r.resolutionDate)
                )

                SELECT
                    RIGHT('0' + CAST([Month] AS VARCHAR(2)), 2) + '/' + CAST([Year] AS VARCHAR(4)) AS [Period],
                    SUM(AllMaterialViews) AS AllMaterialViews,
                    SUM(SingleMaterialViews) AS SingleMaterialViews
                FROM ViewLogs
                GROUP BY [Year], [Month]
                ORDER BY [Year] ASC, [Month] ASC";

            var parameters = new SqlParameter[]
            {
                new SqlParameter("@startDate", startDate) { DbType = System.Data.DbType.DateTime },
                new SqlParameter("@endDate", endDate) { DbType = System.Data.DbType.DateTime }
            };

            using (var db = new AtlasModelCore())
            {
                db.Database.SetCommandTimeout(6000);
                var queryResult = await db.Database.SqlQueryRaw<ReportORM>(sqlQuery, parameters).ToListAsync();
                return queryResult;
            }
        }

        // Activity processing - Meeting

        public async Task<MeetingInfoDTO> GetMeetingInfo(int meetingContentId)
        {
            var sqlQuery = $@"
            SELECT
                C.contentId,
                C.workgroupId,
                CASE WHEN C.[status] = 'READY' THEN OA_TIME.[time] ELSE M.duration END AS MeetingDuration,
                M.[date] as MeetingDate,
                (COUNT(DISTINCT CS.csubId) + COUNT(DISTINCT CG.contentGuestId)) AS TotalParticipants,
                COUNT(DISTINCT ChildAgenda.contentId) AS TotalChildAgendas
            FROM Content C
                JOIN Meeting M ON M.contentId = C.contentId
                LEFT JOIN ContentSubscriber CS ON CS.contentId = C.contentId
                LEFT JOIN ContentGuest CG ON CG.contentId = C.contentId
                LEFT JOIN Content ChildAgenda ON ChildAgenda.parentContentId = C.contentId AND ChildAgenda.[type] = 'MeetingAgendaItem' AND ChildAgenda.deleted != 1
            OUTER APPLY (
                SELECT
                    SUM([time]) AS [time]
                FROM Content                      AS MAI_C WITH(NOLOCK)
                    INNER JOIN MeetingAgendaItem  AS MAI   WITH(NOLOCK) ON MAI_C.contentId = MAI.contentId
                WHERE MAI_C.parentContentId = C.contentId
                    AND ISNULL(MAI_C.deleted, 0) = 0
            ) AS OA_TIME
            WHERE C.contentId = @contentId AND C.deleted != 1
            GROUP BY C.contentId, C.workgroupId,
            CASE WHEN C.[status] = 'READY' THEN OA_TIME.[time] ELSE M.duration END,
            M.[date]";

            var parameters = new SqlParameter[]
            {
                new SqlParameter("@contentId", meetingContentId) { DbType = System.Data.DbType.Int32 }
            };

            using (var db = new AtlasModelCore())
            {
                db.Database.SetCommandTimeout(6000);
                return await db.Database.SqlQueryRaw<MeetingInfoDTO>(sqlQuery, parameters).FirstOrDefaultAsync();
            }
        }

        public async Task<InsightsMeeting> GetInsightsMeeting(int meetingContentId)
        {
            using (var db = new AtlasModelCore())
            {
                return await db.InsightsMeeting.FirstOrDefaultAsync(im => im.ContentId == meetingContentId);
            }
        }

        public async Task<bool> AddInsightsMeetingAsync(InsightsMeeting insightsMeeting)
        {
            using (var db = new AtlasModelCore())
            {
                db.InsightsMeeting.Add(insightsMeeting);
                var result = await db.SaveChangesAsync();
                return result > 0;
            }
        }

        public async Task<bool> DeleteInsightsMeetingAsync(InsightsMeeting insightsMeeting)
        {
            using (var db = new AtlasModelCore())
            {
                db.InsightsMeeting.Remove(insightsMeeting);

                return await db.SaveChangesAsync() > 0;
            }
        }

        public async Task<bool> UpdateInsightsMeetingAsync(InsightsMeeting insightsMeeting)
        {
            using (var db = new AtlasModelCore())
            {
                db.Entry(insightsMeeting).State = EntityState.Modified;
                var result = await db.SaveChangesAsync();
                return result > 0;
            }
        }

        // Activity processing - Resolution

        public async Task<ResolutionInfoDTO> GetResolutionInfo(int resolutionContentId)
        {
            var sqlQuery = $@"
                            SELECT C.contentId, C.workgroupId, IM.insightsMeetingId, P.dueDate 
                            FROM Content C
                            LEFT JOIN Content PC ON PC.contentId = C.parentContentId
                            LEFT JOIN [InsightsMeetingV2] IM ON IM.contentId = PC.contentId
                            JOIN Poll P ON P.contentId = C.contentId
                            WHERE C.contentId = @contentId 
                              AND ISNULL(C.deleted, 0) != 1 
                              AND ISNULL(PC.deleted, 0) != 1 
                            ";

            var parameters = new SqlParameter[]
                {
                    new SqlParameter("@contentId", resolutionContentId) { DbType = System.Data.DbType.Int32 }
                };

            using (var db = new AtlasModelCore())
            {
                db.Database.SetCommandTimeout(6000);
                return await db.Database.SqlQueryRaw<ResolutionInfoDTO>(sqlQuery, parameters).FirstOrDefaultAsync();
            }
        }

        public async Task<InsightsResolution> GetInsightsResolutionByContentId(int contentId)
        {
            using (var db = new AtlasModelCore())
            {
                var result = await db.InsightsResolution.FirstOrDefaultAsync(i => i.ContentId == contentId);

                return result;
            }
        }

        public async Task<bool> AddInsightsResolutionAsync(InsightsResolution insightsResolution)
        {
            using (var db = new AtlasModelCore())
            {
                db.InsightsResolution.Add(insightsResolution);
                var result = await db.SaveChangesAsync() > 0;
                return result;
            }
        }

        public async Task<bool> UpdateInsightsResolutionAsync(InsightsResolution insightsResolution)
        {
            using (var db = new AtlasModelCore())
            {
                db.Entry(insightsResolution).State = EntityState.Modified;
                var result = await db.SaveChangesAsync() > 0;
                return result;
            }
        }

        public async Task<bool> ResolutionDeleteAsync(InsightsResolution insightsResolution)
        {
            using (var db = new AtlasModelCore())
            {
                db.InsightsResolution.Remove(insightsResolution);

                return await db.SaveChangesAsync() > 0;
            }
        }

        // Activity processing - Comment

        public async Task<Entities.Content> GetContent(int contentId)
        {
            using (var db = new AtlasModelCore())
            {
                var content = await db.Content
                    .AsNoTracking()
                    .Include(c => c.Workgroup.Client)
                    .Where(c => c.contentId == contentId)
                    .FirstOrDefaultAsync();

                return content;
            }
        }

        public async Task<InsightsComments> GetInsightsComment(int workgroupId, int? insightsMeetingId, int parentId)
        {
            using (var db = new AtlasModelCore())
            {
                var query = db.InsightsComments
                    .Where(ic => ic.WorkgroupId == workgroupId && ic.ParentContentId == parentId);

                if (insightsMeetingId.HasValue)
                    query = query.Where(ic => ic.InsightsMeetingId == insightsMeetingId.Value);
                else
                    query = query.Where(ic => ic.InsightsMeetingId == null);

                var insightsComment = await query.FirstOrDefaultAsync();
                return insightsComment;
            }
        }

        public async Task<int> GetContentCommentsCount(int contentId)
        {
            using (var db = new AtlasModelCore())
            {
                var count = await db.ContentComment
                    .Where(cc => cc.contentId == contentId && cc.deleted == false)
                    .CountAsync();

                return count;
            }
        }

        public async Task<bool> AddInsightsContentCommentAsync(InsightsComments insightsComments)
        {
            using (var db = new AtlasModelCore())
            {
                db.InsightsComments.Add(insightsComments);
                var result = await db.SaveChangesAsync();
                return result > 0;
            }
        }

        public async Task<bool> UpdateInsightsContentCommentAsync(InsightsComments insightsComment)
        {
            using (var db = new AtlasModelCore())
            {
                db.Entry(insightsComment).State = EntityState.Modified;
                var result = await db.SaveChangesAsync();
                return result > 0;
            }
        }

        // Activity processing - Bluebook

        public async Task<BluebookInfoDTO> GetInsightsBlueBookInfo(int contentId, int userId)
        {
            //The isnull is for the edge or even impossible case where an insightsBluebook is trying to be generated
            //before the insights for its parent meeting is generated. It might not be necessary in the end.
            var sqlQuery = $@"
                            SELECT C.contentId, C.workgroupId, ISNULL(IM.insightsMeetingId, 0) as insightsMeetingId, CA.activityUser as userId, COUNT(*) as totalViews
                            FROM ContentActivity CA
                            JOIN Content C ON C.contentId = CA.contentId
                            LEFT JOIN [InsightsMeetingV2] IM ON IM.contentId = CA.contentId
                            WHERE CA.[type] = 'BLUEBOOK_VIEW' 
                                and CA.contentId = @contentId
                                and (C.deleted IS NULL OR C.deleted != 1)
                                and CA.activityUser = @userId
                            GROUP BY CA.activityUser, C.contentId, C.workgroupId, IM.insightsMeetingId";

            var parameters = new SqlParameter[]
                {
                    new SqlParameter("@contentId", contentId) { DbType = System.Data.DbType.Int32 },
                    new SqlParameter("@userId", userId) { DbType = System.Data.DbType.Int32 }
                };

            using (var db = new AtlasModelCore())
            {
                db.Database.SetCommandTimeout(6000);
                return await db.Database.SqlQueryRaw<BluebookInfoDTO>(sqlQuery, parameters).FirstOrDefaultAsync();
            }
        }

        public async Task<InsightsBlueBookViews> GetInsightsBlueBookViews(int userId, int contentId)
        {
            using (var db = new AtlasModelCore())
            {
                return await db.InsightsBlueBookViews.FirstOrDefaultAsync(ibbv => ibbv.UserId == userId && ibbv.ContentId == contentId);
            }
        }

        public async Task<bool> AddInsightsBlueBookViews(InsightsBlueBookViews insightsBlueBookViews)
        {
            using (var db = new AtlasModelCore())
            {
                db.InsightsBlueBookViews.Add(insightsBlueBookViews);
                var result = await db.SaveChangesAsync();
                return result > 0;
            }
        }

        public async Task<bool> UpdateInsightsBlueBookViews(InsightsBlueBookViews insightsBlueBookViews)
        {
            using (var db = new AtlasModelCore())
            {
                db.Entry(insightsBlueBookViews).State = EntityState.Modified;
                var result = await db.SaveChangesAsync();
                return result > 0;
            }
        }

        // Activity processing - Material View

        public async Task<MaterialViewLogsInfoDTO> GetInsightsMaterialViewLogsInfo(int contentId, int activityUser, int attachmentId, string contentType = null)
        {
            if (string.IsNullOrEmpty(contentType))
            {
                return new MaterialViewLogsInfoDTO();
            }

            if (contentType.ToUpperInvariant().Equals(ContentTypes.MeetingAgendaItem.ToUpperInvariant()))
            {
                return await FetchMeetingAgendaViewLogs(contentId, activityUser, attachmentId);
            }

            return await FetchResolutionViewLogs(contentId, activityUser, attachmentId);
        }

        public async Task<InsightsMaterialViewLogs> GetInsightsMaterialViewLogs(int activityUserId, int contentId, int attachmentId)
        {
            using (var db = new AtlasModelCore())
            {
                return await db.InsightsMaterialViewLogs
                    .FirstOrDefaultAsync(mvl => mvl.UserId == activityUserId && mvl.ParentContentId == contentId && mvl.AttachmentId == attachmentId);
            }
        }

        public async Task<List<InsightsMaterialViewLogs>> GetInsightsMaterialViewLogsList(int attachmentId, bool fetchDeleted)
        {
            var sqlQuery = $@"
                SELECT *   
                FROM [InsightsMaterialViewLogsV2] MVL
                JOIN [ContentAttachment] CA ON MVL.attachmentId = CA.attachmentId
                WHERE MVL.attachmentId = @attachmentId  
                  AND ISNULL(CA.deleted, 0) = @fetchDeleted";

            var parameters = new SqlParameter[]
            {
                new SqlParameter("@attachmentId", attachmentId) { DbType = System.Data.DbType.Int32 },
                new SqlParameter("@fetchDeleted", fetchDeleted ? 1 : 0) { DbType = System.Data.DbType.Int32 }
            };

            using (var db = new AtlasModelCore())
            {
                return await db.Database
                    .SqlQueryRaw<InsightsMaterialViewLogs>(sqlQuery, parameters)
                    .ToListAsync();
            }
        }

        public async Task<bool> AddInsightsMaterialViewLog(InsightsMaterialViewLogs insightsMaterialViewLogs)
        {
            using (var db = new AtlasModelCore())
            {
                db.InsightsMaterialViewLogs.Add(insightsMaterialViewLogs);
                var result = await db.SaveChangesAsync();
                return result > 0;
            }
        }

        public async Task<bool> UpdateInsightsMaterialViewViews(InsightsMaterialViewLogs insightsMaterialViewLogs)
        {
            using (var db = new AtlasModelCore())
            {
                db.Entry(insightsMaterialViewLogs).State = EntityState.Modified;
                var result = await db.SaveChangesAsync();
                return result > 0;
            }
        }

        public async Task<bool> UpdateBulkInsightsMaterialViewLogs(IEnumerable<InsightsMaterialViewLogs> insightsMaterialViewLogs)
        {
            if (insightsMaterialViewLogs == null || !insightsMaterialViewLogs.Any())
                return false;

            using (var db = new AtlasModelCore())
            {
                foreach (var log in insightsMaterialViewLogs)
                {
                    db.Entry(log).State = EntityState.Modified;
                }

                var result = await db.SaveChangesAsync();

                return result > 0;
            }
        }

        private async Task<MaterialViewLogsInfoDTO> FetchResolutionViewLogs(int contentId, int activityUser, int attachmentId)
        {
            var sqlQuery = $@"
                        SELECT C.contentId, C.workgroupId, IM.insightsMeetingId, COUNT(*) as totalViews, CA.attachmentId
                            FROM ContentActivity CAT
                            JOIN Content C ON C.contentId = CAT.contentId
                            JOIN Poll P ON P.contentId = C.contentId 
                            JOIN ContentAttachment CA ON CA.contentId = CAT.contentId AND CA.attachmentid = @attachmentId
                            LEFT JOIN [InsightsMeetingV2] IM ON IM.contentId = C.parentContentId
                            WHERE CAT.[type] in ('{MaterialViewLogsActivities.AttachmentView}', '{MaterialViewLogsActivities.PollReportView}') 
                                and CAT.contentId = @contentId 
                                and CAT.activityUser = @activityUser 
                                and (C.deleted IS NULL OR C.deleted != 1)
                            GROUP BY C.contentId, C.workgroupId, IM.insightsMeetingId, CA.attachmentId";

            var parameters = new SqlParameter[]
                {
                    new SqlParameter("@contentId", contentId) { DbType = System.Data.DbType.Int32},
                    new SqlParameter("@activityUser", activityUser) { DbType = System.Data.DbType.Int32},
                    new SqlParameter("@attachmentId", attachmentId) { DbType = System.Data.DbType.Int32}
                };

            using (var db = new AtlasModelCore())
            {
                db.Database.SetCommandTimeout(6000);
                return await db.Database.SqlQueryRaw<MaterialViewLogsInfoDTO>(sqlQuery, parameters).FirstOrDefaultAsync();
            }
        }

        private async Task<MaterialViewLogsInfoDTO> FetchMeetingAgendaViewLogs(int contentId, int activityUser, int attachmentId)
        {
            var sqlQuery = $@"
                            SELECT C.contentId, C.workgroupId, IM.insightsMeetingId, COUNT(*) as totalViews, CA.attachmentId
                                FROM ContentActivity CAT
                                JOIN Content C ON C.contentId = CAT.contentId
                                JOIN ContentAttachment CA ON CA.contentId = CAT.contentId AND CA.attachmentId = @attachmentId
                                LEFT JOIN [InsightsMeetingV2] IM ON IM.contentId = C.parentContentId
                                WHERE CAT.[type] in ('{MaterialViewLogsActivities.AttachmentView}')
                                    and CAT.contentId = @contentId 
                                    and CAT.activityUser = @activityUser
                                    and (C.deleted IS NULL OR C.deleted != 1)
                                GROUP BY C.contentId, C.workgroupId, IM.insightsMeetingId, CA.attachmentId";

            var parameters = new SqlParameter[]
                {
                    new SqlParameter("@contentId", contentId) { DbType = System.Data.DbType.Int32},
                    new SqlParameter("@activityUser", activityUser) { DbType = System.Data.DbType.Int32},
                    new SqlParameter("@attachmentId", attachmentId) { DbType = System.Data.DbType.Int32}

                };

            using (var db = new AtlasModelCore())
            {
                db.Database.SetCommandTimeout(6000);
                return await db.Database.SqlQueryRaw<MaterialViewLogsInfoDTO>(sqlQuery, parameters).FirstOrDefaultAsync();
            }
        }

        // Activity processing - Minute/Signature

        public async Task<ContentSignatureRequest> GetMinutesSignatureRequest(int parentContentId)
        {
            var sqlQuery = $@"
                            SELECT CSR.* FROM ContentSignatureRequest CSR
                            JOIN Content C on C.contentId = CSR.contentId
                            JOIN Content PC ON PC.contentId = C.parentContentId
                            JOIN MeetingMinute MM ON MM.contentId = C.contentId
                            JOIN ContentAttachment CA ON CA.contentAttachmentId = CSR.contentAttachmentId
                            WHERE PC.contentId = @parentContentId and PC.deleted != 1 and C.deleted != 1 and MM.published = 1 ORDER BY CSR.contentSignatureRequestId DESC ";

            var parameters = new SqlParameter[]
            {
                new SqlParameter("@parentContentId", parentContentId) {DbType = System.Data.DbType.Int32 }
            };

            using (var db = new AtlasModelCore())
            {
                db.Database.SetCommandTimeout(6000);
                return await db.Database.SqlQueryRaw<ContentSignatureRequest>(sqlQuery, parameters).FirstOrDefaultAsync();
            }
        }

        public async Task<bool> IsMeetingMinutePublished(int contentMeetingId)
        {
            var minute = await GetLatestMeetingMinute(contentMeetingId);

            return (minute?.Published ?? false);
        }

        private async Task<MeetingMinuteSimple> GetLatestMeetingMinute(int contentMeetingId)
        {
            using (var db = new AtlasModelCore())
            {
                var latestMinute = await (
                    from mm in db.MeetingMinute
                    join c in db.Content on mm.contentId equals c.contentId
                    join m in db.Meeting on mm.meetingId equals m.meetingId
                    where c.parentContentId == contentMeetingId && (c.deleted == null || c.deleted == false)
                    orderby mm.minuteId descending
                    select new MeetingMinuteSimple
                    {
                        MinuteId = mm.minuteId,
                        MeetingId = mm.meetingId,
                        ContentId = mm.contentId,
                        Published = mm.published,
                        MinuteType = mm.minuteType
                    })
                    .FirstOrDefaultAsync();

                return latestMinute;
            }
        }

        // Reports file

        public async Task<List<InsightsMeetingDto>> GetReportInsightsMeeting(IEnumerable<int> workgroupIdList, DateTime startDate, DateTime endDate)
        {
            var sqlQuery = $@"
                SELECT
                    M.workgroupId,
                    M.contentId,
                    C.createDate,
                    M.meetingDate,
                    M.totalDurationMinutes as DurationMinutes,
                    M.totalParticipants,
                    C.status,
                    M.hasPublishedMinute,
                    M.hasSignedMinute,
                    M.minuteSignatureType
                FROM [InsightsMeetingV2] M
                JOIN Content C ON M.contentId = C.contentId
                WHERE M.workgroupId IN ({string.Join(",", workgroupIdList)})
                AND meetingDate BETWEEN @startDate AND @endDate
                ORDER BY M.meetingDate ASC";

            var parameters = new SqlParameter[]
            {
                new SqlParameter("@startDate", startDate) { DbType = System.Data.DbType.DateTime },
                new SqlParameter("@endDate", endDate) { DbType = System.Data.DbType.DateTime }
            };

            using (var db = new AtlasModelCore())
            {
                db.Database.SetCommandTimeout(6000);
                var result = await db.Database.SqlQueryRaw<InsightsMeetingDto>(sqlQuery, parameters).ToListAsync();
                return result;
            }
        }

        public async Task<List<InsightsResolutionOrAgendaDto>> GetReportResolutionsAndAgendas(int[] workgroupIdList, DateTime startDate, DateTime endDate)
        {
            var sqlQuery = $@"
                SELECT
                    C.workgroupId,
                    m.contentId AS meetingId,
                    C.[type],
                    C.contentId,
                    C.createDate,
                    C.[status]
                FROM Content C
                JOIN Content PC ON C.parentContentId = PC.contentId
                JOIN [InsightsMeetingV2] m ON PC.contentId = m.contentId
                WHERE
                    C.[type] = 'MeetingAgendaItem'
                    AND ISNULL(C.deleted, 0) = 0
                    AND ISNULL(PC.deleted, 0) = 0
                    AND C.workgroupId IN ({string.Join(",", workgroupIdList)})
                    AND m.meetingDate BETWEEN @startDate AND @endDate

                UNION ALL

                SELECT
                    R.workgroupId,
                    M.contentId as meetingId,
                    C.[type],
                    C.contentId,
                    C.createDate,
                    C.[status]
                FROM [InsightsResolutionV2] R
                JOIN Content C ON R.contentId = C.contentId
                LEFT JOIN [InsightsMeetingV2] M ON R.insightsMeetingId = M.insightsMeetingId
                WHERE
                    ISNULL(C.deleted, 0) = 0
                    AND R.workgroupId IN ({string.Join(",", workgroupIdList)})
                    AND R.resolutionDate BETWEEN @startDate AND @endDate";

            var parameters = new SqlParameter[]
            {
                new SqlParameter("@startDate", startDate) { DbType = System.Data.DbType.DateTime },
                new SqlParameter("@endDate", endDate) { DbType = System.Data.DbType.DateTime }
            };

            using (var db = new AtlasModelCore())
            {
                db.Database.SetCommandTimeout(6000);
                var result = await db.Database.SqlQueryRaw<InsightsResolutionOrAgendaDto>(sqlQuery, parameters).ToListAsync();
                return result;
            }
        }

        public async Task<List<InsightsBlueBookDto>> GetReportBlueBooks(int[] workgroupIdList, DateTime startDate, DateTime endDate)
        {
            var sqlQuery = $@"
                SELECT
                    B.workgroupId,
                    B.insightsMeetingId as meetingId,
                    M.totalBlueBookPages as blueBookPages,
                    SUM(B.totalViews) as totalViews,
                    COUNT(b.totalViews) as uniqueViews
                FROM [InsightsBlueBookViewsV2] B
                JOIN [InsightsMeetingV2] M ON B.insightsMeetingId = M.insightsMeetingId
                WHERE B.workgroupId IN ({string.Join(",", workgroupIdList)})
                AND M.meetingDate BETWEEN @startDate AND @endDate
                GROUP BY B.workgroupId, B.insightsMeetingId, M.totalBlueBookPages
                ORDER BY B.workgroupId, B.insightsMeetingId";

            var parameters = new SqlParameter[]
            {
                new SqlParameter("@startDate", startDate) { DbType = System.Data.DbType.DateTime },
                new SqlParameter("@endDate", endDate) { DbType = System.Data.DbType.DateTime }
            };

            using (var db = new AtlasModelCore())
            {
                db.Database.SetCommandTimeout(6000);
                var result = await db.Database.SqlQueryRaw<InsightsBlueBookDto>(sqlQuery, parameters).ToListAsync();
                return result;
            }
        }

        public async Task<List<InsightsMaterialDto>> GetReportMaterials(int[] workgroupIdList, DateTime startDate, DateTime endDate)
        {
            var sqlQuery = $@"
                WITH ViewLogs AS (
                    SELECT 
                        mv.workgroupId,
                        mv.insightsMeetingId, 
                        SUM(mv.totalViews) AS totalViews,
                        COUNT(DISTINCT (CAST(mv.userId AS BIGINT) << 32 | CAST(mv.attachmentId AS BIGINT))) AS uniqueViews
                    FROM [InsightsMaterialViewLogsV2] mv
                    LEFT JOIN [InsightsResolutionV2] r ON mv.parentContentId = r.contentId
                    JOIN [InsightsMeetingV2] m ON mv.insightsMeetingId = m.insightsMeetingId
                    WHERE mv.workgroupId IN ({string.Join(",", workgroupIdList)})
                    AND r.contentId IS NULL
                    AND m.meetingDate BETWEEN @startDate AND @endDate
                    GROUP BY mv.workgroupId, mv.insightsMeetingId

                    UNION ALL

                    SELECT 
                        mv.workgroupId,
                        r.insightsMeetingId,
                        SUM(mv.totalViews) AS totalViews,
                        COUNT(DISTINCT (CAST(mv.userId AS BIGINT) << 32 | CAST(mv.attachmentId AS BIGINT))) AS uniqueViews
                    FROM [InsightsMaterialViewLogsV2] mv
                    JOIN [InsightsResolutionV2] r ON mv.parentContentId = r.contentId
                    WHERE mv.workgroupId IN ({string.Join(",", workgroupIdList)})
                    AND r.resolutionDate BETWEEN @startDate AND @endDate
                    GROUP BY mv.workgroupId, r.insightsMeetingId
                )

                SELECT 
                    workgroupId,
                    insightsMeetingId as MeetingId,
                    SUM(totalViews) AS TotalViews,
                    SUM(uniqueViews) AS UniqueViews
                FROM ViewLogs
                GROUP BY workgroupId, insightsMeetingId
                ORDER BY workgroupId, insightsMeetingId";

            var parameters = new SqlParameter[]
            {
                new SqlParameter("@startDate", startDate) { DbType = System.Data.DbType.DateTime },
                new SqlParameter("@endDate", endDate) { DbType = System.Data.DbType.DateTime }
            };

            using (var db = new AtlasModelCore())
            {
                db.Database.SetCommandTimeout(6000);
                var result = await db.Database.SqlQueryRaw<InsightsMaterialDto>(sqlQuery, parameters).ToListAsync();
                return result;
            }
        }

        public async Task<List<InsightsCommentDto>> GetReportComments(int[] workgroupIdList, DateTime startDate, DateTime endDate)
        {
            var sqlQuery = $@"
                WITH Comments AS (
                    SELECT 
                        c.workgroupId,
                        c.insightsMeetingId,
                        COUNT(c.insightsCommentId) AS totalCommentsPautas,
                        0 AS totalCommentsResolucoes
                    FROM [InsightsCommentsV2] c
                    LEFT JOIN [InsightsResolutionV2] r ON c.parentContentId = r.contentId
                    JOIN [InsightsMeetingV2] m ON c.insightsMeetingId = m.insightsMeetingId
                    WHERE c.workgroupId IN ({string.Join(",", workgroupIdList)})
                    AND r.contentId IS NULL
                    AND m.meetingDate BETWEEN @startDate AND @endDate
                    GROUP BY c.workgroupId, c.insightsMeetingId

                    UNION ALL
                    SELECT 
                        c.workgroupId,
                        r.insightsMeetingId,
                        0 AS totalCommentsPautas,
                        COUNT(c.insightsCommentId) AS totalCommentsResolucoes
                    FROM [InsightsCommentsV2] c
                    JOIN [InsightsResolutionV2] r ON c.parentContentId = r.contentId
                    WHERE c.workgroupId IN ({string.Join(",", workgroupIdList)})
                    AND r.resolutionDate BETWEEN @startDate AND @endDate
                    GROUP BY c.workgroupId, r.insightsMeetingId
                )

                SELECT 
                    workgroupId,
                    insightsMeetingId as MeetingId,
                    COALESCE(SUM(totalCommentsPautas),0) AS AgendaComments,
                    COALESCE(SUM(totalCommentsResolucoes),0) AS ResolutionComments
                FROM Comments
                GROUP BY workgroupId, insightsMeetingId
                ORDER BY workgroupId, insightsMeetingId";

            var parameters = new SqlParameter[]
            {
                new SqlParameter("@startDate", startDate) { DbType = System.Data.DbType.DateTime },
                new SqlParameter("@endDate", endDate) { DbType = System.Data.DbType.DateTime }
            };

            using (var db = new AtlasModelCore())
            {
                db.Database.SetCommandTimeout(6000);
                var result = await db.Database.SqlQueryRaw<InsightsCommentDto>(sqlQuery, parameters).ToListAsync();
                return result;
            }
        }

        // Entire client/workgroup data processing

        public async Task<int[]> GetWorkgroupList(int clientId, bool isOnlyActiveWorkgroups)
        {
            using (var db = new AtlasModelCore())
            {
                var result = await db.Client
                    .Join(db.Workgroup, c => c.clientId, w => w.clientId, (Client, Workgroup) => new { Client, Workgroup })
                    .Where(x =>
                        x.Client.clientId == clientId &&
                        x.Client.deleted != true &&
                        x.Client.blocked != true &&
                        (!isOnlyActiveWorkgroups || !x.Workgroup.archived)
                    )
                    .Select(x => x.Workgroup.workgroupId)
                    .ToArrayAsync();

                return result;
            }
        }

        public async Task<List<InsightsMeeting>> GetInsightsMeetings(IEnumerable<int> workgroupIdList)
        {
            using (var db = new AtlasModelCore())
            {
                var result = await db.InsightsMeeting
                    .Where(meet => workgroupIdList.Contains(meet.WorkgroupId))
                    .ToListAsync();

                return result;
            }

        }

        private async Task<List<InsightsMeeting>> GetInsightsMeetingAndChildren(IEnumerable<int> workgroupIdList, AtlasModelCore db)
        {
            var result = await db.InsightsMeeting
                    .Include(meet => meet.InsightsBlueBookViews)
                    .Include(meet => meet.InsightsComments)
                    .Include(meet => meet.InsightsMaterialViewLogs)
                    .Include(meet => meet.InsightsResolution)
                .Where(meet => workgroupIdList.Contains(meet.WorkgroupId))
                .ToListAsync();

            return result;
        }

        public async Task<bool> UpdateInsightMeetings(IEnumerable<InsightsMeeting> insightMeetings)
        {
            using (var db = new AtlasModelCore())
            {
                foreach (var meeting in insightMeetings)
                {
                    db.Entry(meeting).State = EntityState.Modified;
                }
                var result = await db.SaveChangesAsync();
                return result > 0;
            }
        }

        public async Task<bool> DeleteInsightsForMeetings(IEnumerable<int> insightMeetingsIds)
        {
            using (var db = new AtlasModelCore())
            using (var tx = db.Database.BeginTransaction())
            {
                var meetings = await db.InsightsMeeting
                    .Include(meet => meet.InsightsBlueBookViews)
                    .Include(meet => meet.InsightsComments)
                    .Include(meet => meet.InsightsMaterialViewLogs)
                    .Include(meet => meet.InsightsResolution)
                    .Where(meet => insightMeetingsIds.Contains(meet.InsightsMeetingId))
                    .ToListAsync();

                db.InsightsMeeting.RemoveRange(meetings);
                db.InsightsComments.RemoveRange(
                    db.InsightsComments.Where(c => insightMeetingsIds.Contains(c.InsightsMeetingId.Value)));
                db.InsightsResolution.RemoveRange(
                    db.InsightsResolution.Where(r => insightMeetingsIds.Contains(r.InsightsMeetingId.Value)));
                db.InsightsBlueBookViews.RemoveRange(
                    db.InsightsBlueBookViews.Where(c => insightMeetingsIds.Contains(c.InsightsMeetingId)));
                db.InsightsMaterialViewLogs.RemoveRange(
                    db.InsightsMaterialViewLogs.Where(l => insightMeetingsIds.Contains(l.InsightsMeetingId.Value)));

                var e = await db.SaveChangesAsync();
                tx.Commit();
                return e > 0;
            }
        }

        public async Task<bool> DeletePreviousReportData(IEnumerable<int> wgIds, string[] resourceFilter = null)
        {
            // (comment, resolution, bbView, matView)
            resourceFilter = resourceFilter ?? new string[0];
            var isDeleteAll = resourceFilter == null || resourceFilter.Length == 0;
            var shouldDeleteComments = isDeleteAll || resourceFilter.Contains("comment");
            var shouldDeleteResolutions = isDeleteAll || resourceFilter.Contains("resolution");
            var shouldDeleteBlueBookViews = isDeleteAll || resourceFilter.Contains("bbView");
            var shouldDeleteMaterialViewLogs = isDeleteAll || resourceFilter.Contains("matView");

            using (var db = new AtlasModelCore())
            using (var tx = db.Database.BeginTransaction())
            {
                var meetings = await GetInsightsMeetingAndChildren(wgIds, db);

                if (isDeleteAll)
                {
                    db.InsightsMeeting.RemoveRange(meetings);
                }

                if (shouldDeleteComments)
                {
                    db.InsightsComments.RemoveRange(
                        db.InsightsComments.Where(c => wgIds.Contains(c.WorkgroupId)));
                }

                if (shouldDeleteResolutions)
                {
                    db.InsightsResolution.RemoveRange(
                        db.InsightsResolution.Where(r => wgIds.Contains(r.WorkgroupId)));
                }

                if (shouldDeleteBlueBookViews)
                {
                    db.InsightsBlueBookViews.RemoveRange(
                        db.InsightsBlueBookViews.Where(c => wgIds.Contains(c.WorkgroupId)));
                }

                if (shouldDeleteMaterialViewLogs)
                {
                    db.InsightsMaterialViewLogs.RemoveRange(
                        db.InsightsMaterialViewLogs.Where(l => wgIds.Contains(l.WorkgroupId)));
                }

                var e = await db.SaveChangesAsync();

                tx.Commit();
                return e > 0;
            }
        }

        public async Task<bool> SaveInsightMeetings(IEnumerable<InsightsMeeting> insightMeetings)
        {
            using (var db = new AtlasModelCore())
            {
                db.InsightsMeeting.AddRange(insightMeetings);
                var result = await db.SaveChangesAsync();
                return result > 0;
            }
        }

        public async Task<bool> SaveInsights(
            IEnumerable<InsightsResolution> resolutions,
            IEnumerable<InsightsComments> comments,
            IEnumerable<InsightsBlueBookViews> bbViews,
            IEnumerable<InsightsMaterialViewLogs> matViews)
        {
            using (var db = new AtlasModelCore())
            using (var tx = db.Database.BeginTransaction())
            {
                if (resolutions != null && resolutions.Any())
                {
                    db.InsightsResolution.AddRange(resolutions);
                }

                if (comments != null && comments.Any())
                {
                    db.InsightsComments.AddRange(comments);
                }

                if (bbViews != null && bbViews.Any())
                {
                    db.InsightsBlueBookViews.AddRange(bbViews);
                }

                if (matViews != null && matViews.Any())
                {
                    db.InsightsMaterialViewLogs.AddRange(matViews);
                }

                await db.SaveChangesAsync().ConfigureAwait(false);
                tx.Commit();
                return true;
            }
        }

        public async Task<List<MeetingInfoHistoricalDataProcessDTO>> GetMeetingInfoForReportGen(IEnumerable<int> workgroupIdList)
        {
            var sqlQuery = $@"
                    WITH BaseContent AS (
                        SELECT contentId, workgroupId, [status]
                        FROM Content
                        WHERE deleted = 0 AND workgroupId IN ({string.Join(",", workgroupIdList)})
                    ),
                    MeetingInfo AS (
                        SELECT C.contentId, [date], duration
                        FROM Meeting M
	                    inner join Content C ON C.contentId=  M.contentId
                        WHERE ISNULL(deleted, 0) = 0 AND workgroupId IN ({string.Join(",", workgroupIdList)})

                    ),
                    AgendaTime AS (
                        SELECT
                            MAI_C.parentContentId AS contentId,
                            SUM(MAI.[time]) AS TotalTime
                        FROM Content MAI_C
                        JOIN MeetingAgendaItem MAI ON MAI_C.contentId = MAI.contentId
                        WHERE ISNULL(MAI_C.deleted, 0) = 0 AND workgroupId IN ({string.Join(",", workgroupIdList)})
                        GROUP BY MAI_C.parentContentId
                    ),
                    Subscribers AS (
                        SELECT CS.contentId, COUNT(DISTINCT csubId) AS TotalSubscribers
                        FROM ContentSubscriber CS
	                    inner join Content C ON C.contentId=  CS.contentId
                        WHERE ISNULL(deleted, 0) = 0 AND workgroupId IN ({string.Join(",", workgroupIdList)})
                        GROUP BY CS.contentId

                    ),
                    Guests AS (
                        SELECT CG.contentId, COUNT(DISTINCT contentGuestId) AS TotalGuests
                        FROM ContentGuest CG
	                    inner join Content C ON C.contentId=  CG.contentId
                        WHERE ISNULL(deleted, 0) = 0 AND workgroupId IN ({string.Join(",", workgroupIdList)})
                        GROUP BY CG.contentId
                    ),
                    ChildAgendas AS (
                        SELECT parentContentId AS contentId, COUNT(DISTINCT contentId) AS TotalAgendas
                        FROM Content
                        WHERE [type] = 'MeetingAgendaItem' AND ISNULL(deleted, 0) = 0 AND workgroupId IN ({string.Join(",", workgroupIdList)})
                        GROUP BY parentContentId
                    ),
                    PublishedMinute AS (
                        SELECT
                            MC.parentContentId AS contentId,
                            MAX(MM.minuteId) AS MinuteId
                        FROM Content MC
                        JOIN MeetingMinute MM ON MM.contentId = MC.contentId
                        WHERE ISNULL(MC.deleted, 0) = 0 AND MM.published = 1 AND workgroupId IN ({string.Join(",", workgroupIdList)})
                        GROUP BY MC.parentContentId
                    ),
                    MinuteSignature AS (
                        SELECT
                            MC.parentContentId AS contentId,
                            CSR.status AS SignatureStatus,
                            CSR.signatureType AS SignatureType,
                            ROW_NUMBER() OVER (PARTITION BY MC.parentContentId ORDER BY CSR.contentSignatureRequestId DESC) AS rn
                        FROM ContentSignatureRequest CSR
                        JOIN Content MC ON MC.contentId = CSR.contentId
                        JOIN MeetingMinute MM ON MM.contentId = MC.contentId
                        WHERE ISNULL(MC.deleted, 0) = 0 AND MM.published = 1 AND workgroupId IN ({string.Join(",", workgroupIdList)})
                    ),
                    BluebookPages AS (
                        SELECT meetingId AS contentId, pageLen
                        FROM (
                            SELECT meetingId, pageLen,
                                   ROW_NUMBER() OVER (PARTITION BY meetingId ORDER BY versionDate DESC) AS rn
                            FROM BlueBookVersion
                        ) AS BB
                        WHERE rn = 1
                    )

                    SELECT
                        C.workgroupId,
                        C.contentId,
                        M.[date] AS MeetingDate,
                        CASE WHEN C.[status] = 'READY' THEN AT.TotalTime ELSE M.duration END AS MeetingDuration,

                        ISNULL(S.TotalSubscribers, 0) + ISNULL(G.TotalGuests, 0) AS TotalParticipants,
                        ISNULL(A.TotalAgendas, 0) AS TotalAgendas,

                        CASE WHEN PM.MinuteId IS NULL THEN CAST(0 AS bit) ELSE CAST(1 AS bit) END AS HasPublishedMinute,

                        CASE WHEN MS.SignatureStatus = 'OPEN' THEN CAST(1 AS bit) ELSE CAST(0 AS bit) END AS HasPendingMinuteSignature,
                        CASE WHEN MS.SignatureStatus = 'CLOSED' THEN CAST(1 AS bit) ELSE CAST(0 AS bit) END AS HasSignedMinute,

                        MS.SignatureType AS MinuteSignatureType,
                        ISNULL(BB.pageLen, 0) AS TotalBluebookPages

                    FROM BaseContent C
                    JOIN MeetingInfo M ON M.contentId = C.contentId
                    LEFT JOIN AgendaTime AT ON AT.contentId = C.contentId
                    LEFT JOIN Subscribers S ON S.contentId = C.contentId
                    LEFT JOIN Guests G ON G.contentId = C.contentId
                    LEFT JOIN ChildAgendas A ON A.contentId = C.contentId
                    LEFT JOIN PublishedMinute PM ON PM.contentId = C.contentId
                    LEFT JOIN MinuteSignature MS ON MS.contentId = C.contentId AND MS.rn = 1
                    LEFT JOIN BluebookPages BB ON BB.contentId = C.contentId";

            using (var db = new AtlasModelCore())
            {
                db.Database.SetCommandTimeout(6000);
                return await db.Database
                                   .SqlQueryRaw<MeetingInfoHistoricalDataProcessDTO>(sqlQuery)
                                   .ToListAsync();
            }
        }

        public async Task<List<ResolutionInfoDTO>> GetResolutionInfoForReportGen(IEnumerable<int> workgroupIdList)
        {
            var sqlQuery = $@"
                SELECT C.contentId, C.workgroupId, IM.insightsMeetingId, P.dueDate 
                FROM Content C
                LEFT JOIN Content PC ON PC.contentId = C.parentContentId
                LEFT JOIN [InsightsMeetingV2] IM ON IM.contentId = PC.contentId
                JOIN Poll P ON P.contentId = C.contentId
                WHERE C.workgroupId IN ({string.Join(",", workgroupIdList)})
                    AND ISNULL(C.deleted, 0) != 1 
                    AND ISNULL(PC.deleted, 0) != 1 ";

            using (var db = new AtlasModelCore())
            {
                db.Database.SetCommandTimeout(6000);
                return await db.Database.SqlQueryRaw<ResolutionInfoDTO>(sqlQuery).ToListAsync();
            }

        }

        public async Task<List<CommentInfoDTO>> GetCommentInfoForReportGen(IEnumerable<int> workgroupIdList)
        {
            var sqlQuery = $@"
                SELECT
                    c.workgroupId,
                    im.insightsMeetingId AS insightsMeetingId,
                    c.contentId AS parentContentId,
                    count(cc.contentCommentId) AS totalComments
                FROM [Content] AS c
                    JOIN [ContentComment] AS cc on c.contentId = cc.contentId and cc.deleted != 1
                    LEFT JOIN [InsightsMeetingV2] AS im ON im.contentId = c.parentContentId
                WHERE c.workgroupId IN ({string.Join(",", workgroupIdList)}) and C.[type] != 'MEETING'
                GROUP BY c.workgroupId, im.insightsMeetingId, c.contentId";

            using (var db = new AtlasModelCore())
            {
                db.Database.SetCommandTimeout(6000);
                return await db.Database.SqlQueryRaw<CommentInfoDTO>(sqlQuery).ToListAsync();
            }
        }

        public async Task<List<BluebookInfoDTO>> GetBlueBookViewsInfoForReportGen(IEnumerable<int> workgroupIdList)
        {
            var sqlQuery = $@"
                SELECT
	                ctt.workgroupId,
	                meet.insightsMeetingId,
	                ctt.contentId,
	                cact.activityUser as userId,
	                count(cact.activityUser) as totalViews
                FROM [ContentActivity] cact
                JOIN [Content] as ctt on cact.contentId = ctt.contentId
                JOIN [InsightsMeetingV2] as meet on ctt.contentId = meet.contentId
                WHERE cact.[type] = 'BLUEBOOK_VIEW'
                AND ctt.WorkgroupId IN ({string.Join(",", workgroupIdList)})
                GROUP BY ctt.workgroupId, ctt.contentId, cact.activityUser, meet.insightsMeetingId ";

            using (var db = new AtlasModelCore())
            {
                db.Database.SetCommandTimeout(6000);
                return await db.Database.SqlQueryRaw<BluebookInfoDTO>(sqlQuery).ToListAsync();
            }
        }

        public async Task<List<MaterialLogsInfoDTO>> GetMaterialViewLogsInfoForReportGen(IEnumerable<int> workgroupIdList)
        {
            var sqlQuery = $@"
                SELECT
                    c.workgroupId,
                    im.insightsMeetingId,
                    ca.activityUser as userId,
                    c.contentId as parentContentId,
                    att.attachmentId,
                    count(ca.activityUser) as totalViews
                FROM [ContentActivity] ca
                    JOIN [Content] as c on ca.contentId = c.contentId
                    JOIN [ContentAttachment] as catt on c.contentId = catt.contentId
                    JOIN [Attachment] as att on catt.attachmentId = att.attachmentId
                    LEFT JOIN [InsightsMeetingV2] as im on c.parentContentId = im.contentId
                WHERE (ca.[type] = 'ATTACHMENT_VIEW' OR ca.[type] = 'POLL_REPORT_VIEW')
                    AND c.WorkgroupId IN ({string.Join(",", workgroupIdList)})
                    AND ca.activityUser IS NOT NULL
                    AND ca.activityUser != ''
                GROUP BY c.workgroupId, im.insightsMeetingId, ca.activityUser, c.contentId, att.attachmentId";

            using (var db = new AtlasModelCore())
            {
                db.Database.SetCommandTimeout(6000);
                return await db.Database.SqlQueryRaw<MaterialLogsInfoDTO>(sqlQuery).ToListAsync();
            }
        }

        public async Task<Dictionary<int, int>> SaveBulkInsightsReportRequests(List<InsightsReportRequest> reportRequests)
        {
            var workgroupToRequestIdMap = new Dictionary<int, int>();

            using (var db = new AtlasModelCore())
            {
                db.AtlasInsightsReportRequest.AddRange(reportRequests);

                await db.SaveChangesAsync();

                foreach (var request in reportRequests)
                {
                    workgroupToRequestIdMap[request.WorkgroupId] = request.RequestId;
                }
            }

            return workgroupToRequestIdMap;
        }

        public async Task<bool> UpdateInsightsReportRequest(InsightsReportRequest reportRequest)
        {
            using (var db = new AtlasModelCore())
            {
                var existingRequest = await db.AtlasInsightsReportRequest
                    .FirstOrDefaultAsync(r => r.RequestId == reportRequest.RequestId);

                if (existingRequest == null)
                    return false;

                // Update only the fields that can change
                existingRequest.Status = reportRequest.Status;
                existingRequest.Processed = reportRequest.Processed;
                existingRequest.ProcessedDate = reportRequest.ProcessedDate;
                existingRequest.ErrorMessage = reportRequest.ErrorMessage;
                existingRequest.HasError = reportRequest.HasError;

                await db.SaveChangesAsync();
                return true;
            }
        }
    }
}