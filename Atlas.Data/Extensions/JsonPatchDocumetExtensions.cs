using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.JsonPatch.Operations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace Atlas.Data.Extensions
{
    /// <summary>
    /// Extensions for JsonPatchDocument to retrieve modified fields using nameof for type safety
    /// </summary>
    public static class JsonPatchDocumentExtensions
    {
        /// <summary>
        /// Gets the modified fields and their new values from JsonPatchDocument operations using type-safe field access
        /// </summary>
        /// <typeparam name="T">The type being patched (must be a class)</typeparam>
        /// <param name="patchDoc">The JsonPatchDocument instance</param>
        /// <returns>Dictionary with property names as keys and new values as values</returns>
        public static Dictionary<string, object> GetModifiedFields<T>(this JsonPatchDocument<T> patchDoc) where T : class
        {
            var modifiedFields = new Dictionary<string, object>();

            if (patchDoc?.Operations == null)
                return modifiedFields;

            foreach (var operation in patchDoc.Operations)
            {
                // Handle different operation types
                switch (operation.OperationType)
                {
                    case OperationType.Replace:
                    case OperationType.Add:
                        // For Replace and Add operations, the value is the new value
                        modifiedFields[GetPropertyNameFromPath<T>(operation.path)] = operation.value;
                        break;

                    case OperationType.Remove:
                        // For Remove operations, mark as null/deleted
                        modifiedFields[GetPropertyNameFromPath<T>(operation.path)] = null;
                        break;

                    case OperationType.Move:
                        // For Move operations, track both source and destination
                        if (!string.IsNullOrEmpty(operation.from))
                        {
                            modifiedFields[GetPropertyNameFromPath<T>(operation.from)] = null; // Source becomes null
                            modifiedFields[GetPropertyNameFromPath<T>(operation.path)] = operation.value; // Destination gets the value
                        }
                        break;

                    case OperationType.Copy:
                        // For Copy operations, only the destination is modified
                        modifiedFields[GetPropertyNameFromPath<T>(operation.path)] = operation.value;
                        break;
                }
            }

            return modifiedFields;
        }

        /// <summary>
        /// Gets only the field names that were modified (using property names from the type)
        /// </summary>
        /// <typeparam name="T">The type being patched (must be a class)</typeparam>
        /// <param name="patchDoc">The JsonPatchDocument instance</param>
        /// <returns>List of property names that were modified</returns>
        public static List<string> GetModifiedFieldNames<T>(this JsonPatchDocument<T> patchDoc) where T : class
        {
            if (patchDoc?.Operations == null)
                return new List<string>();

            var propertyNames = new List<string>();

            foreach (var operation in patchDoc.Operations)
            {
                propertyNames.Add(GetPropertyNameFromPath<T>(operation.path));

                // For Move operations, also include the source path
                if (operation.OperationType == OperationType.Move && !string.IsNullOrEmpty(operation.from))
                {
                    propertyNames.Add(GetPropertyNameFromPath<T>(operation.from));
                }
            }

            return propertyNames.Distinct().ToList();
        }

        /// <summary>
        /// Checks if a specific field was modified using nameof for type safety
        /// </summary>
        /// <typeparam name="T">The type being patched (must be a class)</typeparam>
        /// <param name="patchDoc">The JsonPatchDocument instance</param>
        /// <param name="propertyName">The property name (use nameof(T.PropertyName))</param>
        /// <returns>True if the field was modified, false otherwise</returns>
        public static bool IsFieldModified<T>(this JsonPatchDocument<T> patchDoc, string propertyName) where T : class
        {
            if (patchDoc?.Operations == null || string.IsNullOrEmpty(propertyName))
                return false;

            return patchDoc.Operations.Any(op =>
                GetPropertyNameFromPath<T>(op.path).Equals(propertyName, StringComparison.OrdinalIgnoreCase) ||
                (op.OperationType == OperationType.Move &&
                 GetPropertyNameFromPath<T>(op.from).Equals(propertyName, StringComparison.OrdinalIgnoreCase))
            );
        }

        /// <summary>
        /// Gets the new value for a specific field if it was modified using nameof for type safety
        /// </summary>
        /// <typeparam name="T">The type being patched (must be a class)</typeparam>
        /// <param name="patchDoc">The JsonPatchDocument instance</param>
        /// <param name="propertyName">The property name (use nameof(T.PropertyName))</param>
        /// <returns>The new value if field was modified, null otherwise</returns>
        public static object GetFieldValue<T>(this JsonPatchDocument<T> patchDoc, string propertyName) where T : class
        {
            if (patchDoc?.Operations == null || string.IsNullOrEmpty(propertyName))
                return null;

            var operation = patchDoc.Operations.FirstOrDefault(op =>
                GetPropertyNameFromPath<T>(op.path).Equals(propertyName, StringComparison.OrdinalIgnoreCase));

            return operation?.value;
        }

        /// <summary>
        /// Gets the new value for a specific field with strongly typed return
        /// </summary>
        /// <typeparam name="T">The type being patched (must be a class)</typeparam>
        /// <typeparam name="TValue">The expected type of the value</typeparam>
        /// <param name="patchDoc">The JsonPatchDocument instance</param>
        /// <param name="propertyName">The property name (use nameof(T.PropertyName))</param>
        /// <returns>The new value cast to TValue if field was modified, default(TValue) otherwise</returns>
        public static TValue GetFieldValue<T, TValue>(this JsonPatchDocument<T> patchDoc, string propertyName) where T : class
        {
            var value = GetFieldValue(patchDoc, propertyName);

            if (value == null)
                return default(TValue);

            try
            {
                return (TValue)Convert.ChangeType(value, typeof(TValue));
            }
            catch
            {
                return default(TValue);
            }
        }

        /// <summary>
        /// Gets operations grouped by operation type
        /// </summary>
        /// <typeparam name="T">The type being patched (must be a class)</typeparam>
        /// <param name="patchDoc">The JsonPatchDocument instance</param>
        /// <returns>Dictionary with operation types as keys and lists of operations as values</returns>
        public static Dictionary<OperationType, List<Operation<T>>> GetOperationsByType<T>(this JsonPatchDocument<T> patchDoc) where T : class
        {
            if (patchDoc?.Operations == null)
                return new Dictionary<OperationType, List<Operation<T>>>();

            return patchDoc.Operations
                .GroupBy(op => op.OperationType)
                .ToDictionary(g => g.Key, g => g.ToList());
        }

        /// <summary>
        /// Creates a summary of changes with readable property names
        /// </summary>
        /// <typeparam name="T">The type being patched (must be a class)</typeparam>
        /// <param name="patchDoc">The JsonPatchDocument instance</param>
        /// <returns>Dictionary with change summaries</returns>
        public static Dictionary<string, string> GetChangesSummary<T>(this JsonPatchDocument<T> patchDoc) where T : class
        {
            var summary = new Dictionary<string, string>();

            if (patchDoc?.Operations == null)
                return summary;

            foreach (var operation in patchDoc.Operations)
            {
                var propertyName = GetPropertyNameFromPath<T>(operation.path);
                var operationDesc = operation.OperationType switch
                {
                    OperationType.Add => $"Added: {operation.value}",
                    OperationType.Replace => $"Changed to: {operation.value}",
                    OperationType.Remove => "Removed",
                    OperationType.Move => $"Moved from {GetPropertyNameFromPath<T>(operation.from)} to {propertyName}",
                    OperationType.Copy => $"Copied from {GetPropertyNameFromPath<T>(operation.from)}",
                    _ => $"Unknown operation: {operation.OperationType}"
                };

                summary[propertyName] = operationDesc;
            }

            return summary;
        }

        /// <summary>
        /// Sets or updates a field value in the JsonPatchDocument using Replace operation
        /// </summary>
        /// <typeparam name="T">The type being patched (must be a class)</typeparam>
        /// <param name="patchDoc">The JsonPatchDocument instance</param>
        /// <param name="propertyName">The property name (use nameof(T.PropertyName))</param>
        /// <param name="value">The new value to set</param>
        /// <returns>True if the operation was added/updated successfully</returns>
        public static bool SetFieldValue<T>(this JsonPatchDocument<T> patchDoc, string propertyName, object value) where T : class
        {
            if (patchDoc == null || string.IsNullOrEmpty(propertyName))
                return false;

            // Validate that the property exists on the type
            var property = typeof(T).GetProperty(propertyName, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
            if (property == null)
                return false;

            try
            {
                // Get the correct property name with exact casing
                var correctPropertyName = property.Name;

                // Create the JSON path using the correct property name (camelCase for JSON)
                var jsonPath = $"/{char.ToLowerInvariant(correctPropertyName[0])}{correctPropertyName.Substring(1)}";

                // Remove any existing operations for this property
                var existingOperations = patchDoc.Operations
                    .Where(op => GetPropertyNameFromPath<T>(op.path).Equals(propertyName, StringComparison.OrdinalIgnoreCase))
                    .ToList();

                foreach (var existingOp in existingOperations)
                {
                    patchDoc.Operations.Remove(existingOp);
                }

                // Convert value to the correct type if needed
                object convertedValue = value;
                if (value != null && !property.PropertyType.IsAssignableFrom(value.GetType()))
                {
                    // Handle nullable types
                    var targetType = property.PropertyType;
                    if (targetType.IsGenericType && targetType.GetGenericTypeDefinition() == typeof(Nullable<>))
                    {
                        targetType = Nullable.GetUnderlyingType(targetType);
                    }

                    try
                    {
                        convertedValue = Convert.ChangeType(value, targetType);
                    }
                    catch
                    {
                        // If conversion fails, use null for nullable types or original value
                        if (property.PropertyType.IsGenericType && property.PropertyType.GetGenericTypeDefinition() == typeof(Nullable<>))
                        {
                            convertedValue = null;
                        }
                        else
                        {
                            convertedValue = value;
                        }
                    }
                }

                // Create the operation manually and add it to the operations collection
                var operation = new Operation<T>("replace", jsonPath, from: null, value: convertedValue);
                patchDoc.Operations.Add(operation);

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Sets or updates multiple field values in the JsonPatchDocument
        /// </summary>
        /// <typeparam name="T">The type being patched (must be a class)</typeparam>
        /// <param name="patchDoc">The JsonPatchDocument instance</param>
        /// <param name="fieldValues">Dictionary of property names and their new values</param>
        /// <returns>Dictionary indicating success/failure for each field</returns>
        public static Dictionary<string, bool> SetFieldValues<T>(this JsonPatchDocument<T> patchDoc, Dictionary<string, object> fieldValues) where T : class
        {
            var results = new Dictionary<string, bool>();

            if (patchDoc == null || fieldValues == null)
                return results;

            foreach (var kvp in fieldValues)
            {
                results[kvp.Key] = SetFieldValue(patchDoc, kvp.Key, kvp.Value);
            }

            return results;
        }

        /// <summary>
        /// Converts a JSON path to a property name, handling nested objects
        /// </summary>
        /// <typeparam name="T">The type being patched</typeparam>
        /// <param name="path">The JSON path from the patch operation</param>
        /// <returns>The property name that corresponds to the path</returns>
        private static string GetPropertyNameFromPath<T>(string path) where T : class
        {
            if (string.IsNullOrEmpty(path))
                return string.Empty;

            // Remove leading '/' if present
            var cleanPath = path.StartsWith("/") ? path.Substring(1) : path;

            // Split by '/' to handle nested properties
            var pathParts = cleanPath.Split('/');

            // For simple properties, just return the first part
            if (pathParts.Length == 1)
            {
                return MatchPropertyName<T>(pathParts[0]);
            }

            // For nested properties, we'll return the full path but with corrected casing
            var correctedParts = new List<string>();
            var currentType = typeof(T);

            foreach (var part in pathParts)
            {
                var matchedName = MatchPropertyNameForType(currentType, part);
                correctedParts.Add(matchedName);

                // Try to get the property type for the next iteration
                var property = currentType.GetProperty(matchedName, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
                if (property != null)
                {
                    currentType = property.PropertyType;
                }
            }

            return string.Join(".", correctedParts);
        }

        /// <summary>
        /// Matches a property name with correct casing from the type
        /// </summary>
        /// <typeparam name="T">The type to search</typeparam>
        /// <param name="propertyName">The property name to match</param>
        /// <returns>The correctly cased property name</returns>
        private static string MatchPropertyName<T>(string propertyName) where T : class
        {
            return MatchPropertyNameForType(typeof(T), propertyName);
        }

        /// <summary>
        /// Matches a property name with correct casing from the specified type
        /// </summary>
        /// <param name="type">The type to search</param>
        /// <param name="propertyName">The property name to match</param>
        /// <returns>The correctly cased property name</returns>
        private static string MatchPropertyNameForType(Type type, string propertyName)
        {
            if (string.IsNullOrEmpty(propertyName))
                return string.Empty;

            // Get all public instance properties
            var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);

            // Try exact match first
            var exactMatch = properties.FirstOrDefault(p => p.Name.Equals(propertyName, StringComparison.Ordinal));
            if (exactMatch != null)
                return exactMatch.Name;

            // Try case-insensitive match
            var caseInsensitiveMatch = properties.FirstOrDefault(p => p.Name.Equals(propertyName, StringComparison.OrdinalIgnoreCase));
            if (caseInsensitiveMatch != null)
                return caseInsensitiveMatch.Name;

            // If no match found, return the original name
            return propertyName;
        }
    }
}
