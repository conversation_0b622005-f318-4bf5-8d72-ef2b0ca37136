using Atlas.Application.Interfaces;
using Atlas.Application.Models.PlanFeature;
using Atlas.CrossCutting.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Atlas.WebApi.Controllers
{
    [Route("api/[controller]")]
    [Authorize]
    [ApiController]
    public class PlanFeatureController : ControllerBase
    {
        private readonly IPlanFeatureApplication _planFeatureApplication;

        /// Constructor for the PlanFeatureController.
        public PlanFeatureController
        (
            IPlanFeatureApplication planFeatureApplication
        )
        {
            _planFeatureApplication = planFeatureApplication;
        }

        [HttpGet("{featureName}/{planName}")]
        public async Task<ActionResult<ApiResponse<PlanFeatureGetResponse>>> Get
        (
            [FromRoute] PlanFeatureGetRequest request
        )
        {
            var result = await _planFeatureApplication.IsFeatureEnabledForPlanAsync(request);
            return Ok(result);
        }
    }
}
