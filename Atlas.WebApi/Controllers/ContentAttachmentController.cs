using Atlas.Application.Interfaces;
using Atlas.Application.Models.ContentAttachment;
using Atlas.CrossCutting.Models.Responses;
using Atlas.Data.Enums;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Atlas.WebApi.Controllers
{
    [Route("api/{clientId}/{workgroupId}/[controller]")]
    [Authorize]
    [ApiController]
    public class ContentAttachmentController : ControllerBase
    {
        private readonly IContentAttachmentApplication _contentAttachmentApplication;

        public ContentAttachmentController(
            IContentAttachmentApplication contentAttachmentApplication)
        {
            _contentAttachmentApplication = contentAttachmentApplication;
        }

        [HttpPost("{contentUuid}")]
        [Authorize(Policy = PolicyConstants.ClientWorkgroupUserValidation)]
        public async Task<ActionResult<ApiResponse<ContentAttachmentGetResponse>>> Post(
            [FromRoute] int clientId, 
            [FromRoute] int workgroupId, 
            [FromRoute] Guid contentUuid,
            [FromForm] FileUploadRequest request)
        {
            request.clientId = clientId;
            request.workgroupId = workgroupId;
            request.contentUuid = contentUuid;

            var result = await _contentAttachmentApplication.UploadFileAsync(request);
            return Ok(result);
        }

        [HttpDelete("{contentUuid}/{contentAttachmentId}")]
        [Authorize(Policy = PolicyConstants.ClientWorkgroupUserValidation)]
        public async Task<ActionResult<ApiResponse<ContentAttachmentGetResponse>>> Delete(ContentAttachmentDeleteRequest request)
        {
            var result = await _contentAttachmentApplication.ToggleDelete(request);
            return Ok(result);
        }

        [HttpPut("/api/{clientId}/{workgroupId}/[controller]/{contentUuid}/Reorder")]
        [Authorize(Policy = PolicyConstants.ClientWorkgroupUserValidation)]
        public async Task<ActionResult<ApiResponse<ContentAttachmentGetResponse>>> Reorder
            (
               [FromRoute] Guid contentUuid, 
               [FromBody] ContentAttachmentReorderPutRequest request
            )
        {
            request.contentUuid = contentUuid;

            var result = await _contentAttachmentApplication.Reorder(request);
            return Ok(result);
        }

        [HttpGet("{contentUuid}")]
        [Authorize(Policy = PolicyConstants.ClientWorkgroupUserValidation)]
        public async Task<ActionResult<ApiResponse<List<ContentAttachmentGetResponse>>>> Get
        (
          [FromRoute] ContentAttachmentGetRequest request
        )
        {
            var result = await _contentAttachmentApplication.GetContentAttachments(request);
            return Ok(result);
        }

        [HttpPost("{contentUuid}/lock/{contentAttachmentId}")]
        [Authorize(Policy = PolicyConstants.ClientWorkgroupUserValidation)]
        public async Task<ActionResult<ApiResponse<ContentAttachmentGetResponse>>> ToggleLock
        (
          ContentAttachmentLockPostRequest request
        )
        {
            var result = await _contentAttachmentApplication.ToggleLock(request);
            return Ok(result);
        }

        [HttpGet("GetFileWaterMarked/{contentUuid}/{contentAttachmentId}")]
        [Authorize(Policy = PolicyConstants.ClientWorkgroupUserValidation)]
        public async Task<ActionResult<ApiResponse<ContentAttachmentFileWatermarkedGetResponse>>> GetFileWaterMarked
        (
          [FromRoute] ContentAttachmentFileWatermarkedGetRequest request
        )
        {
            var result = await _contentAttachmentApplication.GetFileWaterMarked(request);
            return Ok(result);
        }
    }
}
