using Atlas.Application.Interfaces;
using Atlas.Application.Models.Profile;
using Atlas.CrossCutting.Models.Responses;
using Atlas.Data.Enums;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Atlas.WebApi.Controllers
{
    /// <summary>
    /// Controller for handling profile requests
    /// </summary>
    [ApiController]
    [Authorize]
    [Route("api/[controller]")]
    public class ProfileController : ControllerBase
    {
        private readonly IProfileApplication _profileApplication;

        /// <summary>
        /// Constructor for the ProfileController.
        /// </summary>
        /// <param name="profileApplication"></param>
        /// <param name="logger"></param>
        /// <param name="requestContext"></param>
        public ProfileController
        (
            IProfileApplication profileApplication
        )
        {
            _profileApplication = profileApplication;
        }

        /// <summary>
        /// Get the profile of the current user
        /// </summary>
        /// <returns>The profile of the current user</returns>
        [HttpGet]
        public async Task<ActionResult<ApiResponse<ProfileGetResponse>>> Get()
        {
            var result = await _profileApplication.GetAsync();
            return Ok(result);
        }

        /// <summary>
        /// Get the profile of the current user, simplified version
        /// </summary>
        /// <returns>The profile of the current user, simplified version</returns>
        [HttpGet]
        [Route("simplifiedProfile")]
        public async Task<ActionResult<ApiResponse<ProfileSimplifiedGetResponse>>> GetSimplified()
        {
            var result = await _profileApplication.GetSimplifiedAsync();
            return Ok(result);
        }

        /// <summary>
        /// Get the profile of a user by id
        /// </summary>
        /// <param name="id">The id of the user</param>
        /// <returns>The profile of the user</returns>
        [HttpGet]
        [Route("{id}")]
        public async Task<ActionResult<ApiResponse<ProfileGetResponse>>> Get(int id)
        {
            var result = await _profileApplication.GetAsync(new ProfileGetRequest { id = id });
            return Ok(result);
        }


        /// <summary>
        /// Get user info by id
        /// </summary>
        /// <param name="id">The id of the user</param>
        /// <returns>The profile of the user</returns>
        [HttpGet]
        [Route("userInfo/{id}")]
        public async Task<ActionResult<ApiResponse<ProfileGetUserInfoResponse>>> GetUserInfo(int id)
        {
            var result = await _profileApplication.GetUserInfoAsync(new ProfileGetRequest { id = id });

            return Ok(result);
        }

        /// <summary>
        /// Get a profile by id and days offset
        /// </summary>
        /// <param name="request">The request containing the user id and days offset</param>
        /// <returns>The profile of the user</returns>
        [HttpPost]
        [Route("userInfo")]
        //[EnableThrottling]
        public async Task<ActionResult<ApiResponse<ProfileGetSensibleUserInfoResponse>>> GetSensibleUserInfo([FromBody] ProfileGetSensibleUserInfoRequest request)
        {
            var result = await _profileApplication.GetSensibleUserInfoAsync(request);
            return Ok(result);
        }

        [HttpGet]
        [Route("{id}/{days_offset}")]
        // GET: api/Profile/:id/:days_offset 
        //O ENDPOINT É O MESMO QUE O Get(int id)
        public async Task<ActionResult<ApiResponse<ProfileGetResponse>>> Get(int id, int days_offset)
        {
            var result = await _profileApplication.GetAsync(new ProfileGetRequest { id = id });
            return Ok(result);
        }

        /// <summary>
        /// Exports the list of users related to the specified client as a CSV file.
        /// </summary>
        /// <param name="clientId">The id of the client whose users will be exported.</param>
        /// <returns>A CSV file containing the list of users for the specified client.</returns>
        [HttpGet]
        [Route("GetUserList")]
        [Authorize(Policy = PolicyConstants.SuperOrClientAdminUserScopeValidation)]
        public async Task<IActionResult> GetUserList([FromQuery] int clientId)
        {
            var result = await _profileApplication.GetUserListAsync(clientId);

            return File(result.FileContent, result.ContentType, result.FileName);
        }

        /// <summary>
        /// Updates the profile of the current user.
        /// </summary>
        /// <param name="request">The profile update request data.</param>
        /// <returns>An ApiResponse indicating whether the update was successful.</returns>
        [HttpPut]
        public async Task<ActionResult<ApiResponse<bool>>> PutProfile([FromBody] ProfileUpdateRequest request)
        {
            var result = await _profileApplication.UpdateAsync(request);
            return Ok(result);
        }

        /// PUT: api/Profile/
        /// <summary>
        /// Updates the profile of a user for a specific client.
        /// </summary>
        /// <param name="clientId">The id of the authenticated user is admin joined about userId.</param>
        /// <param name="userId">The id of the user whose profile is to be updated.</param>
        /// <param name="request">The profile user update request data.</param>
        /// <returns>An ApiResponse indicating whether the update was successful.</returns>
        [HttpPut]
        [Route("/api/{clientId}/profile/{userId}")]
        [Authorize(Policy = PolicyConstants.SuperOrClientAdminUserScopeValidation)]
        public async Task<ActionResult<ApiResponse<bool>>> Put
        (
             int clientId, int userId, [FromBody] ProfileUpdateUserRequest request
        )
        {
            request.clientId = clientId;
            request.userId = userId;

            var result = await _profileApplication.UpdateAsync(userId, clientId, request);
            return Ok(result);
        }

        [HttpPut]
        [Route("uploadProfilePicture")]
        public async Task<ActionResult<ApiResponse<ProfileUploadPictureResponse>>> UploadProfilePicture([FromForm] ProfileUploadPictureRequest request)
        {
            var result = await _profileApplication.UploadProfilePictureAsync(request);
            return Ok(result);
        }

        [HttpDelete]
        [Route("deleteProfilePicture")]
        [Authorize(Policy = PolicyConstants.UploadProfilePictureValidation)]
        public async Task<ActionResult<ApiResponse>> DeleteProfilePicture()
        {
            var result = await _profileApplication.RemoveProfilePictureAsync();
            return Ok(result);
        }

        /// <summary>
        /// Delete a user to archive
        /// </summary>
        /// <param name="id">The id of the user to be deleted</param>
        /// <param name="clientId">The id of the client</param>
        /// <returns>A boolean indicating if the deletion was successful</returns>
        [HttpPut]
        [Route("/api/{clientId:int}/[controller]/{id:int}/deleteUserToArchive")]
        [Authorize(Policy = PolicyConstants.SuperOrClientAdminUserScopeValidation)]
        public async Task<ActionResult<ApiResponse<bool>>> PutDeleteUserToArchive(int id, int clientId)
        {
            var request = new ProfileDeleteArchiveRequest(clientId, id);
            var result = await _profileApplication.DeleteUserToArchiveAsync(request);
            return Ok(result);
        }

        [HttpPatch]
        [Route("{id:int}/block")]
        public async Task<ActionResult<ApiResponse<BlockUserResponse>>> BlockOrUnblockUser
        (
            [FromRoute] int id,
            [FromBody] BlockUserRequest request
        )
        {
            request.UserId = id;

            await _profileApplication.BlockOrUnblockUser(request);
            return Ok();
        }

        [HttpPatch]
        [Route("/api/{clientId:int}/[controller]/{id:int}/approve")]
        public async Task<ActionResult<ApiResponse<ApproveUserResponse>>> ApproveOrRejectUser
        (
            [FromRoute] int clientId,
            [FromRoute] int id,
            [FromBody] ApproveUserRequest request
        )
        {
            request.UserId = id;
            request.ClientId = clientId;

            await _profileApplication.ApproveOrRejectUser(request);
            return Ok();
        }

        /// <summary>
        /// Sets or removes the administrator status of a user for a specific client.
        /// </summary>
        /// <param name="id">ID of the user to be updated.</param>
        /// <param name="clientId">ID of the client related to the user.</param>
        /// <param name="request">Request data to set or remove the administrator status.</param>
        /// <returns>Response indicating the result of the operation to change the user's administrator status.</returns>
        [HttpPut]
        [Route("{clientId:int}/setAdmin/{id:int}")]
        [Authorize(Policy = PolicyConstants.ClientAdminValidation)]
        public async Task<ActionResult<ApiResponse<ProfileSetUserAdminResponse>>> SetUserAdminStatus
        (
            [FromRoute] int clientId,
            [FromRoute] int id,
            [FromBody] ProfileSetUserAdminRequest request
        )
        {
            request.UserId = id;
            request.ClientId = clientId;

            await _profileApplication.SetUserAdminStatus(request);
            return Ok();
        }

        [HttpPut]
        [Route("/api/{clientId:int}/[controller]/{userId:int}/resend-invite")]
        [Authorize(Policy = PolicyConstants.SuperOrClientAdminUserScopeValidation)]
        public async Task<ActionResult<ApiResponse<bool>>> ResendInvite
        (
            [FromRoute] int clientId,
            [FromRoute] int userId
        )
        {
            var request = new ResendInviteRequest(clientId, userId);
            var result = await _profileApplication.ResendInviteAsync(request);
            return Ok(result);
        }

        [HttpGet]
        [Route("user-sessions-csv")]
        [Authorize(Policy = PolicyConstants.SuperOrSupportOrClientAdminValidation)]
        public async Task<IActionResult> GetAuditLogExportExcel
       (
        [FromQuery] ProfileUserSessionsCsvGetRequest request
       )
        {
            var result = await _profileApplication.GetUserSessionsCsvData(request);

            return File(
                result.FileContent,
                result.ContentType,
                result.Filename
            );
        }
    }
}