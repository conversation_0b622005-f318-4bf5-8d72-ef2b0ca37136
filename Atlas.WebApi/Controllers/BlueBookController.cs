using Atlas.Application.Interfaces;
using Atlas.Application.Models.Announcement;
using Atlas.Application.Models.BlueBook;
using Atlas.CrossCutting.Models.Responses;
using Atlas.CrossCutting.Policies;
using Atlas.Data.Enums;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Atlas.WebApi.Controllers
{
    [Route("api/{clientId}/{workgroupId}/[controller]")]
    [ApiController]
    public class BlueBookController : ControllerBase
    {
        private IBlueBookApplication _blueBookApplication;

        public BlueBookController(IBlueBookApplication blueBookApplication)
        {
            _blueBookApplication = blueBookApplication;
        }


        /// <summary>
        /// Gets the current bluebook version or generates a new one if the none exists or the version is outdated
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet("{contentUuId}")]
        [AllPolicyAuthorize(PolicyConstants.ClientWorkgroupUserValidation, PolicyConstants.ContentPermissionValidation)]
        public async Task<ActionResult<ApiResponse<BlueBookGetResponse>>> GetBlueBook([FromRoute] BlueBookGetRequest request)
        {
            return Ok(await _blueBookApplication.GetBlueBook(request));
            
        }

    }
}
