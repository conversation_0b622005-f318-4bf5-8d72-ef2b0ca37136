using Atlas.Application.Interfaces;
using Atlas.Application.Models.Session;
using Atlas.CrossCutting.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Atlas.WebApi.Controllers
{
    /// <summary>
    /// Controller for handling profile requests
    /// </summary>
    [ApiController]
    [Authorize]
    [Route("api/[controller]")]
    public class SessionController : ControllerBase
    {
        private readonly ISessionApplication _sessionApplication;
        private readonly ILogger<SessionController> _logger;

        /// <summary>
        /// Constructor for the SessionController.
        /// </summary>
        /// <param name="sessionApplication"></param>
        /// <param name="logger"></param>
        public SessionController
        (
            ISessionApplication sessionApplication,
            ILogger<SessionController> logger
        )
        {
            _sessionApplication = sessionApplication;
            _logger = logger;
        }


        [Route("{userId}")]
        [HttpGet]
        public async Task<ActionResult<ApiResponse<List<SessionGetResponse>>>> Get([FromRoute] SessionGetRequest request)
        {
            var result = await _sessionApplication.GetSessions(request);
            return Ok(result);
        }

        [Route("{sessionKey}/{isLogoff}")]
        [HttpDelete]
        public async Task<ActionResult<ApiResponse<SessionRevokeDeleteResponse>>> Delete([FromRoute] SessionRevokeDeleteRequest request)
        {
            var result = await _sessionApplication.RevokeSession(request);
            return Ok(result);
        }

        [Route("revokeTicket/{sessionKey}")]
        [HttpDelete]
        [AllowAnonymous]
        public async Task<ActionResult<ApiResponse<SessionRevokeAnonymouslyDeleteResponse>>> RevokeSessionAnonymously([FromRoute] SessionRevokeAnonymouslyDeleteRequest request)
        {
            var result = await _sessionApplication.RevokeSessionAnonymously(request);
            return Ok(result);
        }
    }
}
