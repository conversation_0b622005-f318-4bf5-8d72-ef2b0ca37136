using Atlas.Application.Interfaces;
using Atlas.Application.Models.Note;
using Atlas.CrossCutting.AppEnums;
using Atlas.CrossCutting.DTO.Note;
using Atlas.CrossCutting.Models.Responses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Atlas.WebApi.Controllers
{
    /// <summary>
    /// Controller for handling note requests
    /// </summary>
    [ApiController]
    [Route("api/{clientId:int}/{workgroupId}/[controller]")]
    [Authorize]
    public class NoteController : ControllerBase
    {
        private readonly INoteApplication _noteApplication;

        public NoteController(INoteApplication noteApplication)
        {
            _noteApplication = noteApplication;
        }

        /// <summary>
        /// Get a paginated list of notes
        /// </summary>
        /// <returns>A paginated list of notes</returns>
        [HttpGet("~/api/[controller]")]
        public async Task<ActionResult<ApiResponse<PagedResult<NoteListItem>>>> GetList(
            [FromQuery] NoteListGetRequest request)
        {
            var result = await _noteApplication.GetNoteListAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// Get a specific note
        /// </summary>
        /// <returns>The complete note details</returns>
        [HttpGet("{contentUuid:guid}")]
        public async Task<ActionResult<ApiResponse<NoteDetailGetResponse>>> GetNoteByContentUuid(
            [FromRoute] Guid contentUuid)
        {
            var request = new NoteDetailGetRequest { ContentUuid = contentUuid };
            var result = await _noteApplication.GetNoteByContentUuidAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// Create a new Note
        /// </summary>
        /// <returns>Returns a NoteId</returns>
        [HttpPost]
        public async Task<ActionResult<ApiResponse<CreateNoteResponse>>> CreateNote(
            [FromRoute] int clientId,
            [FromRoute] int workgroupId,
            [FromBody] CreateNoteRequest createNoteRequest)
        {
            CreateNoteDto createNoteDto = new CreateNoteDto
            {
                ClientId = clientId,
                WorkgroupId = workgroupId,
                Type = ContentTypes.Note,
                Title = createNoteRequest.Title,
                Text = createNoteRequest.Text
            };

            var result = await _noteApplication.CreateNoteAsync(createNoteDto);

            if (result.Id == Guid.Empty)
                ApiResponse<CreateNoteResponse>.CreateError("One or more erros occured");

            return Ok(result);
        }

        /// <summary>
        /// Update note
        /// </summary>
        /// <returns>Returns a bool result</returns>
        [HttpPut]
        public async Task<ActionResult<ApiResponse<object>>> UpdateNote(
            [FromRoute] int clientId,
            [FromRoute] int workgroupId,
            [FromBody] UpdateNoteRequest updateNoteRequest)
        {
            updateNoteRequest.clientId = clientId;
            updateNoteRequest.workgroupId = workgroupId;
            updateNoteRequest.type = ContentTypes.Note;

            var result = await _noteApplication.UpdateNoteAsync(updateNoteRequest);

            if (result is int && (int)result == -1)
                ApiResponse<CreateNoteResponse>.CreateError("One or more erros occured");

            return Ok();
        }

        /// <summary>
        /// Delete a note
        /// </summary>
        /// <returns>No returns</returns>
        [HttpDelete("{contentUuid}")]
        public async Task<ActionResult<ApiResponse>> DeleteNote(
           [FromRoute] Guid contentUuid,
           string userAgent)
        {
            bool result = await _noteApplication.DeleteNoteAsync(contentUuid, userAgent);

            return Ok(result);
        }
    }
}