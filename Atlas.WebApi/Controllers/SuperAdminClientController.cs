using Atlas.Application.Interfaces;
using Atlas.Application.Models.Admin;
using Atlas.Application.Models.AdminArea;
using Atlas.CrossCutting.DTO.Client;
using Atlas.CrossCutting.Models.Responses;
using Atlas.CrossCutting.Policies;
using Atlas.Data.Enums;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Atlas.WebApi.Controllers
{
    /// <summary>
    /// Controller for handling admin area requests
    /// </summary>
    [ApiController]
    [Authorize]
    [Route("api/[controller]")]
    public class SuperAdminClientController(ISuperAdminClientApplication superAdminClientApplication) : ControllerBase
    {
        private readonly ISuperAdminClientApplication _superAdminClientApplication = superAdminClientApplication;

        // api/SuperAdminClient
        [HttpGet]
        [Authorize(Policy = PolicyConstants.SuperOrSupportOrClientAdminValidation)]
        public async Task<ActionResult<ApiResponse<SuperAdminClientGetResponse>>> GetSuperAdminClient()
        {
            var result = await _superAdminClientApplication.GetSuperAdminClientAsync();
            return Ok(result);
        }

        [HttpGet("/api/{clientId}/[controller]/client")]
        [Authorize(Policy = PolicyConstants.SuperOrSupportOrClientAdminValidation)]
        public async Task<ActionResult<ApiResponse<SuperAdminClientGetByIdResponse>>> GetSuperAdminClientById([FromRoute] ClientGetRequest request)
        {
            var result = await _superAdminClientApplication.GetSuperAdminClientByIdAsync(request);
            return Ok(result);
        }

        // api/SuperAdminClient
        [HttpPost]
        [Authorize(Policy = PolicyConstants.SuperOrSupportOrClientAdminValidation)]
        public async Task<ActionResult<ApiResponse<SuperAdminClientCreateResponse>>> CreateClient([FromBody] SuperAdminClientCreateRequest request)
        {
            var result = await _superAdminClientApplication.CreateClientAsync(request);
            return Ok(result);
        }

        // api/clientId/superadminclient/client
        [HttpPut("/api/{ClientId:int}/[controller]/client")]
        [Authorize(Policy = PolicyConstants.SuperOrSupportOrClientAdminValidation)]
        public async Task<ActionResult<ApiResponse>> UpdateClientDetails([FromRoute] int ClientId, [FromBody] ClientDetailsDTO request)
        {
            await _superAdminClientApplication.UpdateClientDetails(request, ClientId);
            return Ok();
        }

        // api/SuperAdminClient/1
        [HttpPut("{ClientId}")]
        [Authorize(Policy = PolicyConstants.SuperOrSupportOrClientAdminValidation)]
        public async Task<ActionResult<ApiResponse>> UpdateClient([FromBody] ClientUpdateRequest request)
        {
            await _superAdminClientApplication.UpdateSuperAdminClientAsync(request);
            return Ok();
        }

        // api/SuperAdminClient/1/block
        [HttpPut("{ClientId}/block")]
        [Authorize(Policy = PolicyConstants.SuperOrSupportOrClientAdminValidation)]
        public async Task<ActionResult<ApiResponse<bool>>> BlockClient([FromBody] ClientBlockRequest request)
        {
            var result = await _superAdminClientApplication.BlockSuperAdminClientAsync(request);
            return Ok(result);
        }

        // api/SuperAdminClient/{id}/sendToken/{twoFactorMethod}
        [HttpDelete("{Id}/sendToken/{TwoFactorMethod}")]
        [Authorize(Policy = PolicyConstants.SuperOrSupportOrClientAdminValidation)]
        public async Task<ActionResult<ApiResponse>> SendTokenToDeleteClient([FromRoute] ClientDeleteRequest request)
        {
            await _superAdminClientApplication.DeleteSuperAdminClientAsync(request, sending: true);
            return Ok();
        }

        // api/SuperAdminClient/{id}/verifyToken/{twoFactorMethod}/{twoFactorCode}
        [HttpDelete("{Id}/verifyToken/{TwoFactorMethod}/{TwoFactorCode}")]
        [Authorize(Policy = PolicyConstants.SuperOrSupportOrClientAdminValidation)]
        public async Task<ActionResult<ApiResponse>> VerifyTokenToDeleteClient([FromRoute] ClientDeleteRequest request)
        {
            await _superAdminClientApplication.DeleteSuperAdminClientAsync(request, sending: false);
            return Ok();
        }

        [HttpGet("searchEmail")]
        [Authorize(Policy = PolicyConstants.SuperOrSupportOrClientAdminValidation)]
        public async Task<ActionResult<ApiResponse<UserInfoGetResponse>>> GetUserInfoByEmail
        (
            [FromQuery] UserInfoGetRequest request
        )
        {
            var result = await _superAdminClientApplication.GetUserInfoByEmailAsync(request);
            return Ok(result);
        }

        // api/SuperAdminClient/{ClientId}/UploadLogo/{type}
        [HttpPut("{clientId}/upload-logo/{type}")]
        [AllPolicyAuthorize(PolicyConstants.SuperOrSupportOrClientAdminValidation, PolicyConstants.CustomLogoFeatureFlagValidation)]
        public async Task<ActionResult<ApiResponse>> UploadLogoAsync(
            [FromRoute] int clientId,
            [FromRoute] string type,
            [FromForm] AdminUploadLogoRequest request)
        {
            request.clientId = clientId;
            request.type = type;

            var result = await _superAdminClientApplication.UploadLogoAsync(request).ConfigureAwait(false);
            return Ok(result);
        }

        // api/{clientId}/SuperAdminClient/users
        [HttpGet("/api/{clientId}/[controller]/users")]
        [Authorize(Policy = PolicyConstants.SuperOrSupportOrClientAdminValidation)]
        public async Task<ActionResult<ApiResponse<AdminClientUsersGetResponse>>> GetUsersForClient(
            [FromRoute] int clientId
        )
        {
            var request = new AdminClientUsersGetRequest { ClientId = clientId };
            var result = await _superAdminClientApplication.GetUsersForClientAsync(request);
            return Ok(result);
        }

        [HttpPut("{clientId}/SuperAdminUserInviteApprove")]
        [Authorize(Policy = PolicyConstants.SuperOrSupportOrClientAdminValidation)]
        public async Task<ActionResult<ApiResponse<SuperAdminClientApproveUsersResponse>>> ApproveUsers(
            [FromRoute] int clientId,
            [FromBody] SuperAdminClientApproveUsersRequest request)
        {
            request.clientId = clientId;

            var result = await _superAdminClientApplication.ApproveUser(request);
            return Ok(result);
        }

        [HttpPut("{clientId}/SuperAdminUserBlock/{userId}")]
        [Authorize(Policy = PolicyConstants.SuperOrSupportOrClientAdminValidation)]
        public async Task<ActionResult<ApiResponse<SuperAdminClientBlockUsersResponse>>> BlockUsers(
            [FromRoute] int clientId,
            [FromBody] SuperAdminClientBlockUsersRequest request)
        {
            request.clientId = clientId;

            var result = await _superAdminClientApplication.BlockUser(request);
            return Ok(result);
        }

        [HttpGet("{clientId}/workgroups")]
        [Authorize(Policy = PolicyConstants.SuperOrSupportOrClientAdminValidation)]
        public async Task<ActionResult<ApiResponse<AdminClientWorkgroupsGetResponse>>> GetWorkgroupsForClient(
            [FromRoute] int clientId
        )
        {
            var request = new AdminClientWorkgroupsGetRequest { clientId = clientId };
            var result = await _superAdminClientApplication.GetWorkgroupsForClientAsync(request);
            return Ok(result);
        }
    }
}
