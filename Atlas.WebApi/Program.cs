using AspNetCoreRateLimit;
using Atlas.Application;
using Atlas.Auth;
using Atlas.WebApi;
using Atlas.WebApi.Middleware;

var builder = WebApplication.CreateBuilder(args);

builder.Configuration.AddEnvironmentVariables(prefix: "GOV_");
builder.Configuration.AddEnvironmentVariables(prefix: "SHARED_");

builder.Services.AddHttpContextAccessor();

builder.Services.AddConfigureAuth(builder.Configuration);

builder.Services.ConfigureThrottling(builder.Configuration);
builder.Services.ConfigureAzureKeyVault(builder.Configuration);
builder.Services.ConfigurePolicies();
builder.Services.ConfigureRepositories();
builder.Services.ConfigureServices(builder.Configuration);
builder.Services.ConfigureAuthentication();
builder.Services.ConfigureLog();
builder.Services.ConfigureApplication();
builder.Services.ConfigureMapster();

builder.Services.AddControllers().AddNewtonsoftJson(options =>
{
    options.SerializerSettings.ContractResolver = new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver();
    options.SerializerSettings.NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore;
    options.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore;
});

// Configure Swagger with JWT authentication
builder.Services.ConfigureSwagger();

builder.Services.AddHealthChecks();

var app = builder.Build();

app.UseDeveloperExceptionPage();
// Configure the HTTP request pipeline.
// Swagger configuration with /api base path
app.UseSwagger(c =>
{
    // Set custom route template for Swagger JSON
    c.RouteTemplate = "api/swagger/{documentName}/swagger.json";
});

app.UseSwaggerUI(c =>
{
    // Define Swagger endpoint with base path
    c.SwaggerEndpoint("/api/swagger/v1/swagger.json", "My API V1");

    // Serve the Swagger UI at /api/swagger
    c.RoutePrefix = "api/swagger";
});

app.UseIpRateLimiting();

app.UseResponseWrapper(); // Add response wrapper middleware

app.UseHttpsRedirection();

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

app.MapHealthChecks("/healthy").AllowAnonymous();

// Execute Client to Tenant migration during startup
//try
//{
//    using (var scope = app.Services.CreateScope())
//    {
//        var migrationService = scope.ServiceProvider.GetRequiredService<Atlas.Business.ClientToTenantMigrationService>();
//        var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();
        
//        logger.LogInformation("Starting Client to Tenant migration...");
        
//        var migrationResult = await migrationService.ExecuteMigrationAsync();
        
//        if (migrationResult.Success)
//        {
//            logger.LogInformation("Migration completed successfully. Processed {RecordCount} records with {SuccessfulInserts} successful inserts.", 
//                migrationResult.TotalRecordsProcessed, migrationResult.SuccessfulInserts);
//        }
//        else
//        {
//            logger.LogError("Migration failed: {ErrorMessage}", migrationResult.ErrorMessage);
//        }
//    }
//}
//catch (Exception ex)
//{
//    var logger = app.Services.GetRequiredService<ILogger<Program>>();
//    logger.LogError(ex, "Error occurred during migration execution");
//}

// Initialize static HttpContextAccessor for backwards compatibility
Atlas.Data.Entities.AtlasModelCore.HttpContextAccessor = app.Services.GetRequiredService<IHttpContextAccessor>();

app.Run();
