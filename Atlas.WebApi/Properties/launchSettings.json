{"$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:20161", "sslPort": 44370}}, "profiles": {"http": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "api/swagger", "applicationUrl": "http://localhost:31920", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "https": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "api/swagger", "applicationUrl": "https://localhost:31920;http://localhost:5018", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "api/swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Atlas.WebApi.Alpha": {"commandName": "Project", "launchBrowser": true, "launchUrl": "api/swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "alpha_gov"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:31920;https://localhost:31921"}, "Atlas.WebApi.Beta": {"commandName": "Project", "launchBrowser": true, "launchUrl": "api/swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "beta_gov"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:31920;https://localhost:31921"}, "Atlas.WebApi.Capa": {"commandName": "Project", "launchBrowser": true, "launchUrl": "api/swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "capa_gov"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:31920;https://localhost:31921"}, "Atlas.WebApi.Delta": {"commandName": "Project", "launchBrowser": true, "launchUrl": "api/swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "delta_gov"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:31920;https://localhost:31921"}, "Atlas.WebApi.Eta": {"commandName": "Project", "launchBrowser": true, "launchUrl": "api/swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "eta_gov"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:31920;https://localhost:31921"}, "Atlas.WebApi.Gamma": {"commandName": "Project", "launchBrowser": true, "launchUrl": "api/swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "gamma_gov"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:31920;https://localhost:31921"}, "Atlas.WebApi.Staging": {"commandName": "Project", "launchBrowser": true, "launchUrl": "api/swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "staging_gov"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:31920;https://localhost:31921"}}}