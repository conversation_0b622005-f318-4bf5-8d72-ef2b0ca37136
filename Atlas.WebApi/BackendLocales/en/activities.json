{"CONTENT": {"COMMENT_ADD": "Commented {{text}} on {{type}} {{title}}", "COMMENT_ANSWER_ADD": "Replied {{text}} to a comment on {{type}} {{title}}", "ATTACHMENT_ADD": "Added the attachment to {{type}} {{title}}", "UPDATED": "Updated details for {{type}} {{title}}", "CREATED": "Created {{type}} {{title}}", "DELETED": "Deleted {{type}} {{title}}", "STATUS_CLOSED": "Closed the {{type}} {{title}}", "STATUS_OPEN": "Changed the status of {{type}} {{title}} to OPEN", "STATUS_AWAITING_REVIEW": "Send {{type}} {{title}} to review", "STATUS_READY": "Set the {{type}} {{title}} as ready", "STATUS_READY_MEETING": "Set the meeting as Ready", "STATUS_READY_GUESTS": "Sent invite to the meeting guest", "STATUS_PUBLISHED": "Published the {{type}} {{title}}", "SUBSCRIBER_ADD": "Added a subscriber to {{type}} {{title}}", "SUBSCRIBER_DELETE": "Removed a subscriber from {{type}} {{title}}", "SUBSCRIBER_UPDATE": "Updated the subscribers for {{type}} {{title}}", "SUBSCRIBER_UPDATE_MEETING": "Updated the atendees for the meeting", "ATTACHMENT_DOWNLOAD": "Downloaded the attachment {{text}} from {{type}} {{title}}", "ATTACHMENT_DELETE": "Attachment {{text}} at {{type}} {{title}} deleted.", "ATTACHMENT_UNDELETE": "Attachment {{text}} {{title}} undeleted.", "CREATED_MEETING": "Created a new meeting", "CREATED_MEETINGMINUTE": "Created a new meeting minute", "COMMENT_MENTION": "Mentioned {{userName}} on the comment {{text}}", "BLUEBOOK_DOWNLOAD": "Downloaded the meeting's BLUEBOOK", "POLL_VOTE_ADD": "Voted in the resolution {{title}}", "POLL_UNDO_VOTE": "Undone the vote in {{title}}", "STATUS_CLOSED_POLL": "Set the resolution {{title}} as finished", "STATUS_AWAITING_VOTES_POLL": "Edited the Resolution {{title}}.", "COMMENT_DELETE": "Deleted a comment", "COMMENT_UNDELETE": "Recovered a comment", "COMMENT_ANSWER_DELETE": "Deleted a comment reply", "COMMENT_ANSWER_UNDELETE": "Undeleted a comment reply", "MEETING_GUEST_DELETE": "Removed guest from {{type}}", "MEETING_AGENDA_DELETED_GUEST": "Removed guest from {{type}}", "MEETING_GUEST_UPDATE": "Updated guest at {{type}} {{title}}", "MEETING_GUEST_ADD": "Added the guest {{text}}", "STATUS_AWAITING_VOTES": "Edited {{type}} {{title}}", "BLUEBOOK_VERSION_DOWNLOAD": "Downloaded a meeting's BLUEBOOK version", "INCLUDED_MINUTE_ON_BLUEBOOK": "Included the meeting minute on bluebook", "INCLUDED_POLLS_REPORTS_ON_BLUEBOOK": "Included the meeting resolutions reports on bluebook", "STATUS_UNPUBLISHED_MEETINGMINUTE": "Undid the meeting minute publish", "UPDATED_ATTACHMENTS_ORDER": "Updated the attachments order", "UPDATED_AGENDAS_ORDER_MEETING": "Updated the meeting agenda itens order", "RESEND_ANNOUNCEMENT": "Resend the announcement {{title}}", "RESEND_TASK": "Resend the task {{title}}", "RESEND_POLL": "Resend the resolution {{title}}", "REVIEW_REJECTED": "Reproved the task {{title}}", "STATUS_CANCELLED": "Cancelled the {{type}} {{title}}", "MEETING_LISTENER_GUEST_ADD": "Added listener guest {{text}}", "MEETING_LISTENER_GUEST_DELETE": "Removed listener guest {{text}}", "MEETING_LISTENER_RECURRING_GUEST_ADD": "Added saved guest {{text}}", "MEETING_LISTENER_RECURRING_GUEST_DELETE": "Removed saved guest {{text}}", "MEETING_LISTENER_RECURRING_GUEST_UPDATE": "Updated saved guest {{text}}", "MEETING_AGENDA_LISTENER_GUEST_ADD": "Added listener guest {{text}} to meeting agenda", "MEETING_AGENDA_LISTENER_GUEST_DELETE": "Removed listener guest {{text}} from meeting agenda", "MEETING_AGENDA_RECURRING_GUEST_ADD": "Added saved guest {{text}} to meeting agenda", "MEETING_AGENDA_RECURRING_GUEST_DELETE": "<PERSON>moved saved guest {{text}} from meeting agenda", "MEETING_AGENDA_RECURRING_GUEST_UPDATE": "Updated saved guest {{text}} from meeting agenda", "INCLUDED_AGENDA_ATTACHMENT_BLUEBOOK": "Added the attachment {{text}} to {{type}} {{title}} in the BlueBook", "REMOVED_AGENDA_ATTACHMENT_BLUEBOOK": "Removed the attachment {{text}} in {{type}} {{title}} from the BlueBook", "SHARED_BLUEBOOK_NOTES": "Shared a BlueBook note with the user {{sharedUser}}", "CREATED_CONTENT_FROM_FOLDER": "Imported content through a folder {{title}}", "ATTACHMENT_ADD_FROM_FOLDER": "Added the attachment to {{type}} {{title}} through a folder", "IMPORTED_CONTENT": "Imported the {{type}} {{title}} through {{sourceType}} {{sourceContentId}}", "IMPORTED_COMMENTS": "Imported the comments in {{type}} {{title}} through {{sourceType}} {{sourceContentId}}", "IMPORTED_ATTACHMENTS": "Imported the attachments in {{type}} {{title}} through {{sourceType}} {{sourceContentId}}", "POLL_REPORT_VIEW": "Viewed the resolution report", "POLL_REPORT_GENERATED": "Generated the resolution report", "POLL_REPORT_AUTO_GENERATED": "Generated the resolution report (automatically)", "BLUEBOOK_VIEW": "Viewed the meeting's BLUEBOOK", "BLUEBOOK_VERSION_VIEW": "Viewed a meeting's BLUEBOOK version", "ATTACHMENT_VIEW": "Viewed the attachment to {{type}} {{title}}", "MINUTE_VIEW": "Viewed {{type}} {{title}}", "RECURRING_MEETING_CREATED": "Created a recurring meeting", "RECURRING_MEETING_DELETED": "Deleted a recurring meeting", "RECURRING_MEETING_DELETED_RESET": "Deleted a recurring meeting", "RECURRING_MEETING_UPDATED": "Updated a recurring meeting", "RECURRING_MEETING_UPDATED_RESET": "Updated a recurring meeting", "AGENDA_POSTPONED": "Postponed the meeting agenda", "FORM_UNPUBLISHED": "Unpublished the Form", "FORM_PUBLISHED": "Published the Form", "FORM_REPUBLISHED": "Republished the Form", "FORM_DUPLICATED": "Form created based on Form", "FORM_CLOSED": "Closed the Form", "FORM_EXPIRED": "Form automatically closed after exceed the deadline", "FORM_REPORT_EXPORTED": "Exported a report From the form", "FORM_REQUEST_ANSWER_RESEND": "Resent the Form", "REQUEST_ANSWER": "Asked to answer the Form", "RESEND_ICS_MEETING_GUESTS": "ICS resend to meeting listener guest", "RESEND_ICS_AGENDA_PRESENTERS": "ICS resend to external agenda presenter", "RESEND_ICS_AGENDA_GUESTS": "ICS resend to agenda listener guest", "BLUEBOOK_AUTO_SYNC": "Bluebook synchronization performed by the APP", "FORM_ANSWERED": "Answered a form", "EXTERNAL_CORPORATE_BOOK_DOC_UPLOADED": "Added {{type}}"}, "Type": {"Task": "action", "Pipeline": "pipeline", "PipelineItem": "opportunity", "Meeting": "meeting", "MeetingAgendaItem": "meeting agenda", "MeetingMinute": "meeting minute", "KnowledgeArticle": "article", "Poll": "resolution", "Announcement": "an announcement", "AnnouncementMeetingMinute": "an announcement meeting minute", "projects": "Projects", "knowledgeBase": "Knowledge Base", "projectFolders": "Project Folders", "boardFolders": "Board Folders", "ExternalCorpBookDoc": "external document Minutes Book"}, "contentType": {"Poll": "Resolution", "MeetingMinute": "Meeting minute", "KnowledgeDirectory": "Knowledge directory", "Meeting": "Meeting", "MeetingAgendaItem": "Meeting agenda item", "Announcement": "Announcement", "Task": "Task", "Note": "Note", "Form": "Form", "ExternalCorpBookDoc": "External document Minutes Book"}, "list": {"range": "Range", "from": "From", "to": "To", "title": "Browse recent activity", "workgroups": "Boards & Projects", "actionUser": "Action User", "types": "Types", "previous": "Previous", "next": "Next", "loading": "Loading...", "noTypes": "No types found", "noUsers": "No users found", "noWorkgroups": "No boards/projects found", "ACCESS_LOG": "Access log", "CONTENT_LOG": "Content log"}, "export": {"logId": "Log Id", "userId": "User Id", "username": "User name", "activityType": "Activity type", "date": "Activity date", "time": "Time", "action": "Action", "ip": "IP", "contentType": "Content type", "workgroupId": "Board/Project ID", "workgroupName": "Board/Project name", "details": "Details", "contentId": "Content ID", "logType": "Log type", "clientId": "Client Id", "dateTimeFormat": "MM/dd/yyyy hh:mm AM/PM", "dateFormat": "MM/dd/yyyy", "timeFormat": "hh:mm: AM/PM", "log": "Log", "log_user_session": "Log - User Sessions", "ACCESS_LOG": "Access log", "CONTENT_LOG": "Content log", "externalSigner": "External Signer", "userSessionId": "User Session Id", "session": "Session", "startDate": "Start Date", "expireDate": "Expire Date", "userAgent": "User Agent", "location": "Location", "name": "Name", "revoked": "Revoked", "verifiedByToken": "Verified By Token", "lastActivity": "Last Activity", "browserFingerprint": "Browser Fingerprint", "revokeTicket": "Revoke Ticket", "revokeDate": "Revoke Date", "revokeUserId": "Revoke User Id", "deviceId": "<PERSON>ce Id", "userIntegrationId": "User Integration Id", "lastIP": "Last IP", "lastLocation": "Last Location", "lastUserAgent": "Last User Agent", "startDateTime": "Start Date Time", "expireDateTime": "Expire Date Time", "lastActivityTime": "Last Activity Time", "revokeDateTime": "Revoke Date Time", "activityDevice": "<PERSON><PERSON>", "unknownDevice": "Unknown device"}, "auditLog": {"WORKGROUP_CREATE": "Created a board/project", "WORKGROUP_CREATED": "Created a board/project", "WORKGROUP_COPY": "Copied a project", "WORKGROUP_UPDATE": "Updated a board/project", "WORKGROUP_ARCHIVED": "Board/project archived", "USER_ADDED": "Added the user to the board/project", "USER_INVITE": "Invited the user", "CLIENT_BLOCKED": "Blocked the client", "CLIENT_UNBLOCKED": "Unblocked the client", "USER_APPROVED": "Approved the user", "USER_REJECTED": "Rejected the user", "USER_BLOCKED": "Blocked the user", "USER_UNBLOCKED": "Unblocked the user", "USER_DEVICE_UPDATED": "Updated his/her devices", "DEVICE_APPROVED": "Approved the user device", "DEVICE_REJECTED": "Rejected the user device", "DEVICE_REVOKED": "Revoked the user device", "DOMAIN_ADD": "Added the domain", "DOMAIN_REMOVE": "Removed the domain", "USER_DELETED": "Deleted the user", "USER_UNDELETED": "Undeleted the user", "CLIENT_UPDATE": "Updated the client", "CLIENT_UPDATE_LOGO": "Updated the client logo", "CONTENT_ADD": "Added the content", "CONTENT_UPDATE": "Updated the content", "CONTENT_DELETE": "Deleted the content", "ATTACHMENT_DOWNLOAD": "Downloaded the content", "KB_CATEGORY_ADD": "Added a knowledge base category ", "KB_CATEGORY_UPDATE": "Updated the knowledge base category", "KB_CATEGORY_DELETED": "Deleted the knowledge base category", "KB_SHARED_FOLDERS": "Company Folders", "USER_REGISTRATION_STARTED": "User started the registration process", "USER_REGISTRATION_SUCCESS": "User registered successfully", "USER_PASSWORDRECOVERY_REQUESTED": "User requested password recovery", "USER_PASSWORDRECOVERY_VALIDATION": "User started the password recovery process", "USER_PASSWORDRECOVERY_SUCCESS": "User recovered the password successfully", "USER_PASSWORD_CHANGED": "User changed the password", "USER_PROFILE_UPDATED": "User updated the profile", "USER_PROFILEPIC_UPDATED": "User updated the profile photo", "USER_DEVICE_ADDED": "User added a new device", "USER_DEVICE_REQUEST_APPROVAL": "Requested device approval", "USER_ADMIN_REMOVE": "Removed an admin user", "LOGIN_FAILED_OLD_PASSWORD": "<PERSON><PERSON> failed because the user entered an old password", "LOGIN_FAILED": "<PERSON><PERSON> failed ", "LOGIN_FAILED_USERDELETED": "<PERSON><PERSON> failed because this user was deleted", "LOGIN_FAILED_USERBLOCKED": "Login failed because this user is blocked", "LOGIN_FAILED_CLIENTBLOCKED": "<PERSON><PERSON> failed because this client is blocked", "LOGIN_OK": "Login done successfully", "USER_TEMPBLOCK_ABUSE_PREVENTION": "User blocked - SMS abuse prevention", "USER_BLOCKED_LOGIN_ATEMPTS_EXCEEDED": "User blocked due to exceeded attempts to login", "USER_LOCKREQUEST": "Session blocked by user request", "BLUEBOOK_GENERATED": "Bluebook generated", "BLUEBOOK_DOWNLOAD": "Downloaded the meeting's BLUEBOOK", "REVOKED_SESSION": "Session revoked", "SESSION_REVOKED": "Session revoked", "SESSION_REVOKED_EMAIL": "Session revoked by e-mail", "ANNOUNCEMENT_READ": "Announcement read", "STATUS_CLOSED": "Closed the content", "STATUS_OPEN": "Changed the status of the content to OPEN", "AWAITING_REVIEW": "Changed the reviewer", "STATUS_AWAITING_REVIEW": "Send a task to review", "STATUS_AWAITING_VOTES": "Changed the resolution status to awaiting votes", "STATUS_READY": "Set the content as ready", "STATUS_READY_GUESTS": "Meeting called (guests)", "STATUS_READY_MEETING": "Set the meeting as Ready", "STATUS_PUBLISHED": "Published the meeting minute", "STATUS_CANCELLED": "Cancelled the resolution", "STATUS_UNPUBLISHED": "Undid the meeting minute publish", "AUDIT_LOG_EXPORTED": "Exported the audit log", "DATA_WORKGROUP_EXPORTED": "Exported the board/project data", "DATA_KB_EXPORTED": "Exported the client knowledge bases", "USERS_CLIENT_EXPORTED": "Exported the client data - User accepted and downloaded file containing personal data protected by GDPR", "DATA_PROJECT_EXPORTED": "Exported the project data - User accepted and downloaded file containing personal data protected by GDPR", "ATTACHMENT_DELETE": "Attachment deleted.", "ATTACHMENT_UNDELETE": "Attachment undeleted.", "UNDELETE_ATTACHMENT": "Attachment undeleted", "DELETE_ATTACHMENT": "Attachment deleted", "ADMIN_UPDATED_USER_NAME": "Updated the user name", "ADMIN_UPDATED_USER_MOBILE": "Updated the user mobile", "ADMIN_UPDATED_USER_EMAIL": "Updated the user e-mail", "MEETING_LISTENER_GUEST_UPDATE": "Updated listener guest meeting items", "MEETING_UPDATED_GUESTS": "Updated the guest agenda item time", "MEETING_DELETED_GUESTS": "Deleted the guest meeting agenda item", "PERMISSIONS_UPDATE": "Updated the permissions", "PERMISSIONS_ADDED": "Gave permission to an user", "PERMISSIONS_ADD": "Gave permission to an user", "PERMISSIONS_DELETE": "Removed a user permission", "CONTENT_OWNER_DELETE": "Removed content owners", "OWNER_ADD": "Added owners to the content.", "OWNER_UPDATE": "Updated the content owners", "COMMENT_MENTION": "Mentioned an user on the comment", "COMMENT_ADD": "Commented", "COMMENT_DELETE": "Deleted a comment", "COMMENT_UNDELETE": "Recovered a comment", "COMMENT_ANSWER_ADD": "Replied to a comment", "COMMENT_ANSWER_DELETE": "Deleted a comment reply", "COMMENT_ANSWER_UNDELETE": "Undeleted a comment reply", "SHARED": "Shared a content", "CONTENT_SHARED": "Shared a content", "DELETED": "Deleted the content", "SUBSCRIBER_ADD": "Added a subscriber to the content", "SUBSCRIBER_DELETE": "Removed a subscriber from the content", "SUBSCRIBER_UPDATE": "Updated the content subscribers", "SUBSCRIBER_UPDATE_MEETING": "Updated the atendees for the meeting", "ATTACHMENT_ADD": "Added an attachment", "POLL_VOTE_ADD": "Voted in the resolution", "POLL_UNDO_VOTE": "Undone the vote ", "POLL_VOTE_REMOVE": "Undone the vote", "UPDATED": "Updated details for a content", "UPDATED_ATTACHMENTS_ORDER": "Updated the attachments order", "UPDATED_AGENDAS_ORDER": "Updated the meeting agenda itens order", "CREATED": "Created the content", "WORKGROUP_RESTORED": "Board/project restored", "REVIEW_REJECTED": "Reproved the task", "BACKUP_REQUESTED": "Requested a backup", "BOARD_BACKUP_REQUESTED": "Requested a board backup", "BACKUP_DOWNLOADED": "Downloaded the backup", "MEETING_GUEST_ADD": "Added a guest to the meeting agenda item", "MEETING_GUEST_DELETE": "Removed the guest from an agenda item", "RESEND_INVITE": "Resended the registration invite to the user", "USER_ADMIN_SET": "Set the user as client admin", "INCLUDED_MINUTE_ON_BLUEBOOK": "Included the meeting minute on bluebook", "INCLUDED_POLLS_REPORTS_ON_BLUEBOOK": "Included the meeting resolutions reports on bluebook", "RESEND_ANNOUNCEMENT": "Resend the announcement", "RESEND_TASK": "Resend the task", "RESEND_POLL": "Resend the resolution", "WORKGROUP_OWNERS_UPDATED": "Updated the board/project owners", "USER_REMOVED": "Removed the user from the board/projeto", "BLUEBOOK_VERSION_DOWNLOAD": "Downloaded a meeting's BLUEBOOK version", "2FA_AUTHENTICATION_OK": "Token authentication successful", "2FA_AUTHENTICATION_FAILED": "Token authentication failed", "USER_BLOCKED_TOKEN_ATEMPTS_EXCEEDED": "User blocked due to exceeded attempts to validate token", "DISABLED_ATTACHMENT_WATERMARK": "Disabled the attachment watermark", "ENABLED_ATTACHMENT_WATERMARK": "Enabled the attachment watermark", "USER_LOGGED_OUT": "Logged out", "UPDATED_EXPIRE_DAYS_PASSWORD": "Updated the password expire deadline", "UPDATED_STRENGTH_PASSWORD": "Updated the minimum password strength", "UPDATED_PASSWORD_STRENGTH": "Updated the password expire deadline", "UPDATED_PASSWORD_EXPIRATION": "Updated the minimum password strength", "REVIEW_ACCEPTED": "Approved the task", "INTEGRATION_REMOVED": "Integration removed", "INTEGRATION_ADDED": "Integration added", "ACCEPTED_ESIGN_REQUEST_TERMS": "Accepted the terms and conditions for requesting electronic signatures", "ACCEPTED_ESIGN_SIGNATURE_TERMS": "Accepted the terms and conditions for signing electronic documents", "ESIGNATURE_REQUEST": "Requested eletronic signature of a document", "CONTENT_ESIGNATURE": "Electronically signed a document", "CONTENT_ESIGNATURE_DOWNLOAD": "Downloaded an electronically signed document", "RESEND_ESIGNATURE_REQUEST": "Resent document eletronic signature request", "ESIGNATURE_REQUEST_CLOSED": "The eletronic signature of a document has been completed (automatically)", "ESIGNATURE_REQUEST_REMINDER": "Requested eletronic signature of a document (Automatic reminder)", "DIGITAL_SIGNATURE_REQUEST": "Requested digital signature of a document", "CONTENT_DIGITAL_SIGNATURE": "Digitally signed a document", "CONTENT_DIGITAL_SIGNATURE_DOWNLOAD": "Downloaded an digitally signed document", "RESEND_DIGITAL_SIGNATURE_REQUEST": "Resent document digital signature request", "DIGITAL_SIGNATURE_REQUEST_CLOSED": "The digital signature of a document has been completed (automatically)", "DIGITAL_SIGNATURE_REQUEST_REMINDER": "Requested digital signature of a document (Automatic reminder)", "USER_PERSONAL_DATA_UPDATED": "The user personal data was updated", "DELETED_MEETING_AND_MINUTE": "Meeting minute deleted due to meeting deletion", "MEETING_LISTENER_GUEST_ADD": "Added a listener guest", "MEETING_LISTENER_GUEST_DELETE": "Removed listener guest", "MEETING_LISTENER_RECURRING_GUEST_ADD": "Added a saved guest", "MEETING_LISTENER_RECURRING_GUEST_DELETE": "<PERSON><PERSON><PERSON> saved guest", "MEETING_LISTENER_RECURRING_GUEST_UPDATE": "Updated saved guest", "MEETING_AGENDA_LISTENER_GUEST_ADD": "Added a listener guest to meeting agenda", "MEETING_AGENDA_LISTENER_GUEST_DELETE": "Removed listener guest from meeting agenda", "MEETING_AGENDA_RECURRING_GUEST_ADD": "Added a saved guest to meeting agenda", "MEETING_AGENDA_RECURRING_GUEST_DELETE": "Removed saved guest from meeting agenda", "MEETING_AGENDA_RECURRING_GUEST_UPDATE": "Updated saved guest from meeting agenda", "INCLUDED_AGENDA_ATTACHMENT_BLUEBOOK": "Included a meeting agenda attachment in the BlueBook", "REMOVED_AGENDA_ATTACHMENT_BLUEBOOK": "Removed a meeting agenda attachment from the BlueBook", "REQUEST_CODE_BY_WHATSAPP": "Request code by WhatsApp", "USER_CPF_ADDED": "CPF Included by the user", "ADMIN_CPF_ADDED": " Included the user CPF", "SHARED_BLUEBOOK_NOTES": "Shared a BlueBook note with a user", "CREATED_CONTENT_FROM_FOLDER": "Imported content through a folder", "ATTACHMENT_ADD_FROM_FOLDER": "Added an attachment through a folder", "IMPORTED_CONTENT": "Content imported thorugh another content", "IMPORTED_COMMENTS": "Comments imported through another content", "IMPORTED_ATTACHMENTS": "Attachments imported through another content", "CHANGED_CLIENT_PLAN": "Changed the client plan", "ENABLED_PASSWORD_EXPIRATION": "Enabled the password expiration for the client", "DISABLED_PASSWORD_EXPIRATION": "Disabled the password expiration for the client", "ENABLED_DEVICE_APPROVAL": "Enabled device approval for the client", "DISABLED_DEVICE_APPROVAL": "Disabled device approval for the client", "ENABLED_PASSWORD_STRENGTH": "Enabled password strength for the client", "DISABLED_PASSWORD_STRENGTH": "Disabled password strength for the client", "POLL_REPORT_VIEW": "Viewed the resolution report", "POLL_REPORT_GENERATED": "Generated the resolution report", "POLL_REPORT_AUTO_GENERATED": "Generated the resolution report (automatically)", "WORKGROUP_TASKLIST_CREATED": "Added a list to the project", "WORKGROUP_TASKLIST_DELETED": "Removed the project list", "WORKGROUP_TASKLIST_REORDER": "Reordered project lists", "WORKGROUP_TASKLIST_UPDATE": "Updated the project list", "UPDATED_BLOCK_APP_PRINT_SCREEN": "Changed the App print screen block", "UPDATED_BLOCK_FILE_DOWNLOAD": "Changed file download block", "UPDATED_BLOCK_FILE_PRINT": "Changed file printing block", "UPDATED_DAYS_TO_BLOCK_INACTIVE_USERS": "Updated days to block inactive users", "UPDATED_SCREEN_LOCK_MINUTES": "Changed the screen lock time", "UPDATED_WRONG_PASSWORD_ATTEMPTS": "Changed the wrong password attempts", "TASK_MOVED": "Task moved", "STATUS_ACTIVE": "Set the task as active", "REGISTRATION_CODE_FAILED": "Failed to validate the token on registration", "REQUEST_CODE_BY_SMS": "Request Token by SMS", "REQUEST_CODE_BY_VOICE": "Request Token by voice", "RESEND_DIGITAL_SIGNATURE__REQUEST": "Resent document digital signature request", "PERMISSIONS_REMOVED": "Removed a user permission", "BACKUP_CANCELLED": "Cancelled the backup", "BACKUP_FINISHED": "Backup finished", "UPDATED_CLIENT_NAME": "Updated the client name", "UPDATED_CLIENT_FULLNAME": "Updated the client fullname", "UPDATED_CLIENT_TAXNUMBER": "Updated the client tax number", "ENABLED_SIGN_TUTORIAL": "Enabled viewing the document signing tutorial", "DISABLED_SIGN_TUTORIAL": "Disabled viewing the document signing tutorial", "USER_SIGNATURE_UPDATED": "The user updated his signature/initials image", "SIGNATURE_REJECTED": "Rejected a document signature", "FULLNAME_ADDED": "Changed the full name", "ATTACHMENT_NOTIFICATION": "Shared a content", "KB_BACKUP_REQUESTED": "Requested download of Knowledge Base attachments", "KB_BACKUP_REQUESTED_COMPANY": "Requested download of attachments from Company Folders", "ATTACHMENTS_KB_BACKUP_DOWNLOADED": "Downloaded knowledge base attachments", "ATTACHMENTS_KB_BACKUP_DOWNLOADED_COMPANY": " Downloaded attachments from Company Folders", "DIGITAL_SIGNATURE_PRINTERFRIENDLY_DOWNLOAD": "Downloaded the printer-friendly version of a digitally signed document", "DIGITAL_SIGNATURE_EXTERNAL_DOWNLOAD": "Downloaded the signature log of a digitally signed document", "ENABLED_ADDON_SSO": "Add-On/Plan enabled - Single Sign-On", "DISABLED_ADDON_SSO": "Add-On/Plan disabled - Single Sign-On", "ENABLED_ADDON_ACCESS_RESTRICTION": "Add-On/Plan enabled - IP/Region restriction", "DISABLED_ADDON_ACCESS_RESTRICTION": "Add-On/Plan disabled - IP/Region restriction", "ACCESS_RESTRICTION_CHANGED": "Managed the access restriction rules", "CHECKLIST_ITEM_ADD": "Added a checklist item", "CHECKLIST_ITEM_UPDATE": "Updated a checklist item", "CHECKLIST_ITEM_DELETE": "Deleted a checklist item", "CHECKLIST_REORDER": "Reordered the checklist", "ENABLED_ADDON_DEVICE_APPROVAL": "Add-On/Plan enabled - <PERSON><PERSON> Approval", "DISABLED_ADDON_DEVICE_APPROVAL": "Add-On/Plan disabled - <PERSON><PERSON>", "CLIENTOPERATION_REQUESTED_PERMISSION": "Requested batch permission", "BLUEBOOK_VIEW": "Viewed the meeting's BLUEBOOK", "BLUEBOOK_VERSION_VIEW": "Viewed a meeting's BLUEBOOK version", "ATTACHMENT_VIEW": "Viewed the content", "MINUTE_VIEW": "Viewed the minute", "CONTENT_ESIGNATURE_VIEW": "Viewed an electronically signed document", "CONTENT_DIGITAL_SIGNATURE_VIEW": "Viewed an digitally signed document", "DIGITAL_SIGNATURE_EXTERNAL_VIEW": "Viewed the signature log of a digitally signed document", "DIGITAL_SIGNATURE_PRINTERFRIENDLY_VIEW": "Viewed the printer-friendly version of a digitally signed document", "POLL_REPORT_DOWNLOAD": "Downloaded the resolution report", "ADMIN_REVOKED_ALL_SESSIONS": "Administrator Revoked all sessions", "INBOX_ITEM_DISMISS": "Dismissed a inbox item", "LOGIN_FAILED_RESTRICTEDACCESS": "Login failed due to IP/Region restriction", "ENABLED_ADDON_EXTERNAL_SIGNERS": "Add-On/Plan enabled - External Signers", "DISABLED_ADDON_EXTERNAL_SIGNERS": "Add-On/Plan disabled - External Signers", "EXTERNAL_SIGNER_UPDATE": "Updated an external signer data", "EXTERNAL_SIGNER_DELETE": "Deleted an external signer", "LOGIN_FAILED_SSO_ERROR": "<PERSON><PERSON> failed via SSO", "ENABLED_KB_DEFAULTFOLDERS": "Enabled knowledge base default folders", "DISABLED_KB_DEFAULTFOLDERS": "Disabled knowledge base default folders", "ENABLED_2FA_ELECTRONIC_SIGNATURE": "Enabled approval token for electronic signatures", "DISABLED_2FA_ELECTRONIC_SIGNATURE": "Disabled approval token for electronic signatures", "USER_BLOCKED_IMPOSSIBLE_TRAVEL": "User access blocked by \"Impossible Travel\"", "ENABLED_ADDON_IMPOSSIBLE_TRAVEL": "Add-On/Plan enabled - \"Impossible Travel\"", "DISABLED_ADDON_IMPOSSIBLE_TRAVEL": "Add-On/Plan disabled - \"Impossible Travel\"", "SOC_KEY_GENERATE": "Security Logs Key Generated", "ENABLED_ADDON_SECURITY_LOGS": "Add-On/Plan enabled - Security Logs", "DISABLED_ADDON_SECURITY_LOGS": "Add-On/Plan disabled - Security Logs", "RECURRING_MEETING_CREATED": "Created a recurring meeting", "RECURRING_MEETING_DELETED": "Deleted a recurring meeting", "RECURRING_MEETING_DELETED_RESET": "Deleted a recurring meeting", "RECURRING_MEETING_UPDATED": "Updated a recurring meeting", "RECURRING_MEETING_UPDATED_RESET": "Updated a recurring meeting", "RECURRING_MEETING_DECOUPLE": "Occurrence uncoupled from recurrence", "AGENDA_POSTPONED": "Postponed the meeting agenda", "FORM_UNPUBLISHED": "Unpublished the Form", "FORM_PUBLISHED": "Published the Form", "FORM_DUPLICATED": "Form created based on Form", "FORM_CLOSED": "Closed the Form", "FORM_EXPIRED": "Form automatically closed after exceed the deadline", "FORM_REPORT_EXPORTED": "Exported a report From the form", "FORM_REQUEST_ANSWER_RESEND": "Resent the Form", "REQUEST_ANSWER": "Asked to answer the Form", "RESEND_ICS_MEETING_GUESTS": "ICS resend to meeting listener guest", "RESEND_ICS_AGENDA_PRESENTERS": "ICS resend to external agenda presenter", "RESEND_ICS_AGENDA_GUESTS": "ICS resend to agenda listener guest", "BLOCK_SSO_SETTING_ENABLED": "SSO block setting - Enabled", "BLOCK_SSO_SETTING_DISABLED": "SSO block setting - Disabled", "CLIENT_DOMAIN_ENFORCEMENT_ENABLED": "Enabled Authorized Domains", "CLIENT_DOMAIN_ENFORCEMENT_DISABLED": "Disabled Authorized Domains", "INSURANCE_POLICY_CREATED": "Insurance Policy created", "INSURANCE_POLICY_DELETED": "Insurance Policy deleted", "INSURANCE_UPDATED": "Insurance updated", "INSURANCE_ATTACHMENT_ADDED": "Added attachment to Insurance Policy", "INSURANCE_FORM_SUBMITTED": "Insurance Form submission completed", "INSURANCE_FORM_CREATED": "Insurance Form created", "INSURANCE_FORM_UPDATED": "Insurancd Form updated", "INSURANCE_FORM_DELETED": "Insurance Form deleted", "INSURANCE_FORM_DOWNLOAD": "Insurance form downloaded", "INSURANCE_FORM_FILE_VIEW": "Insurance form viewed", "INSURANCE_EXTERNAL_FORM_CREATED": "Insurance Form created (external user)", "INSURANCE_EXTERNAL_FORM_UPDATED": "Insurancd Form updated (external user)", "INSURANCE_EXTERNAL_FORM_SUBMITTED": "Insurance Form submission completed (external user)", "INSURANCE_QUOTE_REQUEST": "Insurance quote request sent", "INSURANCE_ACCEPTED_TERMS": "Accepted the insurance terms and conditions", "INSURANCE_HIDE_TUTORIAL": "Disabled the insurance tutorial", "INSURANCE_SHOW_TUTORIAL": "Viewed the insurance tutorial", "INSURANCE_ATTACHMENT_VIEW": "Viewed an Insurance Attachment", "INSURANCE_DOCUMENT_CREATED": "Insurance Document created", "CLIENT_ISO_IOC_DOCUMENT_VIEW": "ISO/IOC Document viewed", "CLIENT_ISO_IOC_DOCUMENT_DOWNLOAD": "ISO/IOC Document downloaded", "BLUEBOOK_AUTO_SYNC": "Bluebook synchronization performed by the APP", "FORM_ANSWERED": "Answered a form", "UPDATED_CLIENT_COUNTRY": "Updated the client country", "UPDATED_CLIENT_STATUS": "Updated the client status", "UNBLOCK_USER_REQUEST": "Requested self-unlock of the account", "UNBLOCK_USER_SUCCESS": "Account self-unlocked successfully", "CONTENT_ESIGNATURE_DOWNLOAD_NOM151": "Downloaded the constancy", "ENABLED_ADDON_NOM151": "Add-On/Plan enabled - NOM151", "DISABLED_ADDON_NOM151": "Add-On/Plan disabled - NOM151", "OWNER_NOTIFY_CONTENT_COMMENT_ENABLED": "Enabled: owner notification regarding comments on the meeting agenda or resolution", "OWNER_NOTIFY_CONTENT_COMMENT_DISABLED": "Disabled: owner notification regarding comments on the meeting agenda or resolution", "USER_BLOCKED_INACTIVITY": "User blocked for inactivity", "CONTENT_ESIGNATURE_VIEW_PARTIAL": "Viewed a partially electronically signed document", "CONTENT_ESIGNATURE_DOWNLOAD_PARTIAL": "Downloaded a partially electronically signed document", "CONTENT_DIGITAL_SIGNATURE_DOWNLOAD_PARTIAL": "Downloaded a partially digitally signed document", "CONTENT_DIGITAL_SIGNATURE_VIEW_PARTIAL": "Viewed a partially digitally signed document", "POLL_AVAILABLE": "Made resolution available", "POLL_HIDDEN": "Hid the resolution", "SIGNATURE_REQUEST_CANCELLED": "Cancelled signature request", "EXTERNAL_CORPORATE_BOOK_DOC_UPLOADED": "External Minutes Book Document uploaded", "EXTERNAL_CORPORATE_BOOK_DOC_DELETED": "External Minutes Book Document deleted", "INSURANCE_EXTERNAL_FORM_RESEND": "Resent insurance form (external user)", "INSURANCE_EXTERNAL_FORM_COMPLETED": "Finished insurance form (external user)", "DISABLED_ADDON_ATLAS_AI": "Disabled Add-On/Plan - Atlas AI", "ENABLED_ADDON_ATLAS_AI": "Enabled Add-On/Plan - Atlas AI", "ENABLED_ADDON_ATLAS_AI_PROMPT": "Enabled Add-On/Plan - Atlas AI Prompt", "DISABLED_ADDON_ATLAS_AI_PROMPT": "Disabled Add-On/Plan - Atlas AI Prompt", "ENABLED_ATLAS_AI_PROMPT": "Enabled Atlas AI Prompt", "DISABLED_ATLAS_AI_PROMPT": "Disabled Atlas AI Prompt", "ENABLED_ATLAS_AI": "Enabled Atlas AI", "DISABLED_ATLAS_AI": "Disabled Atlas AI", "MEETING_ATLAS_AI_ENABLED": "Meeting - Atlas AI Enabled", "MEETING_ATLAS_AI_DISABLED": "Meeting - Atlas AI Disabled", "MINUTES_BOOK_REQUESTED": "Requested the generation of the minutes book", "MINUTES_BOOK_FINISHED": "Finished the generation of the minutes book", "MINUTES_BOOK_DOWNLOADED": "Downloaded the minutes book", "MINUTES_BOOK_DELETED": "Deleted the minutes book", "MINUTES_BOOK_RENAMED": "Renamed the minutes book", "VIEW_USER_PROFILE": "Viewed the user profile", "ENABLED_NEW_DEVICES_2FA": "Enabled 2FA for known devices (SSO)", "DISABLED_NEW_DEVICES_2FA": "Disabled 2FA for known devices (SSO)", "EXTERNAL_USER_DELETED": "External document request user deleted", "RECURRING_EXTERNAL_USER_DELETED": "Deleted external document request recurring user", "EXTERNAL_USER_ADDED": "External document request user added", "RECURRING_EXTERNAL_USER_ADD": "External document request recurring user added", "EXTERNAL_DOCUMENT_REQUEST_DELETED": "External document request deleted", "EXTERNAL_USER_DELETED_DOCUMENT_REQUEST": "External document request user unlinked due to request deletion", "EXTERNAL_USER_UPDATED_DOCUMENT_REQUEST": "External document request user updated with request info", "EXTERNAL_DOCUMENT_REQUEST": "External document request sent", "EXTERNAL_DOCUMENT_REQUEST_RESENT": "External document request resent", "EXTERNAL_USER_UPDATED": "Updated external document request user", "EXTERNAL_DOCUMENT_REQUEST_COMPLETED": "External document request completed", "EXTERNAL_DOCUMENT_REQUEST_SENT": "Documents sent for the external document request", "EXTERNAL_DOCUMENT_REQUEST_SENT_OWNER": "Information regarding external document request sent to the requester", "RECURRING_EXTERNAL_USER_UPDATED": "Updated external document request recurring user", "ENABLED_ATLAS_AI_HIDE_TRANSCRIPTION": "Atlas AI Meeting Transcription hiding enabled for attendees", "DISABLED_ATLAS_AI_HIDE_TRANSCRIPTION": "Atlas AI Meeting Transcription hiding disabled for attendees", "MEETING_ATLAS_AI_JOIN_REQUESTED": "Requested <PERSON> to join the meeting", "FORM_REPUBLISHED": "Republished the Form", "ENABLED_IMPOSSIBLE_TRAVEL": "Impossible Travel enabled", "DISABLED_IMPOSSIBLE_TRAVEL": "Impossible Travel disabled", "USER_ADMIN_SET_OAUTH": "Added admin via JWT Roles", "USER_ADMIN_REMOVE_OAUTH": "Removed admin via JWT Roles", "UPDATE_SUMMARY_TYPE_MEETING_ATLAS_AI": "Updated the summary type for Atlas AI meeting transcription", "UPDATE_SUMMARY_MEETING_ATLAS_AI": "Updated the summary for Atlas AI meeting transcription", "USER_SESSION_DATA_EXPORT": "Exported user session data", "CLIENT_USER_SESSION_DATA_EXPORT": "Exported user session data", "UPDATED_SIGNATURE_REQUEST": "Updated the signature request", "MEETING_STATUS_CANCELLED": "Cancelled the meeting", "MEETING_STATUS_CANCELLED_AGENDA_GUESTS": "Cancelled the meeting agenda for guests", "MEETING_STATUS_CANCELLED_LISTENER_GUESTS": "Cancelled the meeting for listener guests", "INSIGHTS_REPORT_DOWNLOAD": "Downloaded the Atlas Insights report"}}