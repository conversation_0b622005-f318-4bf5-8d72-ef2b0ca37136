using Atlas.Business.ViewModels;
using Atlas.Data.Entities;
using Atlas.Data.Repository;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Atlas.CrossCutting.AppEnums;

namespace Atlas.Business
{

    public class FeatureManagerService
    {
        int _currentUser;
        protected readonly ClientService _clientService;
        FeatureManagerRepository _repo = new FeatureManagerRepository();

        public FeatureManagerService(int userId)
        {
            _currentUser = userId;
            _clientService = new ClientService(userId);
        }

        public async Task<List<int>> GetClientsWithFeatureEnabled(string featureName)
        {
            List<Client> clients = await _clientService.GetAllForProfile();
            var clientsEnabled = _repo.GetClientsWithFeatureEnabled(clients, featureName);
            return clientsEnabled.Select(c => c.clientId).ToList();
        }

        public async Task<bool> addPlanQuotaLog(int clientId, string featureName, int quotaValue, string logDetails, string updateReason, DateTime? referenceDate = null, string referenceType = null)
        {
            PlanQuotaActivity newLog = new PlanQuotaActivity();

            newLog.clientId = clientId;
            newLog.featureName = featureName;
            newLog.quotaValue = quotaValue;
            newLog.logUserId = _currentUser;
            newLog.logDetails = logDetails;
            newLog.updateReason = updateReason;
            newLog.logDate = DateTime.UtcNow;
            newLog.referenceDate = referenceDate;
            newLog.referenceType = referenceType;

            try
            {
                var isCreated = await _repo.AddPlanQuotaLog(newLog);
                return isCreated;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public async Task<FeatureControl> GetFeatureControl(List<Client> userClients)
        {
            FeatureControl featureControl = new FeatureControl();
            string[] featuresArray = { "POLL", "CHAT", "PROJECT", "SUPPORT_CHAT", "SEARCH", "LOCKSCREEN" };
            string[] allowedPlans = new[] { PlanNames.ENTERPRISE, PlanNames.PROFESSIONAL };

            var features = await isEnabledInAnyClient(featuresArray, userClients);

            featureControl.showProjects = features.ContainsKey(PlanFeatureNames.PROJECT) ? features[PlanFeatureNames.PROJECT] : false;
            featureControl.showChat = features.ContainsKey(PlanFeatureNames.CHAT) ? features[PlanFeatureNames.CHAT] : false;
            featureControl.createPoll = features.ContainsKey(PlanFeatureNames.POLL) ? features[PlanFeatureNames.POLL] : false;
            featureControl.showSupportChat = features.ContainsKey(PlanFeatureNames.SUPPORT_CHAT) ? features[PlanFeatureNames.SUPPORT_CHAT] : false;
            featureControl.showSearch = features.ContainsKey(PlanFeatureNames.SEARCH) ? features[PlanFeatureNames.SEARCH] : false;
            featureControl.showLockscreen = features.ContainsKey(PlanFeatureNames.LOCKSCREEN) ? features[PlanFeatureNames.LOCKSCREEN] : false;
            featureControl.showForms = userClients.Any(c => allowedPlans.Contains(c.planName));
            featureControl.showInsights = userClients.Any(c => c.planName == PlanNames.ENTERPRISE);

            return featureControl;
        }

        public async System.Threading.Tasks.Task checkPlanQuota(int clientId, string featureName, bool exceedsOnBiggerOrIgual = true, long valueToSummarize = 0)
        {
            if (await this.GetPlanQuota(clientId, featureName, exceedsOnBiggerOrIgual, valueToSummarize))
            {
                throw new InvalidOperationException(featureName + "_EXCEEDED");
            }
        }

        /// <summary>
        /// Query the client quota in database and get if is exceeded. 
        /// </summary>
        /// <param name="clientId"></param>
        /// <param name="featureName"></param>
        /// <returns>isExceeded = true|false</returns>
        public async Task<bool> GetPlanQuota(int clientId, string featureName, bool exceedsOnBiggerOrIgual, long valueToSummarize = 0)
        {
            Client client = await _clientService.Get(clientId);
            //Get the planFeature only from clients that has the feature quota enabled
            PlanFeature planFeature = await _repo.GetPlanFeature(client.planName, featureName);

            //If the planFeature is null, it can mean that the client hasn't feature quota enabled, then the fuction return false
            if (planFeature != null && planFeature.featureEnabled)
            {
                List<PlanQuotaActivity> quotasActivity = await _repo.GetPlanQuotaActivities(clientId, featureName, true);

                //Calculates if the client has already reached the feature quota that have an annual frequency
                if (planFeature.featureQuotaFrequency == "Y")
                {
                    var cycle = await CalculateClientCycle(clientId);
                    int[] quotaValues = quotasActivity.Where(q => q.referenceDate <= cycle.endCycleDate && q.referenceDate >= cycle.startCycleDate).Select(q => q.quotaValue).ToArray();

                    long sum = quotaValues.Sum() + valueToSummarize;

                    if (exceedsOnBiggerOrIgual ? sum >= planFeature.featureQuotaAmount : sum > planFeature.featureQuotaAmount)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }

                }
                //Calculates if the storage quota has been reached by the client
                else if (featureName == "STORAGE_QUOTA")
                {
                    long quotaValues = 0;

                    var cycle = await CalculateClientCycle(clientId);
                    var list = quotasActivity.Where(q => q.logDate <= cycle.endCycleDate && q.logDate >= cycle.startCycleDate).Select(q => q.quotaValue);

                    foreach (var q in list)
                    {
                        quotaValues += q;
                    }

                    long sum = quotaValues + valueToSummarize;
                    var quotaGiga = planFeature.featureQuotaAmount * 1073741824;

                    if (sum >= quotaGiga)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
                //Calculates if the client has already reached the quota for features that do not have a defined frequency and that 
                //are not storage_quota such as board_quota
                else
                {
                    int[] quotaValues = quotasActivity.Select(q => q.quotaValue).ToArray();
                    long sum = quotaValues.Sum() + valueToSummarize;

                    if (exceedsOnBiggerOrIgual ? sum >= planFeature.featureQuotaAmount : sum > planFeature.featureQuotaAmount)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
            }
            else
            {
                return false;
            }
        }

        public async Task<bool> isEnabledByContent(Content content, string featureName)
        {
            return await this.isEnabledByClient(featureName, content.Workgroup.clientId);
        }

        public async Task<bool> isEnabledByClient(string featureName, int clientId)
        {
            Client client = await _clientService.Get(clientId);
            return await this.isEnabledByClient(featureName, client.planName);
        }

        public async Task<bool> isEnabledByClient(string featureName, string clientPlanName)
        {
            var clientPlan = await _repo.GetPlanFeature(clientPlanName, featureName);
            return clientPlan != null && clientPlan.featureEnabled;
        }

        public async Task<Dictionary<string, bool>> isEnabledInAnyClient(string[] featuresName, List<Client> userClients = null)
        {
            var dictionaryFeatures = featuresName.ToDictionary(f => f, f => false);
            var usv = new UserService();

            if (userClients == null)
            {
                userClients = await _clientService.GetAllForProfile();
            }

            var dictionary = await _repo.GetEnabledPlanFeaturesDictionary(userClients, featuresName);

            foreach (var p in dictionary)
            {
                if (dictionaryFeatures.ContainsKey(p.Key))
                {
                    dictionaryFeatures[p.Key] = p.Value;
                }
            }

            return dictionaryFeatures;
        }


        public async Task<bool> isEnabledByWorkgroup(int workgroupId, string featureName)
        {
            WorkgroupService wkService = new WorkgroupService(_currentUser);
            var wk = await wkService.Get(workgroupId, false);
            return await isEnabledByClient(featureName, wk.clientId);

        }

        public async Task<ClientCycle> CalculateClientCycle(int clientId)
        {
            Client client = await _clientService.Get(clientId);

            DateTime activationDate = client.planActivationDate.Value;
            DateTime today = DateTime.Now;
            DateTime startDateYear = activationDate;
            DateTime endDateYear = new DateTime(activationDate.Year, 12, 31, 23, 59, 00);

            while (today > endDateYear)
            {
                startDateYear = new DateTime(startDateYear.Year + 1, 1, 1, 00, 00, 00);
                endDateYear = new DateTime(endDateYear.Year + 1, 12, 31, 23, 59, 00);
            }

            ClientCycle cycle = new ClientCycle();
            cycle.startCycleDate = startDateYear;
            cycle.endCycleDate = endDateYear;

            return cycle;

        }

        public async Task<List<string>> GetPlanNamesWithFeatureEnabled(string featureName)
        {
            var planFeatures = await _repo.GetByFeatureNameEnabled(featureName);

            return planFeatures.Select(p => p.planName).ToList();
        }

        public async Task<IEnumerable<PlanFeature>> GetPlanFeaturesByName(IEnumerable<string> features)
        {
            return await _repo.GetPlanFeaturesByName(features);
        }
    }

    public class ClientCycle
    {
        public ClientCycle() { }

        public DateTime startCycleDate { get; set; }
        public DateTime endCycleDate { get; set; }
    }
}
