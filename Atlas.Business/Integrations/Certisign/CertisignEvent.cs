namespace Atlas.Business.Integrations.Certisign
{
    public class CertisignEvent
    {

        public int documentId { get; set; } // DocumentKey at ContentSignatureRequest

        public string name { get; set; } // Supposed to be the signers name, but it is always null for some reason

        public string identifier { get; set; } // Signers CPF, wont be used for now

        public string action { get; set; } // SIGNATURE-ELETRONIC ou SIGNATURE-DIGITAL

        public string message { get; set; } // This field is always null in the context of electronic/digital signatures

        public string apiDownload { get; set; } // Signature files download link

    }
}
