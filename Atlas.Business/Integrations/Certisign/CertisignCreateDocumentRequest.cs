namespace Atlas.Business.Integrations.Certisign
{
    public class CertisignCreateDocumentRequest
    {
        public int signatureStandard { get; set; }

        public CertisignDocument document { get; set; }

        public CertisignSender sender { get; set; }

        public CertisignSigner[] signers { get; set; }   // This is will hold signers using Digital Signatures ( Digital Certificates )

        public CertisignSigner[] electronicSigners { get; set; } // This one will hold the signers using Eletronic Signatures

        // This field is used to set the document type at Certisign in case the signature request has more than 10 signers
        // This field is defined in the Azure Functions Configuration section (app settings) CERTISIGN_CUSTOM_DOC_TYPEID
        public int? typeId { get; set; } 
        
    }
}
