using System.Collections.Generic;

namespace Atlas.Business.Integrations.Certisign
{
    public class CertisignDigitalSignature
    {
        public string date { get; set; }
        public CertisignCertificate certificate { get; set; }
        public object policy { get; set; }
        public bool validPolicy { get; set; }
        public List<string> signedAttributes { get; set; }
        public List<object> unsignedAttributes { get; set; }
        public List<object> attributeCertificates { get; set; }
        public string cnpj { get; set; }
        public string company { get; set; }
        public bool intact { get; set; }
    }
}
