using System.Collections.Generic;

namespace Atlas.Business.Integrations.Certisign
{
    public class CertisignValidateSignaturesResponse
    {

        public string documentName { get; set; }

        public string key {get; set;}

        public string creationDate { get; set; }

        public string documentHash { get; set; }

        public bool? isValid { get; set; }

        public bool hasOriginalVersion { get; set; }

        public bool canDownloadProtocol { get; set; }

        public List<CertisignDigitalSignature> signatures { get; set; }



}
}
