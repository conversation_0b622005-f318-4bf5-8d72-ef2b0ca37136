using System.Text;
using iText.Kernel.Pdf;
using Lacuna.RestPki.Api.PadesSignature;
using Lacuna.RestPki.Client;

namespace Atlas.Business.Integrations.Lacuna
{
    public class PadesVisualElements
    {

		public static PadesVisualRepresentation GetAtlasSignPADESVisualRepresentation(byte[] visualContent, int pageNumber, double x, double y, string userId, PdfPage pdfPage = null, bool isInitial = false)
		{

			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Append("#");
			stringBuilder.Append(userId);
			stringBuilder.Append("\n");

			// This one will be repaced by the Lacuna Rest PKI
			// See tags - https://github.com/LacunaSoftware/RestPkiSamples/blob/master/PadesTags.md
			stringBuilder.Append("{{name}}");
				
			PadesVisualText padesVisualText = new PadesVisualText();
	
			// Create a visual representation.
			var visualRepresentation = new PadesVisualRepresentation()
			{

				//Text = new PadesVisualText("Signed by {{name}}")
				Text = new PadesVisualText()
				{

			  		 Text = stringBuilder.ToString(),
				     FontSize = 13.0,
					 IncludeSigningTime = true,
					 
					HorizontalAlign = PadesTextHorizontalAlign.Left,
					
					Container = new PadesVisualRectangle()
					{
						Left = 10,
						//Top = 32.5,
						Top = 26.5,
						Right = 0,
						Bottom = 7
				
					}
				},
				
				Image = new PadesVisualImage()
				{

					Content = visualContent,
					HorizontalAlign = PadesHorizontalAlign.Left,
					VerticalAlign = PadesVerticalAlign.Top
					
				},
			};


			PadesVisualManualPositioning positioning = new PadesVisualManualPositioning();

			PadesVisualRectangle visualRectangle = new PadesVisualRectangle();

			int rotation = pdfPage.GetRotation();

			float pageWidth = pdfPage.GetPageSize().GetWidth();

			if ( rotation == 90)
			{

				visualRectangle.Right = y + 50;
				visualRectangle.Top = x;

			}else if ( rotation == 270)
			{

				visualRectangle.Left = y;
				visualRectangle.Bottom = x;
			}
			else if (rotation == 180)
			{
				visualRectangle.Right = x + 50;
				visualRectangle.Bottom = y;

			}
			else
			{
				// No rotation, doesnt need any treatment at all
			
				if ( isInitial && ((x + 100) > pageWidth) )
				{
					x = x - (x + 100 - pageWidth); 
				}

				visualRectangle.Left = x ;
				visualRectangle.Top = y;
			}


			visualRectangle.Height = 45;
			//visualRectangle.Width = 250;
			visualRectangle.Width = 100;

			positioning.SignatureRectangle = visualRectangle;
			positioning.PageNumber = pageNumber;

			visualRepresentation.Position = positioning;

			return visualRepresentation;
		}


	}
}
