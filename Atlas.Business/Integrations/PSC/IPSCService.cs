using System.Threading.Tasks;

namespace Atlas.Business.Integrations.PSC
{

    public interface IPSCService
    {
        /// <summary>
        /// Generates the constancy at the PSC provider (currently we only have PSCWorld, but it can be assumed that we will have others in the future)
        /// A constancy is just a record that proves that the document was submitted to the PSC. Its only a regular string.
        /// </summary>
        /// <param name="bytes">The hash used to generate the constancy</param>
        /// <param name="identifier">Identifier of the record, it will be randomly generated if nothing is provided</param>
        /// <param name="skipValidation">By default, we will submit the constancy, retrieve it and test it (encouraged). If false we will just submit the constancy</param>
        /// <returns></returns>
        Task<NOM151ConstancyResult> GenerateNOM151ConstancyAsync(string sha256Hash, string identifier = null, bool skipValidation = false);
    }
}
