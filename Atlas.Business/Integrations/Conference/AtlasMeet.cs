using Atlas.Data.Entities;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Atlas.Business.Integrations.Conference
{
    public class AtlasMeet : IConference
    {
        public ConferenceData CreateConference(Content content, UserIntegrationSession lastIntegrationSession)
        {
            return GetConferenceData(content, lastIntegrationSession);
        }

        public List<UserIntegrationSession> GetLastIntegrationOrSharedIntegration(AtlasModelCore model, int currentUserId)
        {
            if (model == null)
                model = new AtlasModelCore();

            string serviceName = "atlasmeet";

            var lastIntegrationSession = model.UserIntegrationSession.Where(o => o.userId == currentUserId && o.serviceName == serviceName).OrderByDescending(o => o.expiryDate).FirstOrDefault();
            var userIntegration = model.UserIntegration.Where(o => o.userIntegrationId == lastIntegrationSession.userIntegrationId).FirstOrDefault();
            lastIntegrationSession.UserIntegration = userIntegration;

            return new List<UserIntegrationSession>() { lastIntegrationSession };
        }

        public ConferenceData UpdateConference(Content ori, Content mod, UserIntegrationSession lastIntegrationSession)
        {
            return GetConferenceData(mod, lastIntegrationSession);
        }

        ConferenceData IConference.DeleteConference(Content content, UserIntegrationSession lastIntegrationSession)
        {
            throw new NotImplementedException();
        }

        private ConferenceData GetConferenceData(Content content, UserIntegrationSession lastIntegrationSession)
        {
            var conferenceID = Guid.NewGuid().ToString().ToUpper();

            return new ConferenceData()
            {
                conferenceURL = "https://meet.atlasgov.com/" + conferenceID,
                conferenceID = conferenceID,
                conferenceType = "atlasmeet",
                conferenceCreateDate = DateTime.UtcNow
            };
        }
    }
}
