using Atlas.Data.Entities;
using System.Collections.Generic;

namespace Atlas.Business.Integrations.Conference
{
    public interface IConference
    {
        List<UserIntegrationSession> GetLastIntegrationOrSharedIntegration(AtlasModelCore model, int currentUserId);
        ConferenceData CreateConference(Content content, UserIntegrationSession lastIntegrationSession);
        ConferenceData DeleteConference(Content content, UserIntegrationSession lastIntegrationSession);
        ConferenceData UpdateConference(Content ori, Content mod, UserIntegrationSession lastIntegrationSession);
    }
}
