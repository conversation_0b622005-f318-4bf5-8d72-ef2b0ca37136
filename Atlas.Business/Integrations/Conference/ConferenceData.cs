using Atlas.CrossCutting.Helpers.OAuthAuthenticator;
using System;

namespace Atlas.Business.Integrations.Conference
{
    public class ConferenceData
    {

        public string conferenceType { get; set; }
        public string conferenceID { get; set; }
        public string conferenceURL { get; set; }
        public DateTime? conferenceCreateDate { get; set; }
        public string conferenceRequestData { get; set; }
        public string conferenceResponseData { get; set; }
        public OAuth2AuthenticationResult auth_result { get; set; }
        
        public string reason { get; set; }
        public string error { get; set; }
        public string account_id { get; set; }
    }
}
