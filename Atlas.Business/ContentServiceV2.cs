using Atlas.Business.Helpers;
using Atlas.CrossCutting.AppEnums;
using Atlas.Data.Entities;
using Atlas.Data.Repository;
using System;
using System.Collections.Generic;
using System.Security;
using System.Threading.Tasks;
using System.Linq;
using Atlas.Business.ViewModels.MeetingsNavigation;
using Atlas.CrossCutting.DTO.MeetingsNavigational;

namespace Atlas.Business
{
    public class ContentServiceV2
    {
        protected User user;
        protected ContentRepository _repo;

        public ContentServiceV2(User user)
        {
            this.user = user;
            this._repo = new ContentRepository(user.userId);
        }

        public async Task<Content> Get(int contentId)
        {
            return await _repo.GetSimpleContent(contentId);
        }

        public async Task<List<Content>> GetAll(ContentRequestFilter filters)
        {
            Content parentContent = new Content();

            if (filters.parentContentUuid.HasValue)
            {
                parentContent = await _repo.GetSimpleContent(filters.parentContentId.Value);

                if (parentContent.type != ContentTypes.Meeting)
                {
                    throw new InvalidOperationException("INVALID_PARENT_CONTENT_TYPE");
                }

                var client = parentContent.Workgroup.Client;

                if (client.blocked || client.deleted)
                {
                    throw new SecurityException("CLIENT_DELETED_OR_BLOCKED");
                }
            }

            var contentList = await this.GetByFilters(filters, parentContent?.status);

            if (contentList.Any())
            {
                // Post filter actions

                if (filters.type.ToLower() == "meetingminute")
                {
                    var meetingService = new MeetingService(user);
                    var meeting = await meetingService.Get(filters.parentContentUuid.Value);

                    if (!meeting.UAC.view_child_minute)
                    {
                        foreach (var content in contentList)
                        {
                            content.MeetingMinute.Clear();
                        }
                    }
                }

                if (filters.type.ToLower() == "meetingagendaitem")
                {
                    contentList = PermissionHelper.Clear_MeetingAgenda(contentList, user.userId).ToList();
                    var agendaItemList = contentList.Where(o => o.type == ContentTypes.MeetingAgendaItem)
                        .SelectMany(o => o.MeetingAgendaItem)
                        .ToList();

                    updateMeetingAgendas(agendaItemList, parentContent.status);
                }

                if (filters.type.ToLower() == "task")
                {
                    await this.HandleTasksPermissions(contentList, parentContent.contentId, parentContent.workgroupId);
                }
            }

            return contentList;
        }

        private void updateMeetingAgendas(List<MeetingAgendaItem> agendaItemList, string parentStatus)
        {
            if (agendaItemList is null) { return; }

            AtlasModelCore _md = new AtlasModelCore();
            foreach (var child in agendaItemList)
            {
                var attachments = _md.ContentAttachment.Where(o => o.contentId == child.contentId && o.deleted != true).ToList();

                if (attachments != null && attachments.Count > 0)
                {
                    child.hasAttachment = true;
                }


                // Check if there are any video attachments
                var contentAttachmentIds = attachments.Select(ca => ca.attachmentId).ToArray();

                var videoExtensions = VideoHelper.GetValidVideoExtensions();

                int videoCount = _md.Attachment
                    .Where(a => contentAttachmentIds.Contains(a.attachmentId) && videoExtensions.Contains(a.extension))
                    .Count();

                child.totalVideosCount = videoCount;
                child.totalAttachmentCount = attachments.Count - videoCount;

                var agendaGuests = _md.ContentGuest.Where(o => o.contentId == child.contentId).ToArray();

                child.hasGuest = agendaGuests.Any();
                child.totalGuestCount = agendaGuests.Length;

                child.hasAttachmentNotIncludedInBlueBook = attachments.Where(ca => ca.contentId == child.contentId && !ca.includeOnBlueBook).Any();
                child.hasComments = false;
                var listComments = _md.ContentComment.Where(p => p.contentId == child.Content.contentId && !p.deleted).ToList();

                if (listComments != null && listComments.Any())
                {
                    listComments = listComments.Where(t => t.text != "<p></p>" && t.text != "<p><br></p>" && t.text != "." && t.text != "<p> </p>").ToList();
                    child.hasComments = listComments.Any();
                }

                child.totalCommentsCount = listComments.Count;

                child.hasExternalDocumentRequest = _md.ExternalDocumentRequest.Any(edr => edr.ContentId == child.contentId && edr.Status == "PENDING")
                                                   && _md.ContentOwner.Any(co => co.contentId == child.contentId && co.userId == user.userId)
                                                   && parentStatus != "CLOSED";
            }
        }

        private async Task<List<Content>> GetByFilters(ContentRequestFilter filters, string parentStatus = null)
        {
            if (String.IsNullOrEmpty(filters.type) || !filters.parentContentUuid.HasValue)
            {
                throw new InvalidOperationException("NO_REQUIRED_FILTERS");
            }

            var list = await this._repo.GetByFilters(filters, parentStatus);
            return list;
        }

        public async Task<MeetingsNavigationalViewModel> GetMeetingsNavigational(int contentId)
        {
            // 1. pegar uma lista de reunioes ordenadas pela data em ordem crescente
            // 2. buscar o indice da reuniao data (contentId) nessa lista
            // 3. identificar as reunioes imedianamente anterior e posterior a reuniao atual
            // 4. calcular o total de reunioes

            // Uma possivel: V1
            //int index = 55;
            //currentAvailableMeetings[index - 1];
            //currentAvailableMeetings[index + 1];

            MeetingsNavigationalDTO[] currentAvailableMeetings = await _repo.GetUserAvailableMeetings(contentId);

            if (!currentAvailableMeetings.Any() || !currentAvailableMeetings.Any(c => c.ContentId == contentId))
            {
                throw new SecurityException("INVALID_GRANT");
            }

            var currentMeeting = currentAvailableMeetings.First(c => c.ContentId == contentId);

            var previousMeetingsContentIds = new[] { currentAvailableMeetings
                .Where(c => c.MeetingDate < currentMeeting.MeetingDate)
                .OrderByDescending(c => c.MeetingDate)
                // .ThenByDescending(c => c.ContentId)
                .Select(c => c.ContentId)
                .FirstOrDefault() };

            var nextMeetingsContentIds = new[] {currentAvailableMeetings
                .Where(c => c.MeetingDate > currentMeeting.MeetingDate)
                .OrderBy(c => c.MeetingDate)
                // .ThenBy(c => c.ContentId)
                .Select(c => c.ContentId)
                .FirstOrDefault() };

            return new MeetingsNavigationalViewModel
            {
                TotalMeetingsCount = currentAvailableMeetings.Length,
                CurrentMeetingOrder = Array.IndexOf(currentAvailableMeetings, currentMeeting) + 1,
                PreviousMeetingContentIds = previousMeetingsContentIds[0] == 0 ? Array.Empty<int>() : previousMeetingsContentIds,
                NextMeetingContentIds = nextMeetingsContentIds[0] == 0 ? Array.Empty<int>() : nextMeetingsContentIds
            };
        }

        public async System.Threading.Tasks.Task HandleTasksPermissions(List<Content> tasks, int parentContentId, int parentWorkgroupId)
        {
            var permissions = await _repo.GetMyCurrentTasksPermissions(parentContentId);
            var tasksToRemove = new List<int>();

            foreach (var task in tasks)
            {
                if (task == null) continue;

                task.UAC.isExternalWorkgroupTask = task.workgroupId != parentWorkgroupId;

                if (permissions.Any(p => p.contentId == task.contentId) && task.workgroupId != parentWorkgroupId)
                {
                    task.UAC.canSeeExternalWorkgroupTask = true;
                }
                else if (!permissions.Any(p => p.contentId == task.contentId) && task.workgroupId == parentWorkgroupId)
                {
                    tasksToRemove.Add(task.contentId);
                }
            }

            tasks.RemoveAll(t => tasksToRemove.Contains(t.contentId));
        }
    }
}
