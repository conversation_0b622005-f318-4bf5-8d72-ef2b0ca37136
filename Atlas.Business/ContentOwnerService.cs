using Atlas.Business.ViewModels;
using Atlas.Data.Entities;
using Atlas.Data.Repository;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security;
using System.Threading.Tasks;

using Atlas.CrossCutting.AppEnums;
using Newtonsoft.Json;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;

namespace Atlas.Business
{
    public class ContentOwnerService
    {
        private AtlasModelCore _md;
        private IDbContextTransaction _transaction;
        ContentOwnerRepository _repo;
        ContentRepository _c_repo;
        ContentPermissionService _svcContentPermission;
        int _currentUser;
        int content_id;
        private int currentUserId;
        private Guid _contentUuId;
        private string _userAgent;

        public ContentOwnerService(int currentUserId, int _content_id)
        {
            _repo = new ContentOwnerRepository(currentUserId, _content_id);
            _c_repo = new ContentRepository(currentUserId);

            _currentUser = currentUserId;
            content_id = _content_id;
        }

        public ContentOwnerService(int currentUserId, string userAgent, ContentRepository contentRepository)
        {
            _repo = new ContentOwnerRepository(currentUserId);
            _c_repo = contentRepository ?? new ContentRepository(currentUserId);
            _currentUser = currentUserId;
            _userAgent = userAgent;
        }

        public void SetContentUuId(Guid contentUuId)
        {
            _contentUuId = contentUuId;
            _repo.SetContentUuId(contentUuId);
        }

        public ContentOwnerService(int currentUserId, int _content_id, AtlasModelCore _md, IDbContextTransaction _transaction)
        {
            this._md = _md;
            this._transaction = _transaction;
            _repo = new ContentOwnerRepository(currentUserId, _content_id, _md, _transaction);
            //_c_repo = new ContentRepository(currentUserId, _md, _transaction);
            _c_repo = new ContentRepository(currentUserId, new AtlasModelCore(), null); // PLG_Migrate
            _currentUser = currentUserId;
            content_id = _content_id;
        }

        public async Task<int> RemoveRangeAsync(List<ContentOwner> remove_users)
        {
            var owners = await _repo.GetCurrentOwnersWithContentUuIdAsync();

            foreach (var item in remove_users)
            {
                if (owners.Any(ow => ow.userId == item.userId))
                {
                    var x = await this.Delete(item.userId);
                }
            }

            return 1;
        }

        public async Task<bool> Delete(int userId)
        {
            if ((await _repo.DeleteWithContentUuId(userId)))
            {
                new ContentActivityService(_currentUser).Add(content_id, _contentUuId, new ContentActivity()
                {
                    contentId = content_id,
                    date = DateTime.UtcNow,
                    processed = false,
                    type = "CONTENT_OWNER_DELETE",
                    activityUser = _currentUser,
                    subItemId = 0,
                    subItemType = SubItems.OWNER,
                    hidden = false
                }, _userAgent);

                return true;
            }
            else
            {
                return false;
            }
        }

        public async Task<int> Add(int userId, bool grantedByAdmin = false)
        {
            Content content = null;

            //if granted by admin, we should ignore these additional checks
            if (!grantedByAdmin)
            {
                content = await _c_repo.Get(this.content_id);
                var owner = content.ContentOwner.Where(o => o.userId == userId).FirstOrDefault();

                //check if the CURRENT user is owner
                if (!content.ContentOwner.Where(o => o.userId == _currentUser).Any())
                {
                    throw new SecurityException("Current user is not owner of the given content.");
                }

                if (owner != null)
                {
                    throw new ArgumentException("Owner already exists in the given content.");
                }
            }
            else
            {
                //if granted by admin, we should not use contentRepository
                content = await _md.Content.Where(o => o.contentId == content_id).Include(o => o.Child_Content).FirstOrDefaultAsync();
            }

            int added_item;
            if (_repo.Add(userId, out added_item, grantedByAdmin: grantedByAdmin))
            {
                if (content.type == ContentTypes.Meeting)
                {
                    _c_repo.UpdateChildOwnership(content_id, new List<int> { userId }, new List<int> { });
                }

                if (!grantedByAdmin) //if grantedByAdmin, it will be described in another Audit Log
                {
                    //save comment activity
                    new ContentActivityService(_currentUser).Add(content_id, new ContentActivity()
                    {
                        contentId = content_id,
                        date = DateTime.UtcNow,
                        processed = false,
                        type = Operations.OWNER_ADD,
                        activityUser = _currentUser,
                        subItemId = added_item,
                        subItemType = SubItems.OWNER,
                        hidden = false

                    });
                }

                return added_item;
            }
            else
            {
                return 0;
            }
        }

        public async Task<int> Add(Content currentContent, int userId, bool grantedByAdmin = false)
        {
            if (!grantedByAdmin)
            {
                var owner = currentContent.ContentOwner.Where(o => o.userId == userId).FirstOrDefault();

                if (!currentContent.ContentOwner.Where(o => o.userId == _currentUser).Any())
                {
                    throw new SecurityException("Current user is not owner of the given content.");
                }

                if (owner != null)
                {
                    throw new ArgumentException("Owner already exists in the given content.");
                }
            }
            else
            {
                currentContent = await _md.Content.Where(o => o.contentUuid == _contentUuId).Include(o => o.Child_Content).FirstOrDefaultAsync();
            }

            int added_item;

            var result = await _repo.AddWithContentUuId(userId, grantedByAdmin: grantedByAdmin);
            if (result.success)
            {
                added_item = result.addedItem;
                if (currentContent.type == ContentTypes.Meeting)
                {
                    _c_repo.UpdateChildOwnership(currentContent.contentId, new List<int> { userId }, new List<int> { });
                }

                if (!grantedByAdmin)
                {
                    new ContentActivityService(_currentUser).Add(currentContent.contentId, _contentUuId, new ContentActivity()
                    {
                        contentUuid = _contentUuId,
                        contentId = content_id,
                        date = DateTime.UtcNow,
                        processed = false,
                        type = Operations.OWNER_ADD,
                        activityUser = _currentUser,
                        subItemId = added_item,
                        subItemType = SubItems.OWNER,
                        hidden = false

                    }, _userAgent);
                }

                return added_item;
            }
            else
            {
                return 0;
            }
        }


        public async Task<bool> Update(int content_id, List<OwnerListViewModel> contentOwnerList)
        {
            //1. user sends the whole list
            //2. validate permissions to current user update
            //3. loop through the list to check if
            //  3.1. Check if user is in content's workgroup
            //  3.2. Check if user can be unsubscribed (if created, its assigned or its reviewer of the content)
            var users_add = new List<ContentOwner>();
            var users_remove = new List<ContentOwner>();

            if (!contentOwnerList.Select(x => x.Checked).Contains(true))
            {
                throw new SecurityException("MIN_OWNER_USERS");
            }

            //check permissions
            if (!_c_repo.CheckPermissionsForWorkgroup(Operations.SUBSCRIBER_UPDATE, content_id))
            {
                //todo: logar tentativa de acesso
                throw new SecurityException("Unauthorized attempt to add comment into a content in workgroup.");
            }

            ContentService _contentService = new ContentService(_currentUser);
            Content _currentContent = await _contentService.Get(content_id);
            List<User> _wkg_users_allowed = await new WorkgroupService(_currentUser).GetUsers(_currentContent.workgroupId);
            var _currentOwners = _currentContent.ContentOwner;

            if (!_currentContent.UAC.manage_owners)
            {
                throw new SecurityException("NOT_ALLOWED");
            }

            var _currentPermissions = _currentContent.ContentPermission;

            if (_currentPermissions.Count == 0)
            {
                _currentPermissions = _c_repo.List_Permissions(_currentContent.contentId);
            }

            //if meeting, and closed, prevent subscribers to be changed
            //S-29 / the owners can be changed after the CLOSED Status - Leonardo
            //if (_currentContent.type == ContentTypes.Meeting && _currentContent.status == "CLOSED")
            //{
            //    throw new SecurityException("STATUS_INVALID");
            //}

            foreach (var item in contentOwnerList)
            {
                //check if user is allowed in the content's workgroup
                if (!_wkg_users_allowed.Select(o => o.userId).Contains(item.ContentOwner.userId) && item.Checked)
                {
                    // if not allowed in the workgroup, skip it
                    continue;
                }

                //check rules for Content creators & others
                if (OwnerChangeRules(_c_repo._currentUser, _currentContent, item, true).blockChange)
                {
                    //if the change is blocked, skip it
                    continue;
                }

                // determine if the item should be added or removed of the current subscribers list
                if (_currentOwners.Select(o => o.userId).Contains(item.ContentOwner.userId))
                {
                    if (!item.Checked) //if exists and its UNCHECKED.... remove it
                    {
                        users_remove.Add(item.ContentOwner);
                    }
                }
                else
                {
                    if (item.Checked) //if not exists and its CHECKED... Add it
                    {
                        users_add.Add(item.ContentOwner);
                    }
                }

            }

            int entry = _repo.Set_Owners(content_id, _currentUser, users_add, users_remove);

            if (entry > 0)
            {

                // Add Permissions and Owners
                if (_currentContent.type == ContentTypes.Pipeline || _currentContent.type == ContentTypes.Meeting)
                {
                    // instance for permissions and owners
                    var _repoContent = new ContentRepository(_currentUser);

                    #region Update permission to Owner if it's new in meeting
                    // separete users for add permissions
                    var users = users_add.Where(x => !x.User.hasPermission).ToList();

                    if (users.Count() > 0)
                    {
                        // create a content permission object
                        var users_permission = new List<ContentPermission>();
                        foreach (var item in users)
                        {
                            users_permission.Add(new ContentPermission
                            {
                                contentId = item.contentId,
                                userId = item.userId,
                                createDate = DateTime.UtcNow,
                                createUser = _currentUser

                            });
                        }

                        // Update permission only for new members on meeting - parent
                        if (_repoContent.Set_Permission(content_id, users_permission, new List<ContentPermission>()) > 0)
                        {
                            // Update child permissions only for new members on meeting - child
                            _repoContent.UpdateChildPermissions(_currentContent, users.Select(o => o.userId).ToList(), new List<int>());

                        }

                    }
                    #endregion

                    // Update Child Ownership
                    _repoContent.UpdateChildOwnership(content_id, users_add.Select(o => o.userId).ToList(), users_remove.Select(o => o.userId).ToList());

                    await UpdateVideoConference(_contentService, _currentContent);
                }

                if (_currentContent.type == ContentTypes.Form && users_add.Any())
                {
                    // check how many do not have permission and update the list
                    var permissionsList = _c_repo.List_Permissions(_currentContent.contentId);
                    var ownersWithoutPermission = new List<ContentOwner>();

                    foreach (var user in users_add)
                    {
                        if (!permissionsList.Any(p => p.userId == user.userId))
                            ownersWithoutPermission.Add(user);
                    }

                    permissionsList.AddRange(ownersWithoutPermission.Select(p => new ContentPermission()
                    {
                        createUser = _currentUser,
                        userId = p.userId,
                        createDate = DateTime.UtcNow,
                        allowed = true,
                    }));

                    _c_repo.Set_Permission(content_id, permissionsList, new List<ContentPermission>());
                }

                string message = JsonConvert.SerializeObject(new { type = SubItems.OWNER, added = users_add.Select(x => new { x.User.userId, x.User.name }), removed = users_remove.Select(x => new { x.User.userId, x.User.name }) });

                new ContentActivityService(_currentUser).Add(content_id, new ContentActivity()
                {
                    contentId = content_id,
                    date = DateTime.UtcNow,
                    processed = false,
                    type = "OWNER_UPDATE",
                    activityUser = _currentUser,
                    hidden = false,
                    contentData = message,
                    subItemType = SubItems.OWNER
                });
            }

            return entry > 0;
        }
        public async Task<bool> Update(Content currentContent, List<OwnerListViewModel> contentOwnerList)
        {
            var users_add = new List<ContentOwner>();
            var users_remove = new List<ContentOwner>();

            if (!contentOwnerList.Select(x => x.Checked).Contains(true))
            {
                throw new SecurityException("MIN_OWNER_USERS");
            }

            if (!_c_repo.CheckPermissionsForWorkgroup(Operations.SUBSCRIBER_UPDATE, currentContent.contentId))
            {
                throw new SecurityException("Unauthorized attempt to add comment into a content in workgroup.");
            }

            ContentService _contentService = new ContentService(_currentUser);
            List<User> _wkg_users_allowed = await new WorkgroupService(_currentUser).GetUsers(currentContent.workgroupId);
            var _currentOwners = currentContent.ContentOwner;

            if (!currentContent.UAC.manage_owners)
            {
                throw new SecurityException("NOT_ALLOWED");
            }

            var _currentPermissions = currentContent.ContentPermission;

            if (_currentPermissions.Count == 0)
            {
                _currentPermissions = _c_repo.List_Permissions(currentContent.contentId);
            }

            foreach (var item in contentOwnerList)
            {
                if (!_wkg_users_allowed.Select(o => o.userId).Contains(item.ContentOwner.userId) && item.Checked)
                {
                    continue;
                }

                if (OwnerChangeRules(_c_repo._currentUser, currentContent, item, true).blockChange)
                {
                    continue;
                }

                if (_currentOwners.Select(o => o.userId).Contains(item.ContentOwner.userId))
                {
                    if (!item.Checked)
                    {
                        users_remove.Add(item.ContentOwner);
                    }
                }
                else
                {
                    if (item.Checked)
                    {
                        users_add.Add(item.ContentOwner);
                    }
                }
            }

            int entry = _repo.Set_Owners(currentContent.contentId, _currentUser, users_add, users_remove);

            if (entry > 0)
            {
                if (currentContent.type == ContentTypes.Pipeline || currentContent.type == ContentTypes.Meeting)
                {
                    var _repoContent = new ContentRepository(_currentUser);

                    #region Update permission to Owner if it's new in meeting
                    var users = users_add.Where(x => !x.User.hasPermission).ToList();

                    if (users.Count() > 0)
                    {
                        var users_permission = new List<ContentPermission>();
                        foreach (var item in users)
                        {
                            users_permission.Add(new ContentPermission
                            {
                                contentUuid = item.contentUuid,
                                contentId = item.contentId,
                                userId = item.userId,
                                createDate = DateTime.UtcNow,
                                createUser = _currentUser,
                                allowed = true
                            });
                        }

                        if ((await _repoContent.Set_Permission(currentContent.contentUuid, currentContent.contentId, users_permission, new List<ContentPermission>())) > 0)
                        {
                            _repoContent.UpdateChildPermissions(currentContent, users.Select(o => o.userId).ToList(), new List<int>());
                        }
                    }
                    #endregion

                    await _repoContent.UpdateChildOwnership(currentContent.contentUuid, currentContent.contentId, users_add.Select(o => o.userId).ToList(), users_remove.Select(o => o.userId).ToList());

                    await UpdateVideoConference(_contentService, currentContent);
                }

                if (currentContent.type == ContentTypes.Form && users_add.Any())
                {
                    var permissionsList = _c_repo.List_Permissions(currentContent.contentId);
                    var ownersWithoutPermission = new List<ContentOwner>();

                    foreach (var user in users_add)
                    {
                        if (!permissionsList.Any(p => p.userId == user.userId))
                            ownersWithoutPermission.Add(user);
                    }

                    permissionsList.AddRange(ownersWithoutPermission.Select(p => new ContentPermission()
                    {
                        contentUuid = currentContent.contentUuid,
                        contentId = currentContent.contentId,
                        createUser = _currentUser,
                        userId = p.userId,
                        createDate = DateTime.UtcNow,
                        allowed = true,
                    }));

                    await _c_repo.Set_Permission(currentContent.contentUuid, currentContent.contentId, permissionsList, new List<ContentPermission>());
                }

                string message = JsonConvert.SerializeObject(new { type = SubItems.OWNER, added = users_add.Select(x => new { x.User.userId, x.User.name }), removed = users_remove.Select(x => new { x.User.userId, x.User.name }) });

                new ContentActivityService(_currentUser).Add(currentContent.contentId, _contentUuId, new ContentActivity()
                {
                    contentUuid = _contentUuId,
                    contentId = content_id,
                    date = DateTime.UtcNow,
                    processed = false,
                    type = "OWNER_UPDATE",
                    activityUser = _currentUser,
                    hidden = false,
                    contentData = message,
                    subItemType = SubItems.OWNER
                }, _userAgent);
            }

            return entry > 0;
        }

        private async System.Threading.Tasks.Task UpdateVideoConference(ContentService _contentService, Content _currentContent)
        {
            // Update the videoconference data.
            FeatureManagerService fms = new FeatureManagerService(_currentUser);
            var videoConferenceEnabled = await fms.isEnabledByContent(_currentContent, PlanFeatureNames.VIDEO_CONFERENCE);
            var meeting = _currentContent.Meeting.First();

            if (videoConferenceEnabled && meeting.conferenceType == "msgraph")
            {
                Content contentModif = await GetContentWithOwnersValidCompanyDomains(_contentService, _currentContent);

                _contentService.UpdateConference(_currentContent, meeting.conferenceType, contentModif);
            }
        }

        private async Task<Content> GetContentWithOwnersValidCompanyDomains(ContentService contentService, Content currentContent)
        {
            var (companyAADConnect, validDomains) = await contentService.GetValidDomainsForClient(currentContent.Workgroup.clientId);

            var currentOwners = _repo.GetCurrentOwners();

            List<ContentOwner> filtered;
            if (companyAADConnect == true)
            {
                filtered = currentOwners.Where(x => validDomains.Contains("@" + x.User.email.Split('@')[1])).ToList();
            }
            else
            {
                var conferenceResponseData = currentContent.Meeting.First().conferenceResponseData;
                var responseData = JsonConvert.DeserializeObject<Integrations.Conference.MSTeams.MSTeamsOnlineMeetingResponse>(conferenceResponseData);
                var organizerUpn = responseData.participants.organizer.upn;

                try
                {
                    var host = new System.Net.Mail.MailAddress(organizerUpn).Host;
                    filtered = currentOwners.Where(o => o.User.email.Contains("@" + host)).ToList();
                }
                catch (FormatException)
                {
                    // if the email has a format exception try to do a split on the user.email on '@'
                    var host = organizerUpn.Split('@')[1].Trim();
                    filtered = currentOwners.Where(o => o.User.email.Contains("@" + host)).ToList();
                }
                catch (Exception)
                {
                    filtered = currentOwners;
                }
            }

            // Gets the consolidaded list of owners after all previous changes.
            var content = new Content()
            {
                ContentOwner = filtered
            };

            return content;
        }

        private static OwnerListViewModel OwnerChangeRules(User current_usr, Content _content, OwnerListViewModel item, bool updating)
        {
            var new_item = new OwnerListViewModel();
            //if (item.ContentOwner.userId == _content.createUser)
            //{
            //    if (item.ContentOwner.userId == current_usr.userId)
            //    {
            //        //the creator user can unsubscribe himself, so, we'll just trow a warning
            //        new_item.warning = "CREATED";
            //    }
            //    else
            //    {
            //        if (item.Checked)//block change only when it is checked
            //        {
            //            new_item.blockChange = true;
            //            new_item.blockReason = "CREATED";
            //        }
            //    }
            //}

            //if (_content.type == ContentTypes.Task)
            //{
            //    if (item.ContentOwner.userId == _content.assignedUser)
            //    {
            //        if (item.ContentOwner.userId == current_usr.userId)
            //        {
            //            //the ASSIGNED user can unsubscribe himself, so, we'll just trow a warning
            //            new_item.warning = "ASSIGNED";
            //        }
            //        else
            //        {
            //            if (item.Checked && !updating)//block change only when it is checked, and the validation is not occouring during update
            //            {
            //                new_item.blockChange = true;
            //                new_item.blockReason = "ASSIGNED";
            //            }
            //        }
            //    }

            //    if (item.ContentOwner.userId == _content.reviewerUser)
            //    {
            //        if (item.ContentOwner.userId == current_usr.userId)
            //        {
            //            //the reviewer user can unsubscribe himself, so, we'll just trow a warning
            //            new_item.warning = "REVIEWER";
            //        }
            //        else
            //        {
            //            if (item.Checked && !updating)//block change only when it is checked, and the validation is not occouring during update
            //            {
            //                new_item.blockChange = true;
            //                new_item.blockReason = "REVIEWER";
            //            }
            //        }
            //    }
            //}
            return new_item;
        }
    }
}
