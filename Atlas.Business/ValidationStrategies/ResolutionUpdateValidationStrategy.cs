using Atlas.Business.Interfaces;
using Atlas.CrossCutting.DTO.Resolution;
using Atlas.Data.Entities;
using Atlas.Data.Repository;
using System;
using System.Linq;
using System.Security;
using static Atlas.CrossCutting.AppEnums.ContentStatuses;
using static Atlas.CrossCutting.Helpers.Resolution.ResolutionHelperClasses;

namespace Atlas.Business.ValidationStrategies
{
    public class ResolutionUpdateValidationStrategy : IValidationStrategy<ResolutionUpdateDto>
    {
        private readonly ResolutionRepository _repository;
        private readonly int _currentUserId;
        private static readonly int MIN_DURATION_MINUTES = 0;

        public ResolutionUpdateValidationStrategy(ResolutionRepository repository, int currentUserId)
        {
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
            _currentUserId = currentUserId;
        }

        public async System.Threading.Tasks.Task ValidateAsync(ResolutionUpdateDto updateDto)
        {
            var content = await _repository.GetResolutionWithRelationsAsync(updateDto.ContentUuid);
            if (content == null)
                throw new InvalidOperationException(ResolutionValidationMessages.ResolutionNotFound);

            await ValidateAsync(updateDto, content);
        }

        public async System.Threading.Tasks.Task ValidateAsync(ResolutionUpdateDto updateDto, Content content)
        {
            if (updateDto == null)
                throw new InvalidOperationException(ResolutionValidationMessages.ResolutionDataNotFound);

            var poll = content.Poll.FirstOrDefault();
            if (poll == null)
                throw new InvalidOperationException(ResolutionValidationMessages.ResolutionDataNotFound);

            ValidatePermissions(content);
            ValidateWorkgroupStatus(content.Workgroup);
            ValidateTitleChange(updateDto, content, poll);
            ValidateContentStatus(content);
            ValidateDueDate(updateDto.DueDate);
            ValidateDuration(updateDto.Duration);
        }

        private void ValidatePermissions(Content content)
        {
            var isOwner = content.ContentOwner?.Any(co => co.userId == _currentUserId) == true;
            var isCreator = content.createUser == _currentUserId;

            if (!isOwner && !isCreator)
                throw new SecurityException(ResolutionValidationMessages.InvalidGrant);
        }

        private static void ValidateWorkgroupStatus(Workgroup workgroup)
        {
            if (workgroup.archived)
                throw new InvalidOperationException(ResolutionValidationMessages.ArchivedWorkgroup);
        }

        private static void ValidateTitleChange(ResolutionUpdateDto updateDto, Content content, Poll poll)
        {
            if (!string.IsNullOrWhiteSpace(updateDto.Title) && updateDto.Title != content.title)
            {
                if (poll.Votes?.Any(v => !(v.deleted ?? false)) == true)
                    throw new InvalidOperationException(ResolutionValidationMessages.CannotChangeTitleWithVotes);
            }
        }

        private static void ValidateContentStatus(Content content)
        {
            if (content.status == ResolutionStatus.CLOSED || content.status == ResolutionStatus.CANCELLED)
                throw new InvalidOperationException(ResolutionValidationMessages.CannotUpdateClosedResolution);
        }

        private static void ValidateDueDate(DateTime? dueDate)
        {
            if (dueDate.HasValue && dueDate.Value.Date < DateTime.UtcNow.Date)
                throw new InvalidOperationException(ResolutionValidationMessages.InvalidDueDate);
        }

        private static void ValidateDuration(int? duration)
        {
            if (duration.HasValue && (duration.Value < MIN_DURATION_MINUTES))
                throw new InvalidOperationException(ResolutionValidationMessages.InvalidDuration);
        }
    }
}