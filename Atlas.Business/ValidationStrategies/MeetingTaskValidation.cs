using Atlas.Business.Interfaces;
using Atlas.CrossCutting.DTO.AtlasAI;
using Atlas.Data.Repository;
using System;
using System.Threading.Tasks;


namespace Atlas.Business.ValidationStrategies
{
    public class MeetingTaskValidation : IValidationStrategy<MeetingTaskDTO>
    {
        private AIRepository _aiRepository;

        public MeetingTaskValidation(AIRepository aiRepository)
        {
            _aiRepository = aiRepository;
        }

        public async Task ValidateAsync(MeetingTaskDTO taskDTO)
        {
            if (taskDTO.TaskId == Guid.Empty)
            {
                throw new InvalidOperationException("INVALID_TASK_ID");
            }

            if (await TaskAlreadyCreated(taskDTO.TaskId))
            {
                throw new InvalidOperationException("TASK_ALREADY_CREATED");
            }

            if (string.IsNullOrWhiteSpace(taskDTO.Title))
            {
                throw new InvalidOperationException("INVALID_TITLE");
            }

            if (taskDTO.Title.Length > 10000)
            {
                throw new InvalidOperationException("INVALID_TITLE_LENGTH");
            }

            if (taskDTO.AssignedUser == 0)
            {
                throw new InvalidOperationException("INVALID_ASSIGNED_USER");
            }

            if (taskDTO.WorkgroupId == 0)
            {
                throw new InvalidOperationException("INVALID_WORKGROUP");
            }

            if (taskDTO.DueDate == null || taskDTO.DueDate.Date < DateTime.UtcNow.Date)
            {
                throw new InvalidOperationException("INVALID_DUE_DATE");
            }
        }

        private async Task<bool> TaskAlreadyCreated(Guid taskId)
        {
            return await _aiRepository.CheckMeetingTaskAlreadyCreated(taskId);
        }
    }
}
