using Atlas.Business.Interfaces;
using Atlas.CrossCutting.DTO.ExternalDocumentRequest;
using Atlas.CrossCutting.Helpers.PersonalData;
using Atlas.Data.Repository;
using System;

namespace Atlas.Business.ValidationStrategies
{
    public class ExternalUserValidationStrategy : IValidationStrategy<ExternalUserDTO>
    {
        private readonly ExternalUserRepository _externalUserRepository;

        public ExternalUserValidationStrategy(ExternalUserRepository externalUserRepository)
        {
            _externalUserRepository = externalUserRepository;
        }

        public async System.Threading.Tasks.Task ValidateAsync(ExternalUserDTO extUserDTO)
        {
            CheckRequiredFields(extUserDTO);
            CheckValidEmail(extUserDTO.ExternalMail);
            CheckValidPhoneNumber(extUserDTO.ExternalMobile);
            StandardizeRequiredFields(extUserDTO);
        }

        private async System.Threading.Tasks.Task CheckMailAlreadyUsed(ExternalUserDTO extUserDTO, int contentId)
        {
            if (await _externalUserRepository.ExternalMailAlreadyUsed(extUserDTO, contentId))
            {
                throw new InvalidOperationException("INVALID_EMAIL_ALREADY_USED");
            }
        }

        private async System.Threading.Tasks.Task CheckMailAlreadyUsed(ExternalUserDTO extUserDTO, Guid contentUuId)
        {
            if (await _externalUserRepository.ExternalMailAlreadyUsed(extUserDTO, contentUuId))
            {
                throw new InvalidOperationException("INVALID_EMAIL_ALREADY_USED");
            }
        }

        private void CheckRequiredFields(ExternalUserDTO extUserDTO)
        {
            if (string.IsNullOrWhiteSpace(extUserDTO.ExternalName)
                || extUserDTO.ExternalName.Length < 7
                || string.IsNullOrWhiteSpace(extUserDTO.ExternalMobile)
                || string.IsNullOrWhiteSpace(extUserDTO.ExternalMail))
            {
                throw new InvalidOperationException("INVALID_REQUIRED_FIELDS");
            }
        }

        private void CheckValidEmail(string email)
        {
            if (!IsValidEmail(email))
            {
                throw new InvalidOperationException("INVALID_EMAIL");
            }
            else if (email.Length > 100)
            {
                throw new InvalidOperationException("EMAIL_LENGTH_EXCEEDED");
            }
        }

        private void CheckValidPhoneNumber(string phoneNumber)
        {
            if (!IsValidPhoneNumber(phoneNumber))
            {
                throw new InvalidOperationException("INVALID_PHONE_NUMBER");
            }
        }

        private bool IsValidEmail(string email)
        {
            return PersonalDataHelper.ValidateEmail(email);
        }

        private bool IsValidPhoneNumber(string phoneNumber)
        {
            return PersonalDataHelper.ValidatePhoneNumber(phoneNumber);
        }

        private void StandardizeRequiredFields(ExternalUserDTO extUserDTO)
        {
            extUserDTO.ExternalMobile = extUserDTO.ExternalMobile.Trim();
            extUserDTO.ExternalName = extUserDTO.ExternalName.Trim();
            extUserDTO.ExternalMail = extUserDTO.ExternalMail.Trim();
        }

        public async System.Threading.Tasks.Task CheckForAlreadyUsedEmails(ExternalUserDTO extUserDTO, int contentId)
        {
            await this.CheckMailAlreadyUsed(extUserDTO, contentId);
        }

        public async System.Threading.Tasks.Task CheckForAlreadyUsedEmails(ExternalUserDTO extUserDTO, Guid contentUuId)
        {
            await this.CheckMailAlreadyUsed(extUserDTO, contentUuId);
        }
    }
}
