using Atlas.Data.Repository;
using Atlas.Data.Entities;
using System;
using System.Linq;
using Atlas.Business.Interfaces;
using Atlas.CrossCutting.AppEnums;
using Atlas.CrossCutting.DTO.Workgroup;

namespace Atlas.Business.ValidationStrategies
{
    public class ContentValidationStrategy : IContentValidationStrategy<Content>
    {
        private readonly AIRepository _aIRepository;
        private readonly ResolutionRepository _resolutionRepository;
        private string _expectedContentType;


        public ContentValidationStrategy(AIRepository aIRepository)
        {
            _aIRepository = aIRepository;
        }

        public ContentValidationStrategy(ResolutionRepository resolutionRepository)
        {
            _resolutionRepository = resolutionRepository;
        }

        public void SetExpectedContentType(string contentType)
        {
            if (!string.IsNullOrWhiteSpace(contentType) && ContentTypes.PossibleContentTypes.Contains(contentType))
            {
                _expectedContentType = contentType;
            }
        }

        public async System.Threading.Tasks.Task ValidateAsync(Content content)
        {
            if (content is null)
            {
                throw new InvalidOperationException("INVALID_CONTENT");
            }

            if (!string.IsNullOrWhiteSpace(_expectedContentType) && content.type != _expectedContentType)
            {
                throw new InvalidOperationException("INVALID_CONTENT");
            }

            if (content.type != ContentTypes.MeetingMinute && (string.IsNullOrWhiteSpace(content.title) || content.title.Length > 10000))
            {
                throw new InvalidOperationException("INVALID_TITLE");
            }

            if (content.ContentPermission == null || content.ContentOwner == null || content.ContentSubscriber == null)
            {
                throw new Exception("CONTENT_PERMISSION_ERROR");
            }

            await SetRlsClientId(content);
        }

        private async System.Threading.Tasks.Task SetRlsClientId(Content content)
        {
            WorkgroupDTO workgroup = null;

            if (_aIRepository != null)
            {
                workgroup = await _aIRepository.GetWorkgroupByIdAsync(content.workgroupId);
            }
            else if (_resolutionRepository != null)
            {
                workgroup = await _resolutionRepository.GetWorkgroup(content.workgroupId);
            }

            if (workgroup == null)
            {
                throw new InvalidOperationException("WORKGROUP_NOT_FOUND");
            }

            content.rlsClientId = workgroup.clientId;
        }
    }
}
