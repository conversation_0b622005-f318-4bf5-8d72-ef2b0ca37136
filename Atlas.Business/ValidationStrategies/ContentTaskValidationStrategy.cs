using Atlas.Business.Interfaces;
using Atlas.CrossCutting.AppEnums;
using Atlas.Data.Entities;
using Atlas.Data.Repository;
using System;
using System.Threading.Tasks;
using static Atlas.CrossCutting.AppEnums.ContentStatuses;

namespace Atlas.Business.ValidationStrategies
{
    public class ContentTaskValidationStrategy : IContentValidationStrategy<Content>
    {
        private readonly AIRepository _aiRepository;
        private readonly IContentValidationStrategy<Content> _contentValidation;
        private Content _parentContent;

        public ContentTaskValidationStrategy(AIRepository aiRepository, IContentValidationStrategy<Content> contentValidation)
        {
            _aiRepository = aiRepository;
            _contentValidation = contentValidation;
        }

        public async System.Threading.Tasks.Task ValidateAsync(Content task)
        {
            await _contentValidation.ValidateAsync(task);

            if (task.type != ContentTypes.Task)
            {
                throw new Exception("INVALID_CONTENT_TYPE");
            }

            if (task.assignedUser is null || task.assignedUser == 0)
            {
                throw new InvalidOperationException("INVALID_ASSIGNED_USER");
            }

            if (task.parentContentId is null || task.parentContentId == 0)
            {
                throw new InvalidOperationException("INVALID_PARENT_CONTENT");
            }

            _parentContent = await GetParentContentAsync((int)task.parentContentId);

            if (_parentContent is null || _parentContent.type != ContentTypes.Meeting && _parentContent.status == ContentMeetingStatus.CANCELLED)
            {
                throw new InvalidOperationException("INVALID_PARENT_CONTENT");
            }
        }

        private async Task<Content> GetParentContentAsync(int parentContentId)
        {
            return await _aiRepository.GetParentContent(parentContentId);
        }
    }
}
