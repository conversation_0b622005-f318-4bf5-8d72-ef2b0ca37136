using Atlas.Business.Helpers;
using Atlas.Business.Helpers.Queue;
using Atlas.Business.ViewModels;
using Atlas.CrossCutting.AppEnums;
using Atlas.CrossCutting.DTO;
using Atlas.CrossCutting.Helpers;
using Atlas.CrossCutting.Settings;
using Atlas.Data.Entities;
using Atlas.Data.Repository;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SharpRaven;
using SharpRaven.Data;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Threading.Tasks;

namespace Atlas.Business
{
    public class ContentActivityService
    {
        private readonly int _currentUser;
        private readonly AtlasModelCore _mdG;
        private readonly bool _isTransaction;
        private readonly IDbContextTransaction _transaction;
        private readonly User _user;

        public ContentActivityService(int userId)
        {
            _isTransaction = false;
            _currentUser = userId;
        }

        public ContentActivityService(int currentUserId, AtlasModelCore _mdPar, IDbContextTransaction _contextTransaction)
        {
            _isTransaction = true;
            _currentUser = currentUserId;
            _transaction = _contextTransaction;
            _mdG = _mdPar;
        }

        public ContentActivityService(int userId, User usr) : this(userId)
        {
            this._user = usr;
        }

        public async Task<ContentActivityHomeViewModel> Get(ContentRequestFilter filter, bool requestOptions = true)
        {
            try
            {
                ContentActivityHomeViewModel vm = new ContentActivityHomeViewModel();
                ContentActivityRepository repo = new ContentActivityRepository(_currentUser);

                List<ContentActivity> res_act;
                if (!requestOptions)
                {
                    ContentRepository contentRepository = new ContentRepository(_currentUser);
                    res_act = await contentRepository.Home_GetLastUpdates(filter);
                }
                else
                {
                    res_act = await repo.Get(filter, alwaysFilterMinDate: requestOptions);
                }

                var ignoredActivities = new string[]
                {
                    Operations.COMMENT_NOTIFY_OWNER,
                    Operations.EXTERNAL_CORPORATE_BOOK_DOC_CREATED
                };

                var filtered_res_act = res_act.Where(act => !ignoredActivities.Contains(act.type));

                vm.Results = filtered_res_act.Select(o =>
                {
                    string guid = o.Content.type == ContentTypes.KnowledgeDirectory ?
                        KnowledgeBaseHelper.GetContentGuid("knowledgeDirectory", o.contentId) : null;

                    IdentifyCommentAnswer(o);

                    return new
                    {
                        activity = o,
                        isRelatedToUser = false,
                        content = o.Content,
                        guid
                    };
                }).ToList<object>();

                if (requestOptions)
                {
                    vm.PossibleFilter = await repo.GetOptions(filter);
                }
                vm.ActiveFilter = filter;

                return vm;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public async Task<DateTime?> CheckUpdateFromDate(int contentId, DateTime fromDate, bool isAutoSyncBB = false)
        {
            AtlasModelCore _rmd = new AtlasModelCore();
            var content = await _rmd.Content.Where(o => o.contentId == contentId).FirstOrDefaultAsync();

            if (content.lastUpdate == null)
            {
                //update this field if empty...
                content.lastUpdate = DateTime.UtcNow;
                _rmd.Entry(content).State = EntityState.Modified;
                await _rmd.SaveChangesAsync();

                return content.lastUpdate;
            }
            else
            {
                //if its not auto sync, not take in account other conditions such as meeting date, bluebook version, etc
                // because this means that the user is manually viewing/downloading the bluebook
                if (!isAutoSyncBB)
                {
                    if (content.lastUpdate > fromDate)
                    {
                        return content.lastUpdate;
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {

                    var meeting = await _rmd.Meeting.Where(o => o.contentId == contentId).FirstOrDefaultAsync();

                    if (meeting.date > DateTime.UtcNow && meeting.date <= DateTime.UtcNow.AddDays(30))
                    {
                        var bookService = new BlueBookServiceOld(_currentUser);

                        //get last bluebook version for user
                        var lastBluebookVersion = await bookService.GetLastVersionForUser(_currentUser, contentId);

                        //if there is a bluebook version, check if it is newer than fromDate
                        if (lastBluebookVersion != null)
                        {
                            // if the last bluebook version is newer than the content last update
                            // in other words, if the bluebook has been successfully generated after the last content update
                            if (lastBluebookVersion.versionDate >= content.lastUpdate)
                            {
                                //return the date ONLY IF ITS NEWER than fromDate
                                if (lastBluebookVersion.versionDate > fromDate)
                                {
                                    return lastBluebookVersion.versionDate;
                                }
                                else
                                {
                                    return null;
                                }
                            }
                            else
                            {
                                return null;
                            }
                        }
                        else
                        {
                            return null;
                        }
                    }
                    else
                    {
                        return null;
                    }
                }
            }
        }

        //GetForContent
        public async Task<List<ContentActivity>> GetFromContent(Content content)
        {
            ContentActivityRepository car = new ContentActivityRepository(_currentUser);
            return car.GetByContentId(content.contentId).OrderByDescending(ca => ca.date).ToList();
        }

        public async Task<List<ContentActivity>> GetFromContentWithChild(int contentId)
        {
            ContentActivityRepository car = new ContentActivityRepository(_currentUser);
            return car.GetByContentIdWithChild(contentId).OrderBy(ca => ca.date).ToList();
        }

        public async Task<List<ContentActivity>> GetFromContentWithChildPermissions(Content content)
        {
            ContentActivityRepository car = new ContentActivityRepository(_currentUser);
            return car.GetByContentIdWithChildPermissions(content.contentId).OrderBy(ca => ca.date).ToList();
        }

        /// <summary>
        /// INTENTION: bulk activities.
        /// CAUTION: the activities type and the contentId associated must be the same.
        /// CAUTION: this method does not use groupTime logic
        /// </summary>
        /// <param name="activities"></param>
        /// <returns></returns>
        public async Task<bool> Add(List<ContentActivity> activities, string userAgent)
        {
            if (!activities.Any())
            {
                return false;
            }

            string ipAddress = "UNKNOWN";
            string sessionKey = "";

            try
            {
                /*
                 * pingas
                 * if (System.Web.HttpContext.Current != null)
                {
                    ipAddress = UserService.ExtractCurrentIPAddress();

                    var session_key_claim = ((System.Security.Claims.ClaimsIdentity)System.Threading.Thread.CurrentPrincipal.Identity)
                                            .Claims.FirstOrDefault(o => o.Type == "session_key");

                    if (session_key_claim != null)
                        sessionKey = session_key_claim.Value;

                }*/
            }
            catch (Exception ex)
            {
                // Do nothing as the ipAddress/sessionKey was already initialized
                // so continue the execution.
            }

            string activityDevice = string.Empty;

            try
            {
                activityDevice = new DeviceDetectorHelper(userAgent).GetUserAgentDevice();
            }
            catch
            {
                // Let's inform UNKNOWN for devices for now. The way we detect devices properly will be implemented.
                activityDevice = "UNKNOWN";
            }

            foreach (var act in activities)
            {
                if (IsActivityToIgnoreProcessing(act.type))
                {
                    act.processed = true;
                }

                act.device = activityDevice;
                act.IPAddress = ipAddress;
                act.sessionKey = sessionKey;
            }

            using (AtlasModelCore _model = new AtlasModelCore())
            {
                _model.ContentActivity.AddRange(activities);

                int content_id = activities.First().contentId;
                string actType = activities.First().type;

                var content = _model.Content.Find(content_id);

                //update [dbo].Content lastActivity
                //exclude activities that do not justify a new bb generation
                var contentType_activityType_combination = (content.type + "|" + actType).ToUpper();

                if (!BluebookActivityIgnoreListEnum.activitiesToIgnore.Contains(contentType_activityType_combination))
                {
                    if (content != null && ContentChecker.shouldUpdateLastModification(contentType_activityType_combination))
                    {
                        content.lastUpdate = DateTime.UtcNow;
                        _model.Entry(content).State = EntityState.Modified;
                    }
                    if (content != null && content.parentContentId.HasValue &&
                        ContentChecker.shouldUpdateParentLastModification(contentType_activityType_combination))
                    {
                        var parentContent = _model.Content.Find(content.parentContentId);
                        if (parentContent != null)
                        {
                            parentContent.lastUpdate = DateTime.UtcNow;
                            _model.Entry(parentContent).State = EntityState.Modified;
                        }
                    }
                }

                bool success = await _model.SaveChangesAsync() > 0;

                if (success)
                {
                    foreach (var activity in activities)
                    {
                        SendToAzFunctionsSchedulersOnActivityAdd(activity, content_id);
                    }
                }

                return success;
            }
        }

        /// <summary>
        /// OPTIMIZED VERSION: bulk activities with same type and content type but different contentIds.
        /// This method assumes all activities have the same activity.type and activity.Content.type.
        /// Minimizes database queries by batching operations.
        /// </summary>
        /// <param name="activities">List of activities with same type and content type</param>
        /// <returns></returns>
        public async Task<bool> AddOptimized(List<ContentActivity> activities)
        {
            if (!activities.Any()) return false;

            // Get common values from first activity
            string activityType = activities.First().type;

            string ipAddress = "UNKNOWN";
            string sessionKey = "";
            string activityDevice = "";

            //try
            //{
            //    if (System.Web.HttpContext.Current != null)
            //    {
            //        ipAddress = UserService.ExtractCurrentIPAddress();

            //        var session_key_claim = ((System.Security.Claims.ClaimsIdentity)System.Threading.Thread.CurrentPrincipal.Identity)
            //                                .Claims.FirstOrDefault(o => o.Type == "session_key");

            //        if (session_key_claim != null)
            //        {
            //            sessionKey = session_key_claim.Value;
            //        }

            //        string userAgent = System.Web.HttpContext.Current?.Request?.Headers?.GetValues("User-Agent")?.FirstOrDefault();
            //        activityDevice = new DeviceDetectorHelper(userAgent).GetUserAgentDeviceOrEmpty();
            //    }
            //}
            //catch (Exception ex)
            //{
            //    // Do nothing as the ipAddress/sessionKey was already initialized
            //    // so continue the execution.
            //}

            bool shouldIgnoreProcessing = IsActivityToIgnoreProcessing(activityType);

            // Set common properties for all activities
            foreach (var act in activities)
            {
                if (shouldIgnoreProcessing)
                {
                    act.processed = true;
                }

                act.device = activityDevice;
                act.IPAddress = ipAddress;
                act.sessionKey = sessionKey;
            }

            using (AtlasModelCore _model = new AtlasModelCore())
            {
                _model.ContentActivity.AddRange(activities);

                // Get all unique content IDs
                var contentIds = activities.Select(a => a.contentId).Distinct().ToList();

                // Batch fetch all contents at once
                var contents = await _model.Content.Where(c => contentIds.Contains(c.contentId)).ToListAsync();

                // Get content type from first content (assuming all have same type)
                var firstContent = contents.FirstOrDefault();
                if (firstContent == null) return false;

                string contentType = firstContent.type;
                var contentType_activityType_combination = (contentType + "|" + activityType).ToUpper();

                // Check if we need to update lastUpdate fields
                bool shouldUpdateLastModification = !BluebookActivityIgnoreListEnum.activitiesToIgnore.Contains(contentType_activityType_combination) &&
                                                   ContentChecker.shouldUpdateLastModification(contentType_activityType_combination);

                bool shouldUpdateParentLastModification = !BluebookActivityIgnoreListEnum.activitiesToIgnore.Contains(contentType_activityType_combination) &&
                                                         ContentChecker.shouldUpdateParentLastModification(contentType_activityType_combination);

                if (shouldUpdateLastModification)
                {
                    var currentTime = DateTime.UtcNow;

                    // Update all contents lastUpdate
                    foreach (var content in contents)
                    {
                        content.lastUpdate = currentTime;
                        _model.Entry(content).State = EntityState.Modified;
                    }

                    // Handle parent contents if needed
                    if (shouldUpdateParentLastModification)
                    {
                        var parentContentIds = contents
                            .Where(c => c.parentContentId.HasValue)
                            .Select(c => c.parentContentId.Value)
                            .Distinct()
                            .ToList();

                        if (parentContentIds.Any())
                        {
                            var parentContents = await _model.Content.Where(c => parentContentIds.Contains(c.contentId)).ToListAsync();

                            foreach (var parentContent in parentContents)
                            {
                                parentContent.lastUpdate = currentTime;
                                _model.Entry(parentContent).State = EntityState.Modified;
                            }
                        }
                    }
                }

                bool success = await _model.SaveChangesAsync() > 0;

                if (success)
                {
                    // Send messages in batch for better performance
                    SendToAzFunctionsSchedulersOnActivityAddBatch(activities, contentIds);
                }

                return success;
            }
        }

        [Obsolete("Use Add(int content_id, Guid content_uuid, ContentActivity obj, int groupTime = 0) instead")]
        public int Add(int content_id, ContentActivity obj, int groupTime = 0)
        {
            ContentRepository _repo = this._isTransaction ?
                                        new ContentRepository(_currentUser, this._mdG, this._transaction) :
                                        new ContentRepository(_currentUser);
            if (IsActivityToIgnoreProcessing(obj.type))
            {
                obj.processed = true;
            }

            string ipAddress = "UNKNOWN";
            string sessionKey = "";

            try
            {
                /*
                 * PINGAS
                 * if (System.Web.HttpContext.Current != null)
                {
                    ipAddress = UserService.ExtractCurrentIPAddress();

                    var session_key_claim = ((System.Security.Claims.ClaimsIdentity)System.Threading.Thread.CurrentPrincipal.Identity)
                                            .Claims.FirstOrDefault(o => o.Type == "session_key");

                    if (session_key_claim != null)
                        sessionKey = session_key_claim.Value;
                }*/
            }
            catch (Exception ex)
            {
                // Do nothing as the ipAddress/sessionKey was already initialized
                // so continue the execution.
            }

            string userAgent = "";  //System.Web.HttpContext.Current.Request.Headers.GetValues("User-Agent")?.FirstOrDefault();

            obj.device = new DeviceDetectorHelper(userAgent).GetUserAgentDevice();

            obj.IPAddress = ipAddress;
            obj.sessionKey = sessionKey;

            var res = _repo.Add_Activity(content_id, obj, groupTime);

            AtlasModelCore _md = this._mdG ?? new AtlasModelCore();
            var content = _md.Content.Find(content_id);

            //update [dbo].Content lastActivity
            //exclude activities that do not justify a new bb generation
            var contentType_activityType_combination = (content.type + "|" + obj.type).ToUpper();

            if (!BluebookActivityIgnoreListEnum.activitiesToIgnore.Contains(contentType_activityType_combination))
            {
                if (content != null && ContentChecker.shouldUpdateLastModification(contentType_activityType_combination))
                {
                    content.lastUpdate = DateTime.UtcNow;
                    _md.Entry(content).State = EntityState.Modified;
                }
                if (content != null && content.parentContentId.HasValue &&
                    ContentChecker.shouldUpdateParentLastModification(contentType_activityType_combination))
                {
                    var parentContent = _md.Content.Find(content.parentContentId);
                    if (parentContent != null)
                    {
                        parentContent.lastUpdate = DateTime.UtcNow;
                        _md.Entry(parentContent).State = EntityState.Modified;
                    }
                }
                _md.SaveChanges();
            }

            SendToAzFunctionsSchedulersOnActivityAdd(obj, content_id);

            return res;
        }

        public int Add(int content_id, Guid content_uuid, ContentActivity obj, string userAgent, int groupTime = 0)
        {
            ContentRepository _repo = this._isTransaction ?
                                        new ContentRepository(_currentUser, this._mdG, this._transaction) :
                                        new ContentRepository(_currentUser);

            if (content_id == 0)
            {
                content_id = _repo.GetContentIdByUuId(content_uuid);
            }

            if (IsActivityToIgnoreProcessing(obj.type))
            {
                obj.processed = true;
            }

            try
            {
                obj.device = new DeviceDetectorHelper(userAgent).GetUserAgentDevice();
            }
            catch
            {
                // Let's inform UNKNOWN for devices for now. The way we detect devices properly will be implemented.
                obj.device = "UNKNOWN";
            }

            string ipAddress = "UNKNOWN";
            string sessionKey = "";

            try
            {
                /*
                 * PINGAS
                 * if (System.Web.HttpContext.Current != null)
                {
                    ipAddress = UserService.ExtractCurrentIPAddress();

                    var session_key_claim = ((System.Security.Claims.ClaimsIdentity)System.Threading.Thread.CurrentPrincipal.Identity)
                                            .Claims.FirstOrDefault(o => o.Type == "session_key");

                    if (session_key_claim != null)
                        sessionKey = session_key_claim.Value;
                }*/
            }
            catch (Exception ex)
            {
                // Do nothing as the ipAddress/sessionKey was already initialized
                // so continue the execution.
            }

            obj.IPAddress = ipAddress;
            obj.sessionKey = sessionKey;

            var res = _repo.Add_Activity(content_id, content_uuid, obj, groupTime);

            AtlasModelCore _md = this._mdG ?? new AtlasModelCore();
            var content = _md.Content.Find(content_id);

            //update [dbo].Content lastActivity
            //exclude activities that do not justify a new bb generation
            var contentType_activityType_combination = (content.type + "|" + obj.type).ToUpper();

            if (!BluebookActivityIgnoreListEnum.activitiesToIgnore.Contains(contentType_activityType_combination))
            {
                if (content != null && ContentChecker.shouldUpdateLastModification(contentType_activityType_combination))
                {
                    content.lastUpdate = DateTime.UtcNow;
                    _md.Entry(content).State = EntityState.Modified;
                }
                if (content != null && content.parentContentId.HasValue &&
                    ContentChecker.shouldUpdateParentLastModification(contentType_activityType_combination))
                {
                    var parentContent = _md.Content.Find(content.parentContentId);
                    if (parentContent != null)
                    {
                        parentContent.lastUpdate = DateTime.UtcNow;
                        _md.Entry(parentContent).State = EntityState.Modified;
                    }
                }
                _md.SaveChanges();
            }

            SendToAzFunctionsSchedulersOnActivityAdd(obj, content_id);

            return res;
        }
        public int Add(int content_id, Guid content_uuid, ContentActivity obj, string userAgent, StorageSettings storageSettings, int groupTime = 0)
        {
            ContentRepository _repo = this._isTransaction ?
                                        new ContentRepository(_currentUser, this._mdG, this._transaction) :
                                        new ContentRepository(_currentUser);

            if (content_id == 0)
            {
                content_id = _repo.GetContentIdByUuId(content_uuid);
            }

            if (IsActivityToIgnoreProcessing(obj.type))
            {
                obj.processed = true;
            }

            try
            {
                obj.device = new DeviceDetectorHelper(userAgent).GetUserAgentDevice();
            }
            catch
            {
                // Let's inform UNKNOWN for devices for now. The way we detect devices properly will be implemented.
                obj.device = "UNKNOWN";
            }

            string ipAddress = "UNKNOWN";
            string sessionKey = "";

            try
            {
                /*
                 * PINGAS
                 * if (System.Web.HttpContext.Current != null)
                {
                    ipAddress = UserService.ExtractCurrentIPAddress();

                    var session_key_claim = ((System.Security.Claims.ClaimsIdentity)System.Threading.Thread.CurrentPrincipal.Identity)
                                            .Claims.FirstOrDefault(o => o.Type == "session_key");

                    if (session_key_claim != null)
                        sessionKey = session_key_claim.Value;
                }*/
            }
            catch (Exception ex)
            {
                // Do nothing as the ipAddress/sessionKey was already initialized
                // so continue the execution.
            }

            obj.IPAddress = ipAddress;
            obj.sessionKey = sessionKey;

            var res = _repo.Add_Activity(content_id, content_uuid, obj, groupTime);

            AtlasModelCore _md = this._mdG ?? new AtlasModelCore();
            var content = _md.Content.Find(content_id);

            //update [dbo].Content lastActivity
            //exclude activities that do not justify a new bb generation
            var contentType_activityType_combination = (content.type + "|" + obj.type).ToUpper();

            if (!BluebookActivityIgnoreListEnum.activitiesToIgnore.Contains(contentType_activityType_combination))
            {
                if (content != null && ContentChecker.shouldUpdateLastModification(contentType_activityType_combination))
                {
                    content.lastUpdate = DateTime.UtcNow;
                    _md.Entry(content).State = EntityState.Modified;
                }
                if (content != null && content.parentContentId.HasValue &&
                    ContentChecker.shouldUpdateParentLastModification(contentType_activityType_combination))
                {
                    var parentContent = _md.Content.Find(content.parentContentId);
                    if (parentContent != null)
                    {
                        parentContent.lastUpdate = DateTime.UtcNow;
                        _md.Entry(parentContent).State = EntityState.Modified;
                    }
                }
                _md.SaveChanges();
            }

            SendToAzFunctionsSchedulersOnActivityAdd(obj, content_uuid, storageSettings);

            return res;
        }

        public static bool IsActivityToIgnoreProcessing(string activityType)
        {
            // if the activity is in the list of activities that should not be processed, return true
            string[] activityToIgnore = new[] {
                "ATTACHMENT_ADD",
                "ATTACHMENT_DELETE",
                "ATTACHMENT_DOWNLOAD",
                "ATTACHMENT_VIEW",
                "DISABLED_ATTACHMENT_WATERMARK",
                "BLUEBOOK_VERSION_DOWNLOAD",
                "BLUEBOOK_VERSION_VIEW",
                "BLUEBOOK_VIEW",
                "CONTENT_ESIGNATURE_VIEW"
            };

            return activityToIgnore.Contains(activityType);

        }

        private void SendToAzFunctionsSchedulersOnActivityAdd(ContentActivity obj, int content_id)
        {
            //try catch pra garantir que nao vai interromper o andamento da transaçao/request
            try
            {
                String queueName = "contentactivity";
                obj.Content = null;
                //serializar objeto obj / 0
                var objJson = JsonConvert.SerializeObject(obj, new JsonSerializerSettings() { ReferenceLoopHandling = ReferenceLoopHandling.Ignore });


                //Save Message
                MessageSender ms = new MessageSender(queueName);
                ms.SendMessage(objJson);
            }
            // Should log or send a Sentry for every failed message
            catch (Exception ex)
            {
                var ravenClient = new RavenClient("https://6aa2f8bcac3e41e198dc414d24d19e45:<EMAIL>/162539");
                ravenClient.Environment = ConfigurationManager.AppSettings["ENVIRONMENT"] ?? "DEV";
                var sentryEvent = new SharpRaven.Data.SentryEvent(new SentryMessage($"ERROR AddContentActivity: contentId={content_id};type={obj.type}"));
                sentryEvent.Extra = new
                {
                    ex,
                    ex.InnerException
                };

                ravenClient.Capture(sentryEvent);
            }
        }
        private void SendToAzFunctionsSchedulersOnActivityAdd(ContentActivity obj, Guid contentUuid, StorageSettings storageSettings)
        {
            try
            {
                obj.Content = null;

                var objJson = JsonConvert.SerializeObject(obj, new JsonSerializerSettings() { ReferenceLoopHandling = ReferenceLoopHandling.Ignore });

                var queueName = "contentactivity";

                MessageSender messageSender = new MessageSender(storageSettings, queueName);

                messageSender.SendMessage(objJson);
            }
            catch (Exception ex)
            {
                /* nothing to do here, it just doesn't stop the process in case of error. */
            }
        }

        /// <summary>
        /// OPTIMIZED VERSION: Send multiple activities to Azure Functions Schedulers in batch.
        /// Uses a single MessageSender instance to send multiple messages efficiently.
        /// </summary>
        /// <param name="activities">List of activities to send</param>
        /// <param name="contentIds">List of content IDs (for error logging)</param>
        private void SendToAzFunctionsSchedulersOnActivityAddBatch(List<ContentActivity> activities, List<int> contentIds)
        {
            // try catch pra garantir que nao vai interromper o andamento da transaçao/request
            try
            {
                String queueName = "contentactivity";

                // Single MessageSender instance for all messages
                MessageSender ms = new MessageSender(queueName);

                foreach (var activity in activities)
                {
                    // Clear Content reference to avoid circular reference issues
                    activity.Content = null;

                    // Serialize activity
                    var objJson = JsonConvert.SerializeObject(activity, new JsonSerializerSettings() { ReferenceLoopHandling = ReferenceLoopHandling.Ignore });

                    // Send message using the same MessageSender instance
                    ms.SendMessage(objJson);
                }
            }
            // Should log or send a Sentry for every failed message
            catch (Exception ex)
            {
                var ravenClient = new RavenClient("https://6aa2f8bcac3e41e198dc414d24d19e45:<EMAIL>/162539");
                ravenClient.Environment = ConfigurationManager.AppSettings["ENVIRONMENT"] ?? "DEV";

                // Log error with all content IDs involved
                var contentIdsStr = string.Join(",", contentIds);
                var activityTypes = string.Join(",", activities.Select(a => a.type).Distinct());

                var sentryEvent = new SharpRaven.Data.SentryEvent(new SentryMessage($"ERROR AddContentActivityBatch: contentIds={contentIdsStr};types={activityTypes}"));
                sentryEvent.Extra = new
                {
                    ex,
                    ex.InnerException,
                    contentIds,
                    activityCount = activities.Count
                };

                ravenClient.Capture(sentryEvent);
            }
        }

        public async Task<List<AuditlogViewModel>> FetchContentActivities(ContentRequestFilter filters, List<UserRole> userRoles)
        {
            UserRoleRepository usrRepo = new UserRoleRepository(_currentUser);

            if (userRoles.Any(ur => ur.Role.name == "CLIENT_ADMIN" || ur.Role.name == "SUPER_ADMIN" || ur.Role.name == "SUPPORT_ADMIN"))
            {
                ContentActivityRepository ca_repo = new ContentActivityRepository(_currentUser, this._user);

                var res = (await ca_repo.FetchAllAsync(filters, userRoles));

                var ignoredActivities = new string[]
                {
                    Operations.AGENDA_POSTPONED_CANCEL_ICS,
                    Operations.AGENDA_POSTPONED_ORIGIN,
                    Operations.COMMENT_NOTIFY_OWNER,
                    Operations.EXTERNAL_CORPORATE_BOOK_DOC_CREATED
                };

                var res_vm = res.Where(o => !ignoredActivities.Contains(o.Type)).Select(o =>
                {
                    IdentifyCommentAnswer(o);

                    return new AuditlogViewModel()
                    {
                        logId = o.ContentActivityId,
                        activityType = o.Type,
                        details =
                        o.SubItemType == SubItems.PERMISSIONS ||
                        o.SubItemType == SubItems.SUBSCRIBER ||
                        o.SubItemType == SubItems.OWNER ||
                        o.SubItemType == SubItems.TASK_DETAILS_UPDATED ||
                        o.SubItemType == SubItems.AGENDA_TITLE_EXT_ASSIGNER ||
                        o.Type == Operations.ESIGNATURE_REQUEST ||
                        o.Type == Operations.DIGITAL_SIGNATURE_REQUEST ||
                        o.Type == Operations.IMPORTED_CONTENT ||
                        o.Type == Operations.IMPORTED_COMMENTS ||
                        o.Type == Operations.IMPORTED_ATTACHMENTS ||
                        o.Type == Operations.TASK_MOVED ||
                        o.Type == Operations.ATTACHMENT_NOTIFICATION ||
                        o.Type == Operations.CHECKLIST_ITEM_ADD ||
                        o.Type == Operations.CHECKLIST_ITEM_UPDATE ||
                        o.Type == Operations.CHECKLIST_REORDER ||
                        o.Type == Operations.SUBSCRIBER_UPDATE ||
                        o.Type == Operations.AGENDA_POSTPONED ||
                        o.Type == Operations.FORM_ANSWERED ||
                        o.Type == Operations.UPDATED_SIGNATURE_REQUEST ||
                        o.Type == Operations.MEETING_GUEST_ADD

                        ?
                            o.ContentData
                        :

                        (o.Type == Operations.ATTACHMENT_ADD || o.Type == Operations.ATTACHMENT_ADD_FROM_FOLDER ||
                         o.Type == Operations.ATTACHMENT_DOWNLOAD || o.Type == Operations.ATTACHMENT_VIEW) ?
                            "contentAttachmentId: " + (JObject.Parse(o.ContentData)["id"] ?? JObject.Parse(o.ContentData)["contentAttachmentId"] ?? "") :
                        o.SubItemType == SubItems.GUEST ?
                            JsonConvert.DeserializeObject(o.ContentData).ToString() :
                        o.Type == "COMMENT_MENTION" ?
                            "userMentionedId:" + ((dynamic)JsonConvert.DeserializeObject(o.ContentData ?? "{}"))?.userMentionedId :
                        o.Type == "SHARED_BLUEBOOK_NOTES" ?
                            JsonConvert.DeserializeObject(o.ContentData).ToString() :
                            GetAuditDetails(o), // Default logic

                        userId = o.ActivityUser,
                        userName = o.UserName,
                        clientId = o.ClientId,
                        date = o.Date,
                        workgroupColor = o.BulletColor,
                        workgroupName = o.WorkgroupName,
                        workgroupId = o.WorkgroupId,
                        logType = "CONTENT_LOG",
                        contentId = o.ContentId,
                        contentType = o.ContentType,
                        IPAddress = o.IPAddress,
                        sessionKey = o.SessionKey,
                        externalSignerActivity = o.SubItemType == SubItems.EXTERNAL_SIGNER,
                        device = o.Device
                    };
                }).ToList();

                return res_vm;
            }
            else
            {
                throw new Exception("NOT_ADMIN");
            }

        }

        public List<AuditlogViewModel> PrepareContentActivities(List<AuditLogItemView> res)
        {
            var ignoredActivities = new string[]
              {
                    Operations.AGENDA_POSTPONED_CANCEL_ICS,
                    Operations.AGENDA_POSTPONED_ORIGIN,
                    Operations.COMMENT_NOTIFY_OWNER,
                    Operations.EXTERNAL_CORPORATE_BOOK_DOC_CREATED
              };

            var res_vm = res.Where(o => !ignoredActivities.Contains(o.Type)).Select(o =>
            {
                IdentifyCommentAnswer(o);

                return new AuditlogViewModel()
                {
                    logId = o.ContentActivityId,
                    activityType = o.Type,
                    details =
                    o.SubItemType == SubItems.PERMISSIONS ||
                    o.SubItemType == SubItems.SUBSCRIBER ||
                    o.SubItemType == SubItems.OWNER ||
                    o.SubItemType == SubItems.TASK_DETAILS_UPDATED ||
                    o.SubItemType == SubItems.AGENDA_TITLE_EXT_ASSIGNER ||
                    o.Type == Operations.ESIGNATURE_REQUEST ||
                    o.Type == Operations.DIGITAL_SIGNATURE_REQUEST ||
                    o.Type == Operations.IMPORTED_CONTENT ||
                    o.Type == Operations.IMPORTED_COMMENTS ||
                    o.Type == Operations.IMPORTED_ATTACHMENTS ||
                    o.Type == Operations.TASK_MOVED ||
                    o.Type == Operations.ATTACHMENT_NOTIFICATION ||
                    o.Type == Operations.CHECKLIST_ITEM_ADD ||
                    o.Type == Operations.CHECKLIST_ITEM_UPDATE ||
                    o.Type == Operations.CHECKLIST_REORDER ||
                    o.Type == Operations.SUBSCRIBER_UPDATE ||
                    o.Type == Operations.AGENDA_POSTPONED ||
                    o.Type == Operations.FORM_ANSWERED ||
                    o.Type == Operations.UPDATED_SIGNATURE_REQUEST ||
                    o.Type == Operations.MEETING_GUEST_ADD

                    ?
                        o.ContentData
                    :

                    (o.Type == Operations.ATTACHMENT_ADD || o.Type == Operations.ATTACHMENT_ADD_FROM_FOLDER ||
                     o.Type == Operations.ATTACHMENT_DOWNLOAD || o.Type == Operations.ATTACHMENT_VIEW) ?
                        "contentAttachmentId: " + (JObject.Parse(o.ContentData)["id"] ?? JObject.Parse(o.ContentData)["contentAttachmentId"] ?? "") :
                    o.SubItemType == SubItems.GUEST ?
                        JsonConvert.DeserializeObject(o.ContentData).ToString() :
                    o.Type == "COMMENT_MENTION" ?
                        "userMentionedId:" + ((dynamic)JsonConvert.DeserializeObject(o.ContentData ?? "{}"))?.userMentionedId :
                    o.Type == "SHARED_BLUEBOOK_NOTES" ?
                        JsonConvert.DeserializeObject(o.ContentData).ToString() :
                        GetAuditDetails(o), // Default logic

                    userId = o.ActivityUser,
                    userName = o.UserName,
                    clientId = o.ClientId,
                    date = o.Date,
                    workgroupColor = o.BulletColor,
                    workgroupName = o.WorkgroupName,
                    workgroupId = o.WorkgroupId,
                    logType = "CONTENT_LOG",
                    contentId = o.ContentId,
                    contentType = o.ContentType,
                    IPAddress = o.IPAddress,
                    sessionKey = o.SessionKey,
                    externalSignerActivity = o.SubItemType == SubItems.EXTERNAL_SIGNER,
                    device = o.Device
                };
            }).ToList();

            return res_vm;
        }

        private string GetAuditDetails(AuditLogItemView o)
        {
            try
            {
                if (o.Type == Operations.ATTACHMENT_NOTIFICATION && o.SubItemType == SubItems.USER)
                {
                    // KB attachment notification
                    var contentData = (dynamic)JsonConvert.DeserializeObject(o.ContentData ?? "{}");
                    return $"user: {o.SubItemId}, attachment: {contentData.contentAttachment}";
                }
                if (o.Type == Operations.CHECKLIST_ITEM_DELETE)
                {
                    return $"taskCheckListItemId: {o.SubItemId}";
                }

                if (o.SubItemType == SubItems.EXTERNAL_SIGNER)
                {
                    var contentData = (dynamic)JsonConvert.DeserializeObject(o.ContentData ?? "{}");
                    string details = $"name: {contentData.name},\n" +
                                     $"email: {contentData.email}";

                    if (contentData.contentAttachmentId != null)
                    {
                        details += $"\ncontentAttachmentId: {contentData.contentAttachmentId}";
                    }

                    return details;
                }
                if (o.Type == Operations.MEETING_RESEND_INVITE)
                {
                    var json = (dynamic)JsonConvert.DeserializeObject(o.ContentData);
                    return "recipients: " + String.Join(", ", json.users ?? "");
                }

                if (o.Type == Operations.FORM_EXPIRED)
                {
                    return $"user: {o.ActivityUser}";
                }

                // RELATED TO EXTERNAL DOCUMENT REQUEST

                if (o.Type == Operations.EXTERNAL_DOCUMENT_REQUEST_DELETED || o.Type == Operations.EXTERNAL_DOCUMENT_REQUEST_RESENT)
                {
                    //ExternalUser externalUser = JsonConvert.DeserializeObject<ExternalUser>(o.ContentData);
                    var data = JObject.Parse(o.ContentData ?? "{}");
                    var externalUser = data["externalUser"]?.ToObject<ExternalUser>();
                    return externalUser != null ? $"externalUserName: {externalUser.ExternalName}; externalUserEmail: {externalUser.ExternalMail}" : string.Empty;
                }

                string[] edrOperations = new string[] { Operations.EXTERNAL_DOCUMENT_REQUEST, Operations.EXTERNAL_USER_ADDED };

                if (edrOperations.Contains(o.Type))
                {
                    // Parse as JToken to handle both array and object cases
                    var contentDataToken = JToken.Parse(o.ContentData);
                    ExternalUser[] externalUsers = null;

                    if (contentDataToken is JArray)
                    {
                        // Direct array of ExternalUser objects
                        externalUsers = contentDataToken.ToObject<ExternalUser[]>();
                    }
                    else if (contentDataToken is JObject)
                    {
                        // Object with externalUsers property
                        externalUsers = contentDataToken["externalUsers"]?.ToObject<ExternalUser[]>();
                    }

                    return externalUsers != null && externalUsers.Any() ?
                        string.Join("; ", externalUsers.Select(u => $"externalUserName: {u.ExternalName}, externalUserEmail: {u.ExternalMail}"))
                        :
                        string.Empty;
                }

                if (o.SubItemType == SubItems.ANNOUNCEMENT_CREATION_TYPE || o.SubItemType == SubItems.MEETING_ICS_STATUS)
                {
                    return o.ContentData ?? string.Empty;
                }

                return string.Empty;
            }
            catch (Exception)
            {
                return string.Empty;
            }

        }
        // Pingas Json
        private void IdentifyCommentAnswer(ContentActivity activity)
        {
            if (activity.type == Operations.COMMENT_ADD)
            {
                var contentData = JsonConvert.DeserializeObject<CommentContentData>(activity.contentData);
                activity.type = contentData.isCommentAnswer ? Operations.COMMENT_ANSWER_ADD : Operations.COMMENT_ADD;
            }
            else if (activity.type == Operations.COMMENT_DELETE)
            {
                var contentData = JsonConvert.DeserializeObject<CommentContentData>(activity.contentData);
                activity.type = contentData.isCommentAnswer ? Operations.COMMENT_ANSWER_DELETE : Operations.COMMENT_DELETE;
            }
            else if (activity.type == Operations.COMMENT_UNDELETE)
            {
                var contentData = JsonConvert.DeserializeObject<CommentContentData>(activity.contentData);
                activity.type = contentData.isCommentAnswer ? Operations.COMMENT_ANSWER_UNDELETE : Operations.COMMENT_UNDELETE;
            }
        }
        // Pingas Json
        private void IdentifyCommentAnswer(AuditLogItemView activity)
        {
            if (activity.Type == Operations.COMMENT_ADD)
            {
                var contentData = JsonConvert.DeserializeObject<CommentContentData>(activity.ContentData);
                activity.Type = contentData.isCommentAnswer ? Operations.COMMENT_ANSWER_ADD : Operations.COMMENT_ADD;
            }
            else if (activity.Type == Operations.COMMENT_DELETE)
            {
                var contentData = JsonConvert.DeserializeObject<CommentContentData>(activity.ContentData);
                activity.Type = contentData.isCommentAnswer ? Operations.COMMENT_ANSWER_DELETE : Operations.COMMENT_DELETE;
            }
            else if (activity.Type == Operations.COMMENT_UNDELETE)
            {
                var contentData = JsonConvert.DeserializeObject<CommentContentData>(activity.ContentData);
                activity.Type = contentData.isCommentAnswer ? Operations.COMMENT_ANSWER_UNDELETE : Operations.COMMENT_UNDELETE;
            }
        }
    }
}
