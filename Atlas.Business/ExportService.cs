using AngleSharp.Html.Parser;
using Atlas.Business.ViewModels;
using Atlas.Business.ViewModels.Export;
using Atlas.Business.ViewModels.Forms;
using Atlas.Business.ViewModels.Insights;
using Atlas.Business.ViewModels.KnowledgeBase;
using Atlas.CrossCutting.AppEnums;
using Atlas.CrossCutting.DTO.AtlasInsights;
using Atlas.Data.Entities;
using Atlas.Data.Repository;
using i18next_net;
using Microsoft.EntityFrameworkCore;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.Serialization;
using System.Security;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using TimeZoneConverter;

namespace Atlas.Business
{
    public class ExportService
    {
        private readonly User _user;
        private i18next_net.i18next i18n;
        private ContentService _svc;

        static string _environmentUrl;

        public int _currentUser { get; private set; }

        public void SetCurrentUser(int userId)
        {
            _currentUser = userId;
        }

        public ExportService()
        {
            i18n = new i18next(new InitOptions()
            {
                defaultNS = "common",
                localeFileType = LocaleFileTypeEnum.Path,
                fallbackLng = "en"
            });
        }

        public ExportService(int user)
        {
            _currentUser = user;
            _svc = new ContentService(_currentUser);
            i18n = new i18next_net.i18next(new i18next_net.InitOptions()
            {
                defaultNS = "common",
                localeFileType = LocaleFileTypeEnum.Path,
                fallbackLng = "en"
            });

            try
            {
                _environmentUrl = ConfigurationManager.AppSettings["EnvironmentUrl"];
            }
            catch
            {
                _environmentUrl = "https://www.atlasgov.com";
            }
        }

        public ExportService(User user)
        {
            this._user = user;

            i18n = new i18next(new InitOptions()
            {
                defaultNS = "common",
                localeFileType = LocaleFileTypeEnum.Path,
                fallbackLng = "en"
            });
        }

        public async Task<byte[]> exportDataToExcelAsync(ExportDataViewModel exportConfig)
        {
            AtlasModelCore _md = new AtlasModelCore();
            User usr = _md.User.Find(_currentUser);

            string lang = (usr.defaultLanguage ?? "pt").ToLower();
            i18n.changeLanguage(lang);

            // Create a security validation for the exportConfig
            await ValidateExportDataToExcelAsync(exportConfig);

            using (var stream = new MemoryStream())
            {
                using (ExcelPackage package = new ExcelPackage(stream))
                {
                    ExcelWorksheet worksheet;
                    var allWorksheets = package.Workbook.Worksheets;

                    if (exportConfig.exportMeetings)
                    {
                        worksheet = allWorksheets.Add(i18n.t("export:meetings"));
                        await addMeetingsData(worksheet, exportConfig);
                    }

                    if (exportConfig.exportProjects)
                    {
                        worksheet = allWorksheets.Add(i18n.t("export:actions"));
                        await addProjectsData(worksheet, exportConfig);
                    }

                    if (exportConfig.exportPolls)
                    {
                        worksheet = allWorksheets.Add(i18n.t("export:polls"));
                        addPollsData(worksheet, exportConfig);
                    }

                    if (exportConfig.exportKB)
                    {
                        worksheet = allWorksheets.Add(i18n.t("export:knowledgeBase"));
                        addKbData(worksheet, exportConfig);
                    }

                    if (exportConfig.exportSignatures)
                    {
                        worksheet = allWorksheets.Add(i18n.t("export:signatures"));
                        addSignaturesData(worksheet, exportConfig, usr);
                    }

                    if (exportConfig.exportNewKB)
                    {
                        worksheet = allWorksheets.Add(i18n.t("export:knowledgeBase"));

                        WorkgroupService service = new WorkgroupService(_currentUser);
                        var workgroups = await service.Get();

                        var rootWorkgroup = await service.GetWorkgroupRootByClientAsync(exportConfig.clientId);
                        var workgroupIdList = workgroups.Where(w => w.UAC.isOwner && !w.archived).Select(w => w.workgroupId).ToList();
                        workgroupIdList.Add(rootWorkgroup.workgroupId);
                        // TODO: check if it's better to extract it to a method since the kb v2 trash already needs this list.
                        exportConfig.workgroups = workgroupIdList.ToArray();

                        await addNewKbDataAsync(worksheet, exportConfig);
                    }

                    package.Save();

                    if (exportConfig.exportKB || exportConfig.exportNewKB)
                    {
                        ActivityService.Add("DATA_KB_EXPORTED", exportConfig.clientId, null, _currentUser, $"clientId: {exportConfig.clientId}");
                    }
                    else
                    {
                        WorkgroupRepository repo = new WorkgroupRepository(_currentUser);
                        var workgroup = await repo.Get(exportConfig.workgroupId);
                        ActivityService.Add("DATA_WORKGROUP_EXPORTED", workgroup.Client.clientId, exportConfig.workgroupId, _currentUser);
                    }

                    //For local testing
                    //package.SaveAs(new FileInfo(@"c:\workbooks\myworkbook.xlsx"));
                    return stream.ToArray();

                }
            }
        }

        private async System.Threading.Tasks.Task ValidateExportDataToExcelAsync(ExportDataViewModel exportConfig)
        {
            if (exportConfig.clientId > 0 && !exportConfig.exportNewKB)
            {
                UserRoleService service = new UserRoleService(_currentUser);
                bool isClientAdmin = await service.IsAdminOnClient(exportConfig.clientId);
                if (!isClientAdmin)
                {
                    throw new SecurityException("INVALID_GRANT");
                }
            }

            if (exportConfig.workgroupId > 0)
            {
                WorkgroupService workgroupService = new WorkgroupService(_currentUser);
                var workgroup = await workgroupService.GetWorkgroupAsync(exportConfig.workgroupId, false);

                if (!workgroup.UAC.isOwner)
                {
                    throw new SecurityException("INVALID_GRANT");
                }
            }
        }

        public async Task<byte[]> exportDataToExcelAsyncBatch(List<ExportDataViewModel> exportConfig)
        {
            AtlasModelCore _md = new AtlasModelCore();
            User usr = _md.User.Find(_currentUser);

            string lang = (usr.defaultLanguage ?? "pt").ToLower();
            i18n.changeLanguage(lang);

            using (var stream = new MemoryStream())
            {
                using (ExcelPackage package = new ExcelPackage(stream))
                {
                    var allWorksheets = package.Workbook.Worksheets;

                    int currentRowMeetings = 1;
                    int currentRowProjects = 1;
                    int currentRowPolls = 1;
                    int currentRowSignatures = 1;
                    int currentRowKb = 1;

                    var worksheetMeetings = allWorksheets.Add(i18n.t("export:meetings"));
                    var worksheetProjects = allWorksheets.Add(i18n.t("export:actions"));
                    var worksheetPolls = allWorksheets.Add(i18n.t("export:polls"));
                    var worksheetSignatures = allWorksheets.Add(i18n.t("export:signatures"));
                    var worksheetNewKb = allWorksheets.Add(i18n.t("export:knowledgeBase"));



                    foreach (var export in exportConfig)
                    {

                        if (export.exportMeetings)
                        {
                            currentRowMeetings = await addMeetingsData(worksheetMeetings, export, currentRowMeetings);
                        }

                        if (export.exportProjects)
                        {
                            currentRowProjects = await addProjectsData(worksheetProjects, export, currentRowProjects);
                        }

                        if (export.exportPolls)
                        {
                            currentRowPolls = addPollsData(worksheetPolls, export, currentRowPolls);
                        }

                        if (export.exportSignatures)
                        {
                            currentRowSignatures = addSignaturesData(worksheetSignatures, export, usr, currentRowSignatures);
                        }

                        if (export.exportNewKB)
                        {
                            currentRowKb = await addNewKbDataAsync(worksheetNewKb, export, currentRowKb);
                        }
                    }

                    package.Save();

                    var clientIds = exportConfig.Select(ex => ex.clientId).Distinct().ToArray();
                    var workgroupIds = exportConfig.Select(ex => ex.workgroupId).ToArray();

                    foreach (var clientId in clientIds)
                    {
                        ActivityService.Add("DATA_BOARD_EXPORTED", clientId, null, _currentUser, $"\"boards\": [{string.Join(", ", workgroupIds)}]");
                    }

                    return stream.ToArray();
                }
            }
        }

        public byte[] ExportDetailedFormReportToExcel(DetailedReportViewModel detailedReportViewModel, Dictionary<string, string> questionCommentsDict)
        {
            string locale = this._user.defaultLanguage ?? "pt";
            i18n.changeLanguage(locale.ToLower());

            using (var stream = new MemoryStream())
            {
                using (var excel = new ExcelPackage(stream))
                {
                    var worksheet = excel.Workbook.Worksheets.Add(i18n.t("export:form.detailed.worksheetName"));

                    this.AddDetailedFormReportData(worksheet, detailedReportViewModel, questionCommentsDict);

                    excel.Save();

                    // Uncomment to perform local tests
                    //var filename = Environment.ExpandEnvironmentVariables($"%TEMP%\\form-detailed-report-{DateTimeOffset.UtcNow.ToUnixTimeSeconds()}.xlsx");
                    //excel.SaveAs(new FileInfo(filename));
                }

                return stream.ToArray();
            }
        }

        public byte[] ExportConsolidatedFormReportToExcel(FormConsolidatedReportViewModel consolidatedReport)
        {
            string locale = this._user.defaultLanguage ?? "pt";
            i18n.changeLanguage(locale.ToLower());

            using (var stream = new MemoryStream())
            {
                using (ExcelPackage package = new ExcelPackage(stream))
                {
                    string reportSheet = i18n.t("export:form.consolidated.answersWorksheetName");
                    string reportSheetComments = i18n.t("export:form.consolidated.commentsWorksheetName");
                    string reportSheetCommentsRespondentIdColumnTitle = i18n.t("export:form.consolidated.respondent");
                    string questionWithNoComments = i18n.t("export:form.consolidated.noComment");
                    string answersCount = i18n.t("export:form.consolidated.answersCount");
                    string answersPercentage = i18n.t("export:form.consolidated.answersPercentage");
                    string fontName = "Calibri";
                    int fontSize = 9;

                    string otherOption = i18n.t("export:form.otherOption");

                    ExcelWorksheet reportWorksheet = package.Workbook.Worksheets.Add(reportSheet);
                    var commentWorksheet = package.Workbook.Worksheets.Add(reportSheetComments);

                    var questionsReport = consolidatedReport.FormReportQuestion;
                    int line = 1;
                    int commentTabLine = 1;
                    int previousSection = 0;
                    bool firstSectionTitlePrinted = false;
                    int previousFormSectionId = 0;

                    foreach (FormReportQuestion question in questionsReport)
                    {
                        var FREE_ANSWER = "FREE";
                        var questionOptions = question.FormReportQuestionOptions;
                        var questionComments = question.FormReportQuestionComments;

                        if (question != questionsReport.First())
                        {
                            line++;
                        }

                        // firstSectionTitle
                        if (previousSection == 0 && !firstSectionTitlePrinted)
                        {
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 1, line, 3].Merge = true;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 1, line, 3].Style.Fill.PatternType = ExcelFillStyle.Solid;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 1, line, 3].Style.Fill.BackgroundColor.SetColor(Color.DarkGray);


                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 1].Value = question.sectionTitle;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 1].Style.Font.Bold = true;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 1].Style.Font.Name = fontName;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 1].Style.Font.Size = fontSize;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 1].Style.VerticalAlignment = ExcelVerticalAlignment.Bottom;


                            // comments tab firstSectionTitle
                            if (question.type != FREE_ANSWER && question.formSectionId != previousFormSectionId)
                            {
                                commentTabLine = previousFormSectionId == 0 ? commentTabLine : commentTabLine + 2;

                                commentWorksheet.Cells[commentTabLine, 1, commentTabLine, 3].Merge = true;
                                commentWorksheet.Cells[commentTabLine, 1].Style.Font.Bold = true;
                                commentWorksheet.Cells[commentTabLine, 1].Style.Font.Name = fontName;
                                commentWorksheet.Cells[commentTabLine, 1].Style.Font.Size = fontSize;
                                commentWorksheet.Cells[commentTabLine, 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                                commentWorksheet.Cells[commentTabLine, 1].Style.Fill.BackgroundColor.SetColor(Color.DarkGray);

                                commentWorksheet.Cells[commentTabLine, 1].Value = question.sectionTitle;

                                commentTabLine++;
                                previousFormSectionId = question.formSectionId;
                            }

                            line++;
                            firstSectionTitlePrinted = true;
                        }

                        if (question.type != FREE_ANSWER && question.formSectionId != previousFormSectionId)
                        {
                            commentTabLine = previousFormSectionId == 0 ? commentTabLine : commentTabLine + 2;

                            commentWorksheet.Cells[commentTabLine, 1, commentTabLine, 3].Merge = true;
                            commentWorksheet.Cells[commentTabLine, 1].Style.Font.Bold = true;
                            commentWorksheet.Cells[commentTabLine, 1].Style.Font.Name = fontName;
                            commentWorksheet.Cells[commentTabLine, 1].Style.Font.Size = fontSize;
                            commentWorksheet.Cells[commentTabLine, 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                            commentWorksheet.Cells[commentTabLine, 1].Style.Fill.BackgroundColor.SetColor(Color.DarkGray);

                            commentWorksheet.Cells[commentTabLine, 1].Value = question.sectionTitle;

                            commentTabLine++;
                            previousFormSectionId = question.formSectionId;
                        }

                        // sectionTitle
                        if (question.sectionOrder != previousSection)
                        {
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 1, (line), 3].Merge = true;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 1, line, 3].Style.Fill.PatternType = ExcelFillStyle.Solid;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 1, line, 3].Style.Fill.BackgroundColor.SetColor(Color.DarkGray);

                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 1].Value = question.sectionTitle;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 1].Style.Font.Bold = true;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 1].Style.Font.Name = fontName;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 1].Style.Font.Size = fontSize;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 1].Style.VerticalAlignment = ExcelVerticalAlignment.Bottom;

                            line++;
                        }

                        if (question.type == FREE_ANSWER)
                        {
                            // free-answer question styles
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[line, 1, line, 3].Merge = true;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[line, 1, line, 3].Style.Fill.PatternType = ExcelFillStyle.Solid;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[line, 1, line, 3].Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[line, 1, line, 3].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[line, 1].Style.Font.Bold = true;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[line, 1].Style.Font.Name = fontName;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[line, 1].Style.Font.Size = fontSize;

                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[line, 1].Value = question.title;
                        }
                        else
                        {
                            // non free-answer question styles
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[line, 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[line, 1].Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[line, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[line, 1].Style.Font.Bold = true;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[line, 1].Style.Font.Name = fontName;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[line, 1].Style.Font.Size = fontSize;

                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 1].Value = question.title;
                        }

                        // comments tab questionTitle and respondentId
                        if (question.type != FREE_ANSWER)
                        {
                            if (consolidatedReport.anonymousAnswer)
                            {
                                commentWorksheet.Cells[commentTabLine, 1, commentTabLine, 3].Merge = true;
                                commentWorksheet.Cells[commentTabLine, 1].Value = question.title;
                                commentWorksheet.Cells[commentTabLine, 1].Style.Font.Bold = true;
                                commentWorksheet.Cells[commentTabLine, 1].Style.Font.Name = fontName;
                                commentWorksheet.Cells[commentTabLine, 1].Style.Font.Size = fontSize;
                                commentWorksheet.Cells[commentTabLine, 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                                commentWorksheet.Cells[commentTabLine, 1].Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                            }
                            else
                            {
                                commentWorksheet.Cells[commentTabLine, 1].Value = "ID";
                                commentWorksheet.Cells[commentTabLine, 2].Value = reportSheetCommentsRespondentIdColumnTitle;
                                commentWorksheet.Cells[commentTabLine, 3].Value = question.title;

                                commentWorksheet.Cells[commentTabLine, 1].Style.Font.Bold = true;
                                commentWorksheet.Cells[commentTabLine, 1].Style.Font.Name = fontName;
                                commentWorksheet.Cells[commentTabLine, 1].Style.Font.Size = fontSize;
                                commentWorksheet.Cells[commentTabLine, 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                                commentWorksheet.Cells[commentTabLine, 1].Style.Fill.BackgroundColor.SetColor(Color.LightGray);

                                commentWorksheet.Cells[commentTabLine, 2].Style.Font.Bold = true;
                                commentWorksheet.Cells[commentTabLine, 2].Style.Font.Name = fontName;
                                commentWorksheet.Cells[commentTabLine, 2].Style.Font.Size = fontSize;
                                commentWorksheet.Cells[commentTabLine, 2].Style.Fill.PatternType = ExcelFillStyle.Solid;
                                commentWorksheet.Cells[commentTabLine, 2].Style.Fill.BackgroundColor.SetColor(Color.LightGray);

                                commentWorksheet.Cells[commentTabLine, 3].Style.Font.Bold = true;
                                commentWorksheet.Cells[commentTabLine, 3].Style.Font.Name = fontName;
                                commentWorksheet.Cells[commentTabLine, 3].Style.Font.Size = fontSize;
                                commentWorksheet.Cells[commentTabLine, 3].Style.Fill.PatternType = ExcelFillStyle.Solid;
                                commentWorksheet.Cells[commentTabLine, 3].Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                            }

                            commentTabLine++;
                        }

                        if (question.type != FREE_ANSWER)
                        {
                            // Answers count header styles
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[line, 2].Style.Fill.PatternType = ExcelFillStyle.Solid;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[line, 2].Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[line, 2].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[line, 2].Style.Font.Bold = true;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[line, 2].Style.Font.Name = fontName;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[line, 2].Style.Font.Size = fontSize;

                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[line, 2].Value = answersCount;

                            // Percentage of answers header styles
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[line, 3].Style.Fill.PatternType = ExcelFillStyle.Solid;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[line, 3].Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[line, 3].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[line, 3].Style.Font.Bold = true;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[line, 3].Style.Font.Name = fontName;
                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[line, 3].Style.Font.Size = fontSize;

                            reportWorksheet.Workbook.Worksheets[reportSheet].Cells[line, 3].Value = answersPercentage;
                        }

                        line++;

                        var totalAnswers = questionOptions.Sum(opt => opt.count);

                        if (question.type == FREE_ANSWER && questionOptions.Any())
                        {
                            var freeAnswerList = questionOptions.First();

                            foreach (var userFreeAnswer in freeAnswerList.value)
                            {
                                reportWorksheet.Workbook.Worksheets[reportSheet].Cells[line, 1, line, 3].Merge = true;
                                reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 1].Value = userFreeAnswer;
                                reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 1].Style.Font.Name = fontName;
                                reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 1].Style.Font.Size = fontSize;
                                reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
                                reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 1].Style.VerticalAlignment = ExcelVerticalAlignment.Bottom;

                                line++;
                            }
                        }

                        // alternativas da questão aqui / options's question here
                        if (question.type != FREE_ANSWER)
                        {
                            foreach (var questionOption in questionOptions)
                            {
                                var optionTitle = questionOption.title.Trim() == "form:tabQuestions.option.multipleChoice.other" ? otherOption : questionOption.title.Trim();

                                reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 1].Value = optionTitle;
                                reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 1].Style.Font.Name = fontName;
                                reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 1].Style.Font.Size = fontSize;
                                reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
                                reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 1].Style.VerticalAlignment = ExcelVerticalAlignment.Bottom;

                                reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 2].Value = questionOption.count;
                                reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 2].Style.Font.Name = fontName;
                                reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 2].Style.Font.Size = fontSize;

                                float percentage = totalAnswers == 0 ? 0.0f : (questionOption.count * 100 / totalAnswers);
                                reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 3].Value = percentage * 0.01;
                                reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 3].Style.Numberformat.Format = "#0.00%";
                                reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 3].Style.Font.Name = fontName;
                                reportWorksheet.Workbook.Worksheets[reportSheet].Cells[(line), 3].Style.Font.Size = fontSize;

                                line++;
                            }
                        }


                        if (!questionComments.Any() && question.type != FREE_ANSWER)
                        {
                            commentWorksheet.Cells[commentTabLine, 1, commentTabLine, 3].Merge = true;

                            commentWorksheet.Cells[commentTabLine, 1].Value = questionWithNoComments;
                            commentWorksheet.Cells[commentTabLine, 1].Style.Font.Name = fontName;
                            commentWorksheet.Cells[commentTabLine, 1].Style.Font.Size = fontSize;
                            commentWorksheet.Cells[commentTabLine, 1].Style.Font.Italic = true;
                            commentWorksheet.Cells[commentTabLine, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                            commentWorksheet.Cells[commentTabLine, 1].Style.VerticalAlignment = ExcelVerticalAlignment.Bottom;

                            commentTabLine++;
                        }
                        else
                        {
                            foreach (var questionComment in questionComments)
                            {
                                if (question.type != FREE_ANSWER)
                                {
                                    if (consolidatedReport.anonymousAnswer)
                                    {
                                        commentWorksheet.Cells[commentTabLine, 1].Value = questionComment.comment;
                                        commentWorksheet.Cells[commentTabLine, 1].Style.Font.Name = fontName;
                                        commentWorksheet.Cells[commentTabLine, 1].Style.Font.Size = fontSize;
                                        commentWorksheet.Cells[commentTabLine, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
                                        commentWorksheet.Cells[commentTabLine, 1].Style.VerticalAlignment = ExcelVerticalAlignment.Bottom;
                                    }
                                    else
                                    {
                                        commentWorksheet.Cells[commentTabLine, 1].Value = questionComment.User.userId;
                                        commentWorksheet.Cells[commentTabLine, 1].Style.Font.Name = fontName;
                                        commentWorksheet.Cells[commentTabLine, 1].Style.Font.Size = fontSize;
                                        commentWorksheet.Cells[commentTabLine, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
                                        commentWorksheet.Cells[commentTabLine, 1].Style.VerticalAlignment = ExcelVerticalAlignment.Bottom;

                                        commentWorksheet.Cells[commentTabLine, 2].Value = questionComment.User.name;
                                        commentWorksheet.Cells[commentTabLine, 2].Style.Font.Name = fontName;
                                        commentWorksheet.Cells[commentTabLine, 2].Style.Font.Size = fontSize;
                                        commentWorksheet.Cells[commentTabLine, 2].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
                                        commentWorksheet.Cells[commentTabLine, 2].Style.VerticalAlignment = ExcelVerticalAlignment.Bottom;

                                        commentWorksheet.Cells[commentTabLine, 3].Value = questionComment.comment;
                                        commentWorksheet.Cells[commentTabLine, 3].Style.Font.Name = fontName;
                                        commentWorksheet.Cells[commentTabLine, 3].Style.Font.Size = fontSize;
                                        commentWorksheet.Cells[commentTabLine, 3].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
                                        commentWorksheet.Cells[commentTabLine, 3].Style.VerticalAlignment = ExcelVerticalAlignment.Bottom;
                                        commentWorksheet.Cells[commentTabLine, 4].Value = "";
                                    }
                                    commentTabLine++;
                                }
                            }
                        }

                        previousSection = question.sectionOrder;
                    }


                    reportWorksheet.Cells[reportWorksheet.Dimension.Address].AutoFitColumns(10, 65);
                    if (commentWorksheet.Dimension?.Address != null)
                    {
                        commentWorksheet.Cells[commentWorksheet.Dimension.Address].AutoFitColumns(10, 65);
                    }

                    package.Save();

                    // Uncomment to perform local tests
                    // var filename = Environment.ExpandEnvironmentVariables($"%TEMP%\\form-consolidated-report-{DateTimeOffset.UtcNow.ToUnixTimeSeconds()}.xlsx");
                    // package.SaveAs(new FileInfo(filename));
                }
                return stream.ToArray();
            }
        }

        private void AddDetailedFormReportData(ExcelWorksheet worksheet, DetailedReportViewModel detailedReportViewModel, Dictionary<string, string> questionCommentsDict)
        {
            bool isAnonymous = detailedReportViewModel.AnonymousAnswer;
            string respondentText = i18n.t("export:form.detailed.respondent");
            var otherOption = i18n.t("export:form.otherOption");

            // Header
            int col = 1;
            if (isAnonymous)
            {
                worksheet.Cells[1, col++].Value = respondentText + "s";
            }
            else
            {
                worksheet.Cells[1, col++].Value = "ID";
                worksheet.Cells[1, col++].Value = respondentText;
            }

            foreach (var question in detailedReportViewModel.Questions)
            {
                var questionOptions = question.FormQuestionOptions;

                worksheet.Cells[1, col++].Value = question.title;
                worksheet.Cells[1, col++].Value = questionOptions.Any(qo => qo.requiredJustification) ? i18n.t("export:form.detailed.justification")
                                                                                 :
                                                                                 i18n.t("export:form.detailed.comment");
            }

            // Content
            int row = 2;
            foreach (var detailedReportRespondent in detailedReportViewModel.Respondents)
            {
                col = 1;
                if (isAnonymous)
                {
                    worksheet.Cells[row, col++].Value = $"{respondentText} {row - 1}";
                }
                else
                {
                    worksheet.Cells[row, col++].Value = detailedReportRespondent.UserId;
                    worksheet.Cells[row, col++].Value = detailedReportRespondent.Name;
                }

                foreach (var answerViewModel in detailedReportRespondent.Answers)
                {
                    if (!string.IsNullOrWhiteSpace(answerViewModel.answer))
                        answerViewModel.answer = answerViewModel.answer.Trim() == "form:tabQuestions.option.multipleChoice.other" ? otherOption : answerViewModel.answer.Trim();
                    
                    worksheet.Cells[row, col].Value = answerViewModel.answer;

                    string key = $"{detailedReportRespondent.UserId}:{answerViewModel.formQuestionId}";

                    questionCommentsDict.TryGetValue(key, out string comment);
                    worksheet.Cells[row, col + 1].Value = comment;

                    col += 2;
                }
                row++;
            }

            // Apply styles
            worksheet.Cells[1, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            worksheet.Cells[1, 1, 1, col].Style.Font.Bold = true;
            worksheet.Cells[1, 1, row, 1].Style.Font.Bold = true;

            worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
        }

        private async Task<int> addNewKbDataAsync(ExcelWorksheet worksheet, ExportDataViewModel exportConfig, int currentRow = 1)
        {
            string sheetName = i18n.t("export:knowledgeBase");

            if (currentRow == 1)
            {
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 1].Value = i18n.t("export:workgroupId");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 2].Value = i18n.t("export:workgroup");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 3].Value = i18n.t("export:newKB.folderId");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 4].Value = i18n.t("export:newKB.folderCreateDate");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 5].Value = i18n.t("export:newKB.folderCreateUser");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 6].Value = i18n.t("export:newKB.fileName");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 7].Value = i18n.t("export:newKB.fileCreateDate");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 8].Value = i18n.t("export:newKB.parentFolder");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 9].Value = i18n.t("export:url");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 1, currentRow, 9].Style.Font.Bold = true;
            }

            AtlasModelCore model = new AtlasModelCore();
            KnowledgeBaseRepository repo = new KnowledgeBaseRepository();

            var rowList = new List<NewKBRowModel>();

            int y = currentRow;
            var directoryTree = repo.getContentTreeByWorkgroup(exportConfig.workgroups);
            foreach (var directory in directoryTree)
            {
                var workgroup = await model.Workgroup.FindAsync(directory.workgroupId);

                if (workgroup.clientId != exportConfig.clientId)
                    continue;

                var content = await model.Content.SingleOrDefaultAsync(f => f.contentUuid == directory.contentUuid);
                if (content == null)
                {
                    continue;
                }

                worksheet.Cells[++y, 4].Style.Numberformat.Format = i18n.t("export:dateTimeFormat");
                worksheet.Cells[y, 7].Style.Numberformat.Format = i18n.t("export:dateTimeFormat");

                var folderViewModel = new ItemViewModel()
                {
                    Id = content.contentId,
                    Entity = "knowledgeDirectory",
                };
                var folderGuid = folderViewModel.Guid;

                var row = new NewKBRowModel();
                row.workgroupId = content.workgroupId;
                row.workgroupName = content.Workgroup.name;
                row.folderId = content.contentId;
                row.folderCreateDate = TimeZoneInfo.ConvertTimeFromUtc(content.createDate, getUserTimeZone(content));
                row.folderCreateUser = content.createUser;
                row.parentFolder = content.parentContentId;
                row.fileName = content.title;
                row.fileCreateDate = null;
                row.url = $"{_environmentUrl}/knowledge/folderId/{folderGuid}";

                rowList.Add(row);

                var attachmentList = await (
                    from ca in model.ContentAttachment
                    join a in model.Attachment on ca.attachmentId equals a.attachmentId
                    where ca.contentId == content.contentId && ca.deleted != true
                    orderby a.attachmentId
                    select a
                    ).ToListAsync();

                foreach (var attachment in attachmentList)
                {
                    worksheet.Cells[++y, 4].Style.Numberformat.Format = i18n.t("export:dateTimeFormat");
                    worksheet.Cells[y, 7].Style.Numberformat.Format = i18n.t("export:dateTimeFormat");

                    row = new NewKBRowModel()
                    {
                        workgroupId = content.workgroupId,
                        workgroupName = content.Workgroup.name,
                        folderId = content.contentId,
                        folderCreateDate = TimeZoneInfo.ConvertTimeFromUtc(content.createDate, getUserTimeZone(content)),
                        folderCreateUser = content.createUser,
                        fileName = attachment.fileName,
                        fileCreateDate = TimeZoneInfo.ConvertTimeFromUtc(attachment.createDate, getUserTimeZone(content)),
                        parentFolder = content.parentContentId,
                        url = $"{_environmentUrl}/knowledge/folderId/{folderGuid}"
                    };

                    rowList.Add(row);
                }
            }

            worksheet.Workbook.Worksheets[sheetName].Cells[currentRow + 1, 1].LoadFromCollection(rowList);
            worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

            currentRow += rowList.Count;

            return currentRow;
        }

        public async Task<byte[]> exportProjectToExcel(int[] workgroupListId)
        {
            AtlasModelCore _md = new AtlasModelCore();
            User usr = _md.User.Where(o => o.userId == _currentUser).FirstOrDefault();
            TimeZoneInfo userZone = getUserTimeZone(usr);
            string lang = (usr.defaultLanguage ?? "pt").ToLower();
            i18n.changeLanguage(lang);

            using (var stream = new MemoryStream())
            {
                using (ExcelPackage package = new ExcelPackage(stream))
                {
                    ExcelWorksheet projectWorksheet;
                    ExcelWorksheet signaturesWorksheet;
                    HtmlParser htmlParser = new HtmlParser();

                    projectWorksheet = package.Workbook.Worksheets.Add(i18n.t("export:projects"));
                    signaturesWorksheet = package.Workbook.Worksheets.Add(i18n.t("export:signatures"));

                    string projectsSheet = i18n.t("export:projects");
                    projectWorksheet.Workbook.Worksheets[projectsSheet].Cells["A1"].Value = i18n.t("export:projectName");
                    projectWorksheet.Workbook.Worksheets[projectsSheet].Cells["B1"].Value = i18n.t("export:taskId");
                    projectWorksheet.Workbook.Worksheets[projectsSheet].Cells["C1"].Value = i18n.t("export:taskName");
                    projectWorksheet.Workbook.Worksheets[projectsSheet].Cells["D1"].Value = i18n.t("export:assignedUser");
                    projectWorksheet.Workbook.Worksheets[projectsSheet].Cells["E1"].Value = i18n.t("export:dueDate");
                    projectWorksheet.Workbook.Worksheets[projectsSheet].Cells["F1"].Value = i18n.t("export:status");
                    projectWorksheet.Workbook.Worksheets[projectsSheet].Cells["G1"].Value = i18n.t("export:checklist");
                    projectWorksheet.Workbook.Worksheets[projectsSheet].Cells["H1"].Value = i18n.t("export:checklistCompletionPercentage");
                    projectWorksheet.Workbook.Worksheets[projectsSheet].Cells["I1"].Value = i18n.t("export:closeDate");
                    projectWorksheet.Workbook.Worksheets[projectsSheet].Cells["J1"].Value = i18n.t("export:createUser");
                    projectWorksheet.Workbook.Worksheets[projectsSheet].Cells["K1"].Value = i18n.t("export:startDate");
                    projectWorksheet.Workbook.Worksheets[projectsSheet].Cells["L1"].Value = i18n.t("export:column");
                    projectWorksheet.Workbook.Worksheets[projectsSheet].Cells["M1"].Value = i18n.t("export:url");
                    projectWorksheet.Workbook.Worksheets[projectsSheet].Cells["N1"].Value = i18n.t("export:subscribers");
                    projectWorksheet.Workbook.Worksheets[projectsSheet].Cells["O1"].Value = i18n.t("export:description");
                    projectWorksheet.Workbook.Worksheets[projectsSheet].Cells["P1"].Value = i18n.t("export:comments");
                    projectWorksheet.Workbook.Worksheets[projectsSheet].Cells["A1:P1"].Style.Font.Bold = true;

                    var _content_repo = new ContentService(_currentUser);

                    var filter = new ContentRequestFilter()
                    {
                        workgroups = workgroupListId,
                        type = "Task",
                        detailed = true
                    };

                    var tasks = (await _content_repo.GetAll(filter)).Cast<TaskListViewModel>().ToList();
                    tasks = tasks.OrderBy(x => x.workgroupName).ThenBy(x => x.taskId).ToList();

                    HashSet<int> tasksContentIds = new HashSet<int>(tasks.Select(t => t.contentId));

                    var taskChecklistItems = _md.TaskCheckListItem.Where(c => tasksContentIds.Contains(c.contentId))
                                                          .GroupBy(c => c.contentId)
                                                          .ToDictionary(g => g.Key, g => string.Join(";", g.Select(x => x.name)));

                    for (int j = 0; j < tasks.Count; j++)
                    {
                        var item = tasks[j];
                        item.taskChecklistItems = taskChecklistItems.ContainsKey(item.contentId) ? taskChecklistItems[item.contentId] : "";
                    }

                    var tasksComments = _md.ContentComment.Where(cc => tasksContentIds.Contains(cc.contentId) && cc.deleted != true).ToArray();

                    int i = 1;

                    foreach (TaskListViewModel task in tasks)
                    {
                        projectWorksheet.Cells[(i + 1), 5].Style.Numberformat.Format = i18n.t("export:task.dateTimeFormat");
                        projectWorksheet.Cells[(i + 1), 8].Style.Numberformat.Format = "#0.00%";
                        projectWorksheet.Cells[(i + 1), 9].Style.Numberformat.Format = i18n.t("export:task.dateTimeFormat");
                        projectWorksheet.Cells[(i + 1), 11].Style.Numberformat.Format = i18n.t("export:task.dateTimeFormat");
                        projectWorksheet.Cells[(i + 1), 16].Style.WrapText = true;

                        projectWorksheet.Workbook.Worksheets[projectsSheet].Cells[(i + 1), 1].Value = task.workgroupName;
                        projectWorksheet.Workbook.Worksheets[projectsSheet].Cells[(i + 1), 2].Value = task.contentId;
                        projectWorksheet.Workbook.Worksheets[projectsSheet].Cells[(i + 1), 3].Value = task.title;
                        projectWorksheet.Workbook.Worksheets[projectsSheet].Cells[(i + 1), 4].Value = task.assignedUserName;
                        projectWorksheet.Workbook.Worksheets[projectsSheet].Cells[(i + 1), 5].Value = TimeZoneInfo.ConvertTimeFromUtc(task.dueDate, userZone);
                        projectWorksheet.Workbook.Worksheets[projectsSheet].Cells[(i + 1), 6].Value = getTaskStatus(task.status);
                        projectWorksheet.Workbook.Worksheets[projectsSheet].Cells[(i + 1), 7].Value = task.taskChecklistItems;
                        projectWorksheet.Workbook.Worksheets[projectsSheet].Cells[(i + 1), 8].Value = task.checklistOverallFinished;

                        if (task.closeDate != null)
                            projectWorksheet.Workbook.Worksheets[projectsSheet].Cells[(i + 1), 9].Value = TimeZoneInfo.ConvertTimeFromUtc((DateTime)task.closeDate, userZone);

                        projectWorksheet.Workbook.Worksheets[projectsSheet].Cells[(i + 1), 10].Value = task.createUserName;

                        if (task.startDate != null)
                            projectWorksheet.Workbook.Worksheets[projectsSheet].Cells[(i + 1), 11].Value = TimeZoneInfo.ConvertTimeFromUtc((DateTime)task.startDate, userZone);

                        projectWorksheet.Workbook.Worksheets[projectsSheet].Cells[(i + 1), 12].Value = getTaskColumnName(task.columnName);
                        projectWorksheet.Workbook.Worksheets[projectsSheet].Cells[(i + 1), 13].Value = $"{_environmentUrl}/project/{task.workgroupId}/action/{task.contentId}/";
                        projectWorksheet.Workbook.Worksheets[projectsSheet].Cells[(i + 1), 14].Value = string.Join(", ", task.subscribers.Select(subs => subs.name));
                        projectWorksheet.Workbook.Worksheets[projectsSheet].Cells[(i + 1), 15].Value = $"{task.description}";

                        var allComments = tasksComments.Where(c => c.contentId == task.contentId).OrderBy(c => c.date).Select(c =>
                        {
                            var mentions = Regex.Replace(c.text, "<span[^>]*class=\"mention\"[^>]*>", "<span> @");
                            var onlyBodyFromHtml = htmlParser.ParseDocument(mentions);
                            var commentType = c.parentCommentId == null ? i18n.t("export:comment").ToLower() : i18n.t("export:answer").ToLower();
                            return $"{commentType}[{c.contentCommentId}]: {onlyBodyFromHtml.DocumentElement.TextContent}";
                        });

                        var lineBreak = "" + ((char)13) + ((char)10);
                        projectWorksheet.Workbook.Worksheets[projectsSheet].Cells[(i + 1), 16].Value = string.Join(lineBreak, allComments);


                        i++;
                    }

                    this.addSignaturesData(signaturesWorksheet, workgroupListId, usr);

                    projectWorksheet.Cells[projectWorksheet.Dimension.Address].AutoFitColumns();
                    signaturesWorksheet.Cells[signaturesWorksheet.Dimension.Address].AutoFitColumns();

                    double columnWidth = 170;
                    projectWorksheet.Workbook.Worksheets[projectsSheet].Column(16).Width = columnWidth;

                    package.Save();

                    var clientIdList = _md.Workgroup
                        .Where(w => workgroupListId.Contains(w.workgroupId))
                        .Select(w => w.clientId)
                        .Distinct();

                    foreach (var clientId in clientIdList)
                    {
                        ActivityService.Add("DATA_PROJECT_EXPORTED", clientId, null, _currentUser, $"\"projects\": [{string.Join(",", workgroupListId)}]");
                    }

                    //For local testing
                    //package.SaveAs(new FileInfo(@"c:\workbooks\myworkbook.xlsx"));
                    return stream.ToArray();

                }
            }

        }

        private void addKbData(ExcelWorksheet worksheet, ExportDataViewModel exportConfig, int currentRow = 1)
        {
            ContentRequestFilter filter = new ContentRequestFilter();
            filter.workgroups = exportConfig.workgroups;

            KnowledgeBaseService kbService = new KnowledgeBaseService(exportConfig.userId);

            string sheetName = i18n.t("export:knowledgeBase");

            if (currentRow == 1)
            {
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 1].Value = i18n.t("export:workgroupId");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 2].Value = i18n.t("export:categoryId");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 3].Value = i18n.t("export:categoryCreateDate");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 4].Value = i18n.t("export:categoryCreator");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 5].Value = i18n.t("export:articleId");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 6].Value = i18n.t("export:articleCreateDate");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 7].Value = i18n.t("export:articleCreator");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 8].Value = i18n.t("export:documentsCount");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 9].Value = i18n.t("export:url");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 1, currentRow, 9].Style.Font.Bold = true;
            }

            List<KBRowModel> kbRows = new List<KBRowModel>();
            var contents = kbService.ListWithArticles(filter);

            int i = currentRow;
            foreach (KnowledgeBaseViewModel kbvm in contents)
            {
                foreach (KnowledgeCategoryViewModel kcvm in kbvm.categories)
                {
                    if (!kcvm.Articles.Any())
                    {
                        KBRowModel kbRow = new KBRowModel();
                        kbRow.boardId = kbvm.workgroupId;
                        kbRow.categoryId = kcvm.kbCategoryId;

                        worksheet.Cells[(i + 1), 3].Style.Numberformat.Format = i18n.t("export:dateTimeFormat");
                        if (kcvm.createUser.HasValue && kcvm.createDate.HasValue)
                        {

                            AtlasModelCore _md = new AtlasModelCore();
                            var categoryCreator = _md.User.Find(kcvm.createUser);
                            TimeZoneInfo userZone = getUserTimeZone(categoryCreator);

                            kbRow.categoryCreationDate = TimeZoneInfo.ConvertTimeFromUtc(kcvm.createDate.Value, userZone);
                            kbRow.categoryCreatorId = kcvm.createUser;
                        }

                        kbRow.url = $"{_environmentUrl}/knowledge/{kbvm.workgroupId}/category/{kcvm.kbCategoryId}";

                        kbRows.Add(kbRow);
                        i++;
                        continue;
                    }

                    foreach (KnowledgeArticle ka in kcvm.Articles)
                    {
                        AtlasModelCore _md = new AtlasModelCore();

                        KBRowModel kbRow = new KBRowModel();
                        kbRow.boardId = kbvm.workgroupId;
                        kbRow.categoryId = kcvm.kbCategoryId;

                        worksheet.Cells[(i + 1), 3].Style.Numberformat.Format = i18n.t("export:dateTimeFormat");
                        if (kcvm.createUser.HasValue && kcvm.createDate.HasValue)
                        {
                            var categoryCreator = _md.User.Find(kcvm.createUser);
                            TimeZoneInfo timeZone = getUserTimeZone(categoryCreator);

                            kbRow.categoryCreationDate = TimeZoneInfo.ConvertTimeFromUtc(kcvm.createDate.Value, timeZone);
                            kbRow.categoryCreatorId = kcvm.createUser;
                        }

                        var content = _md.Content.Where(c => c.contentId == ka.Content.contentId).FirstOrDefault();
                        TimeZoneInfo userZone = getUserTimeZone(content);
                        worksheet.Cells[(i + 1), 6].Style.Numberformat.Format = i18n.t("export:dateTimeFormat");

                        kbRow.articleId = ka.Content.contentId;
                        kbRow.articleCreationDate = TimeZoneInfo.ConvertTimeFromUtc(ka.Content.createDate, userZone);
                        kbRow.articleCreatorId = ka.Content.createUser;
                        kbRow.attachments = ka.Content.ContentAttachment.Count;

                        kbRow.url = $"{_environmentUrl}/knowledge/{kbvm.workgroupId}/article/{ka.Content.contentId}";
                        kbRows.Add(kbRow);
                        i++;

                        //if (ka.Content.ContentAttachment.Any())
                        //{
                        //    foreach (ContentAttachment ca in ka.Content.ContentAttachment)
                        //    {
                        //        if (ca.deleted == null || ca.deleted == false)
                        //        {
                        //            KBRowModel kbRow = new KBRowModel();


                        //            Attachment attachment = ca.Attachment;

                        //            kbRow.boardId = kbvm.workgroupId;
                        //            kbRow.categoryId = kcvm.kbCategoryId;
                        //            // kbRow.categoryName = kcvm.name;
                        //            kbRow.articleId = ka.contentId;

                        //            // kbRow.creationDate = TimeZoneInfo.ConvertTimeFromUtc(ka.Content.createDate, userZone);
                        //            worksheet.Cells[(i + 1), 5].Style.Numberformat.Format = i18n.t("export:dateTimeFormat");

                        //            // kbRow.articleName = ka.title;
                        //            // kbRow.creatorName = ka.Content.User_Create.name;
                        //            // kbRow.fileName = attachment.fileName;
                        //            // kbRow.uploadDate = TimeZoneInfo.ConvertTimeFromUtc(attachment.createDate, userZone);
                        //            worksheet.Cells[(i + 1), 9].Style.Numberformat.Format = i18n.t("export:dateTimeFormat");
                        //            // kbRow.fileUploaderName = attachment.User.name;
                        //            kbRows.Add(kbRow);
                        //            i++;
                        //        }
                        //    }
                        //}
                        //else
                        //{

                        //    KBRowModel kbRow = new KBRowModel();
                        //    kbRow.boardId = kbvm.workgroupId;
                        //    kbRow.categoryId = kcvm.kbCategoryId;
                        //    // kbRow.categoryName = kcvm.name;
                        //    kbRow.articleId = ka.contentId;
                        //    // kbRow.creationDate = ka.Content.createDate;
                        //    worksheet.Cells[(i + 1), 5].Style.Numberformat.Format = i18n.t("export:dateTimeFormat");

                        //    // kbRow.articleName = ka.title;
                        //    // kbRow.creatorName = ka.Content.User_Create.name;

                        //    kbRows.Add(kbRow);
                        //    i++;
                        //}
                    }
                }
            }


            // kbRows = kbRows.OrderBy(o => o.boardId).ToList();
            worksheet.Workbook.Worksheets[sheetName].Cells[currentRow + 1, 1].LoadFromCollection(kbRows);
            worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
        }

        private TimeZoneInfo getUserTimeZone(User user)
        {
            string db_fuso = "America/Sao_Paulo";
            if (user != null)
            {
                db_fuso = user.defaultTimezone ?? db_fuso;
            }


            string tz;
            try
            {
                tz = TZConvert.IanaToWindows(db_fuso);
            }
            catch (InvalidTimeZoneException)
            {
                tz = "E. South America Standard Time";
            }

            TimeZoneInfo timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(tz);
            return timeZoneInfo;
        }

        private async Task<int> addMeetingsData(ExcelWorksheet worksheet, ExportDataViewModel exportConfig, int currentRow = 1)
        {
            AtlasModelCore _md = new AtlasModelCore();
            //ContentService _svc = new ContentService(this._currentUser);
            var workgroups = new int[] { exportConfig.workgroupId };
            ContentRequestFilter filter = new ContentRequestFilter
            {
                workgroups = workgroups,
                type = ContentTypes.Meeting,
                detailed = true,
                //createDateMin = exportConfig.fromDate,
                //createDateMax = exportConfig.toDate
            };

            var meetingsContent = (await _svc.GetAll(filter)).Select(o => (MeetingViewModel)o)
                .Where(o => (exportConfig.fromDate == null || o.date >= exportConfig.fromDate)
                && (exportConfig.toDate == null || o.date <= exportConfig.toDate)).ToList();


            HashSet<int> contentIds = new HashSet<int>(meetingsContent.Select(i => i.contentId));

            var childMeetingMinutes = _md.Content
                .Where(c => contentIds.Contains((int)c.parentContentId) && c.deleted != true &&
                            c.type == ContentTypes.MeetingMinute && c.MeetingMinute.Any(m => m.published == true))
                .GroupBy(c => c.parentContentId)
                .ToDictionary(g => g.Key, g => g.OrderByDescending(c => c.createDate).First());

            for (int i = 0; i < meetingsContent.Count; i++)
            {
                var item = meetingsContent[i];
                item.publishedMeetingMinute = childMeetingMinutes.TryGetValue(item.contentId, out var childMeetingMinute);
            }

            string sheetName = i18n.t("export:meetings");

            if (currentRow == 1)
            {
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 1].Value = i18n.t("export:workgroupId");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 2].Value = i18n.t("export:workgroup");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 3].Value = "Id";
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 4].Value = i18n.t("export:title");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 5].Value = i18n.t("export:creationDate");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 6].Value = i18n.t("export:creationUserId");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 7].Value = i18n.t("export:creationUserName");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 8].Value = i18n.t("export:meetingDateTime");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 9].Value = i18n.t("export:status");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 10].Value = i18n.t("export:owners");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 11].Value = i18n.t("export:ownersNames");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 12].Value = i18n.t("export:attendees");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 13].Value = i18n.t("export:published");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 14].Value = i18n.t("export:url");

                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 1, currentRow, 14].Style.Font.Bold = true;
            }

            if (meetingsContent.Any())
            {
                List<MeetingRowModel> meetingRows = new List<MeetingRowModel>();
                int newCurrentRow = currentRow;
                foreach (MeetingViewModel m in meetingsContent)
                {
                    var content = _md.Content.Where(c => c.contentId == m.contentId).FirstOrDefault();
                    TimeZoneInfo userZone = getUserTimeZone(content);

                    worksheet.Cells[(newCurrentRow + 1), 5].Style.Numberformat.Format = i18n.t("export:dateTimeFormat");
                    worksheet.Cells[(newCurrentRow + 1), 8].Style.Numberformat.Format = i18n.t("export:dateTimeFormat");

                    MeetingRowModel row = new MeetingRowModel();
                    row.workgroupId = m.workgroupId;
                    row.workgroupName = m.workgroupName;
                    row.contentId = m.contentId;
                    row.title = string.IsNullOrEmpty(m.title) ? i18n.t("export:meeting") : m.title;
                    row.creationDate = TimeZoneInfo.ConvertTimeFromUtc(m.createDate, userZone);
                    row.creatorUserId = m.createUser;
                    row.creatorUserName = m.createUserName;
                    row.meetingDate = TimeZoneInfo.ConvertTimeFromUtc(m.date, userZone);
                    row.status = getMeetingStatus(m.status.ToLower());
                    row.owners = string.Join(",", m.owners.Select(o => o.userId));
                    row.ownersNames = string.Join(",", m.owners.Select(o => o.name));
                    row.attendees = string.Join(",", m.attendees.Select(a => a.userId));
                    row.meetingMinuteStatus = m.publishedMeetingMinute ? i18n.t("export:yes") : i18n.t("export:no");

                    row.url = $"{_environmentUrl}/meeting/details/{m.contentId}";

                    meetingRows.Add(row);
                    newCurrentRow++;
                }

                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow + 1, 1].LoadFromCollection(meetingRows);
                worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
                currentRow = newCurrentRow;
            }

            return currentRow;
        }

        private async Task<int> addProjectsData(ExcelWorksheet worksheet, ExportDataViewModel exportConfig, int currentRow = 1)
        {
            AtlasModelCore _md = new AtlasModelCore();

            //ContentService _svc = new ContentService(this._currentUser);
            var workgroups = new int[] { exportConfig.workgroupId };
            ContentRequestFilter filter = new ContentRequestFilter
            {
                workgroups = workgroups,
                type = ContentTypes.Task,
                createDateMin = exportConfig.fromDate,
                createDateMax = exportConfig.toDate,
                detailed = true,
            };

            var projectsContent = (await _svc.GetAll(filter)).Select(o => (TaskListViewModel)o).ToList();

            HashSet<int> contentIds = new HashSet<int>(projectsContent.Select(i => i.contentId));

            var taskChecklistItems = _md.TaskCheckListItem.Where(c => contentIds.Contains(c.contentId))
                                                          .GroupBy(c => c.contentId)
                                                          .ToDictionary(g => g.Key, g => string.Join(";", g.Select(x => x.name)));

            for (int i = 0; i < projectsContent.Count; i++)
            {
                var item = projectsContent[i];
                item.taskChecklistItems = taskChecklistItems.ContainsKey(item.contentId) ? taskChecklistItems[item.contentId] : "";
            }

            string sheetName = i18n.t("export:actions");

            if (currentRow == 1)
            {
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 1].Value = i18n.t("export:workgroupId");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 2].Value = i18n.t("export:workgroup");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 3].Value = "Id";
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 4].Value = i18n.t("export:taskName");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 5].Value = i18n.t("export:creationDate");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 6].Value = i18n.t("export:creationUserId");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 7].Value = i18n.t("export:creationUserName");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 8].Value = i18n.t("export:dueDate");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 9].Value = i18n.t("export:status");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 10].Value = i18n.t("export:assignedUserId");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 11].Value = i18n.t("export:assignedUserName");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 12].Value = i18n.t("export:checklist");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 13].Value = i18n.t("export:checklistCompletionPercentage");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 14].Value = i18n.t("export:closeDate");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 15].Value = i18n.t("export:meetingId");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 16].Value = i18n.t("export:meetingTitle");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 17].Value = i18n.t("export:subscribersAction");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 18].Value = i18n.t("export:subscribersActionNames");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 19].Value = i18n.t("export:url");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 1, currentRow, 19].Style.Font.Bold = true;
            }


            if (projectsContent.Any())
            {
                List<TaskRowModel> taskRows = new List<TaskRowModel>();
                int newCurrentRow = currentRow;
                foreach (TaskListViewModel t in projectsContent)
                {
                    var content = _md.Content.Where(c => c.contentId == t.contentId).FirstOrDefault();
                    TimeZoneInfo userZone = getUserTimeZone(content);

                    worksheet.Cells[(newCurrentRow + 1), 5].Style.Numberformat.Format = i18n.t("export:dateTimeFormat");
                    worksheet.Cells[(newCurrentRow + 1), 8].Style.Numberformat.Format = i18n.t("export:dateTimeFormat");
                    worksheet.Cells[(newCurrentRow + 1), 13].Style.Numberformat.Format = "#0.00%";

                    TaskRowModel row = new TaskRowModel();
                    row.workgroupId = t.workgroupId;
                    row.workgroupName = t.workgroupName;
                    row.contentId = t.contentId;
                    row.taskName = t.title;
                    row.creationDate = TimeZoneInfo.ConvertTimeFromUtc(t.createDate, userZone);
                    row.creatorUserId = t.createUser;
                    row.creatorUserName = t.createUserName;
                    row.dueDate = TimeZoneInfo.ConvertTimeFromUtc(t.dueDate, userZone);
                    row.status = getStatus(t.status.ToLower());
                    row.assignedUserId = t.assignedUser;
                    row.assignedUserName = t.assignedUserName;
                    row.checklist = t.taskChecklistItems;
                    row.checklistOverallFinished = t.checklistOverallFinished;

                    if (t.status == "CLOSED")
                    {
                        var activityClosedTask = _md.ContentActivity.Where(c => c.contentId == t.contentId && c.type == "STATUS_CLOSED").OrderByDescending(o => o.contentActivityId).FirstOrDefault();
                        row.closeDate = TimeZoneInfo.ConvertTimeFromUtc(activityClosedTask.date, userZone);

                        worksheet.Cells[(newCurrentRow + 1), 12].Style.Numberformat.Format = i18n.t("export:dateTimeFormat");
                    }

                    row.meetingId = t.meetingId;
                    row.meetingTitle = t.meetingTitle ?? "";
                    row.subscribers = string.Join(",", t.subscribers.Select(u => u.userId));
                    row.subscribersActionNames = string.Join(";", t.subscribers.Select(u => u.name));
                    row.url = $"{_environmentUrl}/inbox/action/{t.contentId}";

                    taskRows.Add(row);
                    newCurrentRow++;
                }

                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow + 1, 1].LoadFromCollection(taskRows);
                worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
                currentRow = newCurrentRow;
            }

            return currentRow;
        }

        private int addSignaturesData(ExcelWorksheet worksheet, ExportDataViewModel exportConfig, User usr, int currentRow = 1)
        {
            AtlasModelCore _md = new AtlasModelCore();

            var contentSignerVM = (from csr in _md.ContentSignatureRequest
                                   join cs in _md.ContentSigner on csr.contentSignatureRequestId equals cs.contentSignatureRequestId
                                   // LEFT JOIN
                                   join csig in _md.ContentSignature on new { csr.contentSignatureRequestId, cs.contentSignerId } equals new { csig.contentSignatureRequestId, csig.contentSignerId }
                                   into leftJoin
                                   from csig in leftJoin.DefaultIfEmpty()

                                   join c in _md.Content on cs.contentId equals c.contentId
                                   join requester in _md.User on csr.requesterUserId equals requester.userId

                                   // LEFT JOIN
                                   join signer in _md.User on cs.userId equals signer.userId
                                   into userLeftJoin
                                   from signer in userLeftJoin.DefaultIfEmpty()

                                   where c.workgroupId == exportConfig.workgroupId
                                       && (exportConfig.fromDate == null || csr.requestDate >= exportConfig.fromDate)
                                       && (exportConfig.toDate == null || csr.requestDate <= exportConfig.toDate)
                                       && (csr.status == "CLOSED" || csr.status == "OPEN" || csr.status == "REJECTED")

                                   select new ExportSignaturesViewModel
                                   {
                                       ContentSignatureRequestId = csr.contentSignatureRequestId,
                                       RequesterUserId = csr.requesterUserId,
                                       RequesterName = requester.name,
                                       RequestDate = csr.requestDate,
                                       SignerId = cs.userId,
                                       SignerName = signer.name ?? cs.externalName,
                                       SignDate = csig.signDate,
                                       SignatureRequestStatus = csr.status,
                                       ContentType = c.type,
                                       ContentId = c.contentId,
                                       ContentParentId = c.parentContentId,
                                       ContentTitle = c.title,
                                       SignatureType = csr.signatureType,
                                       ExternalKey = cs.externalKey,
                                       ContentSignerId = cs.contentSignerId.ToString(),
                                       WorkgroupId = c.workgroupId,
                                       Workgroup = c.Workgroup.name
                                   });

            var signaturesData = contentSignerVM.OrderBy(signature => signature.ContentSignatureRequestId).ToList();

            currentRow = prepareSignaturesData(worksheet, signaturesData, usr, currentRow);

            return currentRow;
        }

        private void addSignaturesData(ExcelWorksheet worksheet, int[] workgroupListId, User usr, int currentRow = 1)
        {
            AtlasModelCore _md = new AtlasModelCore();

            var contentSignerVM = (from csr in _md.ContentSignatureRequest
                                   join cs in _md.ContentSigner on csr.contentSignatureRequestId equals cs.contentSignatureRequestId
                                   // LEFT JOIN
                                   join csig in _md.ContentSignature on new { csr.contentSignatureRequestId, cs.contentSignerId } equals new { csig.contentSignatureRequestId, csig.contentSignerId }
                                   into leftJoin
                                   from csig in leftJoin.DefaultIfEmpty()

                                   join c in _md.Content on cs.contentId equals c.contentId
                                   join wk in _md.Workgroup on c.workgroupId equals wk.workgroupId
                                   join requester in _md.User on csr.requesterUserId equals requester.userId

                                   // LEFT JOIN
                                   join signer in _md.User on cs.userId equals signer.userId
                                   into userLeftJoin
                                   from signer in userLeftJoin.DefaultIfEmpty()

                                   where wk.type == "PROJECT" && workgroupListId.Contains(wk.workgroupId)
                                       && (csr.status == "CLOSED" || csr.status == "OPEN" || csr.status == "REJECTED")

                                   select new ExportSignaturesViewModel
                                   {
                                       ContentSignatureRequestId = csr.contentSignatureRequestId,
                                       RequesterUserId = csr.requesterUserId,
                                       RequesterName = requester.name,
                                       RequestDate = csr.requestDate,
                                       SignerId = cs.userId,
                                       SignerName = signer.name ?? cs.externalName,
                                       SignDate = csig.signDate,
                                       SignatureRequestStatus = csr.status,
                                       ContentType = c.type,
                                       ContentId = c.contentId,
                                       ContentParentId = c.parentContentId,
                                       ContentTitle = c.title,
                                       SignatureType = csr.signatureType,
                                       ExternalKey = cs.externalKey,
                                       ContentSignerId = cs.contentSignerId.ToString(),
                                       WorkgroupId = wk.workgroupId,
                                       Workgroup = wk.name
                                   });

            var signaturesData = contentSignerVM.OrderBy(signature => signature.ContentSignatureRequestId).ToList();

            prepareSignaturesData(worksheet, signaturesData, usr, currentRow);
        }

        private int prepareSignaturesData(ExcelWorksheet worksheet, List<ExportSignaturesViewModel> signaturesData, User usr, int currentRow = 1)
        {
            AtlasModelCore _md = new AtlasModelCore();

            var meetingIdList = signaturesData
                .Where(c => (c.ContentType == ContentTypes.MeetingMinute || c.ContentType == ContentTypes.Poll) && c.ContentParentId != null)
                .Select(id => id.ContentParentId);

            List<Tuple<int, string>> meetingTitleList = _md.Content
                .Where(meeting => meetingIdList.Contains(meeting.contentId))
                .Select(content => new { content.contentId, content.title })
                .AsEnumerable()
                .Select(asTuple => new Tuple<int, string>(asTuple.contentId, asTuple.title))
                .ToList();


            foreach (var sigData in signaturesData)
            {
                var meetingTitle = meetingTitleList.Where(tuple => tuple.Item1 == sigData.ContentParentId).Select(tuple => tuple.Item2).DefaultIfEmpty("").First();
                sigData.ContentTitle = meetingTitle;
            }


            string signaturesSheet = i18n.t("export:signatures");

            if (currentRow == 1)
            {
                worksheet.Workbook.Worksheets[signaturesSheet].Cells[currentRow, 1].Value = i18n.t("export:workgroupId");
                worksheet.Workbook.Worksheets[signaturesSheet].Cells[currentRow, 2].Value = i18n.t("export:workgroup");
                worksheet.Workbook.Worksheets[signaturesSheet].Cells[currentRow, 3].Value = i18n.t("export:signature.requestId");
                worksheet.Workbook.Worksheets[signaturesSheet].Cells[currentRow, 4].Value = i18n.t("export:signature.requestUserId");
                worksheet.Workbook.Worksheets[signaturesSheet].Cells[currentRow, 5].Value = i18n.t("export:signature.requesterName");
                worksheet.Workbook.Worksheets[signaturesSheet].Cells[currentRow, 6].Value = i18n.t("export:signature.numberOfSigners");
                worksheet.Workbook.Worksheets[signaturesSheet].Cells[currentRow, 7].Value = i18n.t("export:signature.requestDate");
                worksheet.Workbook.Worksheets[signaturesSheet].Cells[currentRow, 8].Value = i18n.t("export:signature.signerId");
                worksheet.Workbook.Worksheets[signaturesSheet].Cells[currentRow, 9].Value = i18n.t("export:signature.signerName");
                worksheet.Workbook.Worksheets[signaturesSheet].Cells[currentRow, 10].Value = i18n.t("export:signature.signDate");
                worksheet.Workbook.Worksheets[signaturesSheet].Cells[currentRow, 11].Value = i18n.t("export:signature.requestStatus");
                worksheet.Workbook.Worksheets[signaturesSheet].Cells[currentRow, 12].Value = i18n.t("export:signature.requestContentType");
                worksheet.Workbook.Worksheets[signaturesSheet].Cells[currentRow, 13].Value = i18n.t("export:signature.contentId");
                worksheet.Workbook.Worksheets[signaturesSheet].Cells[currentRow, 14].Value = i18n.t("export:meetingTitle");
                worksheet.Workbook.Worksheets[signaturesSheet].Cells[currentRow, 15].Value = i18n.t("export:signature.type");
                worksheet.Workbook.Worksheets[signaturesSheet].Cells[currentRow, 16].Value = i18n.t("export:signature.externalSigner");
                worksheet.Workbook.Worksheets[signaturesSheet].Cells[currentRow, 1, currentRow, 16].Style.Font.Bold = true;
            }


            if (signaturesData.Any())
            {
                currentRow = fillSheet(worksheet, signaturesSheet, signaturesData, usr, currentRow);
            }

            worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

            return currentRow;
        }

        private int fillSheet(ExcelWorksheet worksheet, string signaturesSheet, List<ExportSignaturesViewModel> signatureData, User usr, int currentRow = 1)
        {
            int lineIndex = currentRow;
            var userZone = getUserTimeZone(usr);

            foreach (var data in signatureData)
            {
                int qtySigners = signatureData.Count(sig => sig.ContentSignatureRequestId == data.ContentSignatureRequestId);
                string signerName = data.SignerName;

                bool isExternalSigner = !data.SignerId.HasValue && data.ExternalKey.HasValue;

                worksheet.Workbook.Worksheets[signaturesSheet].Cells[lineIndex + 1, 1].Value = data.WorkgroupId;
                worksheet.Workbook.Worksheets[signaturesSheet].Cells[lineIndex + 1, 2].Value = data.Workgroup;
                worksheet.Workbook.Worksheets[signaturesSheet].Cells[lineIndex + 1, 3].Value = data.ContentSignatureRequestId;
                worksheet.Workbook.Worksheets[signaturesSheet].Cells[lineIndex + 1, 4].Value = data.RequesterUserId;
                worksheet.Workbook.Worksheets[signaturesSheet].Cells[lineIndex + 1, 5].Value = data.RequesterName;
                worksheet.Workbook.Worksheets[signaturesSheet].Cells[lineIndex + 1, 6].Value = qtySigners;

                worksheet.Cells[(lineIndex + 1), 7].Style.Numberformat.Format = i18n.t("export:dateTimeFormat");
                worksheet.Workbook.Worksheets[signaturesSheet].Cells[lineIndex + 1, 7].Value = TimeZoneInfo.ConvertTimeFromUtc(data.RequestDate, userZone);

                if (isExternalSigner)
                {
                    worksheet.Workbook.Worksheets[signaturesSheet].Cells[lineIndex + 1, 8].Value = data.ContentSignerId + " (ext)";
                    worksheet.Workbook.Worksheets[signaturesSheet].Cells[lineIndex + 1, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                }
                else
                {
                    worksheet.Workbook.Worksheets[signaturesSheet].Cells[lineIndex + 1, 8].Value = data.SignerId;
                }

                worksheet.Workbook.Worksheets[signaturesSheet].Cells[lineIndex + 1, 9].Value = signerName;

                if (data.SignDate != null)
                {
                    worksheet.Cells[(lineIndex + 1), 10].Style.Numberformat.Format = i18n.t("export:dateTimeFormat");
                    worksheet.Workbook.Worksheets[signaturesSheet].Cells[lineIndex + 1, 10].Value = TimeZoneInfo.ConvertTimeFromUtc((DateTime)data.SignDate, userZone);
                }

                worksheet.Workbook.Worksheets[signaturesSheet].Cells[lineIndex + 1, 11].Value = getSignatureStatus(data.SignatureRequestStatus);
                worksheet.Workbook.Worksheets[signaturesSheet].Cells[lineIndex + 1, 12].Value = getSignatureContentType(data.ContentType);

                worksheet.Workbook.Worksheets[signaturesSheet].Cells[lineIndex + 1, 13].Value = data.ContentId;
                if (data.ContentType == ContentTypes.MeetingMinute || data.ContentType == ContentTypes.Poll)
                    worksheet.Workbook.Worksheets[signaturesSheet].Cells[lineIndex + 1, 13].Value = data.ContentParentId;

                worksheet.Workbook.Worksheets[signaturesSheet].Cells[lineIndex + 1, 14].Value = data.ContentTitle;
                worksheet.Workbook.Worksheets[signaturesSheet].Cells[lineIndex + 1, 15].Value = getSignatureType(data.SignatureType ?? "electronic");
                worksheet.Workbook.Worksheets[signaturesSheet].Cells[lineIndex + 1, 16].Value = isExternalSigner ? i18n.t("export:yes") : i18n.t("export:no");

                lineIndex++;
            }

            currentRow = lineIndex;

            return currentRow;
        }

        private int addPollsData(ExcelWorksheet worksheet, ExportDataViewModel exportConfig, int currentRow = 1)
        {

            PollService pollService = new PollService(this._currentUser);

            AtlasModelCore _md = new AtlasModelCore();
            List<Content> pollsContent = _md.Content.Where(c => c.type == ContentTypes.Poll &&
                                                           c.workgroupId == exportConfig.workgroupId &&
                                                           (exportConfig.fromDate == null || c.createDate >= exportConfig.fromDate) &&
                                                           (exportConfig.toDate == null || c.createDate <= exportConfig.toDate) &&
                                                           (c.deleted ?? false) == false &&
                                                           c.ContentPermission.Select(o => o.userId).Contains(_currentUser))
                                                            .Include(c => c.Poll)
                                                            .Include(c => c.Workgroup)
                                                            .Include(cs => cs.ContentSubscriber.Select(u => u.User))
                                                            .ToList();

            HashSet<int> parentContentIds = new HashSet<int>(pollsContent.Select(pc => pc.parentContentId.Value));

            var parentContent = _md.Content
                .Where(c => parentContentIds.Contains(c.contentId) && c.deleted != true &&
                            c.type == ContentTypes.Meeting)
                .GroupBy(c => c.contentId)
                .ToDictionary(g => g.Key, g => g.Select(c => c.title).FirstOrDefault());

            for (int i = 0; i < pollsContent.Count; i++)
            {
                var item = pollsContent[i];
                item.parentTitle = parentContent.TryGetValue(item.parentContentId.Value, out var parentContentTitle) ? parentContentTitle : "";
            }


            foreach (Content content in pollsContent)
            {
                Poll poll = content.Poll.FirstOrDefault();

                var pollVotes = _md.PollVote.Where(pv => pv.pollId == poll.pollId).ToList();
                poll.Votes = pollVotes;

                var pollOptions = _md.PollOption.Where(po => po.pollId == poll.pollId).ToList();
                poll.Options = pollOptions;
            }

            string sheetName = i18n.t("export:polls");

            if (currentRow == 1)
            {
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 1].Value = i18n.t("export:workgroupId");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 2].Value = i18n.t("export:workgroup");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 3].Value = "Id";
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 4].Value = i18n.t("export:pollName");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 5].Value = i18n.t("export:creationDate");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 6].Value = i18n.t("export:dueDate");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 7].Value = i18n.t("export:status");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 8].Value = i18n.t("export:closeDate");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 9].Value = i18n.t("export:votes");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 10].Value = i18n.t("export:totalVoters");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 11].Value = i18n.t("export:votersNames");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 12].Value = i18n.t("export:doneVoters");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 13].Value = i18n.t("export:doneVotersNames");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 14].Value = i18n.t("export:pendingVoters");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 15].Value = i18n.t("export:pendingVotersNames");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 16].Value = i18n.t("export:result");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 17].Value = i18n.t("export:meetingId");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 18].Value = i18n.t("export:meetingTitle");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 19].Value = i18n.t("export:url");
                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow, 1, currentRow, 19].Style.Font.Bold = true;
            }

            if (pollsContent.Any())
            {
                List<PollRowModel> pollRows = new List<PollRowModel>();
                int newCurrentRow = currentRow;
                foreach (Content content in pollsContent)
                {
                    TimeZoneInfo userZone = getUserTimeZone(content);
                    worksheet.Cells[(newCurrentRow + 1), 5].Style.Numberformat.Format = i18n.t("export:dateTimeFormat");
                    worksheet.Cells[(newCurrentRow + 1), 6].Style.Numberformat.Format = i18n.t("export:dateTimeFormat");
                    worksheet.Cells[(newCurrentRow + 1), 8].Style.Numberformat.Format = i18n.t("export:dateTimeFormat");

                    PollRowModel row = new PollRowModel();
                    Poll poll = content.Poll.FirstOrDefault();

                    row.workgroupId = content.workgroupId;
                    row.workgroupName = content.Workgroup.name;
                    row.contentId = poll.contentId;
                    row.pollName = poll.Content.title;
                    row.creationDate = TimeZoneInfo.ConvertTimeFromUtc(content.createDate, userZone);
                    row.dueDate = TimeZoneInfo.ConvertTimeFromUtc(poll.dueDate, userZone);

                    row.status = getStatus(content.status.ToLower());

                    string result = "";
                    if (content.status == "CLOSED")
                    {
                        var lastActivity = _md.ContentActivity
                            .Where(o => o.contentId == content.contentId && o.type == "STATUS_CLOSED")
                            .OrderByDescending(o => o.contentActivityId).FirstOrDefault();

                        row.finishDate = TimeZoneInfo.ConvertTimeFromUtc(lastActivity.date, userZone);

                        result = PollService.GetWinningOption(poll);

                        if (poll.pollType.Equals("APPROVAL"))
                        {
                            result = getPollResult(result.ToLower());
                        }
                        else if (poll.pollType.Equals("CUSTOM") && result.Equals("TIE"))
                        {
                            result = i18n.t("export:tie");
                        }
                    }

                    var usersHasVoted = poll.Votes.Where(v => !v.deleted.GetValueOrDefault()).Select(o => o.userId).ToList();
                    var voters = content.ContentSubscriber.ToDictionary(subscriber => subscriber.userId, subscriber => subscriber.User.name);
                    var pendingVoters = voters.Where(v => !usersHasVoted.Contains(v.Key)).Select(o => o.Key).ToList();
                    var pendingVotersNames = voters.Where(v => !usersHasVoted.Contains(v.Key)).Select(o => o.Value).ToArray();
                    var doneVotersNames = voters.Where(v => usersHasVoted.Contains(v.Key)).Select(o => o.Value).ToArray();

                    row.votes = usersHasVoted.Count;
                    row.totalVoters = voters.Count;
                    row.votersNames = string.Join(";", voters.Values);
                    row.doneVoters = string.Join(",", usersHasVoted);
                    row.doneVotersNames = string.Join(";", doneVotersNames);
                    row.pendingVoters = string.Join(",", pendingVoters);
                    row.pendingVotersNames = string.Join(";", pendingVotersNames);

                    row.result = result;
                    row.meetingId = content.parentContentId.Value;
                    row.meetingTitle = content.parentTitle;
                    row.url = $"{_environmentUrl}/inbox/poll/{content.contentId}";

                    pollRows.Add(row);
                    newCurrentRow++;

                }

                worksheet.Workbook.Worksheets[sheetName].Cells[currentRow + 1, 1].LoadFromCollection(pollRows);
                worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
                currentRow = newCurrentRow;
            }

            return currentRow;
        }

        private TimeZoneInfo getUserTimeZone(Content content)
        {
            string db_fuso = "America/Sao_Paulo";
            if (content.User_Assigned != null)
            {
                db_fuso = content.User_Assigned.defaultTimezone ?? db_fuso;
            }


            string tz = "";
            try
            {
                tz = TZConvert.IanaToWindows(db_fuso);

            }
            catch (InvalidTimeZoneException)
            {
                tz = "E. South America Standard Time";
            }

            TimeZoneInfo timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(tz);
            return timeZoneInfo;
        }

        private void saveToFile()
        {
            using (var p = new ExcelPackage())
            {
                var ws = p.Workbook.Worksheets.Add("MySheet");
                ws.Cells["A1"].Value = "This is cell A1";
                p.SaveAs(new FileInfo(@"c:\workbooks\myworkbook.xlsx"));
            }
        }

        private string getStatus(string value)
        {

            switch (value)
            {

                case "ready":
                    return i18n.t("export:ready");
                case "open":
                    return i18n.t("export:open");
                case "closed":
                    return i18n.t("export:closed");
                case "awaiting_votes":
                    return i18n.t("export:awaitingVotes");
                case "awaiting_review":
                    return i18n.t("export:awaitingReview");
                case "cancelled":
                    return i18n.t("export:cancelled");
                case "active":
                    return i18n.t("export:active");

                default:
                    return value;

            }
        }

        public string getTaskColumnName(string ColumnName)
        {
            switch (ColumnName)
            {
                case "DEFAULT_COLUMN_1":
                    return i18n.t("export:backlog");
                case "DEFAULT_COLUMN_2":
                    return i18n.t("export:column2");
                case "DEFAULT_COLUMN_3":
                    return i18n.t("export:column3");
                default:
                    return ColumnName;
            }
        }

        public string getTaskStatus(string ColumnName)
        {
            switch (ColumnName.ToLower())
            {
                case "closed":
                    return i18n.t("export:task.closed");
                case "open":
                    return i18n.t("export:task.open");
                case "awaiting_review":
                    return i18n.t("export:task.inReview");
                case "active":
                    return i18n.t("export:task.active");
                default:
                    return ColumnName;
            }
        }

        private string getMeetingStatus(string value)
        {
            switch (value)
            {

                case "ready":
                    return i18n.t("export:meetingReady");
                case "meetingconcluded":
                    return i18n.t("export:meetingConcluded");
                case "open":
                    return i18n.t("export:meetingOpen");
                case "closed":
                    return i18n.t("export:meetingClosed");
                case "published":
                    return i18n.t("export:published");
                case "cancelled":
                    return i18n.t("export:cancelled");

                default:
                    return value;

            }
        }

        private string getSignatureStatus(string columnName)
        {
            switch (columnName.ToLower())
            {
                case "open":
                    return i18n.t("export:signature.open");
                case "closed":
                    return i18n.t("export:signature.closed");
                case "rejected":
                    return i18n.t("export:signature.rejected");
                default:
                    return columnName;
            }
        }

        private string getSignatureContentType(string columnName)
        {
            switch (columnName.ToLower())
            {
                case "meetingminute":
                    return i18n.t("export:signature.meetingminute");
                case "poll":
                    return i18n.t("export:signature.poll");
                case "knowledgedirectory":
                    return i18n.t("export:signature.knowledgedirectory");
                default:
                    return columnName;
            }
        }

        private string getSignatureType(string columnName)
        {
            switch (columnName.ToLower())
            {
                case "digital":
                    return i18n.t("export:signature.digital");
                case "electronic":
                    return i18n.t("export:signature.electronic");
                default:
                    return columnName;
            }
        }

        private string getPollResult(string result)
        {
            switch (result)
            {

                case "yes":
                    return i18n.t("export:yes");
                case "no":
                    return i18n.t("export:no");
                case "tie":
                    return i18n.t("export:tie");
                case "abstain":
                    return i18n.t("export:abstain");

                default:
                    return result;

            }
        }

        internal async Task<byte[]> ExportInsightsReportAsync(InsightsReportDataViewModel reportViewModel)
        {
            var md = new AtlasModelCore();
            var user = await md.User.FindAsync(_currentUser);

            string lang = (user.defaultLanguage ?? "pt");
            i18n.changeLanguage(lang.ToLower());

            using (var stream = new MemoryStream())
            using (var package = new ExcelPackage(stream))
            {
                var worksheets = package.Workbook.Worksheets;

                // Add Meeting Insights
                var meetingSheet = worksheets.Add(i18n.t("export:meetings"));
                AddMeetingInsights(reportViewModel.Meetings, meetingSheet, user);

                // Add Agenda or Resolution Insights
                var agendaOrResolutionSheet = worksheets.Add(i18n.t("export:insights.agendasOrResolutions"));
                AddAgendaOrResolutionInsights(reportViewModel.AgendasOrResolutions, agendaOrResolutionSheet, user);

                // Add BlueBooks
                var blueBookSheet = worksheets.Add("BlueBook");
                AddBlueBookInsights(reportViewModel.BlueBooks, blueBookSheet);

                // Add Materials
                var materialSheet = worksheets.Add(i18n.t("export:insights.materials"));
                AddMaterialInsights(reportViewModel.Materials, materialSheet);

                // Add Comments
                var commentSheet = worksheets.Add(i18n.t("export:comments"));
                AddCommentInsights(reportViewModel.Comments, commentSheet);

                // Add Meeting minute information
                var minuteSheet = worksheets.Add(i18n.t("export:insights.meetingMinutes"));
                AddMeetingMinuteInsights(reportViewModel.Meetings, minuteSheet);

                package.Save();
                return stream.ToArray();
            }
        }

        private void AddMeetingMinuteInsights(List<InsightsMeetingDto> meetings, ExcelWorksheet minuteSheet)
        {
            // Add headers
            minuteSheet.Cells[1, 1].Value = i18n.t("export:workgroupId");
            minuteSheet.Cells[1, 2].Value = i18n.t("export:insights.meetingId");
            minuteSheet.Cells[1, 3].Value = i18n.t("export:insights.publishedMinute");
            minuteSheet.Cells[1, 4].Value = i18n.t("export:insights.signedMinute");
            minuteSheet.Cells[1, 5].Value = i18n.t("export:insights.minuteSignatureType");

            // Populate data
            int row = 2;
            foreach (var meeting in meetings)
            {
                minuteSheet.Cells[row, 1].Value = meeting.WorkgroupId;
                minuteSheet.Cells[row, 2].Value = meeting.ContentId;
                minuteSheet.Cells[row, 3].Value = meeting.HasPublishedMinute ? i18n.t("export:yes") : i18n.t("export:no");
                minuteSheet.Cells[row, 4].Value = meeting.HasSignedMinute ? i18n.t("export:yes") : i18n.t("export:no");
                minuteSheet.Cells[row, 5].Value = meeting.HasSignedMinute
                    ? (string.Equals(meeting.MinuteSignatureType, MinuteSignatureTypes.Electronic, StringComparison.OrdinalIgnoreCase)
                        ? i18n.t("export:signature.electronic")
                        : i18n.t("export:signature.digital"))
                    : string.Empty;

                row++;
            }

            // Auto-fit columns for all cells
            minuteSheet.Cells.AutoFitColumns();
        }

        private void AddCommentInsights(List<InsightsCommentDto> comments, ExcelWorksheet commentSheet)
        {
            // Add headers
            commentSheet.Cells[1, 1].Value = i18n.t("export:workgroupId");
            commentSheet.Cells[1, 2].Value = i18n.t("export:insights.meetingId");
            // Quantidade de comentários nas deliberações
            commentSheet.Cells[1, 3].Value = i18n.t("export:insights.resolutionCommentCount");
            // Quantidade de comentários nas pautas
            commentSheet.Cells[1, 4].Value = i18n.t("export:insights.agendaCommentCount");

            // Populate data
            int row = 2;
            foreach (var comment in comments)
            {
                commentSheet.Cells[row, 1].Value = comment.WorkgroupId;
                commentSheet.Cells[row, 2].Value = comment.MeetingId;
                commentSheet.Cells[row, 3].Value = comment.ResolutionComments;
                commentSheet.Cells[row, 4].Value = comment.AgendaComments;
                row++;
            }

            // Auto-fit columns for all cells
            commentSheet.Cells.AutoFitColumns();
        }

        private void AddMaterialInsights(List<InsightsMaterialDto> materials, ExcelWorksheet materialSheet)
        {
            // Add headers
            //ID do Board / Projeto
            materialSheet.Cells[1, 1].Value = i18n.t("export:workgroupId");
            //ID da reunião
            materialSheet.Cells[1, 2].Value = i18n.t("export:insights.meetingId");
            //Quantidade de visualizações totais de materiais
            materialSheet.Cells[1, 3].Value = i18n.t("export:insights.totalViews");
            //Quantidade de visualizações únicas de materiais
            materialSheet.Cells[1, 4].Value = i18n.t("export:insights.uniqueViews");

            // Populate data
            int row = 2;
            foreach (var material in materials)
            {
                materialSheet.Cells[row, 1].Value = material.WorkgroupId;
                materialSheet.Cells[row, 2].Value = material.MeetingId;
                materialSheet.Cells[row, 3].Value = material.TotalViews;
                materialSheet.Cells[row, 4].Value = material.UniqueViews;
                row++;
            }

            // Auto-fit columns for all cells
            materialSheet.Cells.AutoFitColumns();
        }

        private void AddBlueBookInsights(List<InsightsBlueBookDto> blueBooks, ExcelWorksheet blueBookSheet)
        {
            // Add headers
            blueBookSheet.Cells[1, 1].Value = i18n.t("export:workgroupId");
            // ID da reunião
            blueBookSheet.Cells[1, 2].Value = i18n.t("export:insights.meetingId");
            // Quantidade de páginas do BlueBook
            blueBookSheet.Cells[1, 3].Value = i18n.t("export:insights.blueBookPages");
            // Quantidade de visualizações totais
            blueBookSheet.Cells[1, 4].Value = i18n.t("export:insights.totalViews");
            // Quantidade de visualizações únicas
            blueBookSheet.Cells[1, 5].Value = i18n.t("export:insights.uniqueViews");

            // Polulate data
            int row = 2;
            foreach (var blueBook in blueBooks)
            {
                blueBookSheet.Cells[row, 1].Value = blueBook.WorkgroupId;
                blueBookSheet.Cells[row, 2].Value = blueBook.MeetingId;
                blueBookSheet.Cells[row, 3].Value = blueBook.BlueBookPages;
                blueBookSheet.Cells[row, 4].Value = blueBook.TotalViews;
                blueBookSheet.Cells[row, 5].Value = blueBook.UniqueViews;
                row++;
            }

            // Auto-fit columns for all cells
            blueBookSheet.Cells.AutoFitColumns();
        }

        private void AddAgendaOrResolutionInsights(List<InsightsResolutionOrAgendaDto> agendasOrResolutions, ExcelWorksheet worksheet, User user)
        {
            var userZone = getUserTimeZone(user);

            // Add headers
            worksheet.Cells[1, 1].Value = i18n.t("export:workgroupId");
            worksheet.Cells[1, 2].Value = i18n.t("export:insights.meetingId");
            worksheet.Cells[1, 3].Value = i18n.t("export:insights.type");
            worksheet.Cells[1, 4].Value = "ID";
            worksheet.Cells[1, 5].Value = i18n.t("export:creationDate");
            worksheet.Cells[1, 6].Value = i18n.t("export:status");

            // Populate data
            int row = 2;
            foreach (var agendaOrResolution in agendasOrResolutions)
            {
                worksheet.Cells[row, 1].Value = agendaOrResolution.WorkgroupId;
                worksheet.Cells[row, 2].Value = agendaOrResolution.MeetingId;
                worksheet.Cells[row, 3].Value = agendaOrResolution.Type == ContentTypes.Poll ? i18n.t("export:poll") : i18n.t("export:meetingAgenda");
                worksheet.Cells[row, 4].Value = agendaOrResolution.ContentId;

                worksheet.Cells[row, 5].Style.Numberformat.Format = i18n.t("export:dateTimeFormat");
                worksheet.Cells[row, 5].Value = TimeZoneInfo.ConvertTimeFromUtc(agendaOrResolution.CreateDate, userZone);

                worksheet.Cells[row, 6].Value = getStatus(agendaOrResolution.Status.ToLower());
                row++;
            }

            // Auto-fit columns for all cells
            worksheet.Cells.AutoFitColumns();
        }

        private void AddMeetingInsights(List<InsightsMeetingDto> meetingsData, ExcelWorksheet worksheet, User user)
        {
            var userZone = getUserTimeZone(user);

            // Add headers
            worksheet.Cells[1, 1].Value = i18n.t("export:workgroupId");
            worksheet.Cells[1, 2].Value = i18n.t("export:insights.meetingId");
            worksheet.Cells[1, 3].Value = i18n.t("export:creationDate");
            worksheet.Cells[1, 4].Value = i18n.t("export:meetingDateTime");
            worksheet.Cells[1, 5].Value = i18n.t("export:insights.meetingDuration");
            worksheet.Cells[1, 6].Value = i18n.t("export:attendees");
            worksheet.Cells[1, 7].Value = i18n.t("export:status");

            // Populate data
            int row = 2;
            foreach (var meeting in meetingsData)
            {
                worksheet.Cells[row, 1].Value = meeting.WorkgroupId;
                worksheet.Cells[row, 2].Value = meeting.ContentId;

                worksheet.Cells[row, 3].Style.Numberformat.Format = i18n.t("export:dateTimeFormat");
                worksheet.Cells[row, 3].Value = TimeZoneInfo.ConvertTimeFromUtc(meeting.CreateDate, userZone);

                worksheet.Cells[row, 4].Style.Numberformat.Format = i18n.t("export:dateTimeFormat");
                worksheet.Cells[row, 4].Value = TimeZoneInfo.ConvertTimeFromUtc(meeting.MeetingDate, userZone);

                worksheet.Cells[row, 5].Value = meeting.DurationMinutes;

                worksheet.Cells[row, 6].Value = meeting.TotalParticipants;
                worksheet.Cells[row, 7].Value = getMeetingStatus(meeting.Status.ToLower());
                row++;
            }

            // Auto-fit columns for all cells
            worksheet.Cells.AutoFitColumns();
        }

        private class MeetingRowModel
        {
            [DataMember(Order = 1)]
            public int workgroupId { get; set; }

            [DataMember(Order = 2)]
            public string workgroupName { get; set; }

            [DataMember(Order = 3)]
            public int contentId { get; set; }

            [DataMember(Order = 4)]
            public string title { get; set; }

            [DataMember(Order = 5)]
            public DateTime creationDate { get; set; }

            [DataMember(Order = 6)]
            public int creatorUserId { get; set; }

            [DataMember(Order = 7)]
            public string creatorUserName { get; set; }

            [DataMember(Order = 8)]
            public DateTime meetingDate { get; set; }

            [DataMember(Order = 9)]
            public string status { get; set; }

            [DataMember(Order = 10)]
            public string owners { get; set; }

            [DataMember(Order = 11)]
            public string ownersNames { get; set; }

            [DataMember(Order = 12)]
            public string attendees { get; set; }

            [DataMember(Order = 13)]
            public string meetingMinuteStatus { get; set; }

            [DataMember(Order = 14)]
            public string url { get; set; }
        }

        private class PollRowModel
        {

            [DataMember(Order = 1)]
            public int workgroupId { get; set; }

            [DataMember(Order = 2)]
            public string workgroupName { get; set; }

            [DataMember(Order = 3)]
            public int contentId { get; set; }

            [DataMember(Order = 4)]
            public string pollName { get; set; }

            [DataMember(Order = 5)]
            public DateTime creationDate { get; set; }

            [DataMember(Order = 6)]
            public DateTime? dueDate { get; set; }

            [DataMember(Order = 7)]
            public string status { get; set; }

            [DataMember(Order = 8)]
            public DateTime? finishDate { get; set; }

            [DataMember(Order = 9)]
            public int votes { get; set; }

            [DataMember(Order = 10)]
            public int totalVoters { get; set; }

            [DataMember(Order = 11)]
            public string votersNames { get; set; }

            [DataMember(Order = 12)]
            public String doneVoters { get; set; }

            [DataMember(Order = 13)]
            public string doneVotersNames { get; set; }

            [DataMember(Order = 14)]
            public String pendingVoters { get; set; }

            [DataMember(Order = 15)]
            public string pendingVotersNames { get; set; }

            [DataMember(Order = 16)]
            public string result { get; set; }

            [DataMember(Order = 17)]
            public int meetingId { get; set; }

            [DataMember(Order = 18)]
            public string meetingTitle { get; set; }

            [DataMember(Order = 19)]
            public string url { get; set; }
        }

        private class TaskRowModel
        {
            [DataMember(Order = 1)]
            public int workgroupId { get; set; }

            [DataMember(Order = 2)]
            public string workgroupName { get; set; }

            [DataMember(Order = 3)]
            public int contentId { get; set; }

            [DataMember(Order = 4)]
            public string taskName { get; set; }

            [DataMember(Order = 5)]
            public DateTime creationDate { get; set; }

            [DataMember(Order = 6)]
            public int creatorUserId { get; set; }

            [DataMember(Order = 7)]
            public string creatorUserName { get; set; }

            [DataMember(Order = 8)]
            public DateTime? dueDate { get; set; }

            [DataMember(Order = 9)]
            public string status { get; set; }

            [DataMember(Order = 10)]
            public int assignedUserId { get; set; }

            [DataMember(Order = 11)]
            public string assignedUserName { get; set; }

            [DataMember(Order = 12)]
            public string checklist { get; set; }

            [DataMember(Order = 13)]
            public decimal? checklistOverallFinished { get; set; }

            [DataMember(Order = 14)]
            public DateTime? closeDate { get; set; }

            [DataMember(Order = 15)]
            public int? meetingId { get; set; }

            [DataMember(Order = 16)]
            public string meetingTitle { get; set; }

            [DataMember(Order = 17)]
            public string subscribers { get; set; }

            [DataMember(Order = 18)]
            public string subscribersActionNames { get; set; }

            [DataMember(Order = 19)]
            public string url { get; set; }
        }

        private class KBRowModel
        {
            [DataMember(Order = 1)]
            public int boardId { get; set; }

            [DataMember(Order = 2)]
            public int? categoryId { get; set; }

            [DataMember(Order = 3)]
            public DateTime? categoryCreationDate { get; set; }

            [DataMember(Order = 4)]
            public int? categoryCreatorId { get; set; }

            [DataMember(Order = 5)]
            public int? articleId { get; set; }

            [DataMember(Order = 6)]
            public DateTime? articleCreationDate { get; set; }

            [DataMember(Order = 7)]
            public int? articleCreatorId { get; set; }

            [DataMember(Order = 8)]
            public int? attachments { get; set; }

            [DataMember(Order = 9)]
            public string url { get; set; }
        }

        private class NewKBRowModel
        {
            [DataMember(Order = 1)]
            public int workgroupId { get; set; }

            [DataMember(Order = 2)]
            public string workgroupName { get; set; }

            [DataMember(Order = 3)]
            public int folderId { get; set; }

            [DataMember(Order = 4)]
            public DateTime folderCreateDate { get; set; }

            [DataMember(Order = 5)]
            public int folderCreateUser { get; set; }

            [DataMember(Order = 6)]
            public string fileName { get; set; }

            [DataMember(Order = 7)]
            public DateTime? fileCreateDate { get; set; }

            [DataMember(Order = 8)]
            public int? parentFolder { get; set; }

            [DataMember(Order = 9)]
            public string url { get; set; }
        }

        private class AuditLogView
        {
            public int activityId { get; set; }
            public int userId { get; set; }
            public string userName { get; set; }
            public string type { get; set; }
            public DateTime timeStamp { get; set; }
            public int workgroupId { get; set; }
            public string workgroupName { get; set; }
            public string message { get; set; }

        }
    }
}
