using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;

namespace Atlas.Business.Policies.Workgroup
{
    public class WorkgroupOwnerValidationHandler : AuthorizationHandler<WorkgroupOwnerValidationRequirement>
    {
        private readonly HttpContext _httpContext;

        public WorkgroupOwnerValidationHandler(IHttpContextAccessor httpContext)
        {
            _httpContext = httpContext?.HttpContext;
        }

        /// <summary>
        /// Handles the requirement that the user must be a workgroup owner.
        /// </summary>
        /// <param name="context">The authorization context.</param>
        /// <param name="requirement">The requirement.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        protected async override Task HandleRequirementAsync(AuthorizationHandlerContext context, WorkgroupOwnerValidationRequirement requirement)
        {
            try
            {
                Data.Entities.AtlasModelCore _atlasModel = new Data.Entities.AtlasModelCore();
                _atlasModel.Database.SetCommandTimeout(999);
                _atlasModel.ChangeTracker.LazyLoadingEnabled = false;

                int.TryParse(_httpContext.User?.FindFirst("userId")?.Value, out int userId);
                var workgroupRouteParams = new[] { "id", "workgroupId" };
                var hasWorkgroupId = false;
                var workgroupId = 0;

                foreach (var param in workgroupRouteParams)
                {
                    if (int.TryParse(_httpContext.GetRouteValue(param)?.ToString(), out workgroupId))
                    {
                        hasWorkgroupId = true;
                        break;
                    }
                }

                var hasClientId = int.TryParse(_httpContext.GetRouteValue("clientId")?.ToString(), out int clientId);

                var isWorkgroupOwner = await _atlasModel.WorkgroupOwner
                .AnyAsync(wo => wo.userId == userId
                    && wo.Workgroup.Client.deleted != true
                    && wo.Workgroup.Client.blocked != true
                    &&
                    (
                        !hasClientId
                        ||
                        wo.Workgroup.clientId == clientId
                    )
                    &&
                    (
                        !hasWorkgroupId
                        ||
                        wo.workgroupId == workgroupId
                    )
                );

                if (isWorkgroupOwner)
                {
                    context.Succeed(requirement);
                }
            }
            catch (Exception)
            {
            }

            return;
        }
    }
}
