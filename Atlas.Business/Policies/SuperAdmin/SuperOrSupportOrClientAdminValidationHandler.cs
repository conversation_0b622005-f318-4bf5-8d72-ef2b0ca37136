using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Atlas.Business.Policies.SuperAdmin
{
    // Renamed to reflect multiple administrative roles
    public class SuperOrSupportOrClientAdminValidationHandler : AuthorizationHandler<SuperOrSupportOrClientAdminValidationRequirement>
    {
        private readonly HttpContext _httpContext;
        private const int SUPER_ADMIN = 2;
        private const int SUPPORT_ADMIN = 3;
        private const int CLIENT_ADMIN = 1004;

        public SuperOrSupportOrClientAdminValidationHandler(IHttpContextAccessor httpContext)
        {
            _httpContext = httpContext?.HttpContext;
        }

        /// <summary>
        /// Handles the requirement that the user must be a super admin, support admin, or client admin for the clientId in the route.
        /// </summary>
        /// <param name="context">The authorization context.</param>
        /// <param name="requirement">The requirement.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        protected override Task HandleRequirementAsync(AuthorizationHandlerContext context, SuperOrSupportOrClientAdminValidationRequirement requirement)
        {
            try
            {
                Data.Entities.AtlasModelCore _atlasModel = new Data.Entities.AtlasModelCore();
                _atlasModel.Database.SetCommandTimeout(999);
                _atlasModel.ChangeTracker.LazyLoadingEnabled = false;

                int.TryParse(_httpContext.User?.FindFirst("userId")?.Value, out int userId);
                bool hasClientId = int.TryParse(_httpContext.GetRouteValue("ClientId")?.ToString(), out int clientId);

                var hasValidUserRoles = _atlasModel.UserRole.Any(ur =>
                    ur.userId == userId
                    &&
                    (
                        ur.clientId == null
                        ||
                        (
                            ur.Client.blocked == false
                            && ur.Client.deleted == false
                        )
                    )
                    && !ur.User.blocked
                    && !ur.User.deleted
                    &&
                    (
                        ur.roleId == SUPER_ADMIN
                        ||
                        ur.roleId == SUPPORT_ADMIN
                        ||
                        (
                          hasClientId && ur.clientId == clientId && ur.roleId == CLIENT_ADMIN
                        )
                        ||
                        (
                          !hasClientId && ur.roleId == CLIENT_ADMIN
                        )
                    )
                );

                if (hasValidUserRoles)
                {
                    context.Succeed(requirement);
                }
            }
            catch (Exception)
            {
                // Handle exceptions as needed, possibly logging them.
            }
            return Task.CompletedTask;
        }
    }
}
