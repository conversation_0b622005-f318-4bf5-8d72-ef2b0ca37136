using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Atlas.Business.Policies.ContentPermission
{
    public class ContentPermissionValidationHandler : BaseAuthorizationHandler<ContentPermissionValidationRequirement>
    {
        public ContentPermissionValidationHandler(IHttpContextAccessor httpContext) : base(httpContext)
        {
        }

        /// <summary>
        /// Handles the requirement that the user must have permission to access the content.
        /// </summary>
        /// <param name="context">The authorization context.</param>
        /// <param name="requirement">The requirement.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context, ContentPermissionValidationRequirement requirement)
        {
            try
            {
                Data.Entities.AtlasModelCore _atlasModel = new Data.Entities.AtlasModelCore();
                _atlasModel.Database.SetCommandTimeout(999);
                _atlasModel.ChangeTracker.LazyLoadingEnabled = false;

                int.TryParse(_httpContext.User?.FindFirst("userId")?.Value, out int userId);
                var hasContentUuId = Guid.TryParse(_httpContext.GetRouteValue("contentUuId")?.ToString(), out Guid contentUuId);

                if (!hasContentUuId)
                {
                    await FailWithResponseAsync(context, 400, "Invalid or missing contentUuId in route parameters");
                    return;
                }

                if (userId <= 0)
                {
                    await FailWithResponseAsync(context, 401, "Invalid or missing userId in token claims");
                    return;
                }

                var hasPermission = _atlasModel.ContentPermission
                    .Any(cp => cp.userId == userId
                              && cp.contentUuid == contentUuId);

                if (hasPermission)
                {
                    context.Succeed(requirement);
                }
                else
                {
                    await FailWithResponseAsync(context, 403, "USER_UNAUTHORIZED");
                }
            }
            catch (Exception ex)
            {
                await FailWithResponseAsync(context, 500, "Internal server error during authorization");
            }
        }
    }
}
