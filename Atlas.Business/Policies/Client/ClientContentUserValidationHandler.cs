using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Atlas.Business.Policies.Client
{
    public class ClientContentUserValidationHandler : AuthorizationHandler<ClientContentUserValidationRequirement>
    {
        private readonly HttpContext _httpContext;

        public ClientContentUserValidationHandler(IHttpContextAccessor httpContext)
        {
            _httpContext = httpContext?.HttpContext;
        }

        /// <summary>
        /// Handles the requirement that the user must be a client workgroup user.
        /// </summary>
        /// <param name="context">The authorization context.</param>
        /// <param name="requirement">The requirement.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        protected override Task HandleRequirementAsync(AuthorizationHandlerContext context, ClientContentUserValidationRequirement requirement)
        {
            try
            {
                Data.Entities.AtlasModelCore _atlasModel = new Data.Entities.AtlasModelCore();
                _atlasModel.Database.SetCommandTimeout(999);
                _atlasModel.ChangeTracker.LazyLoadingEnabled = false;

                string email = _httpContext.User?.FindFirst(ClaimTypes.Email)?.Value ?? _httpContext.User?.FindFirst("email")?.Value;

                UserService svc_usr = new UserService();
                var user = svc_usr.GetUserByEmail(email);

                int.TryParse(_httpContext.GetRouteValue("contentId")?.ToString(), out int contentId);

                var select = from c in _atlasModel.Content
                             join w in _atlasModel.Workgroup on c.workgroupId equals w.workgroupId
                             join wu in _atlasModel.WorkgroupUser on w.workgroupId equals wu.workgroupId
                             where wu.userId == user.userId &&
                                   c.contentId == contentId &&
                                   w.Client.deleted != true &&
                                   w.Client.blocked != true
                             select c;

                if (select.Any())
                {
                    context.Succeed(requirement);
                }
            }
            catch (Exception)
            {
                //Sentry
            }
            return Task.CompletedTask;
        }
    }
}
