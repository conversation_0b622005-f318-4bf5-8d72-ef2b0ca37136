using Atlas.Data.Repository;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text.Json;
using System.Threading.Tasks;
using Atlas.CrossCutting.Helpers;
using Atlas.Business.Core.FeatureFlag;

namespace Atlas.Business.Policies
{
    /// <summary>
    /// Authorization handler that validates platform users using IdentityUser<int>
    /// </summary>
    public class PlatformUserHandler : BaseAuthorizationHandler<PlatformAccessRequirement>
    {
        private readonly UserRepository _userRepository = new UserRepository();
        private readonly AuthUtil _authUtil;
        private readonly IFeatureFlagService _featureFlagService;

        /// <summary>
        /// Constructor that injects the necessary dependencies.
        /// </summary>
        /// <param name="httpContextAccessor">Accessor to obtain the current HttpContext.</param>
        /// <param name="accountRepository">Repository to retrieve user account details.</param>
        public PlatformUserHandler
        (
            IHttpContextAccessor httpContextAccessor,
            IFeatureFlagService featureFlagService
        ) : base(httpContextAccessor)
        {
            _authUtil = new AuthUtil(_httpContext?.User);
            _featureFlagService = featureFlagService;
        }

        //VERIFICAR DEPOIS AS VALIDAÇÕES
        protected override async Task<Task> HandleRequirementAsync(AuthorizationHandlerContext context, PlatformAccessRequirement requirement)
        {
            try
            {
                // 1 - Check if the user is authenticated.
                if (!_httpContext.User.Identity.IsAuthenticated)
                {
                    await FailWithResponseAsync(context, 401, "Unauthorized access.");
                    return Task.CompletedTask;
                }

                // 2 - Retrieve and validate the Authorization header.
                var authHeader = _httpContext.Request.Headers["Authorization"].FirstOrDefault();
                if (string.IsNullOrWhiteSpace(authHeader))
                {
                    await FailWithResponseAsync(context, 401, "Missing Authorization header.");
                    return Task.CompletedTask;
                }

                // Split the header to extract the token.
                var tokenParts = authHeader.Split(" ");
                if (tokenParts.Length < 2)
                {
                    await FailWithResponseAsync(context, 401, "Invalid Authorization header format.");
                    return Task.CompletedTask;
                }

                var token = tokenParts[1];
                JwtSecurityToken jwt;
                try
                {
                    // Parse the JWT token.
                    jwt = new JwtSecurityTokenHandler().ReadToken(token) as JwtSecurityToken;
                }
                catch
                {
                    await FailWithResponseAsync(context, 401, "Invalid token.");
                    return Task.CompletedTask;
                }

                // Validate token expiration.
                if (jwt == null || jwt.ValidTo < DateTime.UtcNow)
                {
                    await FailWithResponseAsync(context, 401, "Expired or invalid token.");
                    return Task.CompletedTask;
                }

                // 3 - Extract the user ID from the claims using the standard Identity claim.
                //DEPOIS VOLTAR PARA O USERID EM VEZ DO EMAIL
                var userIdClaim = _httpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.Email);
                if (userIdClaim == null)// || !int.TryParse(userIdClaim.Value, out int email))
                {
                    await FailWithResponseAsync(context, 401, "Invalid user identifier.");
                    return Task.CompletedTask;
                }

                // 4 - Retrieve the user from the database and check required properties.
                var user = _userRepository.GetSimpleUserData(userIdClaim.Value);
                if (user == null)
                {
                    await FailWithResponseAsync(context, 401, "User not found.");
                    return Task.CompletedTask;
                }

                // Validate that both email and phone number have been confirmed.
                if (!user.emailConfirmed && !user.mobileConfirmed)
                {
                    await FailWithResponseAsync(context, 401, "Email or phone confirmation pending.");
                    return Task.CompletedTask;
                }

                // Check if the user's account is currently locked out.
                var sessionKey = _authUtil.SessionKey;
                UserService userService = new UserService(_featureFlagService);
                bool isSessionLocked = await userService.IsSessionLockedAsync(sessionKey);

                if (isSessionLocked)
                {
                    await FailWithResponseAsync(context, 401, "User session is locked.");
                    return Task.CompletedTask;
                }

                // If all validations pass, succeed the requirement.
                context.Succeed(requirement);
            }
            catch (Exception ex)
            {
                // Log any unexpected exceptions.
                Console.WriteLine($"PLATFORM_USER_VALIDATION_ERROR: {ex.Message}");
                await FailWithResponseAsync(context, 500, "Internal server error.");
            }
            return Task.CompletedTask;
        }

    }
}
