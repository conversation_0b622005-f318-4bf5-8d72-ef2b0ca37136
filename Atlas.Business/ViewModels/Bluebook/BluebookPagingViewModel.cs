using Atlas.Data.Entities;
using System.Collections.Generic;

namespace Atlas.Business.ViewModels.Bluebook
{
    public class BluebookPagingViewModel
    {
        public Attachment attachment { get; set; }

        public int? attachmentId { get; set; }
        public int? contentAttachmentId { get; set; }
        public int? contentId { get; set; }

        public int pageLen { get; set; }
        public int pageStart { get; set; }
        public int pageEnd { get; set; }
        public int documentPosition { get; set; }
        public bool hasPermission { get; set; }

        public string fileName { get; set; }

        public List<ContentAttachmentAnnotation> Annotations { get; set; }
    }
}
