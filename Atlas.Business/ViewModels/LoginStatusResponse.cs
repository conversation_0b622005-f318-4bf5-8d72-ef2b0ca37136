using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace Atlas.Business.ViewModels
{
    [JsonObject(NamingStrategyType = typeof(CamelCaseNamingStrategy), ItemNullValueHandling = NullValueHandling.Ignore)]
    public class LoginStatusResponse
    {
        public bool? RequiresSSO { get; set; }
        public string AuthorizeUrl { get; set; }
        public string Error { get; set; }
    }
}
