using System.Collections.Generic;

namespace Atlas.Business.ViewModels
{
    public class HomeDataViewModel
    {
        public int inbox_totalCount { get; set; }
        public int outbox_totalCount { get; set; }
        public List<LastUpdatesViewModel> lastUpdates { get; set; }
        public List<object> lastUpdates_simple { get; set; }
        public List<MeetingViewModel> upcomingEvents { get; set; }
        public List<object> upcomingEvents_simple { get; set; }
        public int new_inbox_total_count { get; set; }
        public int new_outbox_total_count { get; set; }
    }
}
