using Atlas.CrossCutting.DTO;
using Atlas.Data.Entities;
using System;
using System.Collections.Generic;
using static Atlas.Data.Entities.Workgroup;

namespace Atlas.Business.ViewModels
{
    public class WorkgroupViewModel
    {
        public int workgroupId { get; set; }
        public string name { get; set; }
        public string color { get; set; }
        public int? companyId { get; set; }
        public string companyName { get; set; }
        public int clientId { get; set; }
        public string clientName { get; set; }

        public HomeDataDTO HomeData { get; set; }

        public List<ProfileViewModel> Members { get; set; }
        public IEnumerable<ContentAggregationObject> ContentSummary { get; set; }
        public List<Role> AvailableRoles { get; set; }
        public List<object> Tasks { get; set; }
        public bool archived { get; internal set; }
        public List<LastUpdatesViewModel> lastUpdates { get; set; }
        public string type { get; set; }
        public string bulletColor { get; internal set; }

        public string description { get; set; }
        public List<string> allowedDomains { get; set; }

        public Nullable<bool> disableDomainEnforcement { get; set; }

        public List<WorkgroupOwner> WorkgroupOwner { get; set; }

        public List<WorkgroupTaskList> WorkgroupTaskList { get; set; }

        public WorkgroupUACClass UAC { get; set; }
        public TaskGoalsCount taskStatistics { get; internal set; }

        public bool Equals(WorkgroupViewModel other)
        {
            if (other is null)
                return false;

            return this.workgroupId == other.workgroupId && this.clientId == other.clientId && this.type == other.type;
        }

        public override bool Equals(object obj) => Equals(obj as WorkgroupViewModel);
        public override int GetHashCode() => (workgroupId, clientId, type).GetHashCode();
        public bool blockInviteUsers { get; set; }
        public bool createForm { get; internal set; }
        public bool hasDnoPolicies { get; set; }
        public bool hasCyberPolicies { get; set; }
    }

    public class ContentSummaryViewModel
    {
        public int id { get; set; }
        public string type { get; set; }
        public string status { get; set; }
        public DateTime createDate { get; set; }
    }
}
