using System;

namespace Atlas.Business.ViewModels.ClientBackup
{
    public class ClientBackupFiltersViewModel
    {
        public DateTime? startDate { get; set; }
        public DateTime? endDate { get; set; }
        public int? workgroupId { get; set; }
        public int? clientId { get; set; }
        public int? contentId { get; set; }
        public Guid? contentUuid { get; set; }
        public string entity { get; set; }
    }

    public class ClientBackupResultingFileViewModel
    {
        public string fileGenerated { get; set; }
        public ClientBackupFiltersViewModel filters { get; set; }
    }

    public class BackupHttpResult
    {
        public bool Success { get; set; }
        public string Status { get; set; }
        public string Message { get; set; }
    }
}
