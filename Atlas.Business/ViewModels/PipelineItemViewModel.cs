using System;

namespace Atlas.Business.ViewModels
{
    public class PipelineItemViewModel : ContentBaseViewModel
    {
        public int contentId { get; set; }

        public DateTime createDate { get; set; }
        public int createUser { get; set; }
        public string createUserName { get; set; }
        public string createUserPic { get; set; }


        //inner
        public int pipelineItemId { get; set; }
        public string name { get; set; }
        public string status { get; set; }
        public string step { get; set; }
        public string temperature { get; set; }
        public int? assignedUser { get; set; }
        public string assignedUserName { get; set; }
        public string assignedUserPic { get; set; }
        public decimal? value { get; set; }
        public int? itemOrder { get; set; }
        public string contactInfo { get; set; }
        public string historyInfo { get; set; }
        public int pipelineId { get; set; }
        public string clientName { get; set; }
    }
}
