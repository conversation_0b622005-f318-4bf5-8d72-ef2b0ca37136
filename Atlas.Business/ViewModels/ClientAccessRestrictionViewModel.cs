using Atlas.Data.Entities;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Atlas.Business.ViewModels
{
    [JsonObject(ItemNullValueHandling = NullValueHandling.Ignore)]
    public class ClientAccessRestrictionViewModel
    {
        [JsonProperty(PropertyName = "clientId")]
        public int ClientId { get; set; }

        [JsonProperty(PropertyName = "accessRestrictionAddOn")]
        public bool? AccessRestrictionAddOn { get; set; }

        [JsonProperty(PropertyName = "accessRestrictionEnabled")]
        public bool? AccessRestrictionEnabled { get; set; }

        [JsonProperty(PropertyName = "accessRestrictionMode", NullValueHandling = NullValueHandling.Include)]
        public string AccessRestrictionMode { get; set; }

        [JsonProperty(PropertyName = "clientAccessRestrictions")]
        public ICollection<ClientAccessRestriction> ClientAccessRestrictions { get; set; }

        [JsonProperty(PropertyName = "addRules")]
        public List<ClientAccessRestriction> AddRules { get; set; }

        [JsonProperty(PropertyName = "removeRules")]
        public int[] RemoveRules { get; set; }
    }
}
