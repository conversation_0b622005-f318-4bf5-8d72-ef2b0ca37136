using Newtonsoft.Json;

namespace Atlas.Business.ViewModels.KnowledgeBase
{
    public class UAC
    {
        [JsonIgnore]
        public bool IsOwner { get; set; }
        
        [JsonProperty(PropertyName = "canView")]
        public bool CanView { get; set; }
        
        [JsonProperty(PropertyName = "canUpdate")]
        public bool CanUpdate { get; set; }

        [JsonProperty(PropertyName = "canAddFiles")]
        public bool CanAddFiles { get; set; } = false;
        
        [JsonProperty(PropertyName = "canDelete")]
        public bool CanDelete { get; set; }

        [JsonProperty(PropertyName = "canAddFolder")]
        public bool CanAddFolder { get; set; } = false;

        [JsonProperty(PropertyName = "canManagePermission")]
        public bool CanManagePermission { get; set; } = false;

        [JsonProperty(PropertyName = "canRequestESignature")]
        public bool CanRequestESignature { get; set; } = false;

        [JsonProperty(PropertyName = "canLockFile")]
        public bool CanLockFile { get; set; } = false;

        [JsonProperty(PropertyName = "canViewESignatureSummary")]
        public bool CanViewESignatureSummary { get; internal set; } = false;

        [JsonProperty(PropertyName = "canNotifyAttachment")]
        public bool CanNotifyAttachment { get; internal set; }

        [JsonProperty(PropertyName = "canRequestBackup")]
        public bool CanRequestBackup { get; set; }

        [JsonProperty(PropertyName = "canExportToExcel")]
        public bool CanExportToExcel { get; set; }
        public bool? canShare { get; set; }
    }
}
