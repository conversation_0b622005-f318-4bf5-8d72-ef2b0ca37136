using Atlas.CrossCutting.Enums;
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Atlas.Business.ViewModels
{
    public class ListFormViewModel
    {
        public Guid contentUuid { get; set; }
        public string name { get; set; }
        public string board { get; set; }
        public string workgroupColor { get; set; }
        public string clientName { get; set; }
        public DateTime createdDate { get; set; }
        public string responsables { get; set; }
        [NotMapped]
        public bool? answered { get; set; }
        [NotMapped]
        public int? answers { get; set; }
        [NotMapped]
        public int? totalAnswers { get; set; }
        public EnFormStatus status { get; set; }
        public bool anonymous { get; set; } = false;
        public DateTime? expirationDate { get; set; }
        public string createUserName { get; set; }
        public string profilePic { get; set; }
        public int clientId { get; set; }
        public int workgroupId { get; set; }
        public string workgroupType { get; set; }
    }
}
