using System;
using System.Collections.Generic;

namespace Atlas.Business.ViewModels
{
    public class NoteViewModel
    {
        //id
        public int contentId { get; set; }
        public Guid contentUuid { get; set; }
        public int workgroupId { get; set; }
        public string workgroupName { get; set; }
        public string workgroupColor { get; set; }
        public string workgroupType { get; set; }
        public int clientId { get; set; }
        public string clientName { get; set; }

        //content
        public DateTime createDate { get; set; }
        public int createUser { get; set; }
        public string createUserName { get; set; }
        public string createUserPic { get; set; }
        public string status { get; set; }

        //note
        public int noteId { get; set; }
        public string title { get; set; }
        public string text { get; set; }
        public DateTime? lastUpdate { get; set; }

        //accessories
        public List<string> tags { get; set; }

        //parent
        public int? parentContentId { get; set; }
        public string parentContentType { get; set; }
        public bool isShared { get; set; }
        public bool isWorkgroupArchived { get; set; }
    }
}
