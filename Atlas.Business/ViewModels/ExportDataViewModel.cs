using System;

namespace Atlas.Business.ViewModels
{
    public class ExportDataViewModel
    {
        public int workgroupId { get; set; }
        public int contentId { get; set; }
        public Guid? contentUuid { get; set; }
        public int userId { get; set; }
        public int clientId { get; set; }
        public DateTime? fromDate { get; set; }
        public DateTime? toDate { get; set; }
        public bool exportMeetings { get; set; }
        public bool exportProjects { get; set; }
        public bool exportPolls { get; set; }
        public bool exportKB { get; set; }
        public bool exportActivities { get; set; }
        public int[] workgroups { get; set; }
        public int[] actionUsers { get; set; }
        public string[] types { get; set; }

        /// <summary>
        /// Flag to export data of the KB version 2
        /// </summary>
        public bool exportNewKB { get; set; }

        public bool exportSignatures { get; set; }

        public bool isBatch { get; set; }
    }

    public class ExportUserSessionsDataViewModel
    {
        public int clientId { get; set; }
        public DateTime? startDate { get; set; }
        public DateTime? endDate { get; set; }
    }
}
