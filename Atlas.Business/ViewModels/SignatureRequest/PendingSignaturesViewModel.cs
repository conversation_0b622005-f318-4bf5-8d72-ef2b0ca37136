namespace Atlas.Business.ViewModels.SignatureRequest
{
    public class PendingSignaturesViewModel
    {
        public int contentId { get; set; }
        public string signatureType { get; set; }
        public int contentAttachmentId { get; set; }
        public int contentSignatureRequestId { get; set; }
        public string parentContentTitle { get; set; }
    }

    public class RawPendingSignaturesViewModel
    {
        public int contentSignatureRequestId { get; set; }
        public int contentId { get; set; }
        public string signatureType { get; set; }
        public int contentAttachmentId { get; set; }
        public string filename { get; set; }
        public string contentType { get; set; }
        public string contentTitle { get; set; }
        public string parentContentTitle { get; set; }
    }
}
