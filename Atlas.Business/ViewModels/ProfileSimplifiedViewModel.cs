using Newtonsoft.Json;

namespace Atlas.Business.ViewModels
{
    public class ProfileSimplifiedViewModel
    {
        public string email { get; set; }
        public string mobile { get; set; }
        public string name { get; set; }
        public int userId { get; set; }
        public int clientId { get; set; }
        public bool blocked { get; set; }
        public bool deleted { get; set; }
        public bool isMultiAdmin { get; internal set; }

        [JsonProperty(PropertyName = "isOwnerAnyWorkgroup", NullValueHandling = NullValueHandling.Ignore)]
        public bool isOwnerAnyWorkgroup { get; internal set; }
        public bool? ownerNotifyContentComment { get; set; }
        public string? userAgent { get; set; }
        public bool canEdit { get; set; }
    }
}
