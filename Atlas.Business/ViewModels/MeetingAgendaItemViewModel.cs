using System.Collections.Generic;

namespace Atlas.Business.ViewModels
{
    public class MeetingAgendaItemViewModel
    {

        public int contentId { get; set; }
        public string title { get; set; }

        public int time { get; set; }

        public int? itemOrder { get; set; }

        public string assignedUserPic { get; set; }

        public int assignedUserUserId { get; set; }

        public string assignedUserName { get; set; }

        public bool isAssignedUserDeleted { get; set; }

        public List<string> attachmentFileNames { get; set; }

        public int commentCount { get; set; }

        public bool restricted { get; set; }

        public string agendaItemType { get; set; }

    }
}
