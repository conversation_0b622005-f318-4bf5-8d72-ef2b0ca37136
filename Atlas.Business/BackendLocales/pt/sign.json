{"log": {"the": "o", "validateDocument": "Valide a autenticidade do documento clicando ou escaneando o QR Code ao lado ou acesse", "verifier": " verificador de autenticidade ", "insertCode": "e insira o código: ", "requestedBy": "Solicitação de assinatura iniciada por: ", "in": "em", "signatures": "Assinaturas", "signed": "<PERSON><PERSON><PERSON>", "signedWhen": "<PERSON><PERSON><PERSON> <PERSON>: ", "signedElectronic": "Assinou Eletronicamente", "signedDigital": "Assinou Digitalmente", "pending": "Assinatura Pendente", "dateTime": "Data e hora: ", "email": "E-mail: ", "ipAddress": "Endereço de IP: ", "2fa": "<PERSON><PERSON><PERSON> Autenticação: ", "device": "Dispositivo/Aplicativo: ", "mobile": "Celular: ", "signature": "Assinatura: ", "initials": "Rúbrica: ", "certSerialNumber": "Número de série do certificado: ", "certIssuer": "Emissora: ", "icpCertType": "Tipo: ", "documentHash": "Hash do documento (SHA256): ", "ntpNotice": "Os horários das assinaturas foram obtidos utilizando o protocolo NTP.", "page": "<PERSON><PERSON><PERSON><PERSON>", "nom151Title": "Documento e constância de conservação (NOM 151)", "nom151Notice": "Este documento é legalmente respaldado quanto à sua integridade, uma vez que incorpora um Certificado de Conservação emitido por um Prestador de Serviços de Certificação (PSC) devidamente credenciado pelo Ministério da Economia.", "nom151ConstancyIssuedBy": "Constância emitida por", "nom151ConstancyDate": "Data da Constância", "nom151ValidateConstancyAt": "Valide sua Constância em"}, "token": "Este é o seu código de verificação do Atlas Sign"}