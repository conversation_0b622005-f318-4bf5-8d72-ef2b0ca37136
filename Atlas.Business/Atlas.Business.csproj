<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <OutputType>Library</OutputType>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <UseWindowsForms>false</UseWindowsForms>
    <UseWPF>false</UseWPF>
    <ImportWindowsDesktopTargets>false</ImportWindowsDesktopTargets>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\</OutputPath>
  </PropertyGroup>
  <PropertyGroup>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
  </PropertyGroup>
  <ItemGroup>
    <Content Include="Licenses\production.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Licenses\development.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\check.png" />
    <Content Include="Resources\initialsFrame.png" />
    <None Update="BackendLocales\en\insurances.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="BackendLocales\es\insurances.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="BackendLocales\pt\insurances.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="dev.settings.json" />
    <Content Include="Resources\logo_green_v3.png" />
    <Content Include="Resources\padesSignatureFrame.png" />
    <Content Include="Resources\pending.png" />
    <Content Include="Resources\pending_new.png" />
    <Content Include="Resources\signatureFrame.png" />
    <Content Include="BackendLocales\en\BlueBook.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="BackendLocales\pt\BlueBook.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <None Update="BackendLocales\en\export.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="BackendLocales\en\pushnotification.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <Content Include="BackendLocales\es\BlueBook.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Update="BackendLocales\en\sign.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="BackendLocales\es\export.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="BackendLocales\es\pushnotification.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="BackendLocales\es\sign.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="BackendLocales\pt\export.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="BackendLocales\pt\pushnotification.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="BackendLocales\pt\sign.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Helpers\Search\" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AngleSharp.Css" Version="0.17.0" />
    <PackageReference Include="Azure.Messaging.ServiceBus" Version="7.18.3" />
    <PackageReference Include="Azure.Search.Documents" Version="11.6.0" />
    <PackageReference Include="BouncyCastle.Cryptography" Version="2.5.1" />
    <PackageReference Include="EntityFramework" Version="6.5.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authorization" Version="8.0.12" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
    <PackageReference Include="EPPlus" Version="4.5.3.3" />
    <PackageReference Include="GuerrillaNtp" Version="3.1.0" />
    <PackageReference Include="HtmlSanitizer" Version="9.0.876" />
    <PackageReference Include="itext7.licensekey" Version="3.1.6" />
    <!--<PackageReference Include="iTextSharp" Version="5.5.13.4" />-->
    <PackageReference Include="Lacuna.RestPki.Api" Version="2.3.0" />
    <PackageReference Include="Lacuna.RestPki.Client" Version="2.3.0" />
    <PackageReference Include="Magick.NET-Q16-AnyCPU" Version="14.4.0" />
    <PackageReference Include="Magick.NET.Core" Version="14.4.0" />
    <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.7.0" />
    <PackageReference Include="Azure.Security.KeyVault.Certificates" Version="4.7.0" />
    <PackageReference Include="Azure.Security.KeyVault.Keys" Version="4.7.0" />
    <PackageReference Include="Microsoft.AspNetCore.Routing" Version="2.3.0" />
    <PackageReference Include="Microsoft.Azure.NotificationHubs" Version="4.2.0" />
    <PackageReference Include="Microsoft.Azure.WebJobs" Version="3.0.41" />
    <PackageReference Include="Microsoft.Bcl.Build" Version="1.0.21" />
    <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
    <PackageReference Include="Microsoft.Extensions.FileProviders.Physical" Version="9.0.2" />
    <PackageReference Include="Microsoft.NETCore.Platforms" Version="7.0.4" />
    <PackageReference Include="Microsoft.NETCore.Targets" Version="5.0.0" />
    <PackageReference Include="Nager.Country.Translation" Version="4.1.0" />
    <PackageReference Include="PdfSharpCore" Version="1.3.67" />
    <!--<PackageReference Include="PDFsharp-MigraDoc-gdi" Version="6.1.1" />-->
    <PackageReference Include="QRCoder" Version="1.6.0" />
    <PackageReference Include="Sentry" Version="5.1.0" />
    <PackageReference Include="SharpZipLib" Version="1.4.2" />
    <PackageReference Include="System.Private.Uri" Version="4.3.2" />
    <PackageReference Include="System.Resources.Extensions" Version="9.0.1" />
    <PackageReference Include="System.Security.Cryptography.Xml" Version="9.0.1" />
    <PackageReference Include="System.Security.Permissions" Version="9.0.1" />
	<PackageReference Include="Twilio" Version="7.8.3" />
    <PackageReference Include="WindowsAzure.Storage" Version="9.3.3" />
    <PackageReference Include="MongoDB.Driver" Version="3.4.1" />
	<PackageReference Include="Mapster" Version="7.4.0" />
	<PackageReference Include="Mapster.DependencyInjection" Version="1.0.1" />
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="Helpers\InsuranceDocumentFormSchemas\SchemaHelperClasses\DebtsLoansAndCivilInsurance.cs" />
    <Compile Remove="Helpers\InsuranceDocumentFormSchemas\SchemaHelperClasses\EmployeesSectionV1.cs" />
    <Compile Remove="Helpers\SearchIndex.cs" />
    <Compile Remove="ReportImages.Designer.cs" />
    <EmbeddedResource Remove="ReportImages.resx" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Atlas.Data\Atlas.Data.csproj" />
    <ProjectReference Include="..\Atlas.SendGrid\Atlas.SendGrid.csproj" />
    <ProjectReference Include="..\Hexasoft.Zxcvbn\Hexasoft.Zxcvbn.csproj" />
    <ProjectReference Include="..\i18next-net\i18next-net.csproj" />
  </ItemGroup>
  <ProjectExtensions />
</Project>
