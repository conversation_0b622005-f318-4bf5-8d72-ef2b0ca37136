using Atlas.Business.Helpers;
using Atlas.CrossCutting.AppEnums;
using Atlas.Data.Entities;
using Atlas.Data.Repository;
using i18next_net;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using SharpRaven;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TimeZoneConverter;

namespace Atlas.Business
{
    public class PollService
    {
        public int _currentUser { get; }
        public PollService(int currentUser)
        {
            this._currentUser = currentUser;
        }

        //A. VOTE
        //  -Register vote -ok
        //  -Remove vote -ok
        //B. OPTION
        //  -Add
        //  -Delete
        //  -Update Title


        public async Task<object> VoteRegister(int contentId, int pollOptionId)
        {
            var result = new { success = false, msg = "" };
            AtlasModelCore _md = new AtlasModelCore();
            ContentService svc = new ContentService(_currentUser);

            var content = await svc.Get(contentId);

            if (content == null)
            {
                return new { success = false, msg = "CONTENT_NOT_FOUND" };
            }

            if (content.Workgroup.archived)
            {
                throw new System.Security.SecurityException("ARCHIVED_WORKGROUP");
            }


            var poll = content.Poll.FirstOrDefault();
            if (poll == null)
            {
                return new { success = false, msg = "POLL_OBJECT_NOT_FOUND" };
            }
            if (poll.hidden == true)
            {
                throw new InvalidOperationException("POLL_HIDDEN");
            }

            //==================================
            //1. check if user can vote
            //2. check if user has already voted
            //3. check if option is valid
            //4. register vote
            //5. register activity

            //1. check if user can vote
            //if (!content.UAC.can_vote_meeting_ready_votes_poll)
            //{
            //    return new { success = false, msg = "MEETING_NOT_READY" };
            //}

            if (!content.ContentSubscriber.Select(o => o.userId).Contains(_currentUser))
            {
                return new { success = false, msg = "USER_NOT_VOTER" };
            }

            //2. check if user has already voted
            if (poll.Votes.Where(o => o.deleted != true && o.userId == _currentUser).Any())
            {
                return new { success = false, msg = "USER_ALREADY_VOTED" };
            }

            //3. check if option is valid
            if (!poll.Options.Select(o => o.pollOptionId).Contains(pollOptionId))
            {
                return new { success = false, msg = "OPTION_INVALID" };
            }

            //--------------------------------
            //4. register vote
            //--------------------------------
            PollVote vote = new PollVote();
            vote.pollOptionId = pollOptionId;
            vote.userId = _currentUser;
            vote.pollId = poll.pollId;
            vote.voteDate = DateTime.UtcNow;
            vote.deleted = false;

            // Get the previous vote of the current user
            var lastVoteDate = _md.PollVote
                .Where(o => o.userId == _currentUser && o.pollId == poll.pollId && o.deleted == false)
                .OrderByDescending(o => o.voteDate)
                .Select(o => o.voteDate)
                .FirstOrDefault();

            if (lastVoteDate.HasValue)
            {
                // Compare the Datetime diff to check for duplicated votes (#746)
                TimeSpan diff = vote.voteDate.Value - lastVoteDate.Value;
                int milliseconds = (int)diff.TotalMilliseconds;
                if (milliseconds <= 200)
                {
                    return new { success = false, msg = "DUPLICATED_VOTES" };
                }
                return new { sucess = false, msg = "USER_ALREADY_VOTED" };
            }

            // _md.SaveChanges();

            _md.PollVote.Add(vote);

            bool success = false;
            try
            {
                success = await _md.SaveChangesAsync() > 0;
            }
            catch (Microsoft.EntityFrameworkCore.DbUpdateException)
            {
                // TODO: include additional verification?
                return new { success, msg = "USER_ALREADY_VOTED" };
            }

            if (success)
            {
                //5. register activity
                new ContentActivityService(_currentUser).Add(content.contentId, new ContentActivity()
                {
                    contentId = content.contentId,
                    date = DateTime.UtcNow,
                    processed = false,
                    type = Operations.POLL_VOTE_ADD,
                    activityUser = _currentUser,
                    subItemId = vote.voteId,
                    subItemType = SubItems.VOTE,
                    contentData = JsonConvert.SerializeObject(new { title = content.title }),

                });


                //if is last vote, close the poll
                //HOTFIX: reloading content
                var contentUpdated = await svc.Get(contentId);

                int[] registeredVotesUserIds = contentUpdated.Poll.FirstOrDefault()?.Votes.Where(v => v.deleted != true).Select(o => o.userId).Distinct().ToArray();

                // Bug 9716
                // When counting voters, we must take into account deleted voters who already made their vote
                var countOfVoters = contentUpdated.ContentSubscriber.Where(o => o.User.isDeleted == false || registeredVotesUserIds.Contains(o.userId))
                                                                    .Select(o => o.userId)
                                                                    .Distinct()
                                                                    .Count();

                var wasLastVote = (countOfVoters - registeredVotesUserIds.Count()) == 0;

                if (wasLastVote)
                {
                    var result_close = await svc.SetStep(contentId, true, null, null, true);
                }

                return new { success = true, msg = "OK", objectId = vote.voteId };

            }
            else
            {
                return new { success = false, msg = "OPTION_INVALID" };
            }
        }

        public async System.Threading.Tasks.Task<int> GenerateReport(int pollContentId, bool autoGenerated = true, bool withFooter = true)
        {


            try
            {


                ContentService contentService = new ContentService(_currentUser);

                Content pollContent = await contentService.Get(pollContentId);

                if (autoGenerated && pollContent.Poll.First().hidden == true)
                {
                    return -1;
                }

                // Checking poll status
                if (pollContent.status != "CLOSED" || pollContent.Poll.First().hidden == true)
                {
                    throw new Exception("INVALID_POLL");
                }


                FeatureManagerService fms = new FeatureManagerService(_currentUser);
                bool pollReportEnabled = await fms.isEnabledByContent(pollContent, PlanFeatureNames.POLL_REPORT);

                if (!pollReportEnabled)
                {
                    throw new InvalidOperationException("FEATURE_NOT_INCLUDED");
                }


                AtlasModelCore _md = new AtlasModelCore();
                User currentUserObj = _md.User.Where(u => u.userId == _currentUser).FirstOrDefault();

                PDFGenerator_v2 pdfGenerator = new PDFGenerator_v2(currentUserObj);

                byte[] reportBytes = null;

                //MATHEUS
                //reportBytes = await pdfGenerator.GeneratePollReport(pollContent, currentUserObj, withFooter);

                ContentAttachmentService contentAttachmentService = new ContentAttachmentService(_currentUser, pollContentId);

                string reportFileName = $"PollReport_{pollContentId}.pdf";
                var uploadResult = await contentAttachmentService.Add(
                    file: reportBytes,
                    filename: reportFileName,
                    extension: "pdf",
                    description: null,
                    contentId: pollContentId,
                    supressActivity: true,
                    isPollReport: true
                 );

                if (uploadResult.contentAttachmentId != 0)
                {

                    Poll poll = _md.Poll.Where(p => p.contentId == pollContentId).FirstOrDefault();

                    // Deleting old report, if it exists
                    if (poll.reportContentAttachmentId.HasValue)
                    {
                        AtlasModelCore _mdDelete = new AtlasModelCore();
                        ContentAttachment oldreport = _md.ContentAttachment.Where(o => o.contentAttachmentId == poll.reportContentAttachmentId).FirstOrDefault();
                        oldreport.deleted = true;
                        _mdDelete.Entry(oldreport).State = EntityState.Modified;
                        await _mdDelete.SaveChangesAsync();
                    }

                    poll.reportContentAttachmentId = uploadResult.contentAttachmentId;
                    _md.SaveChanges();


                    ContentActivityService contentActivityService = new ContentActivityService(_currentUser);

                    contentActivityService.Add(pollContentId, new ContentActivity()
                    {
                        date = DateTime.UtcNow,
                        type = autoGenerated ? Operations.POLL_REPORT_AUTO_GENERATED : Operations.POLL_REPORT_GENERATED,
                        activityUser = _currentUser,
                        contentData = JsonConvert.SerializeObject(new
                        {
                            contentId = pollContentId
                        })
                    });

                    return uploadResult.contentAttachmentId;

                }
                else
                {
                    return -1;
                }

            }
            catch (Exception e)
            {

                var ravenClient = new RavenClient("https://6aa2f8bcac3e41e198dc414d24d19e45:<EMAIL>/162539");
                var sentryEvent = new SharpRaven.Data.SentryEvent(new SharpRaven.Data.SentryMessage($"Error during poll report generation: {pollContentId} - autogenerated?: {autoGenerated} "));
                sentryEvent.Extra = new
                {
                    e
                };
                ravenClient.Capture(sentryEvent);

                return -1;

            }


        }

        public async Task<object> GetReport(int pollContentId, bool isDownload = false)
        {

            try
            {

                // Live report generation
                // This will generate a "live" report, respecting the user language
                // This is necessary because the report generated when the poll is closed is used for signing and
                // is one language only (the language of the closure user)
                ContentService contentService = new ContentService(_currentUser);

                Content pollContent = await contentService.Get(pollContentId);

                Poll poll = pollContent.Poll.FirstOrDefault();

                // Checking poll status
                if (poll == null || pollContent.status != "CLOSED" || poll.hidden == true)
                {
                    throw new Exception("INVALID_POLL");
                }


                if (!poll.reportContentAttachmentId.HasValue)
                {
                    throw new Exception("INVALID_POLL_REPORT");
                }


                FeatureManagerService fms = new FeatureManagerService(_currentUser);
                bool pollReportEnabled = await fms.isEnabledByContent(pollContent, PlanFeatureNames.POLL_REPORT);

                if (!pollReportEnabled)
                {
                    throw new InvalidOperationException("FEATURE_NOT_INCLUDED");
                }

                // Locale/Watermark treatment
                AtlasModelCore _md = new AtlasModelCore();
                User currentUserObj = _md.User.Where(u => u.userId == _currentUser).FirstOrDefault();

                string db_fuso = string.IsNullOrWhiteSpace(currentUserObj.defaultTimezone) ? "America/Sao_Paulo" : currentUserObj.defaultTimezone;
                string tz = "";

                try
                {
                    tz = TZConvert.IanaToWindows(db_fuso);

                }
                catch (InvalidTimeZoneException)
                {
                    tz = "E. South America Standard Time";
                }


                TimeZoneInfo userZone = TimeZoneInfo.FindSystemTimeZoneById(tz);

                DateTime utcNow = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, userZone);

                var culture = new System.Globalization.CultureInfo("pt-BR");

                i18next_net.i18next i18n = new i18next_net.i18next(new i18next_net.InitOptions()
                {
                    defaultNS = "common",
                    localeFileType = LocaleFileTypeEnum.Path,
                    fallbackLng = "en"
                });

                var langCode = currentUserObj.defaultLanguage ?? "pt".ToLower();

                i18n.changeLanguage(langCode);

                switch (langCode)
                {
                    case "pt":
                        culture = new System.Globalization.CultureInfo("pt-BR");
                        break;
                    case "en":
                        culture = new System.Globalization.CultureInfo("en-US");
                        break;
                    case "es":
                        culture = new System.Globalization.CultureInfo("es-ES");
                        break;
                    default:
                        break;
                }

                string utcNowCultureFormat = utcNow.ToString(culture);

                string watermarkText = currentUserObj.name + " " + utcNowCultureFormat;
                string watermarkEmail = currentUserObj.email;


                PDFGenerator_v2 pdfGenerator = new PDFGenerator_v2(currentUserObj);
                //MATHEUS
                //byte[] reportBytes = await pdfGenerator.GeneratePollReport(pollContent, currentUserObj);

                //MATHEUS
                //byte[] reportBytesWithWatermark = pdfGenerator.WriteWatermarkAllPages(reportBytes, watermarkText, watermarkEmail, paging: false);

                string reportFileName = $"PollReport_{pollContentId}.pdf";

                AzureHelper azureHelper = new AzureHelper();

                //MATHEUS de reportBytesWithWatermark para new byte
                var uploadResult = await azureHelper.UploadFromByteArray(new byte[1], reportFileName, false);


                if (uploadResult.downloadKey != null)
                {

                    ContentActivityService contentActivityService = new ContentActivityService(_currentUser);
                    contentActivityService.Add(pollContentId, new ContentActivity()
                    {
                        date = DateTime.UtcNow,
                        type = isDownload ? Operations.POLL_REPORT_DOWNLOAD : Operations.POLL_REPORT_VIEW,
                        activityUser = _currentUser,
                        contentData = JsonConvert.SerializeObject(new
                        {
                            contentId = pollContentId
                        })
                    });


                    string url = await azureHelper.GetFileUrl(uploadResult.downloadKey);

                    bool blockFileDownload = false;


                    if (pollContent.Workgroup != null && pollContent.Workgroup.Client != null)
                    {
                        blockFileDownload = pollContent.Workgroup.Client.blockFileDownload;

                    }


                    return new { success = true, url = url, blockFileDownload = blockFileDownload };

                }
                else
                {

                    throw new Exception("REPORT_GENERATION_FAILED");

                }

            }
            catch (Exception e)
            {
                var ravenClient = new RavenClient("https://6aa2f8bcac3e41e198dc414d24d19e45:<EMAIL>/162539");
                var sentryEvent = new SharpRaven.Data.SentryEvent(new SharpRaven.Data.SentryMessage($"Error during live poll report generation: {pollContentId}"));
                sentryEvent.Extra = new
                {
                    e
                };
                ravenClient.Capture(sentryEvent);

                throw e;
            }



        }

        public async Task<object> Unvote(int contentId, int voteId)
        {
            var result = new { success = false, msg = "" };
            AtlasModelCore _md = new AtlasModelCore();
            ContentRepository _repo = new ContentRepository(_currentUser);
            var content = await _repo.Get(contentId);

            if (content == null)
            {
                return new { success = false, msg = "CONTENT_NOT_FOUND" };
            }

            if (content.Workgroup.archived)
            {
                throw new System.Security.SecurityException("ARCHIVED_WORKGROUP");
            }

            var poll = content.Poll.FirstOrDefault();
            if (poll == null)
            {
                return new { success = false, msg = "POLL_OBJECT_NOT_FOUND" };
            }

            if (content.status == "CLOSED" || content.status == "CANCELLED")
            {
                throw new Exception("CLOSED_POLL_CANNOT_UNVOTE");
            }

            // 1. check if user is allowed to perform unvoting
            if (!content.ContentSubscriber.Select(o => o.userId).Contains(_currentUser))
            {
                return new { success = false, msg = "USER_NOT_VOTER" };
            }

            // 2. test if voteId exists
            PollVote pollVote;
            try
            {
                pollVote = poll.Votes.Single(v => v.voteId == voteId && v.userId == _currentUser);
            }
            catch (Exception)
            {
                return new { success = false, msg = "POLLVOTE_NOT_FOUND" };
            }

            if (pollVote.deleted.HasValue && !pollVote.deleted.Value)
            {
                pollVote.deleted = true;
                pollVote.deleteUser = _currentUser;
                pollVote.deleteDate = DateTime.UtcNow;
                _md.Entry(pollVote).State = EntityState.Modified;

                if (await _md.SaveChangesAsync() > 0)
                {
                    // 3. register activity
                    new ContentActivityService(_currentUser).Add(content.contentId, new ContentActivity()
                    {
                        contentId = content.contentId,
                        date = DateTime.UtcNow,
                        processed = false,
                        type = Operations.POLL_UNDO_VOTE,
                        activityUser = _currentUser,
                        subItemType = SubItems.VOTE,
                        subItemId = voteId,
                        contentData = JsonConvert.SerializeObject(new { content.title }),
                    });

                    return new { success = true, msg = "OK" };
                }
                else
                {
                    return new { success = false, msg = "SAVECHANGESASYNC_FAILED" };
                }
            }

            return new { success = false, msg = "UNVOTE_ALREADY_DONE" };
        }

        public async Task<object> VoteRemove(int contentId)
        {
            var result = new { success = false, msg = "" };


            AtlasModelCore _md = new AtlasModelCore();

            ContentRepository _repo = new ContentRepository(_currentUser);

            var content = await _repo.Get(contentId);



            if (content == null)
            {
                return new { success = false, msg = "CONTENT_NOT_FOUND" };
            }

            if (content.Workgroup.archived)
            {
                throw new System.Security.SecurityException("ARCHIVED_WORKGROUP");
            }

            if (content.status == "CLOSED" || content.status == "CANCELLED")
            {
                throw new Exception("CLOSED_POLL_CANNOT_UNVOTE");
            }

            var poll = content.Poll.FirstOrDefault();
            if (poll == null)
            {
                return new { success = false, msg = "POLL_OBJECT_NOT_FOUND" };

            }
            var vote = await _md.PollVote.Where(o => o.userId == _currentUser && o.pollId == poll.pollId).FirstOrDefaultAsync();

            //==================================
            //1. check if voted
            if (vote == null)
            {
                return new { success = false, msg = "VOTE_OBJECT_NOT_FOUND" };
            }

            //2. remove
            _md.PollVote.Remove(vote);


            if (await _md.SaveChangesAsync() > 0)
            {
                //5. register activity
                new ContentActivityService(_currentUser).Add(content.contentId, new ContentActivity()
                {
                    contentId = content.contentId,
                    date = DateTime.UtcNow,
                    processed = false,
                    type = Operations.POLL_VOTE_REMOVE,
                    activityUser = _currentUser

                });

                return new { success = true, msg = "OK" };

            }
            else
            {
                return new { success = false, msg = "SaveChangesAsync_ERROR" };
            }

        }

        public async Task<object> OptionAdd(int contentId, string title)
        {
            AtlasModelCore _md = new AtlasModelCore();
            ContentRepository _repo = new ContentRepository(_currentUser);

            var content = await _repo.Get(contentId);

            if (content == null)
            {
                return new { success = false, msg = "CONTENT_NOT_FOUND." };
            }

            var poll = content.Poll.FirstOrDefault();
            if (poll == null)
            {
                return new { success = false, msg = "POLL_OBJECT_NOT_FOUND" };

            }
            if (poll.pollType != "CUSTOM")
            {
                return new { success = false, msg = "POLL_OBJECT_NOT_FOUND" };

            }

            int currentLastItemOrder = poll.Options.Select(o => o.itemOrder).Max();

            PollOption opt = new PollOption();
            opt.pollId = poll.pollId;
            opt.title = title;
            opt.itemOrder = currentLastItemOrder + 1;

            _md.PollOption.Add(opt);

            if (await _md.SaveChangesAsync() > 0)
            {
                return new { success = true, msg = "OK" };

            }
            else
            {
                return new { success = false, msg = "SAVECHANGESASYNC_FAILED" };

            }
        }
        public async Task<object> OptionUpdate(int contentId, int pollOptionId, string newtitle, int? order = null)
        {
            AtlasModelCore _md = new AtlasModelCore();
            ContentRepository _repo = new ContentRepository(_currentUser);

            var content = await _repo.Get(contentId);

            if (content == null)
            {
                return new { success = false, msg = "CONTENT_NOT_FOUND." };
            }

            var poll = content.Poll.FirstOrDefault();
            if (poll == null)
            {
                return new { success = false, msg = "POLL_OBJECT_NOT_FOUND" };

            }
            PollOption opt = await _md.PollOption.Where(o => o.pollOptionId == pollOptionId && o.pollId == poll.pollId).FirstOrDefaultAsync();

            if (!string.IsNullOrEmpty(newtitle))
            {
                opt.title = newtitle;
            }

            if (order.HasValue)
            {
                opt.itemOrder = order.Value;
            }

            _md.Entry(opt).State = EntityState.Modified;

            if (await _md.SaveChangesAsync() > 0)
            {
                return new { success = true, msg = "OK" };

            }
            else
            {
                return new { success = false, msg = "SAVECHANGESASYNC_FAILED" };

            }
        }
        public async Task<object> OptionRemove(int contentId, int pollOptionId, bool? forceDelete = false)
        {
            var result = new { success = false, msg = "" };


            //--------------------------------------------------------------
            //1.Check if option if from the given contentId
            //2. Check if option has votes and forceDelete = false
            //3. delete it

            AtlasModelCore _md = new AtlasModelCore();
            ContentRepository _repo = new ContentRepository(_currentUser);

            var content = await _repo.Get(contentId);

            if (content == null)
            {
                return new { success = false, msg = "CONTENT_NOT_FOUND." };
            }

            var poll = content.Poll.FirstOrDefault();
            if (poll == null)
            {
                return new { success = false, msg = "POLL_OBJECT_NOT_FOUND" };

            }

            //1.Check if option if from the given contentId
            if (!poll.Options.Select(o => o.pollOptionId).Contains(pollOptionId))
            {
                return new { success = false, msg = "POLL_OPTION_INVALID" };

            }

            //2. Check if option has votes and forceDelete = false
            if (poll.Votes.Select(o => o.pollOptionId).Contains(pollOptionId) && forceDelete == false)
            {
                return new { success = false, msg = "OPTION_HAS_VOTE" };

            }

            //3. delete it
            if (forceDelete.Value)
            {
                var votesToDelete = await _md.PollVote.Where(o => o.pollOptionId == pollOptionId).ToListAsync();

                _md.PollVote.RemoveRange(votesToDelete);
            }

            var optionToDelete = await _md.PollOption.Where(o => o.pollOptionId == pollOptionId).FirstOrDefaultAsync();
            _md.PollOption.Remove(optionToDelete);

            if (await _md.SaveChangesAsync() > 0)
            {
                return new { success = true, msg = "OK" };

            }
            else
            {
                return new { success = false, msg = "SAVECHANGESASYNC_FAILED" };

            }
        }

        public static string GetWinningOption(Poll poll)
        {
            int topVotedCount = 0;
            PollOption topVotedOption = null;
            List<int> tiedOptions = new List<int>();


            foreach (PollOption option in poll.Options)
            {
                if (poll.pollType == "APPROVAL" && option.title == "ABSTAIN")
                {
                    continue;
                }

                if (option.Votes.Count(v => !v.deleted.GetValueOrDefault()) > topVotedCount)
                {
                    topVotedOption = option;
                    topVotedCount = option.Votes.Count(v => !v.deleted.GetValueOrDefault());
                }
            }

            foreach (PollOption option in poll.Options)
            {

                if (poll.pollType == "APPROVAL" && option.title == "ABSTAIN")
                {
                    continue;
                }

                if (option.Votes.Count(v => !v.deleted.GetValueOrDefault()) == topVotedCount)
                {
                    tiedOptions.Add(option.pollOptionId);
                }
            }

            if (tiedOptions.Count == 1)
            {
                return topVotedOption.title;
            }
            else
            {
                return "TIE";
            }
        }
    }
}
