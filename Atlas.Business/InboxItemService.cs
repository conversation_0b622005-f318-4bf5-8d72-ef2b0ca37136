using Atlas.Business.ViewModels;
using Atlas.Data.Abstract;
using Atlas.Data.Entities;
using Atlas.Data.Enums;
using Atlas.Data.Repository;
using Newtonsoft.Json;
using System;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace Atlas.Business
{
    /// <summary>
    /// Implements IInboxItemService to manage InboxItem objects
    /// </summary>
    public class InboxItemService : IInboxItemService
    {
        private readonly IRepository<InboxItem> _itemRepository;
        private readonly IRepository<InboxItemReceiver> _receiverRepository;
        private readonly UserRepository _userRepository;
        private readonly UserDeviceService _userDeviceService;
        /// <summary>
        /// Creates a new instance of InboxItemService.
        /// </summary>
        /// <param name="currentUserId">The current user Id. All related operations will be done with this user</param>
        /// <param name="itemRep">InboxItem repository instance</param>
        /// <param name="receiverRep">InboxItemReceiver repository instance</param>
        /// <param name="userRepository">UserRepository repository instance</param>
        /// <param name="userDeviceService">UserDeviceService service instance</param>
        public InboxItemService(IRepository<InboxItem> itemRep, IRepository<InboxItemReceiver> receiverRep,
            UserRepository userRepository, UserDeviceService userDeviceService)
        {
            _itemRepository = itemRep;
            _receiverRepository = receiverRep;
            _userRepository = userRepository;
            _userDeviceService = userDeviceService;
        }
        /// <summary>
        /// Creates a new instance of InboxItemService.
        /// </summary>
        /// <param name="userId">The current user Id. All related operations will be done with this user</param>
        public InboxItemService()
        {
            _itemRepository = new InboxItemRepository(new AtlasModelCore());
            _receiverRepository = new InboxItemReceiverRepository(new AtlasModelCore());
            _userRepository = new UserRepository();
            _userDeviceService = new UserDeviceService();
        }
        /// <summary>
        /// Creates a new entry in InboxItem table with given parameters.
        /// </summary>
        /// <param name="itemType">Type of InboxItem. See InboxItemType enum for all types</param>
        /// <param name="targetObjectType">Type of TargetObject from InboxItem. See InboxItemTargetObjectType enum for all types</param>
        /// <param name="targetObjectId">Id of the TargetObject</param>
        /// <param name="contentData">Serialized data from InboxItem. Can be used in frontend to build InboxItem visual component</param>
        /// <returns>An InboxItem object if operation succeeds, null if operation fails</returns>
        public InboxItem CreateInboxItem(InboxItemType itemType, InboxItemTargetObjectType targetObjectType, string targetObjectId, string contentData)
        {
            //Checks if an InboxItem register with the same properties already exists
            var existentInboxItem = _itemRepository.GetAll(x => x.inboxItemType == itemType.ToString() &&
            x.targetObjectType == targetObjectType.ToString() && x.targetObjectId == targetObjectId, null,
            "InboxItemReceivers").FirstOrDefault();

            //If an InboxItem register already exists, checks if it has signed receivers, then returns the existent InboxItem if it has no receivers. 
            //This is useful when InboxItem register already exists but by some reason it has no registered receivers (cause the 
            //normal behavior is to delete the InboxItem register when all receivers dismissed the item [See CheckInboxItemWithNoReceiver
            //and ReceiverDismissInboxItem methods])
            if (existentInboxItem != null)
            {
                return existentInboxItem;
            }

            //Creating a new InboxItem register
            var newInboxItem = new InboxItem
            {
                inboxItemId = Guid.NewGuid(),
                inboxItemType = itemType.ToString(),
                targetObjectType = targetObjectType.ToString(),
                targetObjectId = targetObjectId,
                contentData = contentData ?? ""
            };
            _itemRepository.Insert(newInboxItem);
            _itemRepository.Commit();
            return newInboxItem;
        }
        /// <summary>
        /// Creates a new entry in InboxItem table with given parameters.
        /// </summary>
        /// <param name="itemType">Type of InboxItem. See InboxItemType enum for all types</param>
        /// <param name="targetObjectType">Type of TargetObject from InboxItem. See InboxItemTargetObjectType enum for all types</param>
        /// <param name="targetObjectId">Id of the TargetObject</param>
        /// <param name="contentData">Serialized data from InboxItem. Can be used in frontend to build InboxItem visual component</param>
        /// <returns>An InboxItem object if operation succeeds, null if operation fails</returns>
        public async Task<InboxItem> CreateInboxItemAsync(InboxItemType itemType, InboxItemTargetObjectType targetObjectType, string targetObjectId, string contentData)
        {
            //Checks if an InboxItem register with the same properties already exists
            var existentInboxItem = (await _itemRepository.GetAllAsync(x => x.inboxItemType == itemType.ToString() &&
            x.targetObjectType == targetObjectType.ToString() && x.targetObjectId == targetObjectId, null,
            "InboxItemReceivers")).FirstOrDefault();

            //If an InboxItem register already exists, checks if it has signed receivers, then returns the existent InboxItem if it has no receivers. 
            //This is useful when InboxItem register already exists but by some reason it has no registered receivers (cause the 
            //normal behavior is to delete the InboxItem register when all receivers dismissed the item [See CheckInboxItemWithNoReceiver
            //and ReceiverDismissInboxItem methods])
            if (existentInboxItem != null)
            {
                return existentInboxItem;
            }

            //Creating a new InboxItem register
            var newInboxItem = new InboxItem
            {
                inboxItemId = Guid.NewGuid(),
                inboxItemType = itemType.ToString(),
                targetObjectType = targetObjectType.ToString(),
                targetObjectId = targetObjectId,
                contentData = contentData ?? ""
            };
            _itemRepository.Insert(newInboxItem);
            await _itemRepository.CommitAsync();
            return newInboxItem;
        }
        /// <summary>
        /// Creates a new InboxItem of PendingAccessUserBlocked type, and it's receivers entries.
        /// </summary>
        /// <param name="blockedUserId">Blocked user's Id</param>
        /// <returns>An InboxItem object if operation succeeds, null if operation fails</returns>
        public InboxItem CreatePendingAccessUserBlocked(int blockedUserId)
        {

            var blockedUser = _userRepository.GetProfile(blockedUserId);

            var inboxItem = CreateInboxItem(InboxItemType.PendingAccessUserBlocked, InboxItemTargetObjectType.User,
                blockedUser.userId.ToString(), null);

            if (inboxItem == null)
            {
                return null;
            }

            var contentData = new InboxOutboxViewModel()
            {
                inboxItemId = inboxItem.inboxItemId,
                pendingAccessClientId = blockedUser.clientId,
                pendingAccessUserId = blockedUser.userId,
                pendingAccessUserName = blockedUser.name,
                pendingAccessEmail = blockedUser.email,
                pendingAccessBlocked = blockedUser.blocked,
                pendingAccessProfilePic = blockedUser.profilePic,
                pendingAccessRequireApproval = blockedUser.requireApproval,
                type = "PendingUserAccess",
                clientName = blockedUser.Client.name
            };
            var serializedContentData = JsonConvert.SerializeObject(contentData);


            var updateResult = UpdateInboxItem(inboxItem.inboxItemId, serializedContentData);
            CreateInboxItemReceivers(updateResult.inboxItemId);
            return updateResult;
        }
        /// <summary>
        /// Creates a new InboxItem of PendingAccessUserBlocked type, and it's receivers entries.
        /// </summary>
        /// <param name="blockedUserId">Blocked user's Id</param>
        /// <returns>An InboxItem object if operation succeeds, null if operation fails</returns>
        public async Task<InboxItem> CreatePendingAccessUserBlockedAsync(int blockedUserId)
        {

            var updateResult = await InsertPendingAccessUserBlockedEntryAsync(blockedUserId);
            await CreateInboxItemReceiversAsync(updateResult.inboxItemId);
            return updateResult;
        }
        /// <summary>
        /// Creates a new InboxItem of PendingAccessUserBlocked type, and it's receivers entries.
        /// </summary>
        /// <param name="blockedUserId">Blocked user's Id</param>
        /// <returns>An InboxItem object if operation succeeds, null if operation fails</returns>
        public async Task<InboxItem> CreatePendingAccessUserBlockedAsync(int blockedUserId, int receiverId)
        {

            var inboxItem = await InsertPendingAccessUserBlockedEntryAsync(blockedUserId);
            if (inboxItem == null)
            {
                return null;
            }
            await CreateReceiverEntry(inboxItem.inboxItemId, receiverId);
            return inboxItem;
        }
        private async Task<InboxItem> InsertPendingAccessUserBlockedEntryAsync(int blockedUserId)
        {
            var blockedUser = _userRepository.GetProfile(blockedUserId);

            var inboxItem = await CreateInboxItemAsync(InboxItemType.PendingAccessUserBlocked, InboxItemTargetObjectType.User,
                blockedUser.userId.ToString(), null);

            if (inboxItem == null)
            {
                return null;
            }

            var contentData = new InboxOutboxViewModel()
            {
                inboxItemId = inboxItem.inboxItemId,
                pendingAccessClientId = blockedUser.clientId,
                pendingAccessUserId = blockedUser.userId,
                pendingAccessUserName = blockedUser.name,
                pendingAccessEmail = blockedUser.email,
                pendingAccessBlocked = blockedUser.blocked,
                pendingAccessProfilePic = blockedUser.profilePic,
                pendingAccessRequireApproval = blockedUser.requireApproval,
                type = "PendingUserAccess",
                clientName = blockedUser.Client.name
            };
            var serializedContentData = JsonConvert.SerializeObject(contentData);


            var updateResult = await UpdateInboxItemAsync(inboxItem.inboxItemId, serializedContentData);
            return updateResult;
        }
        /// <summary>
        /// Creates a new InboxItem of PendingAccessUserApproval type, and it's receivers entries.
        /// </summary>
        /// <param name="approvalUserId">Id of the user to approve</param>
        /// <returns>An InboxItem object if operation succeeds, null if operation fails</returns>
        public async Task<InboxItem> CreatePendingAccessUserApprovalAsync(int approvalUserId)
        {
            var inboxItem = await InsertPendingAccessUserApprovalEntryAsync(approvalUserId);

            await CreateInboxItemReceiversAsync(inboxItem.inboxItemId);
            return inboxItem;
        }
        /// <summary>
        /// Creates a new InboxItem of PendingAccessUserApproval type, and it's receivers entries.
        /// </summary>
        /// <param name="approvalUserId">Id of the user to approve</param>
        /// <returns>An InboxItem object if operation succeeds, null if operation fails</returns>
        public async Task<InboxItem> CreatePendingAccessUserApprovalAsync(int approvalUserId, int receiverId)
        {
            var inboxItem = await InsertPendingAccessUserApprovalEntryAsync(approvalUserId);

            await CreateReceiverEntry(inboxItem.inboxItemId, receiverId);
            return inboxItem;
        }
        private async Task<InboxItem> InsertPendingAccessUserApprovalEntryAsync(int approvalUserId)
        {
            var approvalUser = _userRepository.GetProfile(approvalUserId);

            var inboxItem = await CreateInboxItemAsync(InboxItemType.PendingAccessUserApproval, InboxItemTargetObjectType.User,
                approvalUser.userId.ToString(), null);

            if (inboxItem == null)
            {
                return null;
            }

            var contentData = new InboxOutboxViewModel()
            {
                inboxItemId = inboxItem.inboxItemId,
                pendingAccessClientId = approvalUser.clientId,
                pendingAccessUserId = approvalUser.userId,
                pendingAccessUserName = approvalUser.name,
                pendingAccessEmail = approvalUser.email,
                pendingAccessBlocked = approvalUser.blocked,
                pendingAccessProfilePic = approvalUser.profilePic,
                pendingAccessRequireApproval = approvalUser.requireApproval,
                type = "PendingUserAccess",
                clientName = approvalUser.Client.name
            };
            var serializedContentData = JsonConvert.SerializeObject(contentData);


            var updateResult = await UpdateInboxItemAsync(inboxItem.inboxItemId, serializedContentData);
            return updateResult;
        }
        /// <summary>
        /// Creates a new InboxItem of PendingAccessDeviceApproval type, and it's receivers entries.
        /// </summary>
        /// <param name="approvalDeviceId">Id of the device to approve</param>
        /// <returns>An InboxItem object if operation succeeds, null if operation fails</returns>
        public async Task<InboxItem> CreatePendingAccessDeviceApprovalAsync(int approvalDeviceId)
        {

            var inboxItem = await InsertPendingAccessDeviceApproval(approvalDeviceId);

            await CreateInboxItemReceiversAsync(inboxItem.inboxItemId);
            return inboxItem;
        }
        /// <summary>
        /// Creates a new InboxItem of PendingAccessDeviceApproval type, and it's receivers entries.
        /// </summary>
        /// <param name="approvalDeviceId">Id of the device to approve</param>
        /// <returns>An InboxItem object if operation succeeds, null if operation fails</returns>
        public async Task<InboxItem> CreatePendingAccessDeviceApprovalAsync(int approvalDeviceId, int receiverId)
        {

            var inboxItem = await InsertPendingAccessDeviceApproval(approvalDeviceId);

            await CreateReceiverEntry(inboxItem.inboxItemId, receiverId);
            return inboxItem;
        }
        private async Task<InboxItem> InsertPendingAccessDeviceApproval(int approvalDeviceId)
        {
            var approvalDevice = await _userDeviceService.Get(approvalDeviceId);

            if (approvalDevice == null)
            {
                return null;
            }
            var user = _userRepository.GetProfile(approvalDevice.userId);
            if (user == null)
            {
                return null;
            }

            var inboxItem = await CreateInboxItemAsync(InboxItemType.PendingAccessDeviceApproval, InboxItemTargetObjectType.Device,
                approvalDevice.deviceEntryId.ToString(), null);

            if (inboxItem == null)
            {
                return null;
            }

            var contentData = new InboxOutboxViewModel()
            {
                inboxItemId = inboxItem.inboxItemId,
                pendingAccessDeviceId = approvalDevice.deviceEntryId,
                pendingAccessUserId = approvalDevice.userId,
                pendingAccessUserName = user.name,
                pendingAccessDeviceName = approvalDevice.name,
                pendingAccessDate = approvalDevice.date,
                pendingAccessClientId = user.clientId,


                type = "PendingDeviceAccess",
                clientName = user.Client.name
            };

            var serializedContentData = JsonConvert.SerializeObject(contentData);

            var updateResult = await UpdateInboxItemAsync(inboxItem.inboxItemId, serializedContentData);
            return updateResult;
        }
        /// <summary>
        /// Creates receiver entries to all admin users of InboxItem's target object Client. 
        /// </summary>
        /// <param name="inboxItemId">Id of the InboxItem</param>
        /// <returns>True if operation succeeds, false if operation fails</returns>
        public bool CreateInboxItemReceivers(Guid inboxItemId)
        {
            //Obtaining InboxItem
            var inboxItem = _itemRepository.GetAll(x => x.inboxItemId == inboxItemId, null, "InboxItemReceivers")
                .FirstOrDefault();
            //Validating InboxItem
            if (inboxItem != null)
            {
                //getting clientIds
                var clientIdArray = new int?[] { };
                //If targetObject is a user
                if (inboxItem.targetObjectType == InboxItemTargetObjectType.User.ToString())
                {
                    var user = _userRepository.GetProfile(Convert.ToInt32(inboxItem.targetObjectId));
                    if (user == null)
                    {
                        return false;
                    }

                    clientIdArray = GetUserClientIdList(user);
                }
                //If targetObject is a device
                else if (inboxItem.targetObjectType == InboxItemTargetObjectType.Device.ToString())
                {
                    var device = _userDeviceService.Get(Convert.ToInt32(inboxItem.targetObjectId)).Result;
                    if (device == null)
                    {
                        return false;
                    }
                    var user = _userRepository.GetProfile(device.userId);
                    if (user == null)
                    {
                        return false;
                    }
                    clientIdArray = GetUserClientIdList(user);
                }

                foreach (var clientId in clientIdArray)
                {
                    //Obtaining client admin users, which will be the receivers of the InboxItem
                    DefineClientAdminsAsReceivers(inboxItem, clientId);
                }

                return true;
            }

            return false;
        }
        private void DefineClientAdminsAsReceivers(InboxItem inboxItem, int? clientId)
        {
            var receiverList = _userRepository.GetAdministrators((int)clientId);
            //Verifying if receiver's register already exists; if not, creates an entry
            foreach (var receiver in receiverList)
            {
                var checkExistingReceiver = _receiverRepository.GetAll(x => x.inboxItemId == inboxItem.inboxItemId &&
                x.receiverUserId == receiver.userId).Any();

                if (!checkExistingReceiver)
                {
                    var newReceiver = new InboxItemReceiver
                    {
                        inboxItemReceiverId = Guid.NewGuid(),
                        inboxItemId = inboxItem.inboxItemId,
                        receiverUserId = receiver.userId
                    };
                    _receiverRepository.Insert(newReceiver);
                    //Commit changes in db
                    _receiverRepository.Commit();
                }

            }
        }
        /// <summary>
        /// Creates receiver entries to all admin users of InboxItem's target object Client. 
        /// </summary>
        /// <param name="inboxItemId">Id of the InboxItem</param>
        /// <returns>True if operation succeeds, false if operation fails</returns>
        public async Task<bool> CreateInboxItemReceiversAsync(Guid inboxItemId)
        {
            InboxItem inboxItem = await GetInboxItem(inboxItemId);
            //Validating InboxItem
            if (inboxItem == null)
            {
                return false;
            }
            //getting clientIds
            var clientIdArray = new int?[] { };
            //If targetObject is a user
            if (inboxItem.targetObjectType == InboxItemTargetObjectType.User.ToString())
            {
                var user = _userRepository.GetProfile(Convert.ToInt32(inboxItem.targetObjectId));
                if (user == null)
                {
                    return false;
                }
                clientIdArray = GetUserClientIdList(user);

            }
            //If targetObject is a device
            else if (inboxItem.targetObjectType == InboxItemTargetObjectType.Device.ToString())
            {
                var device = await _userDeviceService.Get(Convert.ToInt32(inboxItem.targetObjectId));
                if (device == null)
                {
                    return false;
                }
                var user = _userRepository.GetProfile(device.userId);
                if (user == null)
                {
                    return false;
                }
                clientIdArray = GetUserClientIdList(user, InboxItemTargetObjectType.Device);
            }

            foreach (var clientId in clientIdArray)
            {
                //Obtaining client admin users, which will be the receivers of the InboxItem
                await DefineClientAdminsAsReceiversAsync(inboxItem.inboxItemId, clientId);
            }

            return true;
        }
        private static int?[] GetUserClientIdList(User user, InboxItemTargetObjectType? targetType = null)
        {
            int?[] clientIdArray;
            var userRoles = new UserRoleRepository(user.userId).GetUserRolesDetails(user.userId);

            if (targetType.HasValue && targetType.Value == InboxItemTargetObjectType.Device)
            {

                userRoles = userRoles.Where(ur => ur.Client.deviceApprovalAddOn.HasValue &&
                                                          ur.Client.deviceApprovalAddOn.Value == true &&
                                                          ur.Client.deviceApprovalEnabled).ToList();
            }

            clientIdArray = userRoles.Any() ? userRoles.Where(x => x.roleId == 1002).Select(s => s.clientId).ToArray()
                    : new int?[] { user.clientId };
            return clientIdArray;
        }
        private async Task<InboxItem> GetInboxItem(Guid inboxItemId)
        {
            //Obtaining InboxItem
            return (await _itemRepository.GetAllAsync(x => x.inboxItemId == inboxItemId, null, "InboxItemReceivers"))
                .FirstOrDefault();
        }
        private async System.Threading.Tasks.Task DefineClientAdminsAsReceiversAsync(Guid inboxItemId, int? clientId)
        {
            var receiverList = _userRepository.GetAdministrators((int)clientId);
            //Verifying if receiver's register already exists; if not, creates an entry
            foreach (var receiver in receiverList)
            {
                await CreateReceiverEntry(inboxItemId, receiver.userId);

            }
        }
        private async System.Threading.Tasks.Task CreateReceiverEntry(Guid inboxItemId, int receiverUserId)
        {
            var checkExistingReceiver = (await _receiverRepository.GetAllAsync(x => x.inboxItemId == inboxItemId &&
            x.receiverUserId == receiverUserId)).Any();

            if (!checkExistingReceiver)
            {
                var newReceiver = new InboxItemReceiver
                {
                    inboxItemReceiverId = Guid.NewGuid(),
                    inboxItemId = inboxItemId,
                    receiverUserId = receiverUserId
                };
                _receiverRepository.Insert(newReceiver);
                //Commit changes in db
                await _receiverRepository.CommitAsync();
            }
        }
        /// <summary>
        /// Updates the given InboxItem contentData property.
        /// </summary>
        /// <param name="inboxItemId">The Id of the InboxItem to be updated</param>
        /// <param name="contentData">Serialized contentData object to be attached to the InboxItem</param>
        /// <returns>An InboxItem object if operation succeeds, null if operation fails</returns>
        public InboxItem UpdateInboxItem(Guid inboxItemId, string contentData)
        {
            var inboxItem = _itemRepository.GetByID(inboxItemId);
            if (inboxItem != null)
            {
                try
                {
                    inboxItem.contentData = contentData;
                    _itemRepository.Update(inboxItem, new string[] { "contentData" });
                    _itemRepository.Commit();
                    return inboxItem;
                }
                catch (Exception)
                {

                    return null;
                }
            }
            return null;
        }
        /// <summary>
        /// Updates the given InboxItem contentData property.
        /// </summary>
        /// <param name="inboxItemId">The Id of the InboxItem to be updated</param>
        /// <param name="contentData">Serialized contentData object to be attached to the InboxItem</param>
        /// <returns>An InboxItem object if operation succeeds, null if operation fails</returns>
        public async Task<InboxItem> UpdateInboxItemAsync(Guid inboxItemId, string contentData)
        {
            var inboxItem = await _itemRepository.GetByIDAsync(inboxItemId);
            if (inboxItem != null)
            {
                try
                {
                    inboxItem.contentData = contentData;
                    _itemRepository.Update(inboxItem, new string[] { "contentData" });
                    await _itemRepository.CommitAsync();
                    return inboxItem;
                }
                catch (Exception)
                {

                    return null;
                }
            }
            return null;
        }
        /// <summary>
        /// Deletes an InboxItem entry and its receivers.
        /// </summary>
        /// <param name="inboxItemId">The InboxItem Id</param>
        /// <returns>True if operation succeeds, false if operation fails</returns>
        public async Task<bool> DeleteInboxItemAsync(Guid inboxItemId)
        {
            var inboxItem = (await _itemRepository.GetAllAsync(x => x.inboxItemId == inboxItemId, null, "InboxItemReceivers"))
                .FirstOrDefault();
            if (inboxItem != null)
            {
                try
                {
                    foreach (var receiver in inboxItem.InboxItemReceivers)
                    {
                        _receiverRepository.Delete(receiver.inboxItemReceiverId);
                    }
                    _itemRepository.Delete(inboxItemId);
                    await _itemRepository.CommitAsync();
                    return true;
                }
                catch (Exception)
                {
                    return false;
                }
            }
            return false;
        }
        /// <summary>
        /// Checks if given InboxItem has no receivers attached.
        /// </summary>
        /// <param name="inboxItemId">The Id of the InboxItem</param>
        /// <returns>True if the InboxItem has no receivers attached, false if has at least one receiver</returns>
        public async Task<bool> CheckInboxItemWithNoReceiver(Guid inboxItemId)
        {
            var inboxItem = (await _itemRepository.GetAllAsync(x => x.inboxItemId == inboxItemId, null, "InboxItemReceivers"))
                .FirstOrDefault();
            if (inboxItem != null)
            {
                var receiverNumber = inboxItem.InboxItemReceivers.Count;
                return receiverNumber == 0;
            }
            return false;
        }
        public async Task<bool> FindAndRemoveInboxItem(InboxItemType itemType, InboxItemTargetObjectType targetObjectType,
            string targetObjectId)
        {
            var result = false;
            var inboxItemList = await _itemRepository.GetAllAsync(x => x.inboxItemType == itemType.ToString() &&
                x.targetObjectType == targetObjectType.ToString() && x.targetObjectId == targetObjectId);
            foreach (var inboxItem in inboxItemList)
            {
                result = await DeleteInboxItemAsync(inboxItem.inboxItemId);
            }
            return result;
        }
        public async Task<bool> FindAndRemoveInboxItemReceiver(InboxItemType itemType, InboxItemTargetObjectType targetObjectType,
            string targetObjectId, int userId)
        {

            var receiverList = (await _itemRepository.GetAllAsync(x => x.inboxItemType == itemType.ToString() &&
                x.targetObjectType == targetObjectType.ToString() && x.targetObjectId == targetObjectId, null, "InboxItemReceivers"))
                .SelectMany(a => a.InboxItemReceivers).Where(a => a.receiverUserId == userId);

            foreach (var receiver in receiverList)
            {
                _receiverRepository.Delete(receiver.inboxItemReceiverId);
            }
            await _receiverRepository.CommitAsync();

            return receiverList.Any();
        }
        public async Task<bool> RemovePendingAccessUserBlocked(int blockedUserId)
        {
            var result = await FindAndRemoveInboxItem(InboxItemType.PendingAccessUserBlocked, InboxItemTargetObjectType.User,
                blockedUserId.ToString());
            return result;
        }
        public async Task<bool> RemovePendingAccessUserApproval(int approvalUserId)
        {
            var result = await FindAndRemoveInboxItem(InboxItemType.PendingAccessUserApproval, InboxItemTargetObjectType.User,
                approvalUserId.ToString());
            return result;
        }
        public async Task<bool> RemovePendingAccessDeviceApproval(int approvalDeviceId)
        {
            var result = await FindAndRemoveInboxItem(InboxItemType.PendingAccessDeviceApproval, InboxItemTargetObjectType.Device,
                approvalDeviceId.ToString());
            return result;
        }

    }
}
