using Atlas.CrossCutting.Helpers;
using Atlas.Data.Entities;
using Atlas.Data.Repository;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security;
using System.Threading.Tasks;

namespace Atlas.Business
{
    public class UserDeviceService
    {
        private readonly User _currentUser;

        public UserDeviceService()
        {
        }

        public UserDeviceService(User currentUser)
        {
            _currentUser = currentUser;
        }


        public async Task<UserDevice> Get(string fingerprint, int userId)
        {
            using (var _md = new AtlasModelCore())
            {
                return await _md.UserDevice.Where(o => o.fingerprint == fingerprint && o.userId == userId).FirstOrDefaultAsync();
            }
        }

        public async Task<UserDevice> Get(int deviceEntryId)
        {
            using (var _md = new AtlasModelCore())
            {
                return await _md.UserDevice.Include(ud => ud.User).Where(ud => ud.deviceEntryId == deviceEntryId).FirstOrDefaultAsync();
            }
        }


        public async Task<UserDevice> RequestNewDeviceApproval(string fingerprint, User userData, string userAgent, bool isAdmin)
        {

            var ddh = new DeviceDetectorHelper();
            string simpleUserAgent = ddh.GetSimplyfiedString(userAgent);
            string deviceType = ddh.deviceType;

            using (var _md = new AtlasModelCore())
            {
                UserDevice userDevice = new UserDevice();
                userDevice.fingerprint = fingerprint;
                userDevice.name = simpleUserAgent;
                userDevice.date = DateTime.UtcNow;
                userDevice.userId = userData.userId;
                userDevice.token = "";

                if (deviceType == null)
                {
                    deviceType = "Unknown";

                }
                else if (deviceType.Equals("desktop"))
                {
                    deviceType = "Browser";
                }

                userDevice.deviceType = deviceType;
                userDevice = _md.UserDevice.Add(userDevice).Entity;

                if (!isAdmin)
                {
                    userDevice.status = "AWAITING_APPROVAL";
                    userDevice.approved = false;
                }
                else
                {
                    userDevice.status = "APPROVED";
                    userDevice.approved = true;

                }

                await _md.SaveChangesAsync();

                if (userDevice.approved == false)
                {
                    //InboxItemService implementation
                    //Calls InboxItemService to create PendingAccessDeviceApproval InboxItem entry
                    await new InboxItemService().CreatePendingAccessDeviceApprovalAsync(userDevice.deviceEntryId);
                }

                var userRolesDetails = new UserRoleService(userData.userId).GetCurrentUserRolesDetails();
                var anyClientRequireApproval = userRolesDetails.Where(o => o.Client != null &&
                                                                           o.Client.deviceApprovalEnabled &&
                                                                           o.Client.deviceApprovalAddOn == true).ToList();
                ActivityService.Add("USER_DEVICE_REQUEST_APPROVAL", anyClientRequireApproval.Select(o => (int)o.clientId).ToList(), userData.userId);

                return userDevice;
            }

        }


        public async Task<UserDevice> RequestDeviceApproval(UserDevice userDevice, User userData)
        {

            bool shouldSendEmail = true;
            DateTime currentDate = DateTime.UtcNow;


            TimeSpan ts = currentDate - userDevice.date;
            if (ts.TotalHours < 4 && userDevice.status == "AWAITING_APPROVAL")
            {
                shouldSendEmail = false;
            }
            else
            {
                userDevice.date = currentDate;
            }


            userDevice.status = "AWAITING_APPROVAL";
            userDevice.approved = false;

            using (var _md = new AtlasModelCore())
            {
                _md.UserDevice.Update(userDevice);
                await _md.SaveChangesAsync();
            }

            //InboxItemService implementation
            //Calls InboxItemService to create PendingAccessDeviceApproval InboxItem entry
            await new InboxItemService().CreatePendingAccessDeviceApprovalAsync(userDevice.deviceEntryId);

            if (shouldSendEmail)
            {
                var userRolesDetails = new UserRoleService(userData.userId).GetCurrentUserRolesDetails();
                var anyClientRequireApproval = userRolesDetails.Where(o => o.Client != null &&
                                                                           o.Client.deviceApprovalEnabled &&
                                                                           o.Client.deviceApprovalAddOn == true).ToList();
                ActivityService.Add("USER_DEVICE_REQUEST_APPROVAL", anyClientRequireApproval.Select(o => (int)o.clientId).ToList(), userData.userId);
            }


            return userDevice;
        }

        public async Task<bool> CheckDeviceApprovalStatus(int userId, string fingerprint)
        {
            var userDevice = await Get(fingerprint, userId);
            return userDevice != null ? userDevice.approved ?? false : false;
        }

        public async Task<string> CheckDeviceStatus(int userId, string fingerprint)
        {
            var userDevice = await Get(fingerprint, userId);
            return userDevice?.status;
        }


        public async Task<List<UserDevice>> GetAll()
        {
            using (var _md = new AtlasModelCore())
            {
                return await _md.UserDevice.Where(ud => ud.fingerprint != null).OrderByDescending(o => o.date).ToListAsync();
            }
        }


        public async Task<List<UserDevice>> GetDevicesByUserId(int userId)
        {
            using (var _md = new AtlasModelCore())
            {
                return await _md.UserDevice.Where(ud => ud.userId == userId).ToListAsync();
            }
        }

        public async Task<List<UserDevice>> GetNotificationDevicesByUser(int userId)
        {
            using (var _md = new AtlasModelCore())
            {
                return await _md.UserDevice.Where(u => u.userId == userId && !string.IsNullOrEmpty(u.token)).ToListAsync();
            }
        }

        public async Task<List<UserDevice>> GetApprovedNotificationDevicesByUser(int userId)
        {
            using (var _md = new AtlasModelCore())
            {
                return await _md.UserDevice.Where(u => u.userId == userId && u.pushEnabled &&
                    (!string.IsNullOrEmpty(u.token) || !string.IsNullOrEmpty(u.fcmToken)))
                    .ToListAsync();
            }
        }

        public async Task<UserDevice> FindDevice(string fingerprint, int userId)
        {
            var userDevices = await GetDevicesByUserId(userId);

            List<string> receivedFingerprints = ParseFingerprint(fingerprint);
            bool isMobile = receivedFingerprints.Count() == 1;

            foreach (UserDevice userDevice in userDevices)
            {

                if (userDevice.fingerprint != null)
                {
                    List<string> deviceFingerprints = ParseFingerprint(userDevice.fingerprint);
                    var matches = deviceFingerprints.Where(o => receivedFingerprints.Contains(o));
                    if (!isMobile)
                    {

                        if (matches.Count() >= 2)
                        {
                            return userDevice;
                        }
                    }
                    else
                    {
                        if (matches.Count() == deviceFingerprints.Count())
                        {
                            return userDevice;
                        }
                    }
                }

            }

            return null;
        }

        public async Task<UserDevice> Approve(int deviceEntryId)
        {
            var userDevice = await Get(deviceEntryId);
            if (userDevice == null)
            {
                return null;
            }

            UserClientService userClientService = new UserClientService();
            UserRoleService userRoleService = new UserRoleService(_currentUser.userId);

            bool hasPermission = await userRoleService.IsSuperAdmin() || userClientService.IsManagedBy(_currentUser.userId, userDevice.userId);

            if (!hasPermission)
            {
                throw new SecurityException("UNAUTHORIZED_DEVICE_APPROVAL_ATTEMPT");
            }

            if (!(userDevice.approved ?? false))
            {
                userDevice.approved = true;
                userDevice.status = "APPROVED";

                using (var _md = new AtlasModelCore())
                {
                    _md.UserDevice.Update(userDevice);

                    if (await _md.SaveChangesAsync() > 0)
                    {
                        var clientId = await _md.User.Where(o => o.userId == userDevice.userId).Select(o => o.clientId).FirstOrDefaultAsync();
                        string details = string.Format("{{ userId: {0}, deviceId: {1} }}", userDevice.userId, userDevice.deviceEntryId);
                        await ActivityService.AddAsync("DEVICE_APPROVED", clientId, null, _currentUser.userId, details);
                        //InboxItemService implementation
                        //Calls InboxItemService to remove PendingAccessDeviceApproval InboxItem entry
                        await new InboxItemService().RemovePendingAccessDeviceApproval(userDevice.deviceEntryId);
                    }
                }
            }

            return userDevice;
        }


        public async Task<bool> CheckDeviceSessionApproved(string session_key)
        {

            UserRepository repo = new UserRepository();

            UserSession userSession = repo.GetSessionData(session_key);

            int sessionDeviceId = userSession.deviceId.GetValueOrDefault();
            if (sessionDeviceId == 0 || userSession == null)
            {
                return true;
            }

            var ursvc = new UserRoleRepository(userSession.userId);
            var userRoles = ursvc.GetCurrentRolesForUserSynchronously(userSession.userId);
            if (userRoles.Any(ur => ur.name == "CLIENT_ADMIN" || ur.name == "SUPER_ADMIN"))
            {
                return true;
            }

            using (var _md = new AtlasModelCore())
            {
                UserDevice userDevice = await _md.UserDevice.Where(ud => ud.deviceEntryId == sessionDeviceId).FirstOrDefaultAsync();

                var user_client = await _md.User.Include(o => o.Client).Where(o => o.userId == userDevice.userId).FirstOrDefaultAsync();


                var userRolesDetails = ursvc.GetUserRolesDetails(userSession.userId);
                var anyClientRequireApproval = userRolesDetails.Any(o => o.Client != null &&
                                                                           o.Client.deviceApprovalEnabled &&
                                                                           o.Client.deviceApprovalAddOn == true);
                if (anyClientRequireApproval)
                {
                    return userDevice.approved ?? false;
                }

                return true;
            }
        }
        public async Task<string> CheckDeviceSessionStatus(string session_key)
        {


            UserRepository repo = new UserRepository();

            UserSession userSession = repo.GetSessionData(session_key);

            int sessionDeviceId = userSession.deviceId.GetValueOrDefault();

            if (sessionDeviceId == 0 || userSession == null)
            {
                return "APPROVED";
            }

            using (var _md = new AtlasModelCore())
            {
                UserDevice userDevice = await _md.UserDevice.Where(ud => ud.deviceEntryId == sessionDeviceId).FirstOrDefaultAsync();

                var user_client = await _md.User.Include(o => o.Client).Where(o => o.userId == userDevice.userId).FirstOrDefaultAsync();

                var userRolesDetails = new UserRoleService(userSession.userId).GetCurrentUserRolesDetails();
                var anyClientRequireApproval = userRolesDetails.Any(o => o.Client != null &&
                                                                      o.Client.deviceApprovalEnabled &&
                                                                      o.Client.deviceApprovalAddOn == true);
                if (anyClientRequireApproval == false)
                {
                    return "APPROVED";
                }

                return userDevice.status;
            }
        }

        public async Task<UserDevice> Reject(int deviceEntryId)
        {
            UserDevice userDevice = await Get(deviceEntryId);

            if (userDevice == null)
            {
                return null;
            }

            UserClientService userClientService = new UserClientService();
            UserRoleService userRoleService = new UserRoleService(_currentUser.userId);

            bool hasPermission = await userRoleService.IsSuperAdmin() || userClientService.IsManagedBy(_currentUser.userId, userDevice.userId);

            if (!hasPermission)
            {
                throw new SecurityException("UNAUTHORIZED_DEVICE_REJECTION_ATTEMPT");
            }

            if (userDevice.approved ?? false)
            {

                userDevice.status = "REVOKED";
            }
            else
            {
                userDevice.status = "REJECTED";

            }

            userDevice.approved = false;
            using (var _md = new AtlasModelCore())
            {
                _md.UserDevice.Update(userDevice);

                if (await _md.SaveChangesAsync() > 0)
                {
                    var clientId = await _md.User.Where(o => o.userId == userDevice.userId).Select(o => o.clientId).FirstOrDefaultAsync();
                    string details = string.Format("{{ userId: {0}, deviceId: {1} }}", userDevice.userId, userDevice.deviceEntryId);

                    await ActivityService.AddAsync("DEVICE_" + userDevice.status, clientId, null, _currentUser.userId, details);
                    //InboxItemService implementation
                    //Calls InboxItemService to remove PendingAccessDeviceApproval InboxItem entry
                    await new InboxItemService().RemovePendingAccessDeviceApproval(userDevice.deviceEntryId);
                }
            }

            return userDevice;
        }

        private List<string> ParseFingerprint(string fingerprint)
        {
            if (fingerprint == null)
            {
                return null;

            }
            else
            {
                return fingerprint.Split('_').OrderBy(a => a).ToList();
            }
        }
    }
}
