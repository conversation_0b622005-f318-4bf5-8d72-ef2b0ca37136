using Atlas.Business.Interfaces;
using System.Threading.Tasks;

namespace Atlas.Business.Extensions
{
    public static class ContentValidationStrategyExtensions
    {
        public static void Validate(this IContentValidationStrategy<Data.Entities.Content> strategy, Atlas.Data.Entities.Content content)
        {
            Task.Run(() => strategy.ValidateAsync(content)).GetAwaiter().GetResult();
        }
    }
}
