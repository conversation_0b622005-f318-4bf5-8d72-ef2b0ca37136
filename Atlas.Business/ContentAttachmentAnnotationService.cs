using Atlas.Business.Helpers.Annotation;
using Atlas.Business.ViewModels.Annotation;
using Atlas.Business.ViewModels.Bluebook;
using Atlas.Data.Entities;
using Atlas.Data.Repository;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security;
using Atlas.CrossCutting.AppEnums;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace Atlas.Business
{
    public class ContentAttachmentAnnotationService
    {
        public int _currentUser { get; set; }
        public ContentAttachmentAnnotationRepository _repo;

        public ContentAttachmentAnnotationService(int currentUser)
        {
            this._currentUser = currentUser;
            this._repo = new ContentAttachmentAnnotationRepository(_currentUser);
        }


        public async Task<List<ContentAttachmentAnnotation>> GetByContent(int contentId, List<int?> childContents = null)
        {
            var byContent = await _repo.GetByContent(contentId, childContents);
            List<ContentAttachmentAnnotation> shared = await _repo.GetAllSharedByContent(contentId, childContents);

            List<ContentAttachmentAnnotation> final = new List<ContentAttachmentAnnotation>();
            final.AddRange(byContent);
            final.AddRange(shared);
            return final.Where(o => !o.deleted).ToList();

        }
        public async Task<List<ContentAttachmentAnnotation>> GetByAttachment(int contentAttachmentId)
        {
            return await _repo.GetByAttachment(contentAttachmentId);
        }

        public async Task<long> Add(AnnotationExchangeViewModel aex)
        {
            //1. criar Entities.ContentAttachmentAnnotation
            var new_annotation = new ContentAttachmentAnnotation();


            //2. deserialize dos dados 
            var parsedAnnotationVM = Helpers.Annotation.AnnotationParserHelper.Parse(aex.annotationData);
            ContentAttachmentAnnotation existing_annotation = await _repo.GetAnnotationByUUID(parsedAnnotationVM.uuid);

            if (existing_annotation != null)
            {
                return 0;

            }

            //3. calcular a paginaçao relativa ao Anexo original
            var calculatedAnnotation = Helpers.Annotation.AnnotationCalculationHelper.CalculateUpload(parsedAnnotationVM, aex.Bluebook_Result);

            //ver se tem permissao no anexo compartilhado
            if (calculatedAnnotation.contentAttachmentId.HasValue)
            {
                bool hasPermission = await _repo.HasPermissionAttachment(_currentUser, calculatedAnnotation.contentAttachmentId);
                if (!hasPermission)
                {
                    throw new SecurityException("Insufficient permissions to add annotations to requestes Attachment");
                }
            }

            ContentService contentService = new ContentService(_currentUser);
            Content content = await contentService.Get(calculatedAnnotation.contentId);
            FeatureManagerService featureService = new FeatureManagerService(_currentUser);
            bool isFeatureEnabled = await featureService.isEnabledByContent(content, PlanFeatureNames.BLUEBOOK_SH);

            if (!isFeatureEnabled)
            {
                throw new Exception("FEATURE_NOT_INCLUDED");
            }

            //preencher com os campos extraidos do JSON para alimentar as colunas do banco
            new_annotation.uuid = Guid.Parse(calculatedAnnotation.uuid);
            new_annotation.annotationFormat = "AJS";
            new_annotation.annotationType = calculatedAnnotation.type;
            new_annotation.shared = false;

            //dados encontrados quando comparado a anotaçao e a lista original de anexos do BB
            new_annotation.contentAttachmentId = calculatedAnnotation.contentAttachmentId;
            new_annotation.contentId = calculatedAnnotation.contentId;

            if (calculatedAnnotation.@class == "Comment")
            {
                new_annotation.annotationFulltext = calculatedAnnotation.content;
            }
            new_annotation.annotationData = aex.annotationData;
            new_annotation.createDate = DateTime.UtcNow;
            new_annotation.userId = _currentUser;
            new_annotation.deleted = false;

            //replyto
            if (!string.IsNullOrEmpty(parsedAnnotationVM.InReplyTo))
            {
                new_annotation.inReplyTo = new Guid(parsedAnnotationVM.InReplyTo);
            }

            //principal: a pagina relativa!
            new_annotation.pageNumber = calculatedAnnotation.page;

            return await _repo.Add(new_annotation);
        }

        public async Task<List<User>> GetShareUsers(ShareUserRequestViewModel share_req)
        {
            var contentAttachmentId = Helpers.Annotation.AnnotationCalculationHelper.CalculateContentAttachmentIdByPage(share_req.Bluebook_Result, share_req.page_number);
            //Get the meetingAgendaItem contentId or MeetingMinute contentId
            var contentId = Helpers.Annotation.AnnotationCalculationHelper.CalculateContentIdByPage(share_req.Bluebook_Result, share_req.page_number);

            var users = await _repo.GetShareUsers(contentId.Value, contentAttachmentId);

            if (!users.Select(o => o.userId).Contains(_currentUser))
            {
                throw new SecurityException("No access to content/contentAttachment");
            }

            return users;
        }

        public async Task<ShareUserViewModel> GetShareUsersByUUID(string uuid)
        {
            var result = await _repo.GetShareUsersByUUID(uuid);

            if (!result.PossibleUsers.Select(o => o.userId).Contains(_currentUser))
            {
                throw new SecurityException("No access to content/contentAttachment");
            }

            return result;
        }
        public async Task<long> Share(int targetUserId, List<string> aex_list, BluebookResultViewModel Bluebook_Result)
        {
            var new_annotation_list = new List<ContentAttachmentAnnotation>();
            foreach (var aex in aex_list)
            {
                //1. criar Entities.ContentAttachmentAnnotation
                var new_annotation = new ContentAttachmentAnnotation();

                //2. deserialize dos dados 
                var parsedAnnotationVM = Helpers.Annotation.AnnotationParserHelper.Parse(aex);

                //3. calcular a paginaçao relativa ao Anexo original
                var calculatedAnnotation = Helpers.Annotation.AnnotationCalculationHelper.CalculateUpload(parsedAnnotationVM, Bluebook_Result);

                //validacao de permissoes é feita no final desse codigo, antes do Share/Add efetivo no banco

                //preencher com os campos extraidos do JSON para alimentar as colunas do banco
                new_annotation.uuid = Guid.NewGuid();
                new_annotation.originalUuid = Guid.Parse(calculatedAnnotation.uuid);
                new_annotation.annotationFormat = "AJS";
                new_annotation.annotationType = calculatedAnnotation.type;

                //dados encontrados quando comparado a anotaçao e a lista original de anexos do BB
                new_annotation.contentAttachmentId = calculatedAnnotation.contentAttachmentId;
                new_annotation.contentId = calculatedAnnotation.contentId;

                if (calculatedAnnotation.@class == "Comment")
                {
                    new_annotation.annotationFulltext = calculatedAnnotation.content;
                }
                new_annotation.annotationData = aex;
                new_annotation.createDate = DateTime.UtcNow;
                new_annotation.userId = targetUserId;
                new_annotation.deleted = false;

                //share
                new_annotation.shared = true;
                new_annotation.sharedUser = _currentUser;
                new_annotation.sharedDate = DateTime.UtcNow;

                //principal: a pagina relativa!
                new_annotation.pageNumber = calculatedAnnotation.page;
                new_annotation_list.Add(new_annotation);
            }

            var current_attachmentIds = new_annotation_list.Where(o => o.contentAttachmentId.HasValue).Select(o => o.contentAttachmentId.Value).ToList();
            var ids_with_permissions = (await _repo.HasPermissionAttachmentMultiple(targetUserId, current_attachmentIds)).Select(o => o.contentAttachmentId);


            var final_list = new_annotation_list.Where(o =>
                (o.contentAttachmentId.HasValue && ids_with_permissions.Contains(o.contentAttachmentId.Value)) || //os que tem permissao
                (o.contentAttachmentId.HasValue == false)).ToList(); //os que nao estao em nenhum anexo (primeira pagina, etc)

            long entries = await _repo.AddShared(final_list);
            if (entries > 0)
            {
                ContentActivityService service = new ContentActivityService(_currentUser);
                var annotation = final_list.First();

                service.Add(annotation.contentId.Value, new ContentActivity()
                {
                    type = Operations.SHARED_BLUEBOOK_NOTES,
                    date = DateTime.UtcNow,
                    activityUser = _currentUser,
                    contentData = JsonConvert.SerializeObject(new
                    {
                        sharedUser = annotation.userId,
                        annotation.contentId,
                        annotation.contentAttachmentId,
                        annotation.pageNumber,
                        annotation.originalUuid
                    })
                });
            }

            return entries;
        }

        public async Task<bool> Share(IEnumerable<int> targetUsers, List<string> annotationDataList, BluebookResultViewModel bluebook_Result)
        {
            foreach (int userId in targetUsers)
            {
                await Share(userId, annotationDataList, bluebook_Result);
            }

            return true;
        }

        public async Task<BluebookResultViewModel> GetUpdatedAnnotations(int contentId, BluebookResultViewModel bbres, User _currentUserObj)
        {
            if (!new ContentRepository(_currentUser).CheckPermissions(contentId))
            {
                throw new System.Security.SecurityException("Sem permissão para acessar o conteudo.");
            }

            List<int?> childContentsIds = bbres.Paging.Select(o => o.contentId).ToList();
            var annotations_db = await this.GetByContent(contentId, childContentsIds);

            //group all existing contentId vs. attachments combinations
            foreach (var item in bbres.Paging.GroupBy(o => new { o.contentId, o.contentAttachmentId }))
            {
                foreach (var page in item)//this probably would have just one page, but ok
                {
                    //UPDATE ANNOTATION FOR BOTH:
                    //attachments
                    //bluebook header/agenda headers
                    page.Annotations = annotations_db.Where(o => o.contentId == page.contentId && o.contentAttachmentId == page.contentAttachmentId).ToList();
                }
            }

            //1. calcular a paginaçao nova, de acordo com o dinamismo do bluebook (documentos reordenados, excluidos, sem permissao)
            bbres.AnnotationsCalculated = AnnotationCalculationHelper.CalculateDownload(bbres, _currentUserObj);

            //2. converter tudo pra string/serializado em json
            bbres.AnnotationsJSON = AnnotationParserHelper.StringifyAll(bbres.AnnotationsCalculated).ToArray();

            return bbres;
        }

        public async Task<bool> Update(AnnotationExchangeViewModel aex)
        {
            //1. deserialize dos dados 
            var parsedAnnotationVM = Helpers.Annotation.AnnotationParserHelper.Parse(aex.annotationData);

            var foundAnnotation = await _repo.GetByUuid(parsedAnnotationVM.uuid);

            if (parsedAnnotationVM.@class == "Comment")
            {
                foundAnnotation.annotationFulltext = parsedAnnotationVM.content;
            }
            foundAnnotation.annotationData = aex.annotationData;

            return await _repo.Update(foundAnnotation);
        }

        public async Task<bool> Delete(string uuid, bool delete = true)
        {
            return await _repo.Delete(uuid, delete);
        }

        public async Task<bool> ClearAllByContentId(int contentId)
        {
            return await _repo.ClearAllByContentId(contentId);
        }

        public async Task<bool> ShareByUUID(string uuid, MultipleAnnotationExchangeViewModel aex)
        {
            //input: list of selected users
            //database: possible list of already selected users


            AtlasModelCore _md = new AtlasModelCore();
            var result = await _repo.GetShareUsersByUUID(uuid);
            var current = result.CurentUsers.Select(o => o.userId).ToList();


            if (result.Annotation == null)
            {
                return false;
            }
            if (result.Annotation.userId != _currentUser)
            {
                return false;

            }

            List<int> _usersToAdd = new List<int>();
            List<int> _usersToRemove = new List<int>();

            _usersToAdd.AddRange(aex.targetUsers.Where(o => !current.Contains(o)));
            _usersToRemove.AddRange(current.Where(o => !aex.targetUsers.Contains(o)));

            //add selected users that are not already in the database
            foreach (var item in _usersToAdd)
            {
                _md.AnnotationShare.Add(new AnnotationShare()
                {
                    contentId = result.Annotation.contentId.Value,
                    contentAttachmentId = result.Annotation.contentAttachmentId,
                    shareUser = _currentUser,
                    shareDate = DateTime.UtcNow,
                    userId = item,
                    uuid = new Guid(uuid)

                });

            }

            //remove non selected users that is present in the database
            foreach (var item in _usersToRemove)
            {
                _md.AnnotationShare.RemoveRange(_md.AnnotationShare.Where(o => o.uuid == new Guid(uuid) && o.userId == item));
            }

            _md.SaveChanges();

            new ContentActivityService(_currentUser).Add(result.Annotation.contentId.Value, new ContentActivity()
            {
                type = Operations.SHARED_BLUEBOOK_NOTES,
                date = DateTime.UtcNow,
                activityUser = _currentUser,
                contentData = JsonConvert.SerializeObject(new
                {
                    sharedUsers = _usersToAdd,
                    result.Annotation.contentId,
                    result.Annotation.contentAttachmentId,
                    result.Annotation.pageNumber,
                    result.Annotation.originalUuid
                })
            });

            return true;
        }
    }
}
