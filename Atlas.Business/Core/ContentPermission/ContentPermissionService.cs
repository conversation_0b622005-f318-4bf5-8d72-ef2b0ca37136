using System;
using System.Collections.Generic;
using System.Linq;
using System.Security;
using System.Security.Principal;
using System.Threading.Tasks;
using Atlas.Business.ViewModels;
using Atlas.CrossCutting.AppEnums;
using Atlas.CrossCutting.DTO.ContentPermission;
using Atlas.CrossCutting.Helpers;
using Atlas.CrossCutting.Services;
using Atlas.CrossCutting.Settings;
using Atlas.Data.Entities;
using Atlas.Data.Repository;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Newtonsoft.Json;

namespace Atlas.Business.Core.ContentPermission
{
    public class ContentPermissionService : IContentPermissionService
    {
        private readonly AuthUtil _authUtil;
        private readonly StorageSettings _storageSettings;
        private readonly AtlasModelCore _atlasModelCore;
        private IDbContextTransaction _transaction;
        private readonly ContentService _contentService;
        private readonly ContentActivityService _contentActivityService;
        private ContentRepository _contentRepository;
        private readonly Business.ContentPermissionService _legacyContentPermissionService;
        private readonly IRequestContextService _requestContext;

        public static class PermissionAuditOperations
        {
            public const string PERMISSIONS_ADDED = "PERMISSIONS_ADDED";
            public const string PERMISSIONS_REMOVED = "PERMISSIONS_REMOVED";
        }

        private class ContentPermissionData
        {
            public int contentId { get; set; }
            public Guid contentuuid { get; set; }
            public string type { get; set; }
            public string status { get; set; }
            public int? workgroupId { get; set; }
            public int? parentContentId { get; set; }
            public Guid? parentContentUuid { get; set; }
            public int createUser { get; set; }
            public int? assignedUser { get; set; }
            public int? reviewerUser { get; set; }
            public List<ContentPermissionInfo> ContentPermissions { get; set; }

            public class ContentPermissionInfo
            {
                public int userId { get; set; }
                public string creationRule { get; set; }
                public string userName { get; set; }
                public string userEmail { get; set; }
                public string userProfilePic { get; set; }
            }
        }

        public static class AuditSubItemTypes
        {
            public const string USER = "USER";
        }

        public ContentPermissionService(
            IPrincipal principal,
            StorageSettings storageSettings,
            IRequestContextService requestContext,
            IDbContextTransaction transaction = null)
        {
            _authUtil = new AuthUtil(principal);
            _storageSettings = storageSettings;
            _requestContext = requestContext;
            _atlasModelCore = new AtlasModelCore();
            _transaction = transaction;

            _contentRepository = _atlasModelCore != null && _transaction != null
                ? new ContentRepository(_authUtil.UserId, _atlasModelCore, _transaction)
                : new ContentRepository(_authUtil.UserId);

            _contentActivityService = new ContentActivityService(_authUtil.UserId);
            _contentService = new ContentService(_authUtil.UserId, _contentRepository);
            _legacyContentPermissionService = _atlasModelCore != null && _transaction != null
                ? new Atlas.Business.ContentPermissionService(_authUtil.UserId, _atlasModelCore, _transaction)
                : new Atlas.Business.ContentPermissionService(_authUtil.UserId);
        }

        private async Task<int> GetContentIdFromUuidAsync(Guid contentUuid)
        {
            return await _contentRepository.GetContentIdByUuIdAsync(contentUuid);
        }

        public async Task<int> UpdateAsync(Guid contentUuid, List<PermissionListViewModel> list, bool grantedByAdmin = false)
        {
            var users_add = new List<ContentPermissionDTO>();
            var users_remove = new List<ContentPermissionDTO>();

            Content currentContent = null;

            bool shouldManageTransaction = _transaction == null && !grantedByAdmin;
            IDbContextTransaction localTransaction = null;
            var contentActivityService = _contentActivityService;

            try
            {
                if (shouldManageTransaction)
                {
                    localTransaction = await _atlasModelCore.Database.BeginTransactionAsync();
                    _transaction = localTransaction;

                    contentActivityService = new ContentActivityService(_authUtil.UserId, _atlasModelCore, _transaction);
                    var newContentRepository = new ContentRepository(_authUtil.UserId, _atlasModelCore, _transaction);
                    _contentRepository = newContentRepository;
                }

                if (grantedByAdmin)
                {
                    var contentId = await GetContentIdFromUuidAsync(contentUuid);
                    var currentPermissions = await _contentRepository.List_Permissions(contentUuid);

                    foreach (var item in list)
                    {
                        if (!currentPermissions.Select(o => o.userId).Contains(item.ContentPermission.userId))
                        {
                            if (item.Checked)
                            {
                                item.ContentPermission.allowed = true;
                                users_add.Add(new ContentPermissionDTO
                                {
                                    userId = item.ContentPermission.userId,
                                    contentId = contentId,
                                    contentUuid = contentUuid,
                                    allowed = true,
                                    userName = item.ContentPermission.User?.name,
                                    userEmail = item.ContentPermission.User?.email
                                });
                            }
                        }
                    }
                }
                else
                {
                    currentContent = await _contentService.GetContent(contentUuid);
                    await this.ValidateContent(currentContent);

                    bool isPoll = currentContent.type == ContentTypes.Poll;
                    bool isTask = currentContent.type == ContentTypes.Task;
                    bool isKB = currentContent.type == ContentTypes.KnowledgeDirectory;
                    bool isMeetingAgenda = currentContent.type == ContentTypes.MeetingAgendaItem;
                    bool isMeeting = currentContent.type == ContentTypes.Meeting;
                    bool isPastMeeting = currentContent.Meeting.FirstOrDefault()?.date < DateTime.UtcNow;

                    var currentPermissions = currentContent.ContentPermission;

                    if (!currentPermissions.Any())
                        currentPermissions = await _contentRepository.List_Permissions(contentUuid);

                    var original_usersID_permissions = currentPermissions.Select(o => o.userId).ToArray();
                    var permissions_to_remove = list.FindAll(u => original_usersID_permissions.Contains(u.ContentPermission.userId) && !u.Checked);
                    var permissions_to_add = list.FindAll(u => !original_usersID_permissions.Contains(u.ContentPermission.userId) && u.Checked);

                    permissions_to_add.ForEach(p => p.ContentPermission.allowed = true);

                    List<User> _wkg_users_allowed = await new WorkgroupService(_authUtil.UserId).GetUsers(currentContent.workgroupId);

                    this.ValidateUsers(_wkg_users_allowed.Select(u => u.userId), permissions_to_add);

                    if (isMeetingAgenda)
                    {
                        Data.Entities.MeetingMinute minute = currentContent.Meeting.FirstOrDefault().MeetingMinute.FirstOrDefault(o => o.published == true);

                        if (minute != null)
                            await this.CheckIfUserIsNotASigner(minute, permissions_to_remove);
                    }

                    if (isTask && currentContent.parentContentId.HasValue)
                    {
                        ICollection<Data.Entities.ContentOwner> _meeting_owners = await new ContentOwnerRepository(_authUtil.UserId, currentContent.parentContentId.Value).GetCurrentOwnersAsync();

                        if (list.Any(u => !u.Checked && _meeting_owners.Any(o => o.userId == u.ContentPermission.userId)))
                            throw new InvalidOperationException("MEMBER_CANNOT_BE_REMOVED");
                    }

                    bool shouldRemoveSubscribers = !(isPoll || isKB || (isMeeting && isPastMeeting));
                    bool shouldAddSubscribers = !(isPoll || isTask || isKB);

                    if (shouldRemoveSubscribers)
                        await this.RemoveSubscribers(currentContent, permissions_to_remove);

                    if (shouldAddSubscribers)
                        this.AddSubscribers(currentContent, permissions_to_add);

                    if (!isKB)
                        await this.RemoveOwners(currentContent, permissions_to_remove);

                    users_add = permissions_to_add.Select(p => new ContentPermissionDTO
                    {
                        userId = p.ContentPermission.userId,
                        contentId = p.ContentPermission.contentId,
                        contentUuid = contentUuid,
                        allowed = p.ContentPermission.allowed,
                        userName = p.ContentPermission.User?.name,
                        userEmail = p.ContentPermission.User?.email
                    }).ToList();

                    users_remove = permissions_to_remove.Select(p => new ContentPermissionDTO
                    {
                        userId = p.ContentPermission.userId,
                        contentId = p.ContentPermission.contentId,
                        contentUuid = contentUuid,
                        allowed = false,
                        userName = p.ContentPermission.User?.name,
                        userEmail = p.ContentPermission.User?.email
                    }).ToList();
                }

                var entities_add = users_add.Select(dto => new Data.Entities.ContentPermission
                {
                    contentId = dto.contentId,
                    userId = dto.userId,
                    allowed = dto.allowed,
                    createUser = _authUtil.UserId,
                    createDate = DateTime.UtcNow
                }).ToList();

                var entities_remove = users_remove.Select(dto => new Data.Entities.ContentPermission
                {
                    contentId = dto.contentId,
                    userId = dto.userId
                }).ToList();

                int permissionsUpdated = await _contentRepository.Set_Permission(contentUuid, currentContent.contentId, entities_add, entities_remove);

                if (permissionsUpdated == 0)
                {
                    if (shouldManageTransaction)
                    {
                        await localTransaction.RollbackAsync();
                        return permissionsUpdated;
                    }
                }

                if (currentContent == null)
                {
                    currentContent = await _atlasModelCore.Content.Where(o => o.contentUuid == contentUuid).Include(o => o.Child_Content).FirstOrDefaultAsync();
                }

                var updated_childcontent_users_summary = new List<ContentPermissionDTO>();

                if (currentContent.Child_Content.Any())
                {
                    updated_childcontent_users_summary = this.ManageChildPermissions(currentContent, currentContent.contentId, users_add, users_remove);
                }

                if (!grantedByAdmin)
                {
                    if (currentContent.parentContentId.HasValue)
                    {
                        var _currentContent_Parent = await _contentRepository.Get(currentContent.parentContentUuid.Value);
                        await this.ManageMinutePermission(currentContent, users_add, users_remove, _currentContent_Parent);
                    }
                    await this.RegisterAuditLog(currentContent.contentId, contentUuid, users_add, users_remove, updated_childcontent_users_summary);
                }

                if (shouldManageTransaction)
                {
                    await _atlasModelCore.SaveChangesAsync();
                    await localTransaction.CommitAsync();
                }

                return permissionsUpdated;
            }
            catch (Exception)
            {
                if (shouldManageTransaction && localTransaction != null)
                {
                    await localTransaction.RollbackAsync();
                }
                throw;
            }
            finally
            {
                if (shouldManageTransaction)
                {
                    localTransaction?.Dispose();
                    if (_atlasModelCore != null)
                    {
                        _atlasModelCore.Dispose();
                    }
                }
            }
        }

        private void AddSubscribers(Content currentContent, IEnumerable<PermissionListViewModel> permissionsToAdd)
        {
            var existingSubscriberUserIds = new HashSet<int>(
                currentContent.ContentSubscriber.Select(s => s.userId)
            );

            var newSubscribers = permissionsToAdd
                .Where(p => !existingSubscriberUserIds.Contains(p.ContentPermission.userId))
                .ToList();

            if (!newSubscribers.Any())
            {
                return;
            }

            foreach (var permission in newSubscribers)
            {
                var contentSubscriberService = new ContentSubscriberService(_authUtil.UserId, currentContent.contentId);
                contentSubscriberService.SetContentUuId(currentContent.contentUuid);
                contentSubscriberService.SetUserAgent(_requestContext.UserAgent);

                contentSubscriberService.Add(
                    permission.ContentPermission.userId,
                    createInvite: false,
                    grantedByAdmin: false,
                    currentContent
                );
            }
        }

        private async System.Threading.Tasks.Task RemoveSubscribers(Content currentContent, IEnumerable<PermissionListViewModel> permissionsToRemove)
        {
            var subscribersToRemove = permissionsToRemove
                .Select(p => new Data.Entities.ContentSubscriber
                {
                    userId = p.ContentPermission.userId,
                    contentId = p.ContentPermission.contentId
                })
                .ToList();

            if (!subscribersToRemove.Any())
            {
                return;
            }

            var contentUuid = currentContent?.contentUuid ?? throw new InvalidOperationException("Content UUID is required for removing subscribers");

            var contentSubscriberService = new ContentSubscriberService(_authUtil.UserId, currentContent.contentId);
            contentSubscriberService.SetContentUuId(contentUuid);
            contentSubscriberService.SetUserAgent(_requestContext.UserAgent);

            await contentSubscriberService.RemoveRangeAsync(contentUuid, subscribersToRemove);
        }

        private async System.Threading.Tasks.Task RemoveOwners(Content currentContent, IEnumerable<PermissionListViewModel> permissionsToRemove)
        {
            var currentOwners = currentContent.ContentOwner;

            var ownersToRemove = permissionsToRemove
                .Select(p => new Atlas.Data.Entities.ContentOwner
                {
                    userId = p.ContentPermission.userId,
                    contentId = p.ContentPermission.contentId,
                    createUser = _authUtil.UserId
                })
                .ToList();

            if (!ownersToRemove.Any())
            {
                return;
            }

            if (currentOwners.Count() == 1 &&
                ownersToRemove.Any(owner => owner.userId == currentOwners.First().userId))
            {
                throw new InvalidOperationException("ONLY_ONE_OWNER");
            }

            var contentOwnerService = new ContentOwnerService(_authUtil.UserId, currentContent.contentId);
            contentOwnerService.SetContentUuId(currentContent.contentUuid);

            await contentOwnerService.RemoveRangeAsync(ownersToRemove);
        }

        private async System.Threading.Tasks.Task CheckIfUserIsNotASigner(Data.Entities.MeetingMinute minute, List<PermissionListViewModel> permissionsToRemove)
        {
            var minuteAttachment = minute.Content.ContentAttachment.FirstOrDefault();

            if (minuteAttachment == null)
            {
                return;
            }

            var signatureService = new SignatureService(_authUtil.UserId);
            var signatureContext = await signatureService.GetSignatureContext(
                minute.contentId,
                minuteAttachment.contentAttachmentId,
                refresh: false
            );

            var hasUnsignedSigner = signatureContext?.signers != null && signatureContext.signers
                .Any(signer => permissionsToRemove.Exists(permission =>
                    permission.ContentPermission.userId == signer.userId &&
                    signer.signDate == null
                ));

            if (signatureContext?.status != "OPEN" || hasUnsignedSigner)
            {
                throw new InvalidOperationException("MEMBER_CANNOT_BE_REMOVED");
            }
        }

        private void ValidateUsers(IEnumerable<int> usersIdAllowedInWorkgroup, List<PermissionListViewModel> permissionsToAdd)
        {
            permissionsToAdd.RemoveAll(permission => !usersIdAllowedInWorkgroup.Contains(permission.ContentPermission.userId));
        }

        private async System.Threading.Tasks.Task ValidateContent(Content currentContent)
        {
            if (!await _contentRepository.CheckPermissionsForWorkgroup(Operations.PERMISSIONS_UPDATE, currentContent.contentUuid))
            {
                throw new SecurityException("Unauthorized attempt to update permissions on content in workgroup.");
            }

            var featureManagerService = new FeatureManagerService(_authUtil.UserId);
            bool isPermissionManagementEnabled = await featureManagerService.isEnabledByContent(currentContent, PlanFeatureNames.PERMISSION_MANAGEMENT);

            if (!isPermissionManagementEnabled)
            {
                throw new InvalidOperationException("FEATURE_NOT_INCLUDED");
            }

            if (currentContent.status == ContentStatuses.ContentMeetingStatus.CLOSED && currentContent.type == ContentTypes.Poll)
            {
                throw new SecurityException("Unauthorized attempt to edit permission in a closed poll.");
            }
        }

        public async Task<int> AddRangeAsync(Guid contentUuid, List<ContentPermissionDTO> addUsers)
        {
            var contentId = await GetContentIdFromUuidAsync(contentUuid);
            return await AddPermissionsRangeAsync(contentId, contentUuid, addUsers);
        }

        public async Task<int> AddAsync(Guid contentUuid, int newPermissionUserId)
        {
            var contentId = await GetContentIdFromUuidAsync(contentUuid);
            return await AddAsync(contentId, contentUuid, newPermissionUserId);
        }

        public async Task<bool> DeleteAsync(Guid contentUuid, int userId)
        {
            var contentId = await GetContentIdFromUuidAsync(contentUuid);
            return await DeleteAsync(contentId, contentUuid, userId);
        }

        public async Task<List<ContentPermissionDTO>> GetUsersWithPermissionsInfoAsync(Guid contentUuid)
        {
            return await _contentRepository.GetContentPermissions(contentUuid);
        }

        public async Task<List<PermissionUserDto>> GetContentPermissionsAsync(Guid contentUuid)
        {
            ContentPermissionData contentData = await _atlasModelCore.Content
                .Where(c => c.contentUuid == contentUuid)
                .Select(c => new ContentPermissionData
                {
                    contentId = c.contentId,
                    contentuuid = c.contentUuid,
                    type = c.type,
                    status = c.status,
                    workgroupId = c.workgroupId,
                    parentContentId = c.parentContentId,
                    parentContentUuid = c.parentContentUuid,
                    createUser = c.createUser,
                    assignedUser = c.assignedUser,
                    reviewerUser = c.reviewerUser,
                    ContentPermissions = c.ContentPermission.Select(cp => new ContentPermissionData.ContentPermissionInfo
                    {
                        userId = cp.userId,
                        creationRule = cp.creationRule,
                        userName = cp.User.name,
                        userEmail = cp.User.email,
                        userProfilePic = cp.User.profilePic
                    }).ToList()
                })
                .FirstOrDefaultAsync();

            if (contentData == null)
                return new List<PermissionUserDto>();

            var currentPermissions = contentData.ContentPermissions
                .Select(cp => new PermissionUserDto()
                {
                    userId = cp.userId,
                    userName = cp.userName,
                    userEmail = cp.userEmail,
                    creationRule = cp.creationRule,
                    profilePic = cp.userProfilePic,
                    isChecked = true,
                    blockChange = contentData.type == ContentTypes.Poll && contentData.status == "CLOSED"
                }).ToList();

            if (contentData.status != ContentStatuses.ContentMeetingStatus.CLOSED || contentData.type != ContentTypes.Poll)
            {
                if (contentData.type != ContentTypes.Poll && contentData.type != ContentTypes.MeetingAgendaItem)
                {
                    await BuildWorkgroupPermissions(contentData, currentPermissions);
                }
                else if (contentData.status != ContentStatuses.ContentMeetingStatus.CLOSED
                         && contentData.parentContentUuid.HasValue)
                {
                    await BuildParentPermissions(currentPermissions, contentData);
                }
            }

            currentPermissions = currentPermissions.OrderBy(o => o.userName).ToList();

            MarkUsersWithPermissions(currentPermissions, contentData);

            ApplyBusinessRules(currentPermissions, contentData);

            return currentPermissions;
        }

        private async System.Threading.Tasks.Task BuildWorkgroupPermissions(ContentPermissionData contentData, List<PermissionUserDto> currentPermissionUsers)
        {
            var existingUserIds = new HashSet<int>(contentData.ContentPermissions.Select(cp => cp.userId));
            var isContentKbWithparent = (contentData.type == ContentTypes.KnowledgeDirectory && contentData.parentContentId.HasValue);

            var query = _atlasModelCore.WorkgroupUser
                        .Where(wu => wu.workgroupId == contentData.workgroupId && !existingUserIds.Contains(wu.userId))
                        .Join(
                            _atlasModelCore.User,
                            wu => wu.userId,
                            u => u.userId,
                            (wu, u) => new { wu.userId, u.name, u.email, u.profilePic }
                        );

            var workgroupUsers = await query.ToListAsync();

            HashSet<int> parentPermissionIds = null;
            if (isContentKbWithparent)
            {
                parentPermissionIds = new HashSet<int>(
                    await _atlasModelCore.ContentPermission
                        .Where(cp => cp.contentUuid == contentData.parentContentUuid)
                        .Select(cp => cp.userId)
                        .ToListAsync()
                );
            }

            var workgroupPermissionUsers = workgroupUsers
                .Select(u => new PermissionUserDto()
                {
                    userId = u.userId,
                    userName = u.name,
                    userEmail = u.email,
                    profilePic = u.profilePic,
                    isChecked = false,
                    blockChange = isContentKbWithparent && parentPermissionIds != null ? !parentPermissionIds.Contains(u.userId) : false
                }).ToList();

            var existingUserIdsInCurrent = new HashSet<int>(currentPermissionUsers.Select(cpu => cpu.userId));
            currentPermissionUsers.AddRange(workgroupPermissionUsers.Where(wpu => !existingUserIdsInCurrent.Contains(wpu.userId)));
        }

        private async System.Threading.Tasks.Task BuildParentPermissions(List<PermissionUserDto> currentPermissionUsers, ContentPermissionData contentData)
        {
            if (!contentData.parentContentId.HasValue)
                return;

            var existingUserIds = new HashSet<int>(contentData.ContentPermissions.Select(cp => cp.userId));

            var parentPermissionUsers = await _atlasModelCore.ContentPermission
                .Where(cp => cp.contentUuid == contentData.parentContentUuid && !existingUserIds.Contains(cp.userId))
                .Select(cp => new PermissionUserDto()
                {
                    userId = cp.userId,
                    userName = cp.User.name,
                    userEmail = cp.User.email,
                    profilePic = cp.User.profilePic,
                    isChecked = false,
                    blockChange = false
                })
                .ToListAsync();

            var existingUserIdsInCurrent = new HashSet<int>(currentPermissionUsers.Select(cpu => cpu.userId));
            currentPermissionUsers.AddRange(parentPermissionUsers.Where(ppu => !existingUserIdsInCurrent.Contains(ppu.userId)));
        }

        private static void MarkUsersWithPermissions(List<PermissionUserDto> permissions, ContentPermissionData contentData)
        {
            var userIdsWithPermission = new HashSet<int>(contentData.ContentPermissions.Select(cp => cp.userId));

            foreach (var permission in permissions)
            {
                if (userIdsWithPermission.Contains(permission.userId))
                {
                    permission.hasPermission = true;
                }
            }
        }

        private void ApplyBusinessRules(List<PermissionUserDto> permissions, ContentPermissionData contentData)
        {
            var currentUserId = _authUtil.UserId;
            var isTask = contentData.type == ContentTypes.Task;

            foreach (var item in permissions)
            {
                // Creator rules
                if (item.userId == contentData.createUser)
                {
                    if (item.userId == currentUserId)
                    {
                        item.warning = "CREATED";
                    }
                    else
                    {
                        if (item.isChecked && contentData.type != ContentTypes.Meeting &&
                            contentData.type != ContentTypes.MeetingAgendaItem &&
                            contentData.type != ContentTypes.Poll)
                        {
                            item.blockChange = true;
                            item.blockReason = "CREATED";
                        }
                    }
                }

                // Task-specific rules
                if (isTask)
                {
                    ApplyTaskSpecificRules(item, contentData, currentUserId);
                }
            }
        }

        private static void ApplyTaskSpecificRules(PermissionUserDto item, ContentPermissionData contentData, int currentUserId)
        {
            // Assigned user rules
            if (item.userId == contentData.assignedUser)
            {
                if (item.userId == currentUserId)
                {
                    item.warning = "ASSIGNED";
                }
                else if (item.isChecked)
                {
                    item.blockChange = true;
                    item.blockReason = "ASSIGNED";
                }
            }

            // Reviewer user rules
            if (item.userId == contentData.reviewerUser)
            {
                if (item.userId == currentUserId)
                {
                    item.warning = "REVIEWER";
                }
                else if (item.isChecked)
                {
                    item.blockChange = true;
                    item.blockReason = "REVIEWER";
                }
            }
        }

        public async Task<int> AddPermissionsRangeAsync(int contentId, Guid contentUuId, List<ContentPermissionDTO> addUsers)
        {
            var permissions = _contentRepository.List_Permissions(contentId);

            foreach (var item in addUsers)
            {
                if (!permissions.Select(x => x.userId).Contains(item.userId))
                {
                    await this.AddAsync(contentId, contentUuId, item.userId);
                }
            }

            return 1;
        }

        public async System.Threading.Tasks.Task RegisterAuditLog
            (
                int contentId,
                Guid contentUuid,
                List<ContentPermissionDTO> usersAdd,
                List<ContentPermissionDTO> usersRemove,
                List<ContentPermissionDTO> updatedChildContentUsersSummary = null
            )
        {
            var originalContentActivityId = _contentActivityService.Add(contentId, contentUuid, new ContentActivity()
            {
                contentId = contentId,
                contentUuid = contentUuid,
                date = DateTime.UtcNow,
                processed = false,
                type = Operations.PERMISSIONS_UPDATE,
                activityUser = _authUtil.UserId,
                contentData = JsonConvert.SerializeObject(new
                {
                    type = SubItems.PERMISSIONS,
                    added = usersAdd.Select(x => new { x.userId, x.userName }),
                    removed = usersRemove.Select(x => new { x.userId, x.userName })
                }),
                hidden = true,
                subItemType = SubItems.PERMISSIONS
            }, _requestContext.UserAgent);

            //terrible, but should do until we make a bulk contentActivity.Add method
            foreach (var item in usersAdd)
            {
                _contentActivityService.Add(contentId, contentUuid, new ContentActivity()
                {
                    contentId = contentId,
                    contentUuid = contentUuid,
                    date = DateTime.UtcNow,
                    processed = false,
                    type = PermissionAuditOperations.PERMISSIONS_ADDED,
                    activityUser = _authUtil.UserId,
                    hidden = true,
                    subItemType = AuditSubItemTypes.USER,
                    subItemId = item.userId,
                    originalContentActivityId = originalContentActivityId
                }, _requestContext.UserAgent);
            }

            foreach (var item in usersRemove)
            {
                _contentActivityService.Add(contentId, contentUuid, new ContentActivity()
                {
                    contentId = contentId,
                    contentUuid = contentUuid,
                    date = DateTime.UtcNow,
                    processed = false,
                    type = PermissionAuditOperations.PERMISSIONS_REMOVED,
                    activityUser = _authUtil.UserId,
                    hidden = true,
                    subItemType = AuditSubItemTypes.USER,
                    subItemId = item.userId,
                    originalContentActivityId = originalContentActivityId
                }, _requestContext.UserAgent);
            }

            if (updatedChildContentUsersSummary == null)
                return;

            var grouped_contents = updatedChildContentUsersSummary.GroupBy(o => o.contentUuid);

            var childContents = await _contentRepository.GetSimpleContentList(grouped_contents.Select(g => g.Key).ToArray());
            var childContentLookup = childContents.ToDictionary(c => c.contentUuid, c => c);

            foreach (var content_group in grouped_contents)
            {
                if (!childContentLookup.TryGetValue(content_group.Key, out var childContent))
                    continue;

                var childContentUuid = childContent.contentUuid;

                foreach (var user_perm in content_group)
                {
                    _contentActivityService.Add(user_perm.contentId, childContentUuid, new ContentActivity()
                    {
                        contentId = user_perm.contentId,
                        contentUuid = childContentUuid,
                        date = DateTime.UtcNow,
                        processed = false,
                        type = user_perm.allowed ? PermissionAuditOperations.PERMISSIONS_ADDED : PermissionAuditOperations.PERMISSIONS_REMOVED,
                        activityUser = _authUtil.UserId,
                        hidden = true,
                        subItemType = AuditSubItemTypes.USER,
                        subItemId = user_perm.userId,
                        originalContentActivityId = originalContentActivityId
                    }, _requestContext.UserAgent);
                }
            }
        }

        public async Task<int> AddAsync(int contentId, Guid contentUuid, int newPermissionUserId)
        {
            if (!(await _contentRepository.CheckPermissionsForWorkgroup(Operations.PERMISSIONS_ADD, contentUuid)))
            {
                throw new SecurityException("Unauthorized attempt to add a permission into a content in workgroup.");
            }

            var contentPermission = new Data.Entities.ContentPermission();
            contentPermission.contentId = contentId;
            contentPermission.userId = newPermissionUserId;
            contentPermission.createUser = _authUtil.UserId;
            contentPermission.createDate = DateTime.UtcNow;
            contentPermission.allowed = true;

            if ((await _contentRepository.AddPermission(contentUuid, contentPermission)) > 0)
            {
                _contentActivityService.Add(contentId, contentUuid, new ContentActivity()
                {
                    contentId = contentId,
                    contentUuid = contentUuid,
                    date = DateTime.UtcNow,
                    processed = false,
                    type = Operations.PERMISSIONS_ADD,
                    activityUser = _authUtil.UserId,
                    contentData = JsonConvert.SerializeObject(new { type = SubItems.PERMISSIONS, id = contentPermission.contentId }),
                    subItemId = newPermissionUserId,
                    subItemType = SubItems.PERMISSIONS,
                    hidden = true
                }, _requestContext.UserAgent);

                return newPermissionUserId;
            }
            else
            {
                return 0;
            }
        }

        public async Task<bool> DeleteAsync(int contentId, Guid contentUuid, int userId)
        {
            if (!(await _contentRepository.CheckPermissionsForWorkgroup(Operations.PERMISSIONS_DELETE, contentUuid)))
            {
                throw new SecurityException("Unauthorized attempt to delete content permission in workgroup.");
            }

            if ((await _contentRepository.DeletePermission(contentUuid, userId)) > 0)
            {
                new ContentActivityService(_authUtil.UserId).Add(contentId, contentUuid, new ContentActivity()
                {
                    contentId = contentId,
                    contentUuid = contentUuid,
                    date = DateTime.UtcNow,
                    processed = false,
                    type = Operations.PERMISSIONS_DELETE,
                    activityUser = _authUtil.UserId,
                    contentData = JsonConvert.SerializeObject(new { type = SubItems.PERMISSIONS, id = userId }),
                    subItemId = userId,
                    subItemType = SubItems.PERMISSIONS,
                    hidden = true
                }, _requestContext.UserAgent);

                return true;
            }
            else
            {
                return false;
            }
        }

        private List<ContentPermissionDTO> ManageChildPermissions(
            Content currentContent,
            int contentId,
            List<ContentPermissionDTO> usersAdd,
            List<ContentPermissionDTO> usersRemove)
        {
            var result = new List<ContentPermissionDTO>();

            var supportedTypes = new[] { "Pipeline", "Meeting", "KnowledgeDirectory" };
            if (!supportedTypes.Contains(currentContent.type))
            {
                return result;
            }

            var updatedPermissions = _contentRepository.UpdateChildPermissions(
                currentContent,
                usersAdd.Select(u => u.userId).ToList(),
                usersRemove.Select(u => u.userId).ToList()
            );

            result = updatedPermissions.Select(p => new ContentPermissionDTO
            {
                userId = p.userId,
                contentId = p.contentId,
                contentUuid = p.contentUuid,
                allowed = p.allowed,
                userName = p.User?.name,
                userEmail = p.User?.email
            }).ToList();

            return result;
        }

        private async System.Threading.Tasks.Task ManageMinutePermission(
            Content currentContent,
            List<ContentPermissionDTO> usersAdd,
            List<ContentPermissionDTO> usersRemove,
            Content parentContent)
        {
            if (parentContent == null)
                return;

            switch (currentContent.type)
            {
                case ContentTypes.MeetingAgendaItem:
                    await ManageMeetingAgendaItemPermissions(currentContent, usersAdd, usersRemove, parentContent);
                    break;
                case ContentTypes.Poll:
                    await ManagePollPermissions(currentContent, usersAdd, usersRemove, parentContent);
                    break;
                case ContentTypes.Task:
                    await ManageTaskPermissions(currentContent, usersAdd, usersRemove, parentContent);
                    break;
            }
        }

        private async System.Threading.Tasks.Task ManageMeetingAgendaItemPermissions(
            Content currentContent,
            List<ContentPermissionDTO> usersAdd,
            List<ContentPermissionDTO> usersRemove,
            Content parentContent)
        {
            var allAgendas = parentContent.Child_Content
                .Where(c => c.type == ContentTypes.MeetingAgendaItem && c.deleted != true)
                .ToList();

            var userToAddMinute = new List<ContentPermissionDTO>();
            var changeMinutePermissions = usersRemove.Any();

            if (usersAdd.Any())
            {
                var userIds = usersAdd.Select(u => u.userId).ToList();
                var contentIds = allAgendas.Select(a => a.contentId).ToList();
                var allAgendasPermissions = _contentRepository.GetAllForContents(userIds, contentIds);

                foreach (var user in usersAdd)
                {
                    var userPermissions = allAgendasPermissions.Where(p => p.userId == user.userId);
                    var agendasWithoutPermission = allAgendas
                        .Where(a => !userPermissions.Select(p => p.contentId).Contains(a.contentId));

                    if (!agendasWithoutPermission.Any())
                    {
                        userToAddMinute.Add(user);
                    }
                }

                changeMinutePermissions = changeMinutePermissions || userToAddMinute.Any();
            }

            if (changeMinutePermissions)
            {
                await UpdateMinutePermissions(parentContent, userToAddMinute, usersRemove);
            }
        }

        private async System.Threading.Tasks.Task ManagePollPermissions(
            Content currentContent,
            List<ContentPermissionDTO> usersAdd,
            List<ContentPermissionDTO> usersRemove,
            Content parentContent)
        {
            var allPolls = parentContent.Child_Content
                .Where(c => c.type == ContentTypes.Poll && c.deleted != true)
                .ToList();

            var userToAddMinute = new List<ContentPermissionDTO>();
            var userToRemoveMinute = new List<ContentPermissionDTO>();
            var changeMinutePermissions = false;

            if (usersRemove.Any())
            {
                var agendaPermissions = parentContent.Child_Content
                    .Where(c => c.type == ContentTypes.MeetingAgendaItem && c.deleted != true)
                    .ToList();

                if (agendaPermissions.Any())
                {
                    userToRemoveMinute = usersRemove.Where(u =>
                        !agendaPermissions.All(a => a.ContentPermission.Any(cp => cp.userId == u.userId))
                    ).ToList();
                }

                changeMinutePermissions = true;
            }

            if (usersAdd.Any())
            {
                var userIds = usersAdd.Select(u => u.userId).ToList();
                var contentIds = allPolls.Select(p => p.contentId).ToList();
                var allPollPermissions = _contentRepository.GetAllForContents(userIds, contentIds);

                foreach (var user in usersAdd)
                {
                    var userPermissions = allPollPermissions.Where(p => p.userId == user.userId);
                    var pollsWithoutPermission = allPolls
                        .Where(p => !userPermissions.Select(perm => perm.contentId).Contains(p.contentId));

                    if (!pollsWithoutPermission.Any())
                    {
                        userToAddMinute.Add(user);
                    }
                }

                changeMinutePermissions = changeMinutePermissions || userToAddMinute.Any();
            }

            if (changeMinutePermissions)
            {
                await UpdateMinutePermissions(parentContent, userToAddMinute, userToRemoveMinute);
            }
        }

        private async System.Threading.Tasks.Task ManageTaskPermissions(
            Content currentContent,
            List<ContentPermissionDTO> usersAdd,
            List<ContentPermissionDTO> usersRemove,
            Content parentContent)
        {
            if (!currentContent.parentContentId.HasValue)
                return;

            var allTasks = parentContent.Child_Content
                .Where(c => c.type == ContentTypes.Task && c.deleted != true)
                .ToList();

            var userToAddMinute = new List<ContentPermissionDTO>();
            var userToRemoveMinute = new List<ContentPermissionDTO>();
            var changeMinutePermissions = false;

            if (usersRemove.Any())
            {
                var agendaPermissions = parentContent.Child_Content
                    .Where(c => c.type == ContentTypes.MeetingAgendaItem && c.deleted != true)
                    .ToList();

                if (agendaPermissions.Any())
                {
                    userToRemoveMinute = usersRemove.Where(u =>
                        !agendaPermissions.All(a => a.ContentPermission.Any(cp => cp.userId == u.userId))
                    ).ToList();
                }

                changeMinutePermissions = true;
            }

            if (usersAdd.Any())
            {
                var userIds = usersAdd.Select(u => u.userId).ToList();
                var contentIds = allTasks.Select(t => t.contentId).ToList();
                var allTaskPermissions = _contentRepository.GetAllForContents(userIds, contentIds);

                foreach (var user in usersAdd)
                {
                    var userPermissions = allTaskPermissions.Where(p => p.userId == user.userId);
                    var tasksWithoutPermission = allTasks
                        .Where(t => !userPermissions.Select(p => p.contentId).Contains(t.contentId));

                    if (!tasksWithoutPermission.Any())
                    {
                        userToAddMinute.Add(user);
                    }
                }

                changeMinutePermissions = changeMinutePermissions || userToAddMinute.Any();
            }

            if (changeMinutePermissions)
            {
                await UpdateMinutePermissions(parentContent, userToAddMinute, userToRemoveMinute);
            }
        }

        private async System.Threading.Tasks.Task UpdateMinutePermissions(
            Content parentContent,
            List<ContentPermissionDTO> userToAdd,
            List<ContentPermissionDTO> userToRemove)
        {
            var allMinutes = parentContent.Child_Content
                .Where(c => c.type == ContentTypes.MeetingMinute)
                .ToList();

            if (!allMinutes.Any())
                return;

            var entitiesToAdd = userToAdd.Select(dto => new Data.Entities.ContentPermission
            {
                userId = dto.userId,
                allowed = true,
                createUser = _authUtil.UserId,
                createDate = DateTime.UtcNow,
                creationRule = "PSvc_AllAgendas_GrantMinute"
            }).ToList();

            var entitiesToRemove = userToRemove.Select(dto => new Data.Entities.ContentPermission
            {
                userId = dto.userId
            }).ToList();

            foreach (var minute in allMinutes)
            {
                await _contentRepository.Set_Permission(
                    minute.contentUuid,
                    minute.contentId,
                    entitiesToAdd,
                    entitiesToRemove,
                    "PSvc_AllAgendas_GrantMinute"
                );

                await RegisterAuditLog(
                    minute.contentId,
                    minute.contentUuid,
                    userToAdd,
                    userToRemove
                );
            }
        }
    }
}