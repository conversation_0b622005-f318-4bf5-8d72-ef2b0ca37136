using Atlas.Business.ViewModels;
using Atlas.CrossCutting.DTO.ExternalDocumentRequest;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;

namespace Atlas.Application.Interfaces
{
    /// <summary>
    /// Interface for External User business layer operations
    /// </summary>
    public interface IExternalUserService
    {
        /// <summary>
        /// Gets external users by content ID
        /// </summary>
        /// <param name="contentUuId">The content UUID</param>
        /// <param name="clientId">The client ID</param>
        /// <param name="workgroupId">The workgroup ID</param>
        /// <returns>List of external users</returns>
        Task<List<ExternalUserDTO>> GetExternalUsersByContentId(Guid contentUuId, int clientId, int workgroupId);

        /// <summary>
        /// Saves an external user
        /// </summary>
        /// <param name="contentUuId">The content UUID</param>
        /// <param name="clientId">The client ID</param>
        /// <param name="workgroupId">The workgroup ID</param>
        /// <param name="externalUser">The external user data</param>
        /// <returns>The external user ID</returns>
        Task<(int?, int?)> SaveExternalUser(Guid contentUuId, int clientId, int workgroupId, ExternalUserDTO externalUser);

        /// <summary>
        /// Updates an external user
        /// </summary>
        /// <param name="contentUuId">The content UUID</param>
        /// <param name="clientId">The client ID</param>
        /// <param name="workgroupId">The workgroup ID</param>
        /// <param name="externalUser">The external user data</param>
        /// <returns>True if successful</returns>
        Task<bool> UpdateExternalUser(Guid contentUuId, int clientId, int workgroupId, ExternalUserDTO externalUser);

        /// <summary>
        /// Deletes an external user
        /// </summary>
        /// <param name="externalUserId">The external user ID</param>
        /// <param name="recurringGuestId">The recurring guest ID</param>
        /// <param name="contentUuId">The content UUID</param>
        /// <param name="clientId">The client ID</param>
        /// <param name="workgroupId">The workgroup ID</param>
        /// <returns>True if successful</returns>
        Task<bool> DeleteExternalUser(int? externalUserId, int? recurringGuestId, Guid contentUuId, int clientId, int workgroupId);

        /// <summary>
        /// Verifies external user key
        /// </summary>
        /// <param name="externalUserId">The external user ID</param>
        /// <param name="hash">The hash to verify</param>
        /// <returns>Verification result</returns>
        Task<ExternalUser2FAInfo> VerifyExternalKey(int externalUserId, string hash);

        /// <summary>
        /// Sends 2FA code to external user
        /// </summary>
        /// <param name="externalUserSendCode">The send code request</param>
        /// <returns>2FA response</returns>
        Task<ExternalUser2FAInfo> SendExternalUser2FA(ExternalUserSendCodeDTO externalUserSendCode);

        /// <summary>
        /// Validates 2FA code for external user
        /// </summary>
        /// <param name="externalUser2FA">The 2FA validation request</param>
        /// <returns>Validation result</returns>
        Task<TwoFactorConfirmViewModel> ValidateExternalUser2FA(ExternalUser2FADTO externalUser2FA);
    }
}