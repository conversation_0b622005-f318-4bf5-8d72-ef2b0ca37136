using Atlas.Application.Interfaces;
using Atlas.Business.ViewModels;
using Atlas.CrossCutting.DTO.ExternalDocumentRequest;
using Atlas.CrossCutting.Helpers;
using Atlas.CrossCutting.Services;
using Atlas.CrossCutting.Settings;
using Atlas.Data.Entities;
using Atlas.Data.Repository;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Security.Principal;
using System.Threading.Tasks;

namespace Atlas.Business.Core.ExternalUser
{
    public class ExternalUserService : IExternalUserService
    {
        private readonly AuthUtil _authUtil;
        private readonly StorageSettings _storageSettings;
        private readonly AtlasModelCore _atlasModelCore;
        private readonly IDbContextTransaction _transaction;
        private readonly ContentRepository _contentRepository;
        private readonly Business.ExternalUserService _legacyExternalUserService;
        private readonly ILogger<ExternalUserService> _logger;
        private readonly IRequestContextService _requestContext;
        private readonly Microsoft.Extensions.Configuration.IConfiguration _configuration;
        private readonly TwilioSettings _twilioSettings;

        public ExternalUserService(
            IPrincipal principal,
            StorageSettings storageSettings,
            IRequestContextService requestContext,
            AtlasModelCore atlasModelCore = null,
            IDbContextTransaction transaction = null,
            ILogger<ExternalUserService> logger = null,
            Microsoft.Extensions.Configuration.IConfiguration configuration = null,
            TwilioSettings twilioSettings = null)
        {
            _authUtil = new AuthUtil(principal);
            _storageSettings = storageSettings;
            _requestContext = requestContext;
            _atlasModelCore = atlasModelCore;
            _transaction = transaction;
            _logger = logger;
            _configuration = configuration;
            _twilioSettings = twilioSettings;

            //annonymous route
            if (_authUtil.UserId == 0)
            {
                _legacyExternalUserService = new(_requestContext, _configuration, _twilioSettings);
            }
            else
            {
                _contentRepository = _atlasModelCore != null && _transaction != null
                   ? new ContentRepository(_authUtil.UserId, _atlasModelCore, _transaction)
                   : new ContentRepository(_authUtil.UserId);

                _legacyExternalUserService = new(_authUtil.UserId, _requestContext, _configuration, _twilioSettings);
            }
        }

        public async Task<bool> DeleteExternalUser(int? externalUserId, int? recurringGuestId, Guid contentUuId, int clientId, int workgroupId)
        {
            try
            {
                return await _legacyExternalUserService.DeleteExternalUser(externalUserId, recurringGuestId, contentUuId);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error deleting external user. ExternalUserId: {externalUserId}",
                    externalUserId);
                throw;
            }
        }

        public async Task<List<ExternalUserDTO>> GetExternalUsersByContentId(Guid contentUuId, int clientId, int workgroupId)
        {
            try
            {
                return await _legacyExternalUserService.GetExternalUsersByContentUuId(contentUuId);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error getting external users by content ID. ContentUuId: {contentUuId}",
                    contentUuId);
                throw;
            }
        }

        public async Task<(int?, int?)> SaveExternalUser(Guid contentUuId, int clientId, int workgroupId, ExternalUserDTO externalUser)
        {
            try
            {
                var contentId = await _contentRepository.GetContentIdByUuIdAsync(contentUuId);
                return await _legacyExternalUserService.SaveExternalUser(contentUuId, contentId, externalUser);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error saving external user. ContentUuId: {contentUuId}, ExternalUserId: {externalUserId}",
                    contentUuId, externalUser?.ExternalUserId);
                throw;
            }
        }

        public async Task<ExternalUser2FAInfo> SendExternalUser2FA(ExternalUserSendCodeDTO externalUserSendCode)
        {
            try
            {
                return await _legacyExternalUserService.SendExternalUser2FA(externalUserSendCode);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error sending external user 2FA. ExternalUserId: {externalUserId}",
                    externalUserSendCode?.ExternalUserId);
                throw;
            }
        }

        public async Task<bool> UpdateExternalUser(Guid contentUuId, int clientId, int workgroupId, ExternalUserDTO externalUser)
        {
            try
            {
                var contentId = await _contentRepository.GetContentIdByUuIdAsync(contentUuId);
                return await _legacyExternalUserService.UpdateExternalUser(contentUuId, contentId, externalUser);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error updating external user. ContentUuId: {contentUuId}, ExternalUserId: {externalUserId}",
                    contentUuId, externalUser?.ExternalUserId);
                throw;
            }
        }

        public async Task<TwoFactorConfirmViewModel> ValidateExternalUser2FA(ExternalUser2FADTO externalUser2FA)
        {
            try
            {
                return await _legacyExternalUserService.ValidateExternalUser2FA(externalUser2FA);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error validating external user 2FA. ExternalUserId: {externalUserId}",
                    externalUser2FA?.ExternalUserId);
                throw;
            }
        }

        public async Task<ExternalUser2FAInfo> VerifyExternalKey(int externalUserId, string hash)
        {
            try
            {
                return await _legacyExternalUserService.VerifyExternalKey(externalUserId, hash);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error verifying external key. ExternalUserId: {externalUserId}",
                    externalUserId);
                throw;
            }
        }
    }
}
