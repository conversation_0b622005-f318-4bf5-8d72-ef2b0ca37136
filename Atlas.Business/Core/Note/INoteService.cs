using Atlas.Business.ViewModels;
using Atlas.CrossCutting.DTO.Note;
using Atlas.Data.Entities;
using Atlas.Data.Repository;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Atlas.Business.Core.Note
{
    public interface INoteService
    {
        Task<(List<NoteViewModel> Items, int Total)> GetListAsync(ContentRequestFilter filters, CancellationToken ct = default);
        Task<NoteDetailDTO> GetNoteByContentUuidAsync(Guid contentUuid, CancellationToken ct = default);
        Task<Guid> CreateNoteAsync(Content content, string userAgent, List<Atlas.Data.Entities.ContentPermission> permissions,
                                   User createUser = null, List <ContentActivity> activities = null,
                                   List<Atlas.Data.Entities.ContentSubscriber> subscribers = null, bool supressActivity = false);

        Task<object> UpdateNoteAsync(Content content, bool individuallyUpdated = true);

        Task<bool> DeleteAsync(Guid contentUuid, string userAgent, bool individuallyDeleted = true);

    }
}
