using Atlas.Business.Helpers;
using Atlas.Business.ViewModels;
using Atlas.CrossCutting.AppEnums;
using Atlas.CrossCutting.DTO.Note;
using Atlas.CrossCutting.Exceptions;
using Atlas.CrossCutting.Helpers;
using Atlas.CrossCutting.Logging;
using Atlas.Data.Entities;
using Atlas.Data.Repository;
using Ganss.Xss;
using Mapster;
using Newtonsoft.Json;
using SharpRaven;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Security;
using System.Security.Principal;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using static Atlas.CrossCutting.AppEnums.ContentStatuses;

namespace Atlas.Business.Core.Note
{
    public sealed class NoteService : INoteService
    {
        private readonly AuthUtil _authUtil;
        private readonly NoteRepository _noteRepository;
        private readonly int _currentUserId;

        public NoteService(
            IPrincipal principal,
            IStructuredLogger logger)
        {
            _authUtil = new AuthUtil(principal);
            _noteRepository = new NoteRepository(_authUtil.UserId);
            _currentUserId = _authUtil.UserId;
        }

        public async Task<(List<NoteViewModel> Items, int Total)> GetListAsync(ContentRequestFilter filters,
            CancellationToken ct = default)
        {
            ArgumentNullException.ThrowIfNull(filters);

            NormalizePaging(filters);

            var (items, total) = await _noteRepository.GetNoteListAsync(_currentUserId, filters, ct);

            var noteViewModels = items.Adapt<List<NoteViewModel>>();

            // pós-processo: truncar para preview (200)
            foreach (var t in noteViewModels)
            {
                var plain = CrossCutting.HtmlFreeText
                    .HtmlToPlainText(t.text ?? string.Empty)
                    .Replace("\n", " ");

                t.text = plain.Length > 200 ? plain[..200] + "..." : plain;
            }

            var desc = filters.orderBy;
            noteViewModels = desc
                ? noteViewModels.OrderByDescending(x => x.lastUpdate).ThenByDescending(x => x.contentId).ToList()
                : noteViewModels.OrderBy(x => x.lastUpdate).ThenBy(x => x.contentId).ToList();

            return (noteViewModels, total);
        }

        public async Task<NoteDetailDTO> GetNoteByContentUuidAsync(Guid contentUuid, CancellationToken ct = default)
        {
            var noteDetail = await _noteRepository.GetNoteByContentUuidAsync(_currentUserId, contentUuid, ct);
            return noteDetail;
        }

        public async Task<Guid> CreateNoteAsync (Content content, string userAgent, List<Atlas.Data.Entities.ContentPermission> permissions,
                                                 User createUser = null, List<ContentActivity> activities = null,
                                                 List<Atlas.Data.Entities.ContentSubscriber> subscribers = null, bool supressActivity = false)
        {

            List<Atlas.Data.Entities.ContentOwner> contentOwner = null;

            if (activities == null)
                activities = new List<ContentActivity>();

            if ((content.title != null && content.title.Length > 10000) || (content.type == ContentTypes.Announcement && string.IsNullOrWhiteSpace(content.title)))
                throw new ArgumentNullException("ERROR_TITLE");

            WorkgroupViewModel workgroupViewModel = await new WorkgroupService(_currentUserId).Get(content.workgroupId, includeHomeData: false);

            if (workgroupViewModel.archived)
                throw new Exception("BOARD ARCHIVED");

            WorkgroupRepository workgroupRepository = new WorkgroupRepository(_currentUserId);

            var taskToBeAdded = content.Task.FirstOrDefault();
            bool isExternalWkScenario = taskToBeAdded?.externalWorkgroupId != null && taskToBeAdded.externalWorkgroupId != 0;

            if (isExternalWkScenario)
            {
                if (await workgroupRepository.HasClientMismatching((int)taskToBeAdded.externalWorkgroupId, workgroupViewModel.clientId))
                    throw new SecurityException("INVALID_GRANT");
            }

            List<User> workGroupUserList = workgroupRepository.GetAllUsersFromWorkgroup(isExternalWkScenario ? (int)taskToBeAdded.externalWorkgroupId : content.workgroupId);

            if (isExternalWkScenario && !workGroupUserList.Any(u => u.userId == content.assignedUser))
                throw new SecurityException("INVALID_ASSIGNED_USER");

            var boardOwners = workgroupViewModel.WorkgroupOwner;

            FeatureManagerService featureManagerService = new FeatureManagerService(_currentUserId);

            await featureManagerService.checkPlanQuota(workgroupViewModel.clientId, PlanFeatureNames.BOARD_QUOTA, false);

            content.Note.First().text = SanitizeText(content); 

            if (permissions == null)
            {
                permissions = new List<Atlas.Data.Entities.ContentPermission>();

                if (content.assignedUser != null)
                    permissions.Add(new Atlas.Data.Entities.ContentPermission() { createUser = _currentUserId, userId = content.assignedUser.Value, allowed = true, createDate = DateTime.UtcNow });
                else
                {
                    permissions.Add(new Atlas.Data.Entities.ContentPermission() { createUser = _currentUserId, userId = _currentUserId, allowed = true, createDate = DateTime.UtcNow });
                }
            }

            if (contentOwner == null)
            {
                contentOwner = new List<Atlas.Data.Entities.ContentOwner>();

                workGroupUserList.ForEach((x) =>
                {
                    contentOwner.Add(new Atlas.Data.Entities.ContentOwner()
                    {
                        userId = x.userId,
                        createUser = _currentUserId,
                        createDate = DateTime.UtcNow
                    });
                });
            }

            if (subscribers == null)
            {
                subscribers = new List<Atlas.Data.Entities.ContentSubscriber>();

                if (content.assignedUser != null)
                    subscribers.Add(new Atlas.Data.Entities.ContentSubscriber() { userId = content.assignedUser.Value, createDate = DateTime.UtcNow });
                else
                {
                    subscribers.Add(new Atlas.Data.Entities.ContentSubscriber() { userId = _currentUserId, createDate = DateTime.UtcNow });
                }
            }

            if (content.ContentAttachment != null)
                content.ContentAttachment = new List<ContentAttachment>();

            if (isExternalWkScenario)
                content.workgroupId = (int)taskToBeAdded.externalWorkgroupId;

            if (Helpers.ContentChecker.Check(content))
            {
                Content createdContent = null;

                string titleSanitized = content.Meeting.FirstOrDefault()?.title ?? content.MeetingAgendaItem.FirstOrDefault()?.title ?? content.title;

                if (!string.IsNullOrEmpty(titleSanitized))
                    content.title = Regex.Replace(titleSanitized, @"[<>]", "");

                try
                {
                    createdContent = _noteRepository.Add(content.workgroupId, content, permissions, subscribers, contentOwner, activities);
                }
                catch (Exception ex)
                {
                    var ravenClient = new RavenClient("https://6aa2f8bcac3e41e198dc414d24d19e45:<EMAIL>/162539");
                    var sentryEvent = new SharpRaven.Data.SentryEvent(new SharpRaven.Data.SentryMessage("Error while creating content"));

                    sentryEvent.Extra = new
                    {
                        ex,
                        content,
                        permissions,
                        subscribers,
                        contentOwner,
                        activities
                    };

                    ravenClient.Capture(sentryEvent);

                    return Guid.Empty;
                }

                if (createdContent != null)
                {
                    if (supressActivity)
                        return createdContent.contentUuid;

                    string title = content.title;

                    //todo: mover para business
                    activities = new List<ContentActivity>();

                    int? isShared = (content.assignedUser != null ? content.assignedUser : 0);
                    string cType = "CREATED";

                    if (isShared > 0)
                        cType = "SHARED";

                    new ContentActivityService(_currentUserId).Add(createdContent.contentId, createdContent.contentUuid, new ContentActivity()
                    {
                        date = DateTime.UtcNow,
                        type = cType,
                        activityUser = _currentUserId,
                        contentData = JsonConvert.SerializeObject(new
                        {
                            title = title
                        }),
                        hidden = true
                    }, userAgent);
                }

                return createdContent.contentUuid;
            }
            else
            {
                return Guid.Empty;
            }
        }

        public async Task<object> UpdateNoteAsync(Content content, bool individuallyUpdated = true)
        {
            content.individuallyUpdated = individuallyUpdated;

            if (!_noteRepository.CheckPermissionsForWorkgroup(Operations.UPDATE, content.contentId))
                throw new SecurityException("Unauthorized attempt to update data to workgroup.");

            Content currentContent = await new ContentService(_currentUserId).Get(content.contentId);

            Content parentContent = new Content();

            if (currentContent != null && currentContent.parentContentId.HasValue)
            {
                parentContent = await _noteRepository.Get((int)currentContent.parentContentId);

                if (parentContent != null
                    && parentContent.type == ContentTypes.Meeting
                    && parentContent.status == ContentMeetingStatus.CANCELLED)
                    throw new InvalidOperationException("NOT_ALLOWED");
            }

            this.CheckUpdatesUAC(currentContent, content);

            Dictionary<string, string> guest_add = new Dictionary<string, string>();
            Dictionary<string, string> guest_remove = new Dictionary<string, string>();
            Dictionary<string, string> guest_update = new Dictionary<string, string>();

            GuestService guestService = new GuestService(_currentUserId);

            //sanitização no update
            if (content.type == ContentTypes.Note || content.Note.Any())       
                content.Note.First().text = SanitizeText(content);               

            //update do content
            Content updatedContent = await _noteRepository.Update(content.contentId, content);

            string updatedFields = null;

            if (updatedContent != null)
            {
                ContentActivityService contentActivityService = new ContentActivityService(_currentUserId);

                var hidden = true;

                //gambiarra, mudar o tipo do activityType (criar novos se for o caso) 
                if (content.Meeting.Any())
                {
                    if (content.Meeting.FirstOrDefault() != null)
                    {
                        bool changed_date = content.Meeting.FirstOrDefault().date.Year != 1;
                        bool changed_location = content.Meeting.FirstOrDefault().location != null;
                        bool changed_title = content.Meeting.FirstOrDefault().title != null;
                        bool changed_duration = content.Meeting.FirstOrDefault().duration != null;
                        bool changed_sendICS = content.Meeting.FirstOrDefault().sendICS == true;
                        bool changed_conferenceURL = content.Meeting.FirstOrDefault().conferenceURL != null;
                        bool changed_conferenceType = content.Meeting.FirstOrDefault().conferenceType != null;

                        updatedFields = changed_date ? "date;" : "";
                        updatedFields += changed_location ? "location;" : "";
                        updatedFields += changed_title ? "title;" : "";
                        updatedFields += changed_duration ? "duration;" : "";
                        updatedFields += changed_sendICS ? "sendICS;" : "";
                        updatedFields += changed_conferenceType || changed_conferenceURL ? "conference;" : "";

                        //setting processed true to avoid send email in case of an mass update of a recurring meeting
                        var processed = currentContent.recurringMeetingId != null && individuallyUpdated != true;

                        if (!String.IsNullOrEmpty(updatedFields) && !processed)
                        {
                            await _noteRepository.UpdateContentSubscribersRSVP(content.contentId);

                            if (content.Meeting.FirstOrDefault().sendICS == true)
                                await _noteRepository.UpdateGuestsRSVP(content.contentId);
                        }

                        var contentActivity = new ContentActivity()
                        {
                            contentId = content.contentId,
                            date = DateTime.UtcNow,
                            processed = processed,
                            type = "UPDATED",
                            activityUser = _currentUserId,
                            newData = content.Meeting.FirstOrDefault().date.ToString(),
                            hidden = false,
                            updatedFields = updatedFields
                        };

                        var contentActivityId = contentActivityService.Add(content.contentId, contentActivity);

                        //If this is a recurring meeting ocurrence Content, send ICS update of this ocurrence only to Guests
                        if (currentContent.recurringMeetingId != null && individuallyUpdated != true)
                        {
                            var new_notifications = new List<ActivityNotification>();
                            AtlasModelCore md = new AtlasModelCore();
                            List<ContentGuest> contentGuests = md.ContentGuest.Where(cg => cg.contentId == content.contentId).ToList();
                            if (contentGuests.Any(p => p.contentId == content.contentId && p.guestNotification.HasValue && p.guestNotification.Value == true && !string.IsNullOrEmpty(p.guestMail)))
                            {
                                new_notifications.Add(new ActivityNotification()
                                {
                                    contentActivityId = contentActivityId,
                                    userId = _currentUserId,
                                    isRead = false,
                                    notificationReasonFlag = "GUEST",
                                    groupable = false,
                                    sendMail = true,
                                    sms = false,
                                    pushNotification = false,
                                    generationDate = DateTime.UtcNow,
                                    processed = false
                                });
                            }

                            if (new_notifications.Count() > 0)
                            {
                                md.ContentActivityNotification.AddRange(new_notifications);
                                md.SaveChanges();
                            }
                        }

                        //novo metodo pra obter direto do banco (independente do usuario atual ter permissao nas PAUTAS, todos os guests devem ser notificados)
                        var target_guests = _noteRepository.GetGuestsForMeeting(currentContent.contentId);

                        //S-31 - should only notify guests again if location, date or conference has changed
                        if (changed_location || changed_date || changed_conferenceType || changed_conferenceURL || changed_sendICS)
                        {
                            if (target_guests.Any())
                            {
                                contentActivityService.Add(updatedContent.contentId, new ContentActivity()
                                {
                                    date = DateTime.UtcNow,
                                    type = "MEETING_UPDATED_GUESTS",
                                    activityUser = _currentUserId,
                                    contentData = Newtonsoft.Json.JsonConvert.SerializeObject(target_guests),
                                    hidden = true

                                });
                            }

                            var listener_guest = _noteRepository.GetGuestsForMeeting(content.contentId);

                            if (listener_guest.Any())
                            {
                                contentActivityService.Add(updatedContent.contentId, new ContentActivity()
                                {
                                    date = DateTime.UtcNow,
                                    type = "MEETING_LISTENER_GUEST_UPDATE",
                                    activityUser = _currentUserId,
                                    contentData = Newtonsoft.Json.JsonConvert.SerializeObject(listener_guest),
                                    hidden = true
                                });
                            }
                        }
                    }
                }

                if (content.MeetingMinute.Any())
                {
                    if (content.MeetingMinute.FirstOrDefault().published == true)
                    {
                        contentActivityService.Add(content.contentId, new ContentActivity()
                        {
                            contentId = content.contentId,
                            date = DateTime.UtcNow,
                            processed = false,
                            type = "STATUS_PUBLISHED",
                            activityUser = _currentUserId,
                            hidden = hidden

                        });
                    }

                    if (content.MeetingMinute.FirstOrDefault().published == false)
                    {
                        contentActivityService.Add(content.contentId, new ContentActivity()
                        {
                            contentId = content.contentId,
                            date = DateTime.UtcNow,
                            processed = false,
                            type = "STATUS_UNPUBLISHED",
                            activityUser = _currentUserId,
                            hidden = hidden
                        });
                    }
                }

                //GUESTS TO ADD
                if (guest_add.Any())
                {
                    foreach (var item in guest_add)
                    {
                        Dictionary<string, string> target_guests = new Dictionary<string, string>();
                        target_guests.Add(item.Key, item.Value);

                        contentActivityService.Add(content.contentId, new ContentActivity()
                        {
                            date = DateTime.UtcNow,
                            type = "MEETING_GUEST_ADD",
                            activityUser = _currentUserId,
                            hidden = true,
                            contentData = Newtonsoft.Json.JsonConvert.SerializeObject(target_guests)
                        });
                    }

                }

                //GUESTS TO REMOVE
                if (guest_remove.Any())
                {
                    foreach (var item in guest_remove)
                    {
                        Dictionary<string, string> target_guests = new Dictionary<string, string>();
                        target_guests.Add(item.Key, item.Value);

                        contentActivityService.Add(content.contentId, new ContentActivity()
                        {
                            date = DateTime.UtcNow,
                            type = "MEETING_GUEST_DELETE",
                            activityUser = _currentUserId,
                            hidden = true,
                            contentData = Newtonsoft.Json.JsonConvert.SerializeObject(target_guests)
                        });
                    }
                }

                //GUESTS TO UPDATE
                if (guest_update.Any())
                {
                    foreach (var item in guest_update)
                    {
                        Dictionary<string, string> target_guests = new Dictionary<string, string>();
                        target_guests.Add(item.Key, item.Value);

                        if (target_guests.Any())
                        {
                            //entao, criar activity - mandar e-mail
                            contentActivityService.Add(content.contentId, new ContentActivity()
                            {
                                date = DateTime.UtcNow,
                                type = "MEETING_UPDATED_GUESTS",
                                activityUser = _currentUserId,
                                contentData = Newtonsoft.Json.JsonConvert.SerializeObject(target_guests),
                                hidden = true
                            });
                        }
                    }
                }
            }

            MailNotificationHelper mailNotificationHelper = new MailNotificationHelper();

            return 1;
        }

        private static void NormalizePaging(ContentRequestFilter f)
        {
            if (f.pageNumber <= 0) f.pageNumber = 1;
            if (f.pageSize is <= 0 or > 200) f.pageSize = 20;
        }

        public async Task<bool> DeleteAsync(Guid contentUuid, string userAgent, bool individuallyDeleted = true)
        {
            ContentService contentService = new ContentService(_authUtil.UserId);

            var content = await contentService.Get(contentUuid);

            var isWorkgroupOwner = content.Workgroup.WorkgroupOwner.Select(u => u.userId).Contains(_currentUserId);
            var perms = contentService.GetUAC(content);

            #region Validations: business, security, and so on.
            int? isShared = (content.assignedUser != null ? content.assignedUser : 0);

            //Verificar se a nota foi compartilhada
            if (isShared > 0 && content.assignedUser != _currentUserId)
                return false;

            //Verificar se é o proprietário da nota
            if (isShared == 0 && content.createUser != _currentUserId)
                return false;

            Content parentContent = null;

            if (content.parentContentId.HasValue)
                parentContent = await contentService.Get(content.parentContentId.Value);
            #endregion

            bool success = _noteRepository.Delete(contentUuid);

            var processed = content.recurringMeetingId != null;

            if (individuallyDeleted)
                processed = false;

            new ContentActivityService(_currentUserId).Add(content.contentId, content.contentUuid, new ContentActivity()
            {
                date = DateTime.UtcNow,
                type = "DELETED",
                activityUser = _currentUserId,
                hidden = content.type != "MeetingAgendaItem",
                contentData = JsonConvert.SerializeObject(new
                {
                    title = content.title
                }),
                processed = processed
            }, userAgent);

            return success;
        }

        private void CheckUpdatesUAC(Content currentContent, Content content)
        {
            if (!currentContent.UAC.changeStatus && !currentContent.UAC.update)
                throw new HttpCustomException("You don't have permission to update content", HttpStatusCode.Forbidden);
        }

        public async Task<Guid> ShareNoteAsync(Content content, string userAgent, List<Atlas.Data.Entities.ContentPermission> permissions,
                                                 User createUser = null, List<ContentActivity> activities = null,
                                                 List<Atlas.Data.Entities.ContentSubscriber> subscribers = null, bool supressActivity = false)
        {
            {
                List<Atlas.Data.Entities.ContentOwner> contentOwner = null;
                AnnouncementProcessingResult _tempImageProcessingResult = null;

                if (activities == null)
                    activities = new List<ContentActivity>();

                if (!_noteRepository.CheckPermissionsForWorkgroup(content.workgroupId))
                    throw new SecurityException("Unauthorized attempt to include data to workgroup.");

                if ((content.title != null && content.title.Length > 10000))
                    throw new ArgumentNullException("ERROR_TITLE");

                // Check Workgroup deletion
                WorkgroupViewModel workgroupViewModel = await new WorkgroupService(_currentUserId).Get(content.workgroupId, includeHomeData: false);
                if (workgroupViewModel.archived)
                    throw new Exception("BOARD ARCHIVED");

                WorkgroupRepository workgroupRepository = new WorkgroupRepository(_currentUserId);

                var taskToBeAdded = content.Task.FirstOrDefault();
                bool isExternalWkScenario = taskToBeAdded?.externalWorkgroupId != null && taskToBeAdded.externalWorkgroupId != 0;

                if (isExternalWkScenario)
                {
                    if (await workgroupRepository.HasClientMismatching((int)taskToBeAdded.externalWorkgroupId, workgroupViewModel.clientId))
                        throw new SecurityException("INVALID_GRANT");
                }

                List<User> userWorkgroupList = workgroupRepository.GetAllUsersFromWorkgroup(isExternalWkScenario ? (int)taskToBeAdded.externalWorkgroupId : content.workgroupId);

                if (isExternalWkScenario && !userWorkgroupList.Any(u => u.userId == content.assignedUser))
                    throw new SecurityException("INVALID_ASSIGNED_USER");

                List<WorkgroupOwner> boardOwners = workgroupViewModel.WorkgroupOwner;

                FeatureManagerService featureManagerService = new FeatureManagerService(_currentUserId);

                await featureManagerService.checkPlanQuota(workgroupViewModel.clientId, PlanFeatureNames.BOARD_QUOTA, false);

                content.Note.First().text = SanitizeText(content);

                Content parentContent = new Content();

                //default permissions
                if (permissions == null)
                {
                    permissions = new List<Atlas.Data.Entities.ContentPermission>();

                    //if type = note, include only the creator at this point
                    if (content.type == ContentTypes.Note)
                    {
                        //if note, and assignedUser != null, then we add permission only to the assignedUser (because is SHARED)
                        if (content.assignedUser != null)
                            permissions.Add(new Atlas.Data.Entities.ContentPermission() { createUser = _currentUserId, userId = content.assignedUser.Value, allowed = true, createDate = DateTime.UtcNow });
                        else
                        {
                            permissions.Add(new Atlas.Data.Entities.ContentPermission() { createUser = _currentUserId, userId = _currentUserId, allowed = true, createDate = DateTime.UtcNow });
                        }
                    }

                    if (!userWorkgroupList.Any(o => o.userId == _currentUserId))
                    {
                        permissions.Add(new Atlas.Data.Entities.ContentPermission()
                        {
                            createUser = _currentUserId,
                            userId = _currentUserId,
                            createDate = DateTime.UtcNow,
                            allowed = true,
                        });
                    }
                }

                //default owner
                if (contentOwner == null && !content.type.Equals(ContentTypes.KnowledgeDirectory))
                {
                    contentOwner = new List<Atlas.Data.Entities.ContentOwner>();

                    userWorkgroupList.ForEach((x) =>
                    {
                        contentOwner.Add(new Atlas.Data.Entities.ContentOwner()
                        {
                            userId = x.userId,
                            createUser = createUser.userId,
                            createDate = DateTime.UtcNow
                        });
                    });
                }

                //default subscribers
                if (subscribers == null && !content.type.Equals(ContentTypes.KnowledgeDirectory))
                {
                    subscribers = new List<Atlas.Data.Entities.ContentSubscriber>();

                    //type=note => add the creator
                    if (content.type == ContentTypes.Note)
                    {
                        if (content.assignedUser != null)
                            subscribers.Add(new Atlas.Data.Entities.ContentSubscriber() { userId = content.assignedUser.Value, createDate = DateTime.UtcNow });
                        else
                        {
                            subscribers.Add(new Atlas.Data.Entities.ContentSubscriber() { userId = _currentUserId, createDate = DateTime.UtcNow });
                        }
                    }
                }
                if (content.ContentAttachment != null)
                {
                    if (content.ContentAttachment.Count() == 1)
                        content.ContentAttachment = new List<ContentAttachment>();
                }

                if (isExternalWkScenario)
                    content.workgroupId = (int)taskToBeAdded.externalWorkgroupId;

                //check if types contains the respective contentect arrays (ex. Content contentect type=Task, should have the Task contentect array filled)
                if (Helpers.ContentChecker.Check(content))
                {
                    Content newContent = null;

                    var titleSanitized = content.Meeting.FirstOrDefault()?.title ?? content.MeetingAgendaItem.FirstOrDefault()?.title ?? content.title;

                    if (!string.IsNullOrEmpty(titleSanitized))
                        content.title = Regex.Replace(titleSanitized, @"[<>]", ""); ;

                    try
                    {
                        newContent = new ContentRepository(_currentUserId).Add(content.workgroupId, content, permissions, subscribers, contentOwner, activities);
                    }
                    catch (Exception ex)
                    {
                        var ravenClient = new RavenClient("https://6aa2f8bcac3e41e198dc414d24d19e45:<EMAIL>/162539");
                        var sentryEvent = new SharpRaven.Data.SentryEvent(new SharpRaven.Data.SentryMessage("Error while creating content"));
                        sentryEvent.Extra = new
                        {
                            ex,
                            content,
                            permissions,
                            subscribers,
                            contentOwner,
                            activities
                        };
                        ravenClient.Capture(sentryEvent);

                        return Guid.Empty;
                    }

                    //if success Add()
                    if (newContent != null)
                    {
                        //todo: temporary fix to avoid ContentActivity for Minutes of type Attachment
                        if (supressActivity)
                            return newContent.contentUuid;

                        var title = content.title;

                        //todo: mover para business
                        activities = new List<ContentActivity>();

                        if (content.type == ContentTypes.Note)
                        {
                            int? isShared = (content.assignedUser != null ? content.assignedUser : 0);
                            String cType = "CREATED";

                            if (isShared > 0)
                                cType = "SHARED";

                            new ContentActivityService(_currentUserId).Add(newContent.contentId, newContent.contentUuid, new ContentActivity()
                            {
                                date = DateTime.UtcNow,
                                type = cType,
                                activityUser = _currentUserId,
                                contentData = JsonConvert.SerializeObject(new
                                {
                                    title = title
                                }),
                                hidden = true
                            }, userAgent);
                        }
                    }

                    return newContent.contentUuid;
                }
                else
                {
                    return Guid.Empty;
                }
            }
        }

        private bool hasReviewerChanged(Content original, Content modified)
        {
            return (original.reviewerUser != modified.reviewerUser) && modified.reviewerUser != null;
        }

        private string SanitizeText(Atlas.Data.Entities.Content content)
        {
            Ganss.Xss.HtmlSanitizer s = new Ganss.Xss.HtmlSanitizer();
            s.AllowDataAttributes = true;
            s.AllowedSchemes.Add("data");
            s.AllowedAttributes.Add("class");
            s.AllowedAttributes.Add("src");

            // Clean up the unexpected HTML tags
            var invalidTags = new string[] { "form", "button", "div" };
            s.AllowedTags.ExceptWith(invalidTags);

            s.RemovingAttribute += (x, e) => e.Cancel = e.Reason == RemoveReason.NotAllowedUrlValue
             && e.Attribute.Value.Length >= 0xfff0
             && e.Attribute.Value.StartsWith("data:", StringComparison.OrdinalIgnoreCase);

            s.PostProcessNode += (sender, e) =>
            {
                if (e.Node is AngleSharp.Html.Dom.IHtmlAnchorElement a)
                {
                    a.RemoveAttribute("style");
                }
            };

            if (content.Note.Any())
            {
                s.AllowedTags.Clear();
                content.title = Regex.Replace(s.Sanitize(content.title), @"[^\w\s]", "");
            }

            string sanitized_text = s.Sanitize(content.Note.First().text);

            return sanitized_text;
        }
    }
}
