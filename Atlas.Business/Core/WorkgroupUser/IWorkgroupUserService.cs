using Atlas.CrossCutting.DTO;
using Atlas.CrossCutting.Models.Responses;
using System.Threading.Tasks;

namespace Atlas.Business.Core.WorkgroupUser
{
    public interface IWorkgroupUserService
    {
        Task<PagedResult<UserListInfoDto>> GetWorkgroupUsersForClientAsync(int clientId, int pageNumber = 0, int pageSize = 0, string? searchTerm = null);
        Task<PagedResult<UserListInfoDto>> List(int clientId, int workgroupId, int pageNumber = 0, int pageSize = 0, string? searchTerm = null);
    }
}
