using Atlas.Business.ViewModels;
using Atlas.Business.Workflow;
using Atlas.CrossCutting.AppEnums;
using Atlas.CrossCutting.DTO.Task;
using Atlas.CrossCutting.DTO.Workgroup;
using Atlas.CrossCutting.Exceptions;
using Atlas.CrossCutting.Exceptions.ErrorCodes;
using Atlas.CrossCutting.Helpers;
using Atlas.CrossCutting.Logging;
using Atlas.CrossCutting.Settings;
using Atlas.Data.Dtos.TaskDtos;
using Atlas.Data.Entities;
using Atlas.Data.Repository;
using Ganss.Xss;
using Newtonsoft.Json;
using SharpRaven;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Security;
using System.Security.Principal;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using static Atlas.CrossCutting.AppEnums.ContentStatuses;

namespace Atlas.Business.Core.TaskService
{
    public class TaskService : ITaskService
    {
        private readonly AuthUtil _authUtil;
        private readonly ContentRepository _repo;
        private readonly StorageSettings _storageSettings;
        private readonly IStructuredLogger _logger;

        public TaskService
        (
            IPrincipal principal,
            StorageSettings storageSettings,
            IStructuredLogger logger

        )
        {
            _authUtil = new AuthUtil(principal);
            _repo = new ContentRepository(_authUtil.UserId);
            _storageSettings = storageSettings;
            _logger = logger;
        }

        /// <summary>
        /// Ensures that a workgroup has at least one task list (column).
        /// If no columns exist, creates the first default column.
        /// </summary>
        /// <param name="workgroupId">The workgroup ID to check</param>
        /// <param name="clientId">The client ID for activity logging</param>
        /// <returns>The first task list (column) of the workgroup, either existing or newly created</returns>
        private async Task<WorkgroupTaskListDTO> EnsureWorkgroupHasTaskList(int workgroupId, int clientId)
        {
            var workgroupRepository = new WorkgroupRepository(_authUtil.UserId);

            // Get existing task lists for the workgroup
            var taskLists = await workgroupRepository.GetTaskLists(workgroupId);

            // If there are existing task lists, return the first one
            if (taskLists != null && taskLists.Any())
            {
                return taskLists.OrderBy(tl => tl.itemOrder).First();
            }

            // No task lists exist, create the first default column
            var firstDefaultColumn = new WorkgroupTaskList
            {
                name = "DEFAULT_COLUMN_1", // Using the same naming convention as AddDefaultTaskListsForProject
                description = "",
                itemOrder = 1,
                workgroupId = workgroupId
            };

            // Create the task list using the repository method
            var createdTaskList = await workgroupRepository.CreateAsync(firstDefaultColumn);

            if (createdTaskList != null)
            {
                // Log the activity
                await ActivityService.AddNewAsync(_storageSettings, "WORKGROUP_TASKLIST_CREATED", clientId, workgroupId, _authUtil.UserId, JsonConvert.SerializeObject(createdTaskList));

                // Convert to DTO
                return new WorkgroupTaskListDTO
                {
                    workgroupTaskListId = createdTaskList.workgroupTaskListId,
                    name = createdTaskList.name,
                    description = createdTaskList.description,
                    itemOrder = createdTaskList.itemOrder,
                    deleted = createdTaskList.deleted,
                    workgroupId = createdTaskList.workgroupId
                };
            }

            return null;
        }

        /// <summary>
        /// Create a new task and add it to the board.
        /// </summary>
        /// <param name="workgroupId">The id of the workgroup where the task will be added.</param>
        /// <param name="obj">The Content object that represents the task.</param>
        /// <param name="supressActivity">If true, the activity will not be created.</param>
        /// <returns>The created task id.</returns>
        public async Task<TaskCreateResponseDto> CreateAsync(int workgroupId, Content obj, string userAgent, bool supressActivity = false)
        {
            obj.type = ContentTypes.Task;
            int generateRandom_qtd = 0;
            List<Atlas.Data.Entities.ContentOwner> contentOwner = new List<Atlas.Data.Entities.ContentOwner>();
            List<ContentActivity> activities = new List<ContentActivity>();

            // Valida tamanho do title do content
            if ((obj.title != null && obj.title.Length > 10000)
                || (obj.type == ContentTypes.Announcement && string.IsNullOrWhiteSpace(obj.title)))
            {
                throw new ArgumentNullException("ERROR_TITLE");
            }

            WorkgroupService ws = new WorkgroupService(_authUtil.UserId);
            var ws1 = await ws.Get(workgroupId, includeHomeData: false);

            WorkgroupRepository _w_repo = new WorkgroupRepository(_authUtil.UserId);

            var taskToBeAdded = obj.Task.FirstOrDefault();
            bool isExternalWkScenario = taskToBeAdded?.externalWorkgroupId != null && taskToBeAdded.externalWorkgroupId != 0;

            if (isExternalWkScenario)
            {
                if (await _w_repo.HasClientMismatching((int)taskToBeAdded.externalWorkgroupId, ws1.clientId))
                    throw new SecurityException("INVALID_GRANT");
            }


            var workgroup_users_list = _w_repo.GetAllUsersFromWorkgroup(isExternalWkScenario ? (int)taskToBeAdded.externalWorkgroupId : workgroupId);
            var boardOwners = ws1.WorkgroupOwner;

            FeatureManagerService featureSvc = new FeatureManagerService(_authUtil.UserId);

            //----------------------------------------------
            //Check if the board quota was exceeded for the client
            //If the quota is exceeded the client's users won't be able to create any content on this client
            await featureSvc.checkPlanQuota(ws1.clientId, PlanFeatureNames.BOARD_QUOTA, false);


            var parentContent = new Content();

            if (obj.parentContentUuid != null && !Guid.TryParse(obj.parentContentUuid.ToString(), out Guid parentContentUuid))
            {
                obj.parentContentUuid = null;
            }

            if (obj.parentContentUuid != null)
            {
                ContentService contentService = new ContentService(_authUtil.UserId);
                parentContent = await contentService.Get((Guid)obj.parentContentUuid);

                obj.parentContentId = parentContent.parentContentId;

                if (!parentContent.UAC.add_child_task)
                {
                    return new TaskCreateResponseDto { Id = Guid.Empty };
                }

                if (!isExternalWkScenario && parentContent.type == ContentTypes.Meeting && parentContent.workgroupId != workgroupId)
                {
                    throw new SecurityException("Unauthorized attempt to create a content that does not belongs to the Board.");
                }

            }
            if (obj.Task.Any())
            {
                //1. process title

                var task = obj.Task.First();

                var patt = @"\@\[(.*)\]\((\d*)\)";
                var rgx = new Regex(patt);
                var matches = rgx.Matches(obj.title);

                if (matches.Count > 0)
                {
                    var match = matches[0];
                    // obj.assignedUser = Convert.ToInt32(match.Groups[2].Value);
                    var newTitle = obj.title.Replace(match.Groups[0].Value, "");
                    newTitle = newTitle.TrimStart();
                    if (newTitle.Length > 1)
                    {
                        newTitle = char.ToUpper(newTitle[0]) + newTitle.Substring(1);
                    }

                    obj.title = newTitle;


                }
                else if (!obj.assignedUser.HasValue)
                {
                    throw new ArgumentNullException("Assigned user cannot be null.");
                }

                Regex genRnd = new Regex(@"(\[(GenerateRandom)\((\d{0,9})\)\])");
                if (genRnd.IsMatch(obj.title))
                {
                    int qtd = 0;
                    if (int.TryParse(genRnd.Match(obj.title).Groups[3].Value, out qtd))
                    {
                        generateRandom_qtd = qtd;
                    }
                }
            }


            //default permissions
            var permissions = new List<Data.Entities.ContentPermission>();

            workgroup_users_list.ForEach((x) =>
            {
                permissions.Add(new Data.Entities.ContentPermission() { createUser = _authUtil.UserId, userId = x.userId, allowed = true, createDate = DateTime.UtcNow });
            });

            //se o usuário que esta criando não tem permissão para ver por roles, adiciona
            //todo: revisar regra
            if (!workgroup_users_list.Any(o => o.userId == _authUtil.UserId))
            {
                permissions.Add(new Data.Entities.ContentPermission()
                {
                    createUser = _authUtil.UserId,
                    userId = _authUtil.UserId,
                    createDate = DateTime.UtcNow,
                    allowed = true,
                });
            }


            //default owner
            workgroup_users_list.ForEach((x) =>
            {
                contentOwner.Add(new Atlas.Data.Entities.ContentOwner()
                {
                    userId = x.userId,
                    createUser = _authUtil.UserId,
                    createDate = DateTime.UtcNow
                });
            });


            //default subscribers
            var subscribers = new List<Atlas.Data.Entities.ContentSubscriber>();
            subscribers.Add(new Atlas.Data.Entities.ContentSubscriber() { userId = _authUtil.UserId, createDate = DateTime.UtcNow });

            //if type=task, then add assigned user
            if (obj.assignedUser.HasValue)
            {
                if (_authUtil.UserId != obj.assignedUser.Value)
                {
                    subscribers.Add(new Atlas.Data.Entities.ContentSubscriber() { userId = obj.assignedUser.Value, createDate = DateTime.UtcNow });
                }
            }

            //todo: if workgroup_type=Project add ALL users
            if (obj.Workgroup == null)
            {
                //todo:fix this
                obj.Workgroup = await _w_repo.Get(obj.workgroupId);
            }

            /*
             *  S47 
             *  WorkItem 2942 - Ações criadas em projetos via notas não possuem nenhuma coluna atrelada
             * 
             *  A lógica abaixo irá atrelar uma ação na situação descrita acima na primeira coluna do projeto 
             *  A primeira coluna será sempre a coluna cujo o order for o menor (geralmente 0 ou 1)
             * 
             *  Atualização: verifica se o workgroup não tem nenhuma coluna e cria uma se necessário
             */
            if
            (
                obj.type.Equals(ContentTypes.Task) &&
                !obj.Task.FirstOrDefault().workgroupTaskListId.HasValue
            )
            {
                // Primeiro, verifica se existem task lists no workgroup
                if (obj.Workgroup.WorkgroupTaskList == null || !obj.Workgroup.WorkgroupTaskList.Any(wtl => !wtl.deleted))
                {
                    // Nenhuma coluna existe, precisa criar a primeira coluna padrão
                    var createdTaskList = await EnsureWorkgroupHasTaskList(obj.workgroupId, ws1.clientId);
                    if (createdTaskList != null)
                    {
                        obj.Task.FirstOrDefault().workgroupTaskListId = createdTaskList.workgroupTaskListId;

                        // Atualiza a coleção do workgroup para refletir a nova coluna criada
                        obj.Workgroup.WorkgroupTaskList = new List<WorkgroupTaskList>
                        {
                            new WorkgroupTaskList
                            {
                                workgroupTaskListId = createdTaskList.workgroupTaskListId,
                                name = createdTaskList.name,
                                description = createdTaskList.description,
                                itemOrder = createdTaskList.itemOrder ?? 1,
                                deleted = false,
                                workgroupId = obj.workgroupId
                            }
                        };
                    }
                }
                else
                {
                    // Usa a lógica existente para encontrar a primeira coluna
                    WorkgroupTaskList workgroupTaskList = obj.Workgroup.WorkgroupTaskList.Where(wtl => wtl.deleted == false).OrderBy(wtl => wtl.itemOrder).FirstOrDefault();

                    if (workgroupTaskList != null)
                    {
                        obj.Task.FirstOrDefault().workgroupTaskListId = workgroupTaskList.workgroupTaskListId;
                    }
                }
            }

            obj.ContentAttachment = new List<ContentAttachment>();

            if (isExternalWkScenario)
            {
                workgroupId = (int)taskToBeAdded.externalWorkgroupId;
                obj.workgroupId = (int)taskToBeAdded.externalWorkgroupId;
            }

            //check if types contains the respective object arrays (ex. Content object type=Task, should have the Task object array filled)
            if (Helpers.ContentChecker.Check(obj))
            {
                Content res_repo = null;

                var titleSanitized = obj.Meeting.FirstOrDefault()?.title ?? obj.MeetingAgendaItem.FirstOrDefault()?.title ?? obj.title;
                if (!string.IsNullOrEmpty(titleSanitized))
                {
                    titleSanitized = Regex.Replace(titleSanitized, @"[<>]", "");
                    switch (obj.type)
                    {
                        case ContentTypes.Meeting:
                            obj.Meeting.First().title = titleSanitized;
                            break;
                        case ContentTypes.MeetingAgendaItem:
                            obj.MeetingAgendaItem.First().title = titleSanitized;
                            break;
                        default:
                            obj.title = titleSanitized;
                            break;
                    }
                }

                try
                {
                    res_repo = _repo.Add(workgroupId, obj, permissions, subscribers, contentOwner, activities);
                }
                catch (Exception ex)
                {
                    var ravenClient = new RavenClient("https://6aa2f8bcac3e41e198dc414d24d19e45:<EMAIL>/162539");
                    var sentryEvent = new SharpRaven.Data.SentryEvent(new SharpRaven.Data.SentryMessage("Error while creating content"));
                    sentryEvent.Extra = new
                    {
                        ex,
                        obj,
                        permissions,
                        subscribers,
                        contentOwner,
                        activities
                    };
                    ravenClient.Capture(sentryEvent);

                    return new TaskCreateResponseDto { Id = Guid.Empty };
                }


                //if success Add()
                if (res_repo != null)
                {
                    //todo: temporary fix to avoid ContentActivity for Minutes of type Attachment
                    if (supressActivity)
                    {
                        return new TaskCreateResponseDto { Id = res_repo.contentUuid };
                    }

                    var title = obj.title;

                    if (obj.type != ContentTypes.Note)
                    {
                        //S-32 - For announcements of type 'systemGenerated', use the createDate of the previous contentActivity that triggered the autoGen
                        var createDate = DateTime.UtcNow;

                        if (obj.type == ContentTypes.Announcement)
                        {
                            var an = obj.Announcement.FirstOrDefault();

                            if (an.systemGenerated && obj.createDate != null)
                            {
                                createDate = obj.createDate;
                            }
                        }

                        string contentData = JsonConvert.SerializeObject(new
                        {
                            title = title
                        });

                        bool processed = false;

                        string subItemType = "";

                        new ContentActivityService(_authUtil.UserId).Add(res_repo.contentId, res_repo.contentUuid, new ContentActivity()
                        {
                            date = createDate,
                            type = "CREATED",
                            activityUser = _authUtil.UserId,
                            contentData = contentData,
                            subItemType = subItemType,
                            processed = processed,
                        }, userAgent);
                    }
                }

                return new TaskCreateResponseDto { Id = res_repo.contentUuid };
            }
            else
            {
                return new TaskCreateResponseDto { Id = Guid.Empty };
            }
        }

        public async Task<(Content updated, List<PredecessorSummaryDto> summaries)> UpdateAsync(Content mod, string userAgent)
        {
            // Retrieve original content with full context
            var contentService = new ContentService(_authUtil.UserId);
            var ori = await contentService.Get(mod.contentUuid);

            if (ori.type != ContentTypes.Task)
                throw new InvalidOperationException("NOT_ALLOWED");


            var originalTask = ori.Task.FirstOrDefault();
            var modifiedTask = mod.Task.FirstOrDefault();

            bool taskAlreadyClosed = ori?.status.ToLower() == "closed";

            if (modifiedTask != null)
            {
                if (modifiedTask.startDate.HasValue && modifiedTask.startDate.Value > ori.Task.First().dueDate)
                {
                    throw new InvalidOperationException("INVALID_START_DATE");
                }

                if (taskAlreadyClosed && !string.IsNullOrWhiteSpace(modifiedTask.description))
                {
                    throw new InvalidOperationException("Cannot change description in closed tasks");
                }

                bool reviewerUserHasChanged = ori.reviewerUser.HasValue
                    && mod.reviewerUser.HasValue
                    && ori.reviewerUser.Value != mod.reviewerUser.Value;

                if (reviewerUserHasChanged && taskAlreadyClosed)
                {
                    throw new InvalidOperationException("Cannot change description in closed tasks");
                }

                // UAC update checks for Task (v2 CheckUpdatesUAC Task case)
                if (originalTask.workgroupTaskListId != modifiedTask.workgroupTaskListId && !ori.UAC.can_edit_tasklist)
                    throw new SecurityException("CANT_EDIT_TASKLIST");
                if (modifiedTask.dueDate != default(DateTime) && originalTask.dueDate != modifiedTask.dueDate && !ori.UAC.can_edit_duedate)
                    throw new SecurityException("CANT_EDIT_DUEDATE");
                if (modifiedTask.startDate.HasValue && ori.Task.First().startDate != modifiedTask.startDate && !ori.UAC.can_edit_startdate)
                    throw new SecurityException("CANT_EDIT_STARTDATE");
                if (!string.IsNullOrWhiteSpace(mod.title) && ori.title != mod.title && !ori.UAC.can_edit_title)
                    throw new SecurityException("CANT_EDIT_TITLE");
                if (modifiedTask.predecessorContentUuidSet == true && originalTask.predecessorContentUuid != modifiedTask.predecessorContentUuid && !ori.UAC.can_edit_predecessor)
                    throw new SecurityException("CANT_EDIT_PREDECESSOR");
                if (mod.assignedUser.HasValue && ori.assignedUser != mod.assignedUser && !ori.UAC.can_edit_assigned)
                    throw new SecurityException("CANT_EDIT_ASSIGNED");

                await HandlePredecessorTaskScenario(modifiedTask, originalTask, ori);
            }

            // Permission checks (v2 logic)
            if (!_repo.CheckPermissions(ori.contentUuid))
                throw new SecurityException("Unauthorized update requested to object.");

            if (!_repo.CheckPermissionsForWorkgroup(Operations.UPDATE, ori.contentId))
                throw new SecurityException("Unauthorized attempt to update data to workgroup.");

            // Parent meeting cancelled check
            if (ori.parentContentId.HasValue)
            {
                var parent = await _repo.Get(ori.parentContentUuid.Value);
                if (parent != null && parent.type == ContentTypes.Meeting && parent.status == ContentMeetingStatus.CANCELLED)
                    throw new InvalidOperationException("NOT_ALLOWED");
            }

            // Archived workgroup check
            if (ori.Workgroup.archived && ori.type != ContentTypes.Note)
                throw new SecurityException("ARCHIVED_WORKGROUP");

            // Subscriber addition if assignment changed
            if (mod.assignedUser.HasValue && ori.assignedUser != mod.assignedUser)
            {
                if (!ori.ContentSubscriber.Any(cs => cs.userId == mod.assignedUser.Value))
                    ori.ContentSubscriber.Add(new Data.Entities.ContentSubscriber() { userId = mod.assignedUser.Value, createDate = DateTime.UtcNow });
            }

            if (ori.Workgroup.type.Equals(WorkgroupTypes.PROJECT))
            {
                if (modifiedTask != null)
                {
                    var taskListHasChanged = modifiedTask.workgroupTaskListId.HasValue && ori.Task.FirstOrDefault().workgroupTaskListId != modifiedTask.workgroupTaskListId;
                    var taskOrderHaveNotBeenSpecified = !modifiedTask.taskListOrder.HasValue;
                    if (taskListHasChanged && taskOrderHaveNotBeenSpecified)
                    {
                        await MoveTaskToLastPosition(ori.contentId, modifiedTask, saveChanges: false);
                    }
                }
            }

            // FIXED: Sanitize and store task description properly
            string sanitizedDescription = null;
            if (!string.IsNullOrWhiteSpace(modifiedTask?.description))
            {
                var sanitizer = new HtmlSanitizer();
                sanitizer.AllowDataAttributes = true;
                sanitizer.AllowedSchemes.Add("data");
                sanitizer.AllowedAttributes.Add("class");
                sanitizer.AllowedAttributes.Add("src");
                var invalidTags = new[] { "form", "button", "div" };
                foreach (var tag in invalidTags)
                    sanitizer.AllowedTags.Remove(tag);

                sanitizedDescription = sanitizer.Sanitize(modifiedTask.description);
            }

            // Merge fields into original
            if (!string.IsNullOrWhiteSpace(mod.title))
                ori.title = mod.title;

            if (mod.reviewerUser.HasValue)
                ori.reviewerUser = mod.reviewerUser;

            if (mod.needsReviewer.HasValue)
                ori.needsReviewer = mod.needsReviewer;

            // FIXED: Use sanitized description instead of original
            if (sanitizedDescription != null)
                originalTask.description = sanitizedDescription;

            if (modifiedTask.dueDate != default(DateTime))
                originalTask.dueDate = modifiedTask.dueDate;
            if (modifiedTask.startDate.HasValue)
                originalTask.startDate = modifiedTask.startDate;
            if (modifiedTask.workgroupTaskListId.HasValue)
                originalTask.workgroupTaskListId = modifiedTask.workgroupTaskListId;
            if (modifiedTask.taskListOrder.HasValue)
                originalTask.taskListOrder = modifiedTask.taskListOrder;
            if (mod.assignedUser.HasValue)
                ori.assignedUser = mod.assignedUser;

            // Persist update
            var updated = await _repo.Update(ori.contentUuid, mod);

            HandleTaskUac(content: updated, meetingHasBeenCancelled: ori.parent_status == ContentMeetingStatus.CANCELLED);

            List<PredecessorSummaryDto> summaries = new List<PredecessorSummaryDto>() { };

            if (modifiedTask.predecessorContentUuidSet == true
                && modifiedTask.predecessorContentUuid != null
                && modifiedTask.predecessorContentUuid != Guid.Empty)
            {
                summaries = await _repo.GetPredecessorSummariesAsync(new List<Guid>
                {
                    (Guid)modifiedTask.predecessorContentUuid
                });
            }

            // Activity log for CLOSED status
            if (updated.status == "CLOSED")
            {
                var activityService = new ContentActivityService(_authUtil.UserId);

                var activity = new ContentActivity
                {
                    contentId = updated.contentId,
                    contentUuid = updated.contentUuid,
                    date = DateTime.UtcNow,
                    processed = false,
                    hidden = false,
                    type = "STATUS_CLOSED",
                    activityUser = _authUtil.UserId,
                    contentData = JsonConvert.SerializeObject(new { title = updated.title })
                };

                activityService.Add(updated.contentId, updated.contentUuid, activity, userAgent);
            }
            else if (updated.status == "AWAITING_REVIEW" && hasReviewerChanged(ori, mod))
            {
                var details = TaskDetailsUpdated(ori, mod);

                var contentActivityService = new ContentActivityService(_authUtil.UserId);
                contentActivityService.Add(updated.contentId, updated.contentUuid, new ContentActivity()
                {
                    contentId = updated.contentId,
                    contentUuid = updated.contentUuid,
                    date = DateTime.UtcNow,
                    processed = false,
                    type = "AWAITING_REVIEW",
                    activityUser = _authUtil.UserId,
                    newData = JsonConvert.SerializeObject(new
                    {
                        title = updated.title
                    }),
                    hidden = false,
                    subItemType = details != null ? SubItems.TASK_DETAILS_UPDATED : null,
                    contentData = details != null ? JsonConvert.SerializeObject(details) : null,
                }, userAgent);
            }
            else
            {
                object taskDetails = mod.Task.Any() ? TaskDetailsUpdated(ori, mod) : null;

                var contentActivityService = new ContentActivityService(_authUtil.UserId);
                contentActivityService.Add(updated.contentId, updated.contentUuid, new ContentActivity()
                {
                    contentId = updated.contentId,
                    contentUuid = updated.contentUuid,
                    date = DateTime.UtcNow,
                    processed = false,
                    type = "UPDATED",
                    activityUser = _authUtil.UserId,
                    contentData = taskDetails != null ? JsonConvert.SerializeObject(taskDetails) : null,
                    newData = mod.Meeting.FirstOrDefault()?.title
                            ?? mod.MeetingAgendaItem.FirstOrDefault()?.title
                            ?? mod.KnowledgeArticle.FirstOrDefault()?.title
                            ?? mod.Pipeline.FirstOrDefault()?.name
                            ?? mod.PipelineItem.FirstOrDefault()?.name,
                    hidden = false,
                    subItemType = taskDetails != null ? SubItems.TASK_DETAILS_UPDATED : null
                }, userAgent);
            }

            return (updated, summaries);
        }

        public async System.Threading.Tasks.Task MoveTaskToLastPosition(int contentId, Data.Entities.Task modifiedTask, bool saveChanges = false)
        {
            // REMINDER: when the FIRST action is CREATED, taskListOrder default value is NULL
            var tasks = await _repo.GetTasksByWorkgroupTaskListId(modifiedTask.workgroupTaskListId);

            if (tasks.Count == 0) return;

            // Context: we're catching actions with the same listOrder (even in production, potencially)
            // Code's INTENTION is properly update the order when the scenario occurs
            var hasSameListOrder = tasks.GroupBy(tk => tk.taskListOrder).Any(group => group.Count() > 1);

            if (hasSameListOrder)
            {
                var sortedTasks = tasks.Where(tk => tk.contentId != contentId).OrderBy(tk => tk.taskListOrder).ToList();

                short listOrder = 0;
                foreach (var task in sortedTasks)
                {
                    task.taskListOrder = listOrder;
                    listOrder++;
                }

                _repo.MarkEntityStateAsModified(sortedTasks);

                modifiedTask.taskListOrder = listOrder;
            }
            else
            {
                var maxTaskListOrder = tasks.Where(tk => tk.taskListOrder != null && tk.contentId != contentId).Max(tk => tk.taskListOrder) ?? 0;
                modifiedTask.taskListOrder = (short)(maxTaskListOrder + 1);
            }

            if (saveChanges)
                await _repo.UpdateTaskListOrder(contentId, modifiedTask.taskListOrder);

        }

        private async System.Threading.Tasks.Task HandlePredecessorTaskScenario(
            Data.Entities.Task modifiedTask,
            Data.Entities.Task originalTask,
            Content originalContent)
        {
            if (modifiedTask.predecessorContentUuidSet == true
                && originalContent.Workgroup.type == WorkgroupTypes.PROJECT
                && !originalContent.parentContentId.HasValue
                && !originalContent.parentContentUuid.HasValue)
            {
                var hasValidPredecessorUuid = modifiedTask.predecessorContentUuid is Guid uuid &&
                                              uuid != Guid.Empty;

                if (hasValidPredecessorUuid)
                {
                    var predecessorTask = await _repo.GetOnlyTaskInfoAsync((Guid)modifiedTask.predecessorContentUuid);
                    var predecessorFound = predecessorTask?.Task?.FirstOrDefault() is var task && task != null;

                    if (predecessorFound && task.dueDate > originalTask.dueDate)
                    {
                        throw new InvalidOperationException(
                            "Invalid predecessor task. Predecessor task due date cannot be later than due date of main action");
                    }

                    if (predecessorFound && predecessorTask.deleted.GetValueOrDefault())
                    {
                        throw new InvalidOperationException("Predecessor task has been deleted");
                    }

                    originalTask.predecessorContentUuid = modifiedTask.predecessorContentUuid;
                }
                else
                {
                    originalTask.predecessorContentUuid = null;
                    originalTask.predecessorContentId = null;
                }
            }
        }

        private bool hasReviewerChanged(Content original, Content modified)
        {
            return (original.reviewerUser != modified.reviewerUser) && modified.reviewerUser != null;
        }

        private object TaskDetailsUpdated(Content original, Content modified) // Will need to be a tuple in the future
        {
            bool assignedChanged = original.assignedUser != modified.assignedUser && modified.assignedUser != null;
            if (assignedChanged)
            {
                return new
                {
                    assigned = new
                    {
                        previous = original.assignedUser,
                        current = modified.assignedUser
                    }
                };
            }

            bool reviewerChanged = original.reviewerUser != modified.reviewerUser && modified.reviewerUser != null;
            if (reviewerChanged)
            {
                return new
                {
                    reviewer = new
                    {
                        previous = original.reviewerUser,
                        current = modified.reviewerUser
                    }
                };
            }

            var origTask = original.Task.First();
            var modTask = modified.Task.First();
            bool dueDateChanged = origTask.dueDate != modTask.dueDate && modTask.dueDate != DateTime.MinValue;
            if (dueDateChanged)
            {
                return new
                {
                    dueDate = new
                    {
                        previous = origTask.dueDate.ToString("s"),
                        current = modTask.dueDate.ToString("s"),
                    }
                };
            }

            bool startDateChanged = origTask.startDate != modTask.startDate && modTask.startDate.HasValue && modTask.startDate != DateTime.MinValue;
            if (startDateChanged)
            {
                return new
                {
                    startDate = new
                    {
                        previous = origTask.startDate.HasValue ? origTask.startDate.Value.ToString("s") : null,
                        current = modTask.startDate.Value.ToString("s"),
                    }
                };
            }

            return null;
        }

        public async Task<TaskDetailResultViewModel> GetTaskByContentUuid(Guid contentUuid)
        {
            if (!_repo.CheckPermissions(contentUuid))
                throw new HttpCustomException(GeneralErrorCodes.AccessDenied, HttpStatusCode.Unauthorized, DateTime.Now);

            var task = await _repo.GetTaskByContentUuid(contentUuid);

            // ⬇️ backfill V2→V3
            await EnsureColumnForTaskAsync(task);

            bool meetingHasBeenCancelled = task.parentContentUuid.HasValue && (await _repo.MeetingHasBeenCancelled(task.parentContentUuid.Value));

            HandleTaskUac(task, meetingHasBeenCancelled);

            var predUuids = task?.Task?.Select(t => t.predecessorContentUuid).Where(u => u.HasValue && u.Value != Guid.Empty)
                .Select(u => u!.Value).Distinct().ToList() ?? new List<Guid>();

            var summaries = predUuids.Count > 0 ? await _repo.GetPredecessorSummariesAsync(predUuids) : new List<PredecessorSummaryDto>();

            return new TaskDetailResultViewModel { Content = task, Predecessors = summaries };
        }

        public List<TaskListViewModel> GetTasksList(ContentRequestFilter request)
        {
            var wgId = (request.workgroups != null && request.workgroups.Length > 0) ? request.workgroups[0] : 0;

            // backfill V2→V3 só se tiver WG válido
            if (wgId > 0)
            {
                EnsureColumnsForWorkgroupAsync(wgId).GetAwaiter().GetResult();
            }

            ContentService contentService = new ContentService(_authUtil.UserId);
            var tasks = contentService.GetAll_Tasks(request);
            return tasks;
        }

        public async Task<List<Content>> GetTasksByParentContentAsync(int clientId, int workgroupId, Guid parentContentUuid)
        {
            var tasks = (await _repo.GetTasksByParentContentUuid(parentContentUuid)).ToList();

            // ⬇️ backfill V2→V3
            await EnsureColumnsForTasksAsync(tasks);

            if (tasks.Count != 0) await HandleTasksPermissions(tasks, workgroupId);

            return tasks;
        }

        public async Task<IEnumerable<PermissionListViewModel>> GetTaskPermissions(Guid contentUuid)
        {
            try
            {
                // 1. Input validation
                if (contentUuid == Guid.Empty)
                    throw new ArgumentException("Invalid content UUID");

                // 3. Get content with necessary includes to avoid N+1
                ContentService contentService = new(_authUtil.UserId);
                var content = await contentService.Get(contentUuid);

                // 4. Validação de tipo de conteúdo
                if (content.type != ContentTypes.Task)
                    throw new InvalidOperationException("Content is not a task");

                // 5. Verificar permissão de gerenciamento
                if (!content.UAC.manage_permissions)
                    throw new SecurityException("User cannot manage permissions for this task");

                // 6. Buscar usuários do workgroup uma única vez
                WorkgroupService wsvc = new(_authUtil.UserId);
                var workgroupUsers = await wsvc.GetUsers(content.workgroupId);

                // 7. Create HashSets for O(1) lookup instead of O(n)
                var existingPermissionUserIds = new HashSet<int>(
                    content.ContentPermission.Select(cp => cp.userId)
                );

                // 8. Build permissions list more efficiently
                var permissionsList = new List<PermissionListViewModel>(
                    workgroupUsers.Count + existingPermissionUserIds.Count
                );

                // 9. Add existing permissions
                foreach (var permission in content.ContentPermission)
                {
                    permissionsList.Add(new PermissionListViewModel
                    {
                        ContentPermission = permission,
                        Checked = true,
                        blockChange = false
                    });

                    // Marcar que o usuário tem permissão
                    if (permission.User != null)
                        permission.User.hasPermission = true;
                }

                // 10. Adicionar usuários do workgroup sem permissão
                foreach (var user in workgroupUsers)
                {
                    if (!existingPermissionUserIds.Contains(user.userId))
                    {
                        permissionsList.Add(new PermissionListViewModel
                        {
                            ContentPermission = new Data.Entities.ContentPermission
                            {
                                User = user,
                                userId = user.userId,
                                contentId = content.contentId,
                                contentUuid = content.contentUuid
                            },
                            Checked = false,
                            blockChange = false
                        });
                    }
                }

                // 11. Aplicar regras de bloqueio usando um método separado
                ApplyBlockingRules(permissionsList, content);

                // 12. Ordenar apenas uma vez no final
                return permissionsList.OrderBy(p => p.ContentPermission.User?.name ?? string.Empty);
            }
            catch (Exception ex) when (!(ex is SecurityException || ex is ArgumentException || ex is InvalidOperationException))
            {
                _logger.LogError($"Error getting task permissions for {contentUuid}", ex);
                throw new InvalidOperationException("Failed to retrieve task permissions", ex);
            }
        }

        private void ApplyBlockingRules(List<PermissionListViewModel> permissionsList, Content content)
        {
            // Use dictionary for fast lookup of special roles
            var specialRoles = new Dictionary<int?, string>();

            // createUser is int (not nullable)
            specialRoles[content.createUser] = "CREATED";

            if (content.assignedUser.HasValue)
                specialRoles[content.assignedUser] = "ASSIGNED";
            if (content.reviewerUser.HasValue)
                specialRoles[content.reviewerUser] = "REVIEWER";

            foreach (var item in permissionsList)
            {
                var userId = item.ContentPermission.userId;

                if (specialRoles.TryGetValue(userId, out var role))
                {
                    if (userId == _authUtil.UserId)
                    {
                        // Current user - show warning only
                        item.warning = role;
                    }
                    else if (item.Checked)
                    {
                        // Other users - block changes if checked
                        item.blockChange = true;
                        item.blockReason = role;
                    }
                }
            }
        }

        public async Task<Dictionary<string, int>> GetCounters(int workgroupId)
        {
            var counters = await _repo.GroupTasksByStatusAsync(workgroupId);
            return counters;
        }

        private async System.Threading.Tasks.Task HandleTasksPermissions(
            List<Content> tasks,
            int parentWorkgroupId)
        {
            if (tasks == null || !tasks.Any())
                return;

            var parentContentUuid = tasks.First().parentContentUuid.GetValueOrDefault();
            var userPermissions = await _repo.GetMyCurrentTasksPermissions(parentContentUuid);

            var permittedContentIds = new HashSet<int>(userPermissions.Select(p => p.contentId));

            foreach (var task in tasks)
            {
                var isExternalTask = task.workgroupId != parentWorkgroupId;
                task.UAC.isExternalWorkgroupTask = isExternalTask;

                task.UAC.canSeeExternalWorkgroupTask = isExternalTask && permittedContentIds.Contains(task.contentId);

                if (isExternalTask && permittedContentIds.Contains(task.contentId))
                {
                    task.UAC.canSeeExternalWorkgroupTask = true;
                }
            }
        }

        private void HandleTaskUac(Content content, bool meetingHasBeenCancelled)
        {
            bool isContentOwner = content.ContentOwner.Select(a => a.userId).Contains(_authUtil.UserId) || content.ContentOwner.Count == 0;
            bool isCreateUser = _authUtil.UserId == content.createUser;
            bool isAssignedUser = content.assignedUser == _authUtil.UserId;
            bool isSubscriber = content.ContentSubscriber.Any(o => o.userId == _authUtil.UserId);
            bool isReviewer = content.reviewerUser == _authUtil.UserId;
            bool isWorkgroupOwner = content.Workgroup.WorkgroupOwner.Any(o => o.userId == _authUtil.UserId);
            bool isTaskOwner = isCreateUser || isWorkgroupOwner;
            bool isProject = content.Workgroup.type == WorkgroupTypes.PROJECT;
            bool canResend = (isWorkgroupOwner && !(isAssignedUser && isReviewer)) || (isCreateUser && !(isAssignedUser && isReviewer));

            content.UAC = new Content.UACClass()
            {
                isSubscriber = isSubscriber,
                isOwner = isContentOwner,
                add_comment = !meetingHasBeenCancelled,
                add_answer = !meetingHasBeenCancelled,
                add_attachment = (isTaskOwner || isSubscriber || isAssignedUser || isReviewer) && !meetingHasBeenCancelled,

                delete = (isTaskOwner) && content.status == "OPEN" && !meetingHasBeenCancelled,
                update = isTaskOwner,
                changeStatus = isTaskOwner || isAssignedUser || isReviewer,

                delete_attachment = isTaskOwner,
                delete_comment = isTaskOwner,

                undelete_attachment = isTaskOwner,
                undelete_comment = isTaskOwner,

                manage_permissions = isTaskOwner,
                manage_subscribers = isTaskOwner || isAssignedUser,

                can_resend = canResend,
                can_edit = !(content.status == "CLOSED"),
                can_edit_duedate = (isWorkgroupOwner || isCreateUser || (isTaskOwner && !isAssignedUser)) && content.status != "CLOSED" && !meetingHasBeenCancelled,
                can_edit_title = (isWorkgroupOwner || isCreateUser || (isTaskOwner && !isAssignedUser)) && content.status != "CLOSED" && !meetingHasBeenCancelled,
                can_edit_startdate = ((isProject && isWorkgroupOwner) || isCreateUser || isAssignedUser || isTaskOwner) && content.status != "CLOSED" && !meetingHasBeenCancelled,
                can_edit_description = ((isProject && isWorkgroupOwner) || isCreateUser || isTaskOwner || isAssignedUser) && content.status != "CLOSED" && !meetingHasBeenCancelled,
                can_edit_predecessor = ((isProject && isWorkgroupOwner) || isCreateUser || isTaskOwner) && content.status != "CLOSED" && !meetingHasBeenCancelled,
                can_edit_assigned = ((isProject && isWorkgroupOwner) || isCreateUser || isTaskOwner) && content.status != "CLOSED" && !meetingHasBeenCancelled,
                can_edit_tasklist = (isWorkgroupOwner || isAssignedUser || isCreateUser) && !meetingHasBeenCancelled,
                can_add_checklist_item = (isAssignedUser || isTaskOwner) && !meetingHasBeenCancelled,
                can_edit_checklist_item = (isAssignedUser || isTaskOwner) && !meetingHasBeenCancelled,
                can_delete_checklist_item = (isAssignedUser || isTaskOwner) && !meetingHasBeenCancelled,
                can_reorder_checklist = (isAssignedUser || isTaskOwner) && !meetingHasBeenCancelled
            };

            var possibleNextStatuses = new List<string>();

            switch (content.status)
            {
                case "OPEN":
                    if (isCreateUser || isAssignedUser || isWorkgroupOwner || isProject)
                    {
                        possibleNextStatuses.Add("ACTIVE");
                    }

                    if (isWorkgroupOwner || isAssignedUser || isCreateUser || isSubscriber)
                    {
                        possibleNextStatuses.Add("AWAITING_REVIEW");
                    }

                    if (isCreateUser || isAssignedUser || isWorkgroupOwner || isProject)
                    {
                        possibleNextStatuses.Add("CLOSED");
                    }

                    break;
                case "ACTIVE":
                    if (isCreateUser || isAssignedUser || isWorkgroupOwner || isProject || isSubscriber)
                    {
                        possibleNextStatuses.Add("OPEN");
                    }

                    if (isWorkgroupOwner || isAssignedUser || isCreateUser || isSubscriber)
                    {
                        possibleNextStatuses.Add("AWAITING_REVIEW");
                    }

                    if (isCreateUser || isAssignedUser || isWorkgroupOwner || isProject || isSubscriber)
                    {
                        possibleNextStatuses.Add("CLOSED");
                    }
                    break;
                case "AWAITING_REVIEW":
                    if (isAssignedUser || isReviewer || isTaskOwner)
                    {
                        possibleNextStatuses.Add("OPEN");
                        possibleNextStatuses.Add("ACTIVE");
                        possibleNextStatuses.Add("AWAITING_REVIEW");
                    }

                    if (isReviewer || isWorkgroupOwner || isCreateUser)
                    {
                        possibleNextStatuses.Add("CLOSED");
                    }
                    break;
                case "CLOSED":
                    if (isCreateUser || isAssignedUser || isReviewer || isWorkgroupOwner || isProject || isSubscriber)
                    {
                        possibleNextStatuses.Add("OPEN");
                        possibleNextStatuses.Add("ACTIVE");
                        possibleNextStatuses.Add("AWAITING_REVIEW");
                    }

                    break;
                default:
                    break;
            }

            content.UAC.possibleNextStatuses = [.. possibleNextStatuses];

            foreach (var item in content.ContentAttachment)
            {
                var isAttachmentCreator = item.Attachment != null && _authUtil.UserId == item.Attachment.createUser;

                item.UAC.delete = (isTaskOwner || isAttachmentCreator) && !meetingHasBeenCancelled;
                item.UAC.undelete = (isTaskOwner || isAttachmentCreator) && !meetingHasBeenCancelled;
                item.UAC.manage_lock = isTaskOwner || isAttachmentCreator;
                item.UAC.lockable = (isTaskOwner || isAttachmentCreator) && ConverterExtensions.included_exts.Contains((item.Attachment?.extension ?? "").ToLower());
            }

            foreach (var item in content.ContentComment)
            {
                var isCommentCreator = _authUtil.UserId == item.userId;

                item.UAC.delete = (isTaskOwner || isCommentCreator) && !meetingHasBeenCancelled;
                item.UAC.undelete = (isTaskOwner || isCommentCreator) && !meetingHasBeenCancelled;
                item.UAC.delete_answer = (isTaskOwner || isCommentCreator) && !meetingHasBeenCancelled;
                item.UAC.undelete_answer = (isTaskOwner || isCommentCreator) && !meetingHasBeenCancelled;
            }
        }

        public async System.Threading.Tasks.Task DeleteAsync(Guid contentUuid)
        {
            // Retrieve original content
            var contentService = new ContentService(_authUtil.UserId);
            var ori = await contentService.Get(contentUuid);

            // Permission checks (v2 logic)
            if (!_repo.CheckPermissions(ori.contentUuid))
                throw new SecurityException("Unauthorized delete requested to object.");
            if (!_repo.CheckPermissionsForWorkgroup(Operations.DELETE, ori.contentId))
                throw new SecurityException("Unauthorized attempt to delete data to workgroup.");

            // Content type check: only Task
            if (ori.type != ContentTypes.Task)
                throw new InvalidOperationException("CONTENT_TYPE_ERROR");

            // Parent meeting cancelled check
            if (ori.parentContentId.HasValue)
            {
                var parent = await _repo.Get(ori.parentContentUuid.Value);
                if (parent != null && parent.type == ContentTypes.Meeting && parent.status == ContentMeetingStatus.CANCELLED)
                    throw new InvalidOperationException("NOT_ALLOWED");
            }

            // UAC delete permission check
            var uac = ori.UAC;
            if (!uac.delete)
                throw new SecurityException("NOT_ALLOWED");

            // Status must be OPEN
            if (ori.status != "OPEN")
                throw new InvalidOperationException("NOT_ALLOWED");

            // Archived workgroup check
            if (ori.Workgroup.archived)
                throw new SecurityException("ARCHIVED_WORKGROUP");

            // Perform delete via repository
            _repo.Delete(contentUuid);

            // Log deletion activity
            var activityService = new ContentActivityService(_authUtil.UserId);
            activityService.Add(ori.contentId, contentUuid, new ContentActivity
            {
                date = DateTime.UtcNow,
                processed = false,
                type = "DELETED",
                activityUser = _authUtil.UserId,
                contentData = JsonConvert.SerializeObject(new { title = ori.title })
            }, null);
        }

        public async Task<bool> ResendEmailForContentAsync(Guid contentUuid)
        {
            var contentService = new ContentService(_authUtil.UserId);
            var content = await contentService.Get(contentUuid);

            var allowedUsers = content.ContentPermission.Select(cp => cp.userId);
            if (!allowedUsers.Contains(_authUtil.UserId))
                throw new SecurityException("You can't resend this content");

            if (!content.UAC.can_resend)
                throw new SecurityException("You can't resend this content");

            if (content.type != ContentTypes.Task)
                throw new InvalidOperationException("CONTENT_TYPE_ERROR");

            if (content.status == "CLOSED")
                throw new InvalidOperationException("Cannot resend email for a closed task.");

            var activity = new ContentActivity
            {
                contentId = content.contentId,
                contentUuid = content.contentUuid,
                date = DateTime.UtcNow,
                type = "RESEND_TASK",
                activityUser = _authUtil.UserId,
                hidden = false
            };

            var activityService = new ContentActivityService(_authUtil.UserId);
            var success = await activityService.Add(new List<ContentActivity> { activity }, null);

            return success;
        }

        public async Task<SetStepByNameViewModel> SetStepByNameAsync(
            Guid contentUuid,
            string stepName,
            string userAgent)
        {
            // Obtém o conteúdo existente
            var contentService = new ContentService(_authUtil.UserId);
            var content = await contentService.Get(contentUuid);
            var originalStatus = content.status;

            // 1) Permissões V2
            if (!_repo.CheckPermissions(contentUuid))
                throw new SecurityException("Unauthorized set step requested to object.");
            if (!_repo.CheckPermissionsForWorkgroup(Operations.UPDATE, content.contentId))
                throw new SecurityException("Unauthorized attempt to update data to workgroup.");

            // 2) Só Task e UAC
            if (content.type != ContentTypes.Task)
                throw new InvalidOperationException("CONTENT_TYPE_ERROR");
            if (!content.UAC.changeStatus)
                throw new SecurityException("CANT_CHANGE_STATUS");
            if (content.UAC.possibleNextStatuses == null
                || !content.UAC.possibleNextStatuses.Contains(stepName))
                throw new InvalidOperationException("STATUS_NOT_ALLOWED");

            // 3) Parent Meeting cancelado?
            if (content.parentContentId.HasValue)
            {
                var parent = await _repo.Get(content.parentContentUuid.Value);
                if (parent.type == ContentTypes.Meeting
                    && parent.status == ContentMeetingStatus.CANCELLED)
                    throw new InvalidOperationException("NOT_ALLOWED");
            }

            // 4) Workflow V2
            var wf = new ContentWorkflow(content, _authUtil.UserId);
            if (!wf.SetStateByName(content, stepName))
            {
                var errs = wf.ValidationErrors.Any()
                    ? wf.ValidationErrors.ToArray()
                    : new[] { "NO_ERR" };

                return new SetStepByNameViewModel
                {
                    Success = false,
                    Errors = errs
                };
            }

            // 5) Persiste status e closeDate/user
            var newState = wf.ResultState;
            var mod = new Content
            {
                contentUuid = content.contentUuid,
                status = newState
            };
            if (newState is "CANCELLED" or "CLOSED")
            {
                mod.closeDate = DateTime.UtcNow;
                mod.closeUser = _authUtil.UserId;
            }
            await _repo.Update(content.contentId, mod);

            // 7) Subscriber em AWAITING_REVIEW
            if (newState == "AWAITING_REVIEW")
            {
                var reviewer = content.reviewerUser;
                if (reviewer.HasValue
                    && !content.ContentSubscriber.Any(s => s.userId == reviewer.Value))
                {
                    new ContentSubscriberService(_authUtil.UserId, content.contentId)
                        .Add(reviewer.Value);
                }
            }

            var title = content.title;
            var activitySvc = new ContentActivityService(_authUtil.UserId);

            // 8) Activities de review (bulk)
            if (newState == "OPEN"
                && originalStatus == "AWAITING_REVIEW"
                && content.reviewerUser == _authUtil.UserId)
            {
                await activitySvc.Add(
                    new List<ContentActivity>
                    {
                        new ContentActivity
                        {
                            contentId = content.contentId,
                            date = DateTime.UtcNow,
                            processed = false,
                            type = "REVIEW_REJECTED",
                            activityUser = _authUtil.UserId,
                            contentData = JsonConvert.SerializeObject(new { title })
                        }
                    },
                    userAgent);
            }

            if (newState == "CLOSED"
                && originalStatus == "AWAITING_REVIEW"
                && content.reviewerUser == _authUtil.UserId)
            {
                await activitySvc.Add(
                    new List<ContentActivity>
                    {
                        new ContentActivity
                        {
                            contentId = content.contentId,
                            date = DateTime.UtcNow,
                            processed = false,
                            type = "REVIEW_ACCEPTED",
                            activityUser = _authUtil.UserId,
                            contentData = JsonConvert.SerializeObject(new { title })
                        }
                    },
                    userAgent);
            }

            // 9) Reordena Task fechada
            var closedTask = content.Task.FirstOrDefault();
            if (originalStatus != "CLOSED"
                && newState == "CLOSED"
                && closedTask?.workgroupTaskListId != null)
            {
                await contentService.MoveTaskToLastPosition(
                    closedTask.contentId,
                    closedTask,
                    saveChanges: true);
            }

            // 10) Retorno raw para Application montar o DTO
            return new SetStepByNameViewModel
            {
                Success = true,
                NewState = newState
            };
        }

        public async Task<SetNextStepViewModel> SetStepAsync(Guid contentUuid, string userAgent, bool forward, string comment)
        {
            comment ??= string.Empty;

            // 1) Busca objeto e guarda status original
            var contentService = new ContentService(_authUtil.UserId);
            var content = await contentService.Get(contentUuid);
            var originalStatus = content.status;

            // 2) Permissões
            if (!_repo.CheckPermissions(contentUuid))
                throw new SecurityException("Unauthorized set next step requested.");
            if (!_repo.CheckPermissionsForWorkgroup(Operations.UPDATE, content.contentId))
                throw new SecurityException("Unauthorized attempt to update data to workgroup.");

            // 3) Regra V2: se for Task, só changeStatus; se não, valida lastVoteReason
            if (content.type == ContentTypes.Task)
            {
                if (!content.UAC.changeStatus)
                    throw new SecurityException("CANT_CHANGE_STATUS");
            }
            else
            {
                if (!content.UAC.changeStatus)
                    throw new SecurityException("IS_NOT_OWNER");
            }

            // 4) Pai Meeting cancelado?
            if (content.parentContentId.HasValue)
            {
                var parent = await _repo.Get(content.parentContentUuid.Value);
                if (parent?.type == ContentTypes.Meeting
                 && parent.status == ContentMeetingStatus.CANCELLED)
                    throw new InvalidOperationException("NOT_ALLOWED");
            }

            // 5) Workflow: avança ou retrocede conforme forward
            var wf = new ContentWorkflow(content, _authUtil.UserId);
            wf.SetState(forward);
            if (wf.ValidationErrors.Any())
            {
                throw new SecurityException(wf.ValidationErrors.First());
            }

            // 6) VALIDAÇÃO V2 EXTRA (Task): INVALID_NEXT_STATUS
            if (content.type == ContentTypes.Task
             && !content.UAC.possibleNextStatuses.Contains(wf.ResultState))
                throw new SecurityException("INVALID_NEXT_STATUS");

            // 7) Persiste novo status e data de fechamento
            var newState = wf.ResultState;
            var mod = new Content
            {
                contentUuid = content.contentUuid,
                status = newState
            };
            if (newState is "CANCELLED" or "CLOSED")
            {
                mod.closeDate = DateTime.UtcNow;
                mod.closeUser = _authUtil.UserId;
            }

            if (mod.status == "OPEN" && originalStatus == "AWAITING_REVIEW"
                && content.reviewerUser == _authUtil.UserId && comment == null)
            {
                throw new Exception("NO_REJECTION_REASON");
            }

            await _repo.Update(content.contentId, mod);

            if (content.type != ContentTypes.Meeting
                && comment != "" && comment != "undefined" && !string.IsNullOrWhiteSpace(comment))
            {
                await new ContentCommentService(_authUtil.UserId, content.contentId, content.contentUuid).Add(comment, supressNotification: true);
            }

            // 9) Subscriber em AWAITING_REVIEW
            if (newState == "AWAITING_REVIEW")
            {
                var rev = content.reviewerUser;
                if (rev.HasValue && !content.ContentSubscriber.Any(s => s.userId == rev.Value))
                    new ContentSubscriberService(_authUtil.UserId, content.contentId)
                        .Add(rev.Value);
            }

            // 10) Activities de revisão (bulk)
            var title = content.title;
            var activitySvc = new ContentActivityService(_authUtil.UserId);

            if (newState == "OPEN"
             && originalStatus == "AWAITING_REVIEW"
             && content.reviewerUser == _authUtil.UserId)
            {
                activitySvc.Add(content.contentId, content.contentUuid,
                    new ContentActivity
                    {
                        contentId = content.contentId,
                        contentUuid = content.contentUuid,
                        date = DateTime.UtcNow,
                        processed = false,
                        type = "REVIEW_REJECTED",
                        activityUser = _authUtil.UserId,
                        contentData = JsonConvert.SerializeObject(new { title })
                    }, userAgent);
            }

            if (newState == "CLOSED"
             && originalStatus == "AWAITING_REVIEW"
             && content.reviewerUser == _authUtil.UserId)
            {
                activitySvc.Add(content.contentId, content.contentUuid,
                    new ContentActivity
                    {
                        contentId = content.contentId,
                        contentUuid = content.contentUuid,
                        date = DateTime.UtcNow,
                        processed = false,
                        type = "REVIEW_ACCEPTED",
                        activityUser = _authUtil.UserId,
                        contentData = JsonConvert.SerializeObject(new { title })
                    }, userAgent);
            }

            // 11) Reordena task fechada
            var closedTask = content.Task.FirstOrDefault();
            if (originalStatus != "CLOSED"
             && newState == "CLOSED"
             && closedTask?.workgroupTaskListId != null)
            {
                await contentService.MoveTaskToLastPosition(
                    closedTask.contentId, closedTask, saveChanges: true);
            }

            // 13) Retorna raw para Application
            return new SetNextStepViewModel
            {
                NewState = newState
            };
        }


        /// <summary>
        /// Creates a new checklist item for a task.
        /// </summary>
        /// <param name="item">The checklist item data with contentUuid.</param>
        /// <param name="userAgent">The user agent string for activity logging.</param>
        /// <returns>The ID of the created checklist item.</returns>
        public async Task<int> CreateChecklistItemAsync(ChecklistItemViewModel item, string userAgent)
        {
            // Use ContentChecklistService to add the item (it will get content by UUID internally)
            var checklistService = new ContentChecklistService(_authUtil.UserId);
            return await checklistService.Add(item, userAgent);
        }

        /// <summary>
        /// Updates a checklist item for a task.
        /// </summary>
        /// <param name="item">The checklist item data to update.</param>
        /// <param name="userAgent">The user agent string for activity logging.</param>
        /// <returns>True if the update was successful, false otherwise.</returns>
        public async Task<bool> UpdateChecklistItemAsync(ChecklistItemViewModel item, string userAgent)
        {
            // Use ContentChecklistService to update the item
            var checklistService = new ContentChecklistService(_authUtil.UserId);
            return await checklistService.Put(item, userAgent);
        }

        /// <summary>
        /// Deletes a checklist item.
        /// </summary>
        /// <param name="checklistItemId">The ID of the checklist item to delete.</param>
        /// <param name="userAgent">The user agent string for activity logging.</param>
        /// <returns>True if the deletion was successful, false otherwise.</returns>
        public async Task<bool> DeleteChecklistItemAsync(int checklistItemId, string userAgent)
        {
            // Use ContentChecklistService to delete the item
            var checklistService = new ContentChecklistService(_authUtil.UserId);
            return await checklistService.Delete(checklistItemId, userAgent);
        }

        /// <summary>
        /// Reorders checklist items for a task.
        /// </summary>
        /// <param name="contentUuid">The UUID of the task content.</param>
        /// <param name="items">The checklist items with new order.</param>
        /// <param name="userAgent">The user agent string for activity logging.</param>
        /// <returns>True if the reordering was successful, false otherwise.</returns>
        public async Task<bool> ReorderChecklistItemsAsync(Guid contentUuid, IEnumerable<ChecklistItemViewModel> items, string userAgent)
        {
            // Use ContentChecklistService to reorder the items
            // The service will validate permissions and the repository will validate security
            var checklistService = new ContentChecklistService(_authUtil.UserId);
            return await checklistService.Reorder(contentUuid, items, userAgent);
        }
        /// <summary>
        /// Gets checklist items for a task.
        /// </summary>
        /// <param name="contentUuid">The UUID of the task content.</param>
        /// <returns>A list of checklist items associated with the task.</returns>
        public async Task<List<ChecklistItemDto>> GetChecklistItemsAsync(Guid contentUuid)
        {
            var contentService = new ContentService(_authUtil.UserId);
            var content = await contentService.Get(contentUuid);

            if (!_repo.CheckPermissions(contentUuid))
                throw new SecurityException("Unauthorized access to checklist items.");

            if (content.type != ContentTypes.Task)
                throw new InvalidOperationException("CONTENT_TYPE_ERROR");

            if (content.TaskCheckListItem == null || !content.TaskCheckListItem.Any())
                return new List<ChecklistItemDto>();

            return content.TaskCheckListItem
                .OrderBy(item => item.itemOrder)
                .Select(item => new ChecklistItemDto
                {
                    taskChecklistItemId = item.taskCheckListItemId,
                    name = item.name,
                    finished = item.finished,
                    itemOrder = item.itemOrder,
                    createDate = item.createDate,
                    createUser = item.createUser,
                    lastUpdate = item.lastUpdate,
                    contentId = item.contentId,
                    contentUuid = item.contentUuid
                })
                .ToList();
        }

        private async System.Threading.Tasks.Task EnsureColumnForTaskAsync(Content c)
        {
            if (c?.Task == null || !c.Task.Any()) return;

            var task = c.Task.First();
            var id = task.workgroupTaskListId;

            // Já tem coluna válida?
            var lists = c.Workgroup?.WorkgroupTaskList;
            var hasValidColumn = id.HasValue && id.Value != 0
                                  && lists != null
                                  && lists.Any(w => w.workgroupTaskListId == id.Value);
            if (hasValidColumn) return;

            var workGroupRepo = new WorkgroupRepository(_authUtil.UserId);
            var workGroup = await workGroupRepo.Get(c.workgroupId);
            var clientId = workGroup.clientId;

            // Pega a primeira coluna (ou cria a default + activity)
            var firstColumn = await EnsureWorkgroupHasTaskList(c.workgroupId, clientId);
            if (firstColumn == null) return;

            // Persiste só se estiver sem/ inválida
            await _repo.AssignTaskListToTaskIfMissingOrInvalidAsync(c.contentId, firstColumn.workgroupTaskListId);

            task.workgroupTaskListId = firstColumn.workgroupTaskListId;
        }

        private async System.Threading.Tasks.Task EnsureColumnsForTasksAsync(IEnumerable<Content> contents)
        {
            if (contents == null) return;

            // Quais tasks estão "sem coluna"? (null/0 OU id inválido no board OU lista não carregada)
            var withoutColumn = contents
                .Where(c => c?.Task != null && c.Task.Any())
                .Where(c =>
                {
                    var task = c.Task.First();
                    var id = task.workgroupTaskListId;

                    // Caso clássico: null/0
                    if (!id.HasValue || id.Value == 0)
                        return true;

                    // Se a lista do board não veio, trate como sem coluna para garantir backfill
                    var lists = c.Workgroup?.WorkgroupTaskList;
                    if (lists == null)
                        return true;

                    // Id presente mas não existe no board => inválido => tratar como sem coluna
                    return !lists.Any(wtl => wtl.workgroupTaskListId == id.Value);
                })
                .ToList();

            if (withoutColumn.Count == 0) return;

            var workgroupId = withoutColumn.First().workgroupId;

            var workGroupRepo = new WorkgroupRepository(_authUtil.UserId);
            var workGroup = await workGroupRepo.Get(workgroupId);
            var clientId = workGroup.clientId;

            // Pega a primeira coluna (ou cria a default + activity WORKGROUP_TASKLIST_CREATED)
            var firstColumn = await EnsureWorkgroupHasTaskList(workgroupId, clientId);
            if (firstColumn == null) return;

            // Força atribuição no banco para exatamente esses conteúdos
            await _repo.AssignTaskListToTasksAsync(withoutColumn.Select(x => x.contentId), firstColumn.workgroupTaskListId);

            foreach (var c in withoutColumn)
                c.Task.First().workgroupTaskListId = firstColumn.workgroupTaskListId;
        }

        private async System.Threading.Tasks.Task EnsureColumnsForWorkgroupAsync(int workgroupId)
        {
            if (workgroupId <= 0) return;

            var workGroupRepo = new WorkgroupRepository(_authUtil.UserId);
            var workGroup = await workGroupRepo.Get(workgroupId);
            var clientId = workGroup.clientId;

            var firstColumn = await EnsureWorkgroupHasTaskList(workgroupId, clientId);
            if (firstColumn == null) return;

            await _repo.AssignTaskListToWorkgroupTasksIfMissingOrInvalidAsync(workgroupId, firstColumn.workgroupTaskListId);
        }
    }
}
