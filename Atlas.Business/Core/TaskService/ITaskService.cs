using Atlas.Business.ViewModels;
using Atlas.CrossCutting.DTO.Task;
using Atlas.Data.Dtos.TaskDtos;
using Atlas.Data.Entities;
using Atlas.Data.Repository;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Atlas.Business.Core.TaskService
{
    public interface ITaskService
    {
        Task<TaskDetailResultViewModel> GetTaskByContentUuid(Guid contentUuid);
        List<TaskListViewModel> GetTasksList(ContentRequestFilter filters);
        Task<List<Content>> GetTasksByParentContentAsync(int clientId, int workgroupId, Guid parentContentUuid);
        Task<Dictionary<string, int>> GetCounters(int workgroupId);
        Task<IEnumerable<PermissionListViewModel>> GetTaskPermissions(Guid contentUuid);

        Task<TaskCreateResponseDto> CreateAsync(int workgroupId, Content obj, string userAgent, bool supressActivity = false);
        System.Threading.Tasks.Task DeleteAsync(Guid contentUuid);
        Task<bool> ResendEmailForContentAsync(Guid contentUuid);
        Task<SetStepByNameViewModel> SetStepByNameAsync(Guid contentUuid, string stepName, string userAgent);
        Task<SetNextStepViewModel> SetStepAsync(Guid contentUuid, string userAgent, bool forward, string comment);
        Task<(Content updated, List<PredecessorSummaryDto> summaries)> UpdateAsync(Content updatedContent, string userAgent);

        // Checklist methods - optimized signatures with UserAgent support
        Task<int> CreateChecklistItemAsync(ChecklistItemViewModel item, string userAgent);
        Task<bool> UpdateChecklistItemAsync(ChecklistItemViewModel item, string userAgent);
        Task<bool> DeleteChecklistItemAsync(int checklistItemId, string userAgent);
        Task<bool> ReorderChecklistItemsAsync(Guid contentUuid, IEnumerable<ChecklistItemViewModel> items, string userAgent);
        Task<List<ChecklistItemDto>> GetChecklistItemsAsync(Guid contentUuid);
    }
}
