using System;
using System.Threading.Tasks;
using Atlas.CrossCutting.DTO.ContentComment;

namespace Atlas.Business.Core.ContentComment
{
    public interface IContentCommentService
    {
        Task<Data.Entities.ContentComment> AddComment(ContentCommentCreateDto request, string userAgent);

        Task<Data.Entities.ContentComment> DeleteCommentAsync(Guid contentUuid, int commentId, bool delete, string userAgent);
    }
}
