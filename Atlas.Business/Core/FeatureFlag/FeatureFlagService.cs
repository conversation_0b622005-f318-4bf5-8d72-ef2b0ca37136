using Atlas.CrossCutting.Exceptions;
using Atlas.CrossCutting.Exceptions.ErrorCodes;
using Atlas.CrossCutting.Helpers;
using Atlas.CrossCutting.Models.FeatureFlag;
using Atlas.Data.Entities;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using System.Security.Principal;

namespace Atlas.Business.Core.FeatureFlag
{
    /// <summary>
    /// Service for feature flag validation and management
    /// </summary>
    public class FeatureFlagService : IFeatureFlagService
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConfiguration _configuration;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly AuthUtil _authUtil;

        public FeatureFlagService(IHttpClientFactory httpClientFactory, IConfiguration configuration, IHttpContextAccessor httpContextAccessor, IPrincipal principal)
        {
            _httpClientFactory = httpClientFactory;
            _configuration = configuration;
            _httpContextAccessor = httpContextAccessor;
            _authUtil = new AuthUtil(principal);
        }

        #region Private Helper Methods

        /// <summary>
        /// Extracts and validates the JWT token from the HTTP context
        /// </summary>
        /// <returns>The extracted JWT token</returns>
        /// <exception cref="HttpCustomException">Thrown when token is invalid or missing</exception>
        private string ExtractAndValidateToken()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            var authHeader = httpContext?.Request.Headers["Authorization"].FirstOrDefault();
            
            if (string.IsNullOrWhiteSpace(authHeader) || !authHeader.StartsWith("Bearer "))
            {
                throw new HttpCustomException(GeneralErrorCodes.FeatureUnauthorized, HttpStatusCode.Unauthorized);
            }

            return authHeader.Substring("Bearer ".Length);
        }

        /// <summary>
        /// Converts client IDs to tenant UUIDs with optional mapping tracking
        /// </summary>
        /// <param name="clientIds">Array of client IDs to convert</param>
        /// <param name="token">JWT token for UUID extraction</param>
        /// <param name="clientTenantMapping">Optional dictionary to track client-tenant mapping</param>
        /// <returns>List of resolved tenant UUIDs</returns>
        private async Task<List<Guid>> ConvertClientIdsToTenantIds(int[] clientIds, string token, Dictionary<int, Guid> clientTenantMapping = null)
        {
            var tenantIds = new List<Guid>();

            foreach (var clientId in clientIds)
            {
                // Try to get ClientUuid from JWT first
                Guid? clientUuid = GetClientUuidFromJWT(token, clientId);

                // If not found in JWT, query the database
                if (!clientUuid.HasValue)
                {
                    clientUuid = await GetClientUuidFromDatabaseAsync(clientId);
                }

                // Only add valid UUIDs and track mapping if requested
                if (clientUuid.HasValue && clientUuid.Value != Guid.Empty)
                {
                    tenantIds.Add(clientUuid.Value);
                    clientTenantMapping?.Add(clientId, clientUuid.Value);
                }
            }

            return tenantIds;
        }

        /// <summary>
        /// Builds query parameters for the feature flag API request
        /// </summary>
        /// <param name="tenantIds">List of tenant UUIDs</param>
        /// <param name="featureKeys">Array of feature keys</param>
        /// <returns>Complete query string for the API request</returns>
        private string BuildFeatureFlagQueryString(IEnumerable<Guid> tenantIds, string[] featureKeys)
        {
            var queryParams = new List<string>();

            // Add TenantId parameters (multiple values)
            foreach (var tenantId in tenantIds)
            {
                queryParams.Add($"TenantId={HttpUtility.UrlEncode(tenantId.ToString())}");
            }

            // Add Keys parameters (multiple values)
            foreach (var key in featureKeys)
            {
                queryParams.Add($"Keys={HttpUtility.UrlEncode(key)}");
            }

            return string.Join("&", queryParams);
        }

        /// <summary>
        /// Makes an HTTP request to the feature flag API
        /// </summary>
        /// <param name="requestUrl">The complete request URL</param>
        /// <param name="token">JWT token for authorization</param>
        /// <returns>The HTTP response</returns>
        private async Task<HttpResponseMessage> MakeFeatureFlagApiRequest(string requestUrl, string token)
        {
            using var httpClient = _httpClientFactory.CreateClient();
            httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            return await httpClient.GetAsync(requestUrl);
        }

        #endregion

        /// <summary>
        /// Validates if a feature flag is enabled for a specific client using the current HTTP context
        /// </summary>
        /// <param name="featureKey">The feature flag key to validate</param>
        /// <param name="clientId">The client ID</param>
        /// <returns>True if the feature is enabled, false otherwise</returns>
        public async Task<bool> ValidateFeatureFlagAsync(string featureKey, int clientId)
        {
            try
            {
                var token = ExtractAndValidateToken();
                return await ValidateFeatureFlagAsync(featureKey, clientId, token);
            }
            catch (HttpCustomException)
            {
                // Re-throw HttpCustomException to preserve the error code and status
                throw;
            }
            catch (Exception)
            {
                // In case of any other error, return false
                return false;
            }
        }

        /// <summary>
        /// Validates if a feature flag is enabled for a specific client
        /// </summary>
        /// <param name="featureKey">The feature flag key to validate</param>
        /// <param name="clientId">The client ID</param>
        /// <param name="authToken">The JWT authentication token</param>
        /// <returns>True if the feature is enabled, false otherwise</returns>
        public async Task<bool> ValidateFeatureFlagAsync(string featureKey, int clientId, string authToken)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(featureKey))
                {
                    throw new ArgumentException("Feature key cannot be null or empty", nameof(featureKey));
                }

                if (string.IsNullOrWhiteSpace(authToken))
                {
                    throw new ArgumentException("Auth token cannot be null or empty", nameof(authToken));
                }

                if (clientId <= 0)
                {
                    throw new ArgumentException("Client ID must be greater than 0", nameof(clientId));
                }

                // Try to get ClientUuid from JWT first to reduce latency
                Guid? clientUuid = GetClientUuidFromJWT(authToken, clientId);

                // If not found in JWT, query the database
                if (!clientUuid.HasValue)
                {
                    clientUuid = await GetClientUuidFromDatabaseAsync(clientId);
                }

                // Validate that ClientUuid is not empty or invalid
                if (!clientUuid.HasValue || clientUuid.Value == Guid.Empty)
                {
                    throw new FeatureFlagUnauthorizedException(featureKey);
                }

                // Create HTTP client
                using var httpClient = _httpClientFactory.CreateClient();
                
                // Set up the request to feature flags API using the dynamic feature key
                var baseUrl = _configuration["FeatureFlagApiUrl"];
                var requestUrl = $"{baseUrl}/api/feature-flags/{featureKey}?TenantId={clientUuid}";

                // Add JWT token to the request
                httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", authToken);

                // Make the HTTP GET request
                var response = await httpClient.GetAsync(requestUrl);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    
                    if (!string.IsNullOrEmpty(responseContent))
                    {
                        // Parse the JSON response
                        var featureFlagResponse = JsonConvert.DeserializeObject<FeatureFlagApiResponse>(responseContent);
                        
                        // Check if the request was successful and the feature is enabled
                        return featureFlagResponse?.Success == true && featureFlagResponse?.Data?.IsEnabled == true;
                    }
                }
                
                return false;
            }
            catch (HttpCustomException)
            {
                // Re-throw HttpCustomException to preserve the error code and status
                throw;
            }
            catch (Exception)
            {
                // In case of any other error, return false
                return false;
            }
        }

        /// <summary>
        /// Gets the client UUID from JWT token
        /// </summary>
        /// <param name="token">The JWT token</param>
        /// <param name="clientId">The client ID to search for</param>
        /// <returns>The ClientUuid if found, null otherwise</returns>
        public Guid? GetClientUuidFromJWT(string token, int clientId)
        {
            try
            {
                var handler = new JwtSecurityTokenHandler();
                var jwtToken = handler.ReadJwtToken(token);

                // Get the clients claim
                var clientsClaim = jwtToken.Claims.FirstOrDefault(c => c.Type == "clients");
                if (clientsClaim == null || string.IsNullOrEmpty(clientsClaim.Value))
                {
                    return null;
                }

                // Parse the clients JSON array
                var clientsArray = JsonConvert.DeserializeObject<dynamic[]>(clientsClaim.Value);
                if (clientsArray == null)
                {
                    return null;
                }

                // Find the client with the matching clientId
                var matchingClient = clientsArray.FirstOrDefault(c => 
                    c.clientId != null && (int)c.clientId == clientId);

                if (matchingClient?.clientUuid != null)
                {
                    if (Guid.TryParse(matchingClient.clientUuid.ToString(), out Guid uuid))
                    {
                        return uuid;
                    }
                }

                return null;
            }
            catch (Exception)
            {
                // If any error occurs parsing JWT, return null to fallback to database query
                return null;
            }
        }

        /// <summary>
        /// Gets the client UUID from database
        /// </summary>
        /// <param name="clientId">The client ID</param>
        /// <returns>The ClientUuid if found, null otherwise</returns>
        public async Task<Guid?> GetClientUuidFromDatabaseAsync(int clientId)
        {
            try
            {
                using (var dbContext = new AtlasModelCore())
                {
                    var client = await dbContext.Client
                        .Where(c => c.clientId == clientId)
                        .Select(c => new { c.clientUuid })
                        .FirstOrDefaultAsync();

                    return client?.clientUuid;
                }
            }
            catch (Exception)
            {
                // If any error occurs querying database, return null
                return null;
            }
        }

        /// <summary>
        /// Validates feature flags for multiple tenants and keys using client IDs from HTTP context
        /// </summary>
        /// <param name="featureKeys">Array of feature flag keys to validate</param>
        /// <param name="clientIds">Array of client IDs</param>
        /// <returns>Dictionary with feature flag results</returns>
        public async Task<Dictionary<string, bool>> ValidateFeatureFlagsAsync(string[] featureKeys, int[] clientIds)
        {
            try
            {
                if (featureKeys == null || featureKeys.Length == 0)
                {
                    throw new ArgumentException("Feature keys cannot be null or empty", nameof(featureKeys));
                }

                if (clientIds == null || clientIds.Length == 0)
                {
                    throw new ArgumentException("Client IDs cannot be null or empty", nameof(clientIds));
                }

                var token = ExtractAndValidateToken();

                // Convert client IDs to tenant UUIDs using helper method
                var tenantIds = await ConvertClientIdsToTenantIds(clientIds, token);

                if (tenantIds.Count == 0)
                {
                    throw new HttpCustomException(GeneralErrorCodes.FeatureUnauthorized, HttpStatusCode.Unauthorized);
                }

                // Build request URL using helper method
                var baseUrl = _configuration["FeatureFlagApiUrl"];
                var requestUrl = $"{baseUrl}/api/feature-flags?{BuildFeatureFlagQueryString(tenantIds, featureKeys)}";

                // Make API request using helper method
                var response = await MakeFeatureFlagApiRequest(requestUrl, token);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    
                    if (!string.IsNullOrEmpty(responseContent))
                    {
                        // Parse the JSON response using the correct model
                        var featureFlagResponse = JsonConvert.DeserializeObject<FeatureFlagMultiTenantApiResponse>(responseContent);
                        
                        if (featureFlagResponse?.Success == true && featureFlagResponse.Data?.Tenants != null)
                        {
                            // Initialize result dictionary with all features as false
                            var results = new Dictionary<string, bool>();
                            foreach (var key in featureKeys)
                            {
                                results[key] = false;
                            }

                            // Process each tenant's flags and set to true if ANY tenant has the feature enabled
                            foreach (var tenant in featureFlagResponse.Data.Tenants)
                            {
                                if (tenant?.Flags != null)
                                {
                                    foreach (var key in featureKeys)
                                    {
                                        if (tenant.Flags.ContainsKey(key) && tenant.Flags[key])
                                        {
                                            results[key] = true; // If any tenant has it enabled, mark as true
                                        }
                                    }
                                }
                            }

                            return results;
                        }
                    }
                }
                
                // Return all features as disabled if request failed
                return featureKeys.ToDictionary(key => key, key => false);
            }
            catch (HttpCustomException)
            {
                // Re-throw HttpCustomException to preserve the error code and status
                throw;
            }
            catch (Exception)
            {
                // In case of any other error, return all features as disabled
                return featureKeys.ToDictionary(key => key, key => false);
            }
        }

        /// <summary>
        /// Validates if a user has permission to access a feature using feature flags.
        /// Checks the feature flag across all client IDs associated with the user.
        /// Returns true if the user has access, false otherwise. Throws HttpCustomException on validation errors.
        /// </summary>
        /// <param name="featureKey">The feature flag key to validate</param>
        /// <returns>True if the user has access to the feature, false otherwise</returns>
        /// <exception cref="HttpCustomException">Thrown when validation fails due to system errors</exception>
        public async Task<bool> ValidateUserFeatureFlag(string featureKey)
        {
            try
            {
                var userId = _authUtil.UserId;

                if (userId <= 0)
                {
                    throw new HttpCustomException(GeneralErrorCodes.FeatureUnauthorized, HttpStatusCode.Unauthorized);
                }

                // Get user's client IDs using helper method
                var clientIds = GetValidatedUserClientIds(userId);

                // Use ValidateFeatureFlagsAsync to check if user has permission to access the feature
                var featureFlags = await ValidateFeatureFlagsAsync(
                    new[] { featureKey },
                    clientIds
                );

                // Check if the feature is enabled for at least one of the user's clients
                bool hasFeatureAccess = featureFlags.ContainsKey(featureKey) &&
                                       featureFlags[featureKey];

                return hasFeatureAccess;
            }
            catch (Exception)
            {
                // In case of any other error, throw unauthorized with feature key
                throw new FeatureFlagUnauthorizedException(featureKey);
            }
        }

        /// <summary>
        /// Gets the client IDs that have a specific feature flag enabled for a user
        /// </summary>
        /// <param name="featureKey">The feature flag key to validate</param>
        /// <returns>Array of client IDs that have the feature enabled</returns>
        public async Task<int[]> GetEnabledClientIdsForUserFeatureFlag(string featureKey)
        {
            try
            {
                var userId = _authUtil.UserId;

                if (userId <= 0)
                {
                    throw new HttpCustomException(GeneralErrorCodes.FeatureUnauthorized, HttpStatusCode.Unauthorized);
                }

                // Get user's client IDs using helper method
                var clientIds = GetUserClientIds(userId);

                if (clientIds.Length == 0)
                {
                    return new int[0];
                }

                // Use ValidateFeatureFlagsAsync to get feature flag results per client
                var featureFlags = await ValidateFeatureFlagsAsync(
                    new[] { featureKey },
                    clientIds
                );

                // If the feature is enabled for any client, we need to determine which specific clients
                // For now, return all client IDs if the feature is enabled for any of them
                // This is a simplified approach since we don't have detailed per-client info from ValidateFeatureFlagsAsync
                if (featureFlags.ContainsKey(featureKey) && featureFlags[featureKey])
                {
                    return clientIds;
                }

                return new int[0];
            }
            catch (Exception)
            {
                // In case of any error, return empty array
                return new int[0];
            }
        }

        /// <summary>
        /// Gets the client IDs for a user with validation
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>Array of client IDs for the user</returns>
        /// <exception cref="HttpCustomException">Thrown when user has no valid client IDs</exception>
        private int[] GetValidatedUserClientIds(int userId)
        {
            var userClientService = new UserClientService();
            var clientIds = userClientService.ClientsByUser(userId);

            if (clientIds == null || !clientIds.Any())
            {
                throw new HttpCustomException(GeneralErrorCodes.FeatureUnauthorized, HttpStatusCode.Unauthorized);
            }

            return clientIds.ToArray();
        }

        /// <summary>
        /// Gets the client IDs for a user without throwing exceptions
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>Array of client IDs for the user, or empty array if none found</returns>
        private int[] GetUserClientIds(int userId)
        {
            try
            {
                var userClientService = new UserClientService();
                var clientIds = userClientService.ClientsByUser(userId);

                return clientIds?.Any() == true ? clientIds.ToArray() : new int[0];
            }
            catch
            {
                return new int[0];
            }
        }
    }
}