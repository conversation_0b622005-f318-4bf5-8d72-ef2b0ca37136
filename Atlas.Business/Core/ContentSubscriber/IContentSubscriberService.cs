using Atlas.CrossCutting.DTO.ContentPermission;
using Atlas.CrossCutting.DTO.ContentSubscriber;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Atlas.Business.Core.ContentSubscriber
{
    public interface IContentSubscriberService
    {
        Task<IEnumerable<ContentSubscriberDTO>> GetSubscribersAsync(Guid contentUuId, bool? onlyCheckedSubscribers);
        Task<bool> UpdateSubscribersAsync(Guid contentUuId, List<ContentSubscriberDTO> subscribers, bool? recurrenceShouldNotify = null);
        Task<bool> ResendMeetingInviteAsync(Guid contentUuId, List<ContentSubscriberDTO> subscribers);
        Task<bool> DeleteSubscriberAsync(Guid contentUuId, int subscriberUserId);
        Task<bool> SetRSVPResponseAsync(Guid contentUuId, int userId, bool? response);
        Task<bool> SendRSVPAsync(Guid contentUuId);
        Task<List<ContentPermissionDTO>> GetSubscribersInfoAsync(Guid contentUuId);
        Task<IEnumerable<ContentSubscriberDTO>> GetPossibleAssignedUsersAsync(Guid contentUuId);
    }
}