using Atlas.CrossCutting.DTO.ContentOwner;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Atlas.Business.Core.ContentOwner
{
    /// <summary>
    /// Interface for Content Owner business layer operations
    /// </summary>
    public interface IContentOwnerService
    {
        /// <summary>
        /// Gets the list of content owners for a specific content
        /// </summary>
        /// <param name="contentId">Content identifier</param>
        /// <returns>List of content owners</returns>
        Task<IEnumerable<ContentOwnerDTO>> GetOwnersAsync(Guid contentUuId);

        /// <summary>
        /// Gets the list of available users that can be added as owners for a specific content
        /// </summary>
        /// <param name="workgroupId">Workgroup identifier</param>
        /// <param name="contentId">Content identifier</param>
        /// <returns>List of available users</returns>
        Task<IEnumerable<ContentOwnerDTO>> GetAvailableUsersAsync(int workgroupId, Guid contentUuId, int[] ownersList);

        /// <summary>
        /// Adds a user as owner to a specific content
        /// </summary>
        /// <param name="contentId">Content identifier</param>
        /// <param name="userId">User identifier to add as owner</param>
        /// <returns>True if the operation was successful</returns>
        Task<int> AddOwnerAsync(Guid contentUuId, int userId);

        /// <summary>
        /// Removes a user as owner from a specific content
        /// </summary>
        /// <param name="contentId">Content identifier</param>
        /// <param name="userId">User identifier to remove as owner</param>
        /// <returns>True if the operation was successful</returns>
        Task<bool> RemoveOwnerAsync(Guid contentUuId, int userId);

        /// <summary>
        /// Updates the complete list of content owners
        /// </summary>
        /// <param name="contentId">Content identifier</param>
        /// <param name="owners">List of owners to update</param>
        /// <returns>Number of affected users</returns>
        Task<bool> UpdateOwnersAsync(Guid contentUuId, List<ContentOwnerDTO> owners);
    }
}
