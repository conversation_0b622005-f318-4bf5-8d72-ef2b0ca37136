using Atlas.CrossCutting.AppEnums;
using Atlas.CrossCutting.DTO.MeetingMinute;
using Atlas.CrossCutting.Helpers;
using Atlas.CrossCutting.Settings;
using Atlas.Data.Entities;
using Atlas.Data.Repository;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Principal;
using System.Threading.Tasks;

namespace Atlas.Business.Core.MeetingMinute
{
    public class MeetingMinuteService : IMeetingMinuteService
    {
        readonly private AuthUtil _authUtil;
        readonly private int _currentUserId;
        readonly private ContentService _contentService;
        readonly private ContentRepository _contentRepository;
        readonly private StorageSettings _storageSettings;
        readonly private UserService _userService;
        readonly private ContentServiceV2 _contentService_V2;
        readonly private WorkgroupService _workgroupService;
        private readonly User _currentUser;

        public MeetingMinuteService(IPrincipal principal, StorageSettings storageSettings)
        {
            _authUtil = new AuthUtil(principal);
            _currentUserId = _authUtil.UserId;
            _storageSettings = storageSettings;

            if (_currentUserId != 0)
            {
                _contentRepository = new ContentRepository(_currentUserId);
                _contentService = new ContentService(_currentUserId);
                _userService = new UserService();
                _workgroupService = new WorkgroupService(_currentUserId);
                _currentUser = _userService.GetUserByUserId(_currentUserId);
                _contentService_V2 = new ContentServiceV2(_currentUser);
            }
        }

        public async Task<Guid> CreateMeetingMinute(MeetingMinuteCreateDto meetingMinute, string userAgent)
        {
            var parentMeeting = await _contentRepository.GetSimpleContent(meetingMinute.meetingUuid, isMeeting: true)
                ?? throw new InvalidOperationException("MEETING_NOT_FOUND");

            var parentMeetingMeetingId = parentMeeting.Meeting.First().meetingId;

            var contentMeetingMinute = new Content()
            {
                parentContentUuid = parentMeeting.contentUuid,
                parentContentId = parentMeeting.contentId,
                type = ContentTypes.MeetingMinute,
                workgroupId = meetingMinute.workgroupId,
                MeetingMinute = new List<Data.Entities.MeetingMinute>()
                {
                    new Data.Entities.MeetingMinute()
                    {
                        meetingId = parentMeetingMeetingId,
                        text = meetingMinute.text,
                    }
                },
            };

            return await _contentService.Add(meetingMinute.workgroupId, contentMeetingMinute, createUser: _currentUser, userAgent: userAgent);
        }

        public async Task<Content> GetMeetingMinute(Guid contentUuId)
        {
            return await _contentService.GetContent(contentUuId);
        }

        public async Task<MeetingMinuteUacDto> GetMeetingMinuteUACAsync(Content content)
        {
            if (content.type != ContentTypes.MeetingMinute)
            {
                throw new ArgumentException("Content must be of type MeetingMinute", nameof(content));
            }

            Content parentMeeting = null;
            if (content.parentContentUuid.HasValue)
            {
                parentMeeting = await _contentRepository.GetSimpleContent(content.parentContentUuid.Value, includeRelatedEntities: true, isMeeting: true);
            }

            var isParentMeetingOwner = parentMeeting?.ContentOwner?.Any(co => co.userId == _currentUserId) == true;
            var isContentOwner = isParentMeetingOwner;

            var workgroup = await _workgroupService.Get(content.workgroupId, includeHomeData: false);
            var isWorkgroupOwner = workgroup.WorkgroupOwner?.Any(wo => wo.userId == _currentUserId) == true;

            var parentMeetingCancelled = content.parent_status == ContentStatuses.ContentMeetingStatus.CANCELLED;
            var parentMeetingReady = content.parent_status == ContentStatuses.ContentMeetingStatus.READY;
            var parentMeetingOpen = content.parent_status == ContentStatuses.ContentMeetingStatus.OPEN;

            bool hasPermissionInAllAgendas = true;
            if (parentMeeting != null)
            {
                hasPermissionInAllAgendas = !parentMeeting.Child_Content
                    .Where(o => o.type == ContentTypes.MeetingAgendaItem)
                    .SelectMany(o => o.MeetingAgendaItem)
                    .Where(o => o.agendaItemType == "RESTRICTED")
                    .Any();
            }

            var isPublished = content.MeetingMinute?.FirstOrDefault()?.published == true;

            var signatureRequest = content.ContentSignatureRequest?.FirstOrDefault();
            var isSigner = signatureRequest?.ContentSigner?.Any(s => s.userId == _currentUserId) == true;

            var uacDto = new MeetingMinuteUacDto
            {
                IsOwner = isContentOwner,

                AddComment = !parentMeetingCancelled,
                AddAnswer = !parentMeetingCancelled,
                AddAttachment = isContentOwner && !parentMeetingCancelled,

                Delete = isContentOwner && !parentMeetingCancelled,
                Update = isContentOwner && !parentMeetingCancelled,
                ChangeStatus = isContentOwner && !parentMeetingCancelled,

                DeleteAttachment = isContentOwner && !parentMeetingCancelled,
                UndeleteAttachment = isContentOwner && !parentMeetingCancelled,

                DeleteComment = isContentOwner && !parentMeetingCancelled,
                UndeleteComment = isContentOwner && !parentMeetingCancelled,

                ManageOwners = isContentOwner && !parentMeetingCancelled,
                ManagePermissions = isContentOwner,

                ViewChildMinute = hasPermissionInAllAgendas,
                ExportMinute = hasPermissionInAllAgendas && isContentOwner,

                RequestESignature = isContentOwner && !parentMeetingCancelled,
                DownloadESignature = isSigner || isContentOwner,
                ViewESignatureSummary = isSigner || isContentOwner
            };

            return uacDto;
        }
    }
}
