using Atlas.CrossCutting.Constants;
using Atlas.CrossCutting.DTO.Email;
using Atlas.CrossCutting.Logging;
using Atlas.CrossCutting.Settings;
using Azure.Messaging.ServiceBus;
using MongoDB.Bson.IO;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Atlas.Business.Core.Email
{
    public class EmailService : IEmailService
    {
        private ServiceBusSettings _serviceBusSettings;
        private IStructuredLogger _logger;

        public EmailService(            
            ServiceBusSettings serviceBusSettings,
            IStructuredLogger logger
        )
        {
            _serviceBusSettings = serviceBusSettings;
            _logger = logger;
        }

        /// <summary>
        /// Sends a common usage email request to the service bus
        /// </summary>
        /// <param name="emailRequestDTO">DTO containing the payload data for the email and the template</param>
        /// <returns>A boolean representing the operation succeess</returns>
        public async Task<bool> SendEmailRequest(EmailRequestDTO emailRequestDTO)
        {
            try
            {
                // Any email orchestration can happen here (scheduling, debounce, cancellation, etc)
                ServiceBusClient serviceBusClient = new(_serviceBusSettings.ConnectionString);

                ServiceBusSender sender = serviceBusClient.CreateSender("mail_common");

                // The usage of Newtonsoft is intentional here as it serializes the payloadd (a derived class) out of the box
                // If for some reason you plan to change the serializer, make sure the payload is correctly serialized according to the chosen serializer
                ServiceBusMessage message = new(Newtonsoft.Json.JsonConvert.SerializeObject(emailRequestDTO)); 
                
                await sender.SendMessageAsync(message);
            }
            catch (Exception ex)
            {
                // Log the error but the client decides what to do with it
                _logger.LogError($"ERROR_SENDING_COMMON_EMAIL_REQUEST", ex);
                return false;
            }

            return true;
        }

        /// <summary>
        /// Sends a request to the dedicated ICS email queue
        /// </summary>
        /// <param name="emailRequestDTO">DTO containing the payload data for the email and the template</param>
        /// <param name="sessionId">SessionId for the email queue</param>
        /// <returns>A boolean representing the operation succeess</returns>
        public async Task<bool> SendICSEmailRequest(EmailRequestDTO emailRequestDTO, string sessionId)
        {
            try
            {
                // Any email orchestration can happen here (scheduling, debounce, cancellation, etc)
                ServiceBusClient serviceBusClient = new(_serviceBusSettings.ConnectionString);

                ServiceBusSender sender = serviceBusClient.CreateSender("mail_ics");

                // The usage of Newtonsoft is intentional here as it serializes the payloadd (a derived class) out of the box
                // If for some reason you plan to change the serializer, make sure the payload is correctly serialized according to the chosen serializer
                ServiceBusMessage message = new(Newtonsoft.Json.JsonConvert.SerializeObject(emailRequestDTO));
                message.SessionId = sessionId;

                await sender.SendMessageAsync(message);
            }
            catch (Exception ex)
            {
                // Log the error but the client decides what to do with it
                _logger.LogError($"ERROR_SENDING_ICS_EMAIL_REQUEST", ex);
                return false;
            }

            return true;
        }
    }
}
