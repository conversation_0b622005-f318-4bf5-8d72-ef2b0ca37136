using Atlas.Business.Helpers;
using Atlas.Business.Interfaces;
using Atlas.Business.ValidationStrategies;
using Atlas.CrossCutting.AppEnums;
using Atlas.CrossCutting.DTO.ExternalDocumentRequest;
using Atlas.CrossCutting.Services;
using Atlas.CrossCutting.Settings;
using Atlas.Data.Entities;
using Atlas.Data.Repository;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace Atlas.Business
{
    public class ExternalDocumentRequestService
    {
        readonly private int _currentUserId;
        readonly private ExternalDocumentRequestRepository _externalDocumentRequestRepository;
        readonly private ContentActivityService _contentActivityService;
        readonly private ExternalUserService _externalUserService;
        private readonly IValidationStrategy<ExternalDocumentRequestDTO> _externalDocumentRequestValidationStrategy;
        private readonly IRequestContextService _requestContext;
        private readonly StorageSettings _storageSettings;
        private readonly IConfiguration _configuration;

        public ExternalDocumentRequestService(IConfiguration configuration)
        {
            _configuration = configuration;
            _externalDocumentRequestRepository = new ExternalDocumentRequestRepository();
        }

        public ExternalDocumentRequestService(IConfiguration configuration, int currentUser)
        {
            _currentUserId = currentUser;
            _configuration = configuration;
            _externalDocumentRequestRepository = new ExternalDocumentRequestRepository(_currentUserId);
            _contentActivityService = new ContentActivityService(_currentUserId);
            _externalUserService = new ExternalUserService(_currentUserId);
            _externalDocumentRequestValidationStrategy = new ExternalDocumentRequestValidationStrategy(_externalDocumentRequestRepository);
        }

        public ExternalDocumentRequestService(IConfiguration configuration, int currentUser, IRequestContextService requestContext, StorageSettings storageSettings)
        {
            _currentUserId = currentUser;
            _requestContext = requestContext;
            _configuration = configuration;
            _externalDocumentRequestRepository = new ExternalDocumentRequestRepository(_currentUserId);
            _contentActivityService = new ContentActivityService(_currentUserId);
            _externalUserService = new ExternalUserService(_currentUserId, requestContext);
            _externalDocumentRequestValidationStrategy = new ExternalDocumentRequestValidationStrategy(_externalDocumentRequestRepository);
            _storageSettings = storageSettings;
        }

        public ExternalDocumentRequestService(IConfiguration configuration, IRequestContextService requestContext, StorageSettings storageSettings)
        {
            _configuration = configuration;
            _requestContext = requestContext;
            _externalDocumentRequestRepository = new ExternalDocumentRequestRepository();
            _externalUserService = new ExternalUserService(configuration);
            _externalDocumentRequestValidationStrategy = new ExternalDocumentRequestValidationStrategy(_externalDocumentRequestRepository);
            _storageSettings = storageSettings;
        }

        private async System.Threading.Tasks.Task CheckContentPermission(int contentId)
        {
            if (!await _externalDocumentRequestRepository.HasAgendaOwnership(contentId))
            {
                throw new UnauthorizedAccessException("INVALID_GRANT");
            }
            ;
        }

        public async Task<int?> SendExternalDocumentRequest(Guid contentUuId, int contentId, ExternalDocumentRequestDTO externalDocumentRequest)
        {
            await CheckContentPermission(contentId);
            await _externalDocumentRequestValidationStrategy.ValidateAsync(externalDocumentRequest);

            var externalUsers = ((ExternalDocumentRequestValidationStrategy)_externalDocumentRequestValidationStrategy).GetExternalUsers();

            using var transaction = await _externalDocumentRequestRepository.BeginTransactionAsync();

            try
            {
                var newExternalDocumentRequest = new ExternalDocumentRequest
                {
                    contentUuid = contentUuId,
                    ContentId = contentId,
                    RequesterUserId = _currentUserId,
                    RequestDate = DateTime.UtcNow,
                    Status = "PENDING",
                    RequesterInstructions = externalDocumentRequest.requesterInstructions
                };

                var documentRequest = await _externalDocumentRequestRepository.CreateExternalDocumentRequest(newExternalDocumentRequest);

                if (documentRequest == null)
                {
                    throw new InvalidOperationException("Failed to create external document request");
                }

                var updateExternalUsersSuccess = await _externalDocumentRequestRepository.UpdateExternalUsers(externalUsers.ToList(), documentRequest.ExternalDocumentRequestId);

                if (!updateExternalUsersSuccess)
                {
                    throw new InvalidOperationException("Failed to update external users");
                }

                var requesterUser = await _externalDocumentRequestRepository.GetRequesterUser(documentRequest.RequesterUserId);
                var agendaContent = await _externalDocumentRequestRepository.GetAgendaContent(contentId);

                _contentActivityService.Add(documentRequest.ContentId, documentRequest.contentUuid, new ContentActivity()
                {
                    contentUuid = documentRequest.contentUuid,
                    contentId = documentRequest.ContentId,
                    date = DateTime.UtcNow,
                    activityUser = _currentUserId,
                    type = Operations.EXTERNAL_DOCUMENT_REQUEST,
                    subItemId = documentRequest.ExternalDocumentRequestId,
                    contentData = JsonConvert.SerializeObject(new
                    {
                        externalUsers,
                        documentRequest.ExternalDocumentRequestId,
                        requesterUser,
                        agendaContent,
                        documentRequest.RequesterInstructions
                    })
                }, _requestContext.UserAgent);

                await _externalDocumentRequestRepository.CommitAsync();
                return documentRequest.ExternalDocumentRequestId;
            }
            catch
            {
                await _externalDocumentRequestRepository.RollbackAsync();
                throw;
            }
        }

        public async Task<bool> DeleteExternalDocumentRequest(int externalUserId)
        {
            bool success;

            using var atlasModel = new AtlasModelCore();

            var externalDocumentRequest = await atlasModel.ExternalDocumentRequest
                .Where(edr =>
                    edr.ExternalUsers.Any(eu => eu.ExternalUserId == externalUserId)
                )
                .Include(edr => edr.ExternalAttachments)
            .FirstOrDefaultAsync();

            if (externalDocumentRequest is null)
                throw new InvalidOperationException("EXTERNAL_DOCUMENT_REQUEST_NOT_FOUND");

            await CheckContentPermission(externalDocumentRequest.ContentId);

            success = await _externalUserService.UpdateExternalUser(externalUserId, externalDocumentRequest.ContentId);

            if (!success)
            {
                throw new InvalidOperationException("UNABLE_TO_UPDATE_LINKED_EXTERNAL_USER");
            }

            var remainingExtUsers = await _externalDocumentRequestRepository.GetExternalUsersByDocumentRequest(externalDocumentRequest.ExternalDocumentRequestId);

            if (!remainingExtUsers.Any())
            {
                success = await _externalDocumentRequestRepository.DeleteExternalDocumentRequest(externalDocumentRequest);

                if (success)
                {
                    await UpdateAuditTrail(externalDocumentRequest.contentUuid, externalDocumentRequest.ContentId, Operations.EXTERNAL_DOCUMENT_REQUEST_DELETED, externalDocumentRequest);
                }
            }

            return success;
        }

        public async Task<bool> DeleteAttachmentByExternalUser(int externalDocumentRequestId, Guid contentUuId, int contentId, int contentAttachmentId, string externalUserHash, int externalUserId)
        {
            var externalDocumentRequest = await ValidateUpload(new ExternalUserService(_configuration), externalDocumentRequestId, externalUserId, externalUserHash);

            ContentAttachmentService svc = new ContentAttachmentService(externalDocumentRequest.RequesterUserId, contentUuId);
            var successOnDelete = await svc.ToggleDelete(contentAttachmentId, deletedByExternalUser: true, string.Empty);

            if (!successOnDelete) return false;

            var externalAttachment = await _externalDocumentRequestRepository.GetExternalAttachment(externalDocumentRequestId, externalUserId, contentAttachmentId);

            successOnDelete = await _externalDocumentRequestRepository.DeleteAttachmentByExternalUser(externalAttachment);
            return successOnDelete;
        }

        public async Task<ExternalDocumentRequestDTO> GetExternalDocumentRequest(int externalDocumentRequestId)
        {
            var externalDocumentRequest = await _externalDocumentRequestRepository.GetExternalDocumentRequest(externalDocumentRequestId)
                ?? throw new InvalidOperationException("EXTERNAL_DOCUMENT_REQUEST_NOT_FOUND");

            await CheckContentPermission(externalDocumentRequest.ContentId);

            var externalUsers = await _externalDocumentRequestRepository.GetExternalUsersByDocumentRequest(externalDocumentRequestId);

            return new ExternalDocumentRequestDTO
            {
                externalDocumentRequestId = externalDocumentRequest.ExternalDocumentRequestId,
                contentId = externalDocumentRequest.ContentId,
                contentUuId = externalDocumentRequest.contentUuid,
                ExternalUsers = externalUsers.Select(e => new ExternalUserDTO()
                {
                    ExternalUserId = e.ExternalUserId,
                    ExternalName = e.ExternalName,
                    ExternalMail = e.ExternalMail,
                    ExternalMobile = e.ExternalMobile,
                    ExternalDocumentRequestId = e.ExternalDocumentRequestId,
                    IsRecurring = e.IsRecurring,
                    SentDate = e.SentDate,
                    WorkgroupId = e.ExternalDocumentRequest.Content.workgroupId
                }).ToList(),
                requesterInstructions = externalDocumentRequest.RequesterInstructions
            };
        }

        public async Task<ExternalDocumentRequestUploadResultDTO> GetExternalDocumentForUpload(int externalDocumentRequestId, Guid contentUuId, int contentId, string externalUserHash, int externalUserId)
        {
            var externalDocumentRequest = await ValidateUpload(new ExternalUserService(_configuration), externalDocumentRequestId, externalUserId, externalUserHash);

            var externalAttachments = await _externalDocumentRequestRepository
                .GetExternalAttachmentsUploadResult(externalDocumentRequestId, externalUserId);

            var requesterUserData = await _externalDocumentRequestRepository.GetRequesterUser(externalDocumentRequest.RequesterUserId);

            return new ExternalDocumentRequestUploadResultDTO()
            {
                contentUuId = externalDocumentRequest.contentUuid,
                externalDocumentRequestId = externalDocumentRequest.ExternalDocumentRequestId,
                contentId = externalDocumentRequest.ContentId,
                externalUserId = externalUserId,
                requesterInstructions = externalDocumentRequest.RequesterInstructions,
                requesterUserName = requesterUserData.Name,
                ExternalAttachments = externalAttachments,
                meetingAgendaTitle = externalDocumentRequest.Content.title
            };
        }

        public async Task<bool> ResendExternalDocumentRequest(int externalUserId)
        {

            var documentRequest = await _externalDocumentRequestRepository.GetExternalDocumentRequestByUser(externalUserId)
                ?? throw new InvalidOperationException("EXTERNAL_DOCUMENT_REQUEST_NOT_FOUND");

            await CheckContentPermission(documentRequest.ContentId);

            if (documentRequest.Status == "CLOSED")
            {
                throw new InvalidOperationException("DOCUMENT_REQUEST_CLOSED");
            }

            var parentMeeting = await _externalDocumentRequestRepository.GetParentMeeting(documentRequest.ContentId) ?? throw new InvalidOperationException("MEETING_NOT_FOUND");

            if (parentMeeting.status == "CLOSED")
            {
                throw new InvalidOperationException("MEETING_CLOSED");
            }

            var externalUser = await _externalDocumentRequestRepository.GetExternalUser(externalUserId);

            await UpdateAuditTrail(documentRequest.contentUuid, documentRequest.ContentId, Operations.EXTERNAL_DOCUMENT_REQUEST_RESENT, documentRequest, externalUser);

            return true;
        }

        private async System.Threading.Tasks.Task UpdateAuditTrail(Guid contentUuId, int contentId, string activityType, ExternalDocumentRequest externalDocumentRequest, ExternalUser externalUser = null)
        {

            RequesterUserDTO requesterUser = null;
            AgendaContentDTO agendaContent = null;

            if (activityType == Operations.EXTERNAL_DOCUMENT_REQUEST_RESENT)
            {
                requesterUser = await _externalDocumentRequestRepository.GetRequesterUser(externalDocumentRequest.RequesterUserId);
                agendaContent = await _externalDocumentRequestRepository.GetAgendaContent(contentId);
            }


            _contentActivityService.Add(contentId, contentUuId, new ContentActivity()
            {
                date = DateTime.UtcNow,
                type = activityType,
                activityUser = _currentUserId,
                subItemType = SubItems.EXTERNAL_DOCUMENT_REQUEST,
                subItemId = externalDocumentRequest.ExternalDocumentRequestId,
                contentData = activityType == Operations.EXTERNAL_DOCUMENT_REQUEST_RESENT && externalUser != null ?
                                                JsonConvert.SerializeObject(new
                                                {
                                                    externalUser,
                                                    externalDocumentRequest.ExternalDocumentRequestId,
                                                    externalDocumentRequest.RequesterInstructions,
                                                    requesterUser,
                                                    agendaContent
                                                })
                                                :
                                                JsonConvert.SerializeObject(externalUser)
            }, _requestContext.UserAgent);
        }

        public async Task<UploadResult> UploadExternalDocument
            (MultipartFileStreamProvider provider, string originalPath, int externalDocumentRequestId, int contentId, Guid contentUuId, string externalUserHash, int externalUserId)
        {
            var externalDocumentRequest = await ValidateUpload(new ExternalUserService(_configuration), externalDocumentRequestId, externalUserId, externalUserHash);
            await _externalDocumentRequestRepository.SetStatus(externalDocumentRequestId, "UPLOADING");

            ContentAttachmentService _service = new(externalDocumentRequest.RequesterUserId, contentUuId, _storageSettings);
            UploadResult result = await _service.AddFileOrChunks(contentUuId, provider, originalPath, uploadByExternalUser: true);

            if (result.status == System.Net.HttpStatusCode.OK && result.contentAttachmentId != 0)
            {
                await _externalDocumentRequestRepository.SaveExternalAttachment(new ExternalAttachment()
                {
                    contentUuid = contentUuId,
                    ContentId = contentId,
                    ContentAttachmentId = result.contentAttachmentId,
                    ExternalDocumentRequestId = externalDocumentRequestId,
                    ExternalUserId = externalUserId
                });
            }

            return result;
        }

        public async Task<bool> FinishExternalDocumentUpload(int externalDocumentRequestId, Guid contentUuId, int contentId, string externalUserHash, int externalUserId)
        {
            var externalDocumentRequest = await ValidateUpload(new ExternalUserService(_configuration), externalDocumentRequestId, externalUserId, externalUserHash);

            var repo = new ExternalDocumentRequestRepository(externalDocumentRequest.RequesterUserId);

            if (!await repo.AllUploadHasBeenCompleted(externalUserId, externalDocumentRequestId))
                throw new InvalidOperationException("UPLOAD_NOT_COMPLETED");

            var externalUser = externalDocumentRequest.ExternalUsers.First(ext => ext.ExternalUserId == externalUserId);
            var successOnUpdateExternalUser = await repo.UpdateSendDateForExternalUser(externalUser, DateTime.UtcNow);

            if (!successOnUpdateExternalUser) return false;

            RequesterUserDTO requesterUser = await repo.GetRequesterUser(externalDocumentRequest.RequesterUserId);
            AgendaContentDTO agendaContent = await repo.GetAgendaContentByContentUuid(contentUuId);
            string[] attachmentsNames = await repo.GetAttachmentsNames(externalUserId, externalDocumentRequestId);

            var contentActivityService = new ContentActivityService(externalDocumentRequest.RequesterUserId);

            string contentData = JsonConvert.SerializeObject(new
            {
                externalUsers = new ExternalUser[] { externalUser },
                externalDocumentRequestId = externalDocumentRequest.ExternalDocumentRequestId,
                requesterUser,
                agendaContent,
                requesterInstructions = externalDocumentRequest.RequesterInstructions,
                attachmentsNames
            });


            SaveContentActivity(contentActivityService, contentUuId, contentId, externalDocumentRequest.RequesterUserId,
                externalDocumentRequestId, externalUserId, Operations.EXTERNAL_DOCUMENT_REQUEST_SENT_OWNER, contentData);

            if (await repo.FinishExternalDocumentUpload(externalDocumentRequest))
            {
                SaveContentActivity(contentActivityService, contentUuId, contentId, externalDocumentRequest.RequesterUserId,
                    externalDocumentRequestId, externalUserId, Operations.EXTERNAL_DOCUMENT_REQUEST_COMPLETED, contentData);
            }

            return successOnUpdateExternalUser;
        }

        private void SaveContentActivity(ContentActivityService contentActivityService,
            Guid contentUuId,
            int contentId, int requesterUserId, int externalDocumentRequestId,
            int externalUserId, string operation, string contentData)
        {
            contentActivityService.Add(contentId, contentUuId, new ContentActivity()
            {
                date = DateTime.UtcNow,
                type = operation,
                activityUser = requesterUserId,
                subItemId = externalDocumentRequestId,
                contentData = contentData
            }, _requestContext.UserAgent);
        }

        private async Task<ExternalDocumentRequest> ValidateUpload
            (ExternalUserService externalUserService, int externalDocumentRequestId, int externalUserId, string externalUserHash)
        {
            var externalDocumentRequest = await _externalDocumentRequestRepository
                .GetExternalDocumentRequestAndContent(externalDocumentRequestId)
                ?? throw new InvalidOperationException("EXTERNAL_DOCUMENT_REQUEST_NOT_FOUND");

            string[] invalidStatuses = new string[] { "EXPIRED", "COMPLETED" };

            if (invalidStatuses.Contains(externalDocumentRequest.Status))
                throw new InvalidOperationException("INVALID_DOCUMENT");

            ExternalUser[] externalUsers = await _externalDocumentRequestRepository.GetExternalUsersByDocumentRequest(externalDocumentRequestId);
            externalDocumentRequest.ExternalUsers = externalUsers;

            var externalUser = externalUsers.FirstOrDefault(external => external.ExternalUserId == externalUserId) ??
                throw new UnauthorizedAccessException("INVALID_GRANT");

            if (!externalUserService.VerifyExternalKey(externalUser.ExternalKey.ToString(), externalUserHash))
                throw new UnauthorizedAccessException("INVALID_GRANT");

            if (!await CheckExternalPermissionsForWorkgroup(externalDocumentRequest))
                throw new UnauthorizedAccessException("INVALID_GRANT");

            return externalDocumentRequest;
        }

        public async Task<bool> CheckExternalPermissionsForWorkgroup(ExternalDocumentRequest externalDocumentRequest)
        {
            return await _externalDocumentRequestRepository.CheckExternalPermissionsForWorkgroup(externalDocumentRequest);
        }
    }
}
