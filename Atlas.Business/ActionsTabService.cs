using Atlas.Business.Helpers;
using Atlas.CrossCutting.DTO.ActionsTab;
using Atlas.Data.Repository;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Atlas.Business
{
    public class ActionsTabService
    {
        readonly private int _currentUser = 0;
        readonly private ActionsTabRepository _actionsTabRepository;

        public ActionsTabService(int currentUser)
        {
            _currentUser = currentUser;
            _actionsTabRepository = new ActionsTabRepository(_currentUser);
        }

        public async Task<ActionsTabDTO> GetActions(ActionsTabFilters filters)
        {
            filters.Page = filters.Page <= 0 ? 1 : filters.Page;
            filters.ItemsPerPage = filters.ItemsPerPage <= 0 ? 10 : filters.ItemsPerPage;

            var actionsList = await _actionsTabRepository.GetActions
                (filters.Page, filters.ItemsPerPage, filters.Status, filters.AssignedUsers, filters.WorkgroupIdList, filters.StartDate, filters.EndDate);

            var actionsTabDTO = new ActionsTabDTO
            {
                TotalOfItems = await _actionsTabRepository.GetTotalItems(filters.Status, filters.AssignedUsers, filters.WorkgroupIdList, filters.StartDate, filters.EndDate),
                ActionsTab = actionsList
            };

            return actionsTabDTO;
        }

        public async Task<IEnumerable<ActionsTabCountersDTO>> GetCounters(ActionsTabFilters filters)
        {
            var counters = await _actionsTabRepository.GetCounters(filters.Status, filters.AssignedUsers, filters.WorkgroupIdList, filters.StartDate, filters.EndDate);
            return counters;
        }

        public async Task<IEnumerable<WorkgroupUsers>> GetPossibleAssignedUsers(ActionsTabFilters filters)
        {
            var possible_assigned_users = await _actionsTabRepository.GetPossibleAssignedUsers(filters.WorkgroupIdList);
            return possible_assigned_users;
        }
    }
}
