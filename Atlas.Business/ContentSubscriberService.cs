using Atlas.Business.ViewModels;
using Atlas.CrossCutting.AppEnums;
using Atlas.CrossCutting.DTO.ContentSubscriber;
using Atlas.Data.Entities;
using Atlas.Data.Repository;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security;
using System.Threading.Tasks;
using static Atlas.CrossCutting.AppEnums.ContentStatuses;

namespace Atlas.Business
{
    public class ContentSubscriberService
    {
        //ContentCommentRepository _repo;
        ContentRepository _repo;
        int _currentUser;
        int content_id;
        Guid _contentUuid;
        string _userAgent;
        private AtlasModelCore _md;
        private IDbContextTransaction _transaction;

        public ContentSubscriberService(int currentUserId, int _content_id)
        {
            //_repo = new ContentCommentRepository(userId, content_id);
            _repo = new ContentRepository(currentUserId);

            _currentUser = currentUserId;
            content_id = _content_id;
        }

        public void SetContentUuId(Guid contentUuid)
        {
            _contentUuid = contentUuid;
        }

        public void SetUserAgent(string userAgent)
        {
            _userAgent = userAgent;
        }

        public ContentSubscriberService(int currentUserId, Guid contentUuid)
        {
            //_repo = new ContentCommentRepository(userId, content_id);
            _repo = new ContentRepository(currentUserId);

            _currentUser = currentUserId;
            _contentUuid = contentUuid;
        }

        /// <summary>
        /// USE WITH CAUTION!!
        /// Initially required by the recurring meeting service (update master).
        /// </summary>
        /// <param name="currentUserId"></param>
        public ContentSubscriberService(int currentUserId)
        {
            _repo = new ContentRepository(currentUserId);
            _currentUser = currentUserId;
        }

        public ContentSubscriberService(int userId, int _content_id, AtlasModelCore _md, IDbContextTransaction _transaction, string userAgent = null)
        {
            this._md = _md;
            this._transaction = _transaction;
            _repo = new ContentRepository(userId, _md, _transaction);

            _currentUser = userId;
            content_id = _content_id;
            _userAgent = userAgent;
        }

        public async Task<int> RemoveRangeAsync(Guid contentUuid, List<ContentSubscriber> remove_users)
        {
            using var atlasModel = new AtlasModelCore();

            var subscribersUserIds = await atlasModel.ContentSubscriber
                .Where(
                    cs => cs.contentUuid == contentUuid
                    && cs.Content.deleted != true
                )
                .Select(cs => cs.userId).ToArrayAsync();

            foreach (var item in remove_users)
            {
                if (subscribersUserIds.Contains(item.userId))
                {
                    var x = await this.DeleteAsyncUsingUuId(item.userId, _userAgent);
                }
            }

            return 1;
        }

        public async Task<object> ResendMeetingInvite(int contentId, List<ContentSubscriber> subscribers)
        {
            ContentService contentService = new ContentService(_currentUser);
            var content = await contentService.Get(contentId);

            // Validations
            if (content == null || content.type != ContentTypes.Meeting)
                throw new InvalidOperationException("MEETING_NOT_FOUND");

            if (!content.UAC.resendInvite)
                throw new SecurityException("INVALID_USER");

            var meeting = content.Meeting.FirstOrDefault();
            bool sendIcs = content.recurringMeetingId.HasValue ?
                content.RecurringMeeting.sendIcs ?? true :
                meeting.sendICS ?? true;

            if (!sendIcs)
            {
                throw new InvalidOperationException("INVITE_DISABLED");
            }

            var resendList = subscribers
                .Where(s => content.ContentSubscriber
                    .Select(cs => cs.userId)
                    .Contains(s.userId))
                .ToList();

            if (resendList.Count == 0)
                throw new InvalidOperationException("INVALID_LIST_LENGTH");

            if (content.status != StatusMeeting.Open && content.status != StatusMeeting.Ready)
                throw new InvalidOperationException("INVALID_MEETING_STATUS");

            if (content.recurringMeetingId.HasValue)
            {
                await RecurringMeetingService.IncrementIcsSequence(content.RecurringMeeting);
            }

            new ContentActivityService(_currentUser).Add(contentId, new ContentActivity()
            {
                contentId = contentId,
                date = DateTime.UtcNow,
                processed = false,
                type = Operations.MEETING_RESEND_INVITE,
                subItemType = content.status,
                activityUser = _currentUser,
                contentData = JsonConvert.SerializeObject(new
                {
                    users = resendList.Select(u => u.userId)
                })
            });

            return true;
        }
        public async Task<bool> ResendMeetingInvite(List<ContentSubscriberDTO> subscribers)
        {
            ContentService contentService = new ContentService(_currentUser);
            var content = await contentService.Get(_contentUuid);

            // Validations
            if (content == null || content.type != ContentTypes.Meeting)
                throw new InvalidOperationException("MEETING_NOT_FOUND");

            if (!content.UAC.resendInvite)
                throw new SecurityException("INVALID_USER");

            var meeting = content.Meeting.FirstOrDefault();
            bool sendIcs = content.recurringMeetingId.HasValue ?
                content.RecurringMeeting.sendIcs ?? true :
                meeting.sendICS ?? true;

            if (!sendIcs)
            {
                throw new InvalidOperationException("INVITE_DISABLED");
            }

            var resendList = subscribers
                .Where(s => content.ContentSubscriber
                    .Select(cs => cs.userId)
                    .Contains(s.userId))
                .ToList();

            if (resendList.Count == 0)
                throw new InvalidOperationException("INVALID_LIST_LENGTH");

            if (content.status != StatusMeeting.Open && content.status != StatusMeeting.Ready)
                throw new InvalidOperationException("INVALID_MEETING_STATUS");

            if (content.recurringMeetingId.HasValue)
            {
                await RecurringMeetingService.IncrementIcsSequence(content.RecurringMeeting);
            }

            new ContentActivityService(_currentUser).Add(content.contentId, _contentUuid, new ContentActivity()
            {
                contentId = content.contentId,
                date = DateTime.UtcNow,
                processed = false,
                type = Operations.MEETING_RESEND_INVITE,
                subItemType = content.status,
                activityUser = _currentUser,
                contentData = JsonConvert.SerializeObject(new
                {
                    users = resendList.Select(u => u.userId)
                })
            }, _userAgent);

            return true;
        }

        public async Task<bool> DeleteAsync(int userId)
        {
            // Neste caso não podemos validar a Role já que não sabemos a implicação de adicionar 
            // a operação "SUBSCRIBER_DELETE" para a role "DEFAULT"

            ////check permissions
            //if (!_repo.CheckPermissionsForWorkgroup(Operations.SUBSCRIBER_DELETE, content_id))
            //{
            //    //todo: logar tentativa de acesso
            //    throw new SecurityException("Unauthorized attempt to delete content in workgroup.");
            //}
            if (_repo.DeleteSubscriberByUserIdAndContentId(content_id, userId) > 0)
            {
                var addedids = new int[] { };
                var removedids = new int[] { userId };

                new ContentActivityService(_currentUser).Add(content_id, new ContentActivity()
                {
                    contentId = content_id,
                    date = DateTime.UtcNow,
                    processed = false,
                    type = Operations.SUBSCRIBER_UPDATE,
                    activityUser = _currentUser,
                    contentData = JsonConvert.SerializeObject(new { type = SubItems.SUBSCRIBER, ids_added = addedids, ids_removed = removedids })

                });

                return true;
            }
            else
            {
                return false;
            }
        }

        public async Task<bool> DeleteAsyncUsingUuId(int userId, string userAgent)
        {
            if (await _repo.DeleteSubscriberByUserIdAndContentUuId(_contentUuid, userId))
            {
                var addedids = new int[] { };
                var removedids = new int[] { userId };


                new ContentActivityService(_currentUser).Add(content_id, _contentUuid, new ContentActivity()
                {
                    contentId = content_id,
                    date = DateTime.UtcNow,
                    processed = false,
                    type = Operations.SUBSCRIBER_UPDATE,
                    activityUser = _currentUser,
                    contentData = JsonConvert.SerializeObject(new { type = SubItems.SUBSCRIBER, ids_added = addedids, ids_removed = removedids })

                }, userAgent);

                return true;
            }
            else
            {
                return false;
            }
        }

        public bool Delete(int subscriber_id)
        {
            //check permissions
            if (!_repo.CheckPermissionsForWorkgroup(Operations.SUBSCRIBER_DELETE, content_id))
            {
                //todo: logar tentativa de acesso
                throw new SecurityException("Unauthorized attempt to delete content in workgroup.");
            }

            if (_repo.Delete_Subscriber(content_id, subscriber_id) > 0)
            {

                new ContentActivityService(_currentUser).Add(content_id, new ContentActivity()
                {
                    contentId = content_id,
                    date = DateTime.UtcNow,
                    processed = false,
                    type = Operations.SUBSCRIBER_DELETE,
                    activityUser = _currentUser,
                    contentData = JsonConvert.SerializeObject(new { type = SubItems.SUBSCRIBER, id = subscriber_id }),
                    subItemId = subscriber_id,
                    subItemType = SubItems.SUBSCRIBER,

                });

                return true;
            }
            else
            {
                return false;
            }
        }

        [Obsolete("Use the new Update method instead. This method is kept for backward compatibility and will be removed in future versions.")]
        public async Task<int> Update(int content_id, List<SubscriberListViewModel> list, bool? recurrenceShouldNotify = null)
        {

            //1. user sends the whole list
            //2. validate permissions to current user update
            //3. loop through the list to check if
            //  3.1. Check if user is in content's workgroup
            //  3.2. Check if user can be unsubscribed (if created, its assigned or its reviewer of the content)
            var users_add = new List<ContentSubscriber>();
            var users_remove = new List<ContentSubscriber>();

            //check permissions
            if (!_repo.CheckPermissionsForWorkgroup(Operations.SUBSCRIBER_UPDATE, content_id))
            {
                //todo: logar tentativa de acesso
                throw new SecurityException("Unauthorized attempt to add comment into a content in workgroup.");
            }
            Content _currentContent = await _repo.Get(content_id);

            if (_currentContent.type == ContentTypes.Task && _currentContent.parent_status == ContentMeetingStatus.CANCELLED)
            {
                throw new SecurityException("STATUS_INVALID");
            }

            List<User> _wkg_users_allowed = await new WorkgroupService(_currentUser).GetUsers(_currentContent.workgroupId);
            var _currentSubscribers = _currentContent.ContentSubscriber.ToList();
            var _currentPermissions = _currentContent.ContentPermission.ToList();

            if (_currentPermissions.Count() == 0)
            {
                _currentPermissions = _repo.List_Permissions(_currentContent.contentId);
            }

            //if poll, and awaiting votes, prevent subscribers to be removed
            //if (_currentContent.type == ContentTypes.Poll && _currentContent.status == "AWAITING_VOTES")
            //{
            //    throw new SecurityException("STATUS_INVALID");
            //}

            if (_currentContent.type == ContentTypes.Poll)
            {

                if (_currentContent.status != "OPEN" || _currentContent.status != "AWAITING_VOTES")
                {

                    // S-48 Poll Modal, Edit button is no longer needed, subscribers can be added/removed individually via VoterManager in the frontend
                    // The rule remains the same though: If there are already any votes, even if it is deleted, there will be no possiblity of managing subscribers

                    Poll poll = _currentContent.Poll.FirstOrDefault();

                    if (poll.Votes.Any())
                    {
                        throw new SecurityException("STATUS_INVALID");
                    }


                }

            }
            //if meeting, and closed, prevent subscribers to be changed
            if (_currentContent.type == ContentTypes.Meeting && MeetingRestrictedParticipantsStatus.Contains(_currentContent.status))
            {
                throw new SecurityException("STATUS_INVALID");
            }

            //if form is closed prevent subscribers to be changed
            if (_currentContent.type == ContentTypes.Form && _currentContent.status == "CLOSED")
            {
                throw new SecurityException("STATUS_INVALID");
            }

            foreach (var item in list)
            {
                //check if user is allowed in the content's workgroup
                if (!_wkg_users_allowed.Select(o => o.userId).Contains(item.ContentSubscriber.userId) && item.Checked)
                {
                    // if not allowed in the workgroup, skip it
                    continue;
                }

                // determine if the item should be added or removed of the current subscribers list
                if (_currentSubscribers.Select(o => o.userId).Contains(item.ContentSubscriber.userId))
                {
                    if (!item.Checked) //if exists and its UNCHECKED.... remove it
                    {
                        users_remove.Add(item.ContentSubscriber);
                    }
                }
                else
                {
                    if (item.Checked) //if not exists and its CHECKED... Add it
                    {
                        users_add.Add(item.ContentSubscriber);

                        if (!_currentPermissions.Select(o => o.userId).Contains(item.ContentSubscriber.userId))
                        {
                            _repo.Add_Permission(_currentContent.contentId, new ContentPermission()
                            {
                                contentId = _currentContent.contentId,
                                userId = item.ContentSubscriber.userId,
                                createDate = DateTime.UtcNow,
                                createUser = _currentUser,
                                allowed = true
                            });
                        }
                    }
                }

            }

            if (_repo.Set_Subscribers(content_id, users_add, users_remove) > 0)
            {
                if (_currentContent.type == "Pipeline" || _currentContent.type == "Meeting")
                {

                    //esse caso é pra reuniões passadas apenas,(conselheiro adicionado no board depois)
                    var users_to_add_permission = users_add.Select(o => o.userId).Where(o => !_currentPermissions.Select(a => a.userId).Contains(o)).ToList();
                    _repo.UpdateChildPermissions(_currentContent, users_to_add_permission, new List<int>());
                }

                if (_currentContent.type == ContentTypes.Form)
                {
                    var formOwnerList = _currentContent.ContentOwner;
                    foreach (var removeUserId in users_remove.Select(user => user.userId))
                    {
                        if (_currentPermissions.Select(o => o.userId).Contains(removeUserId) &&
                            !formOwnerList.Any(o => o.userId == removeUserId))
                        {
                            _repo.Delete_Permission(content_id, removeUserId);
                        }
                    }
                }

                var contentActivityService = new ContentActivityService(_currentUser);
                if (recurrenceShouldNotify.HasValue)
                {
                    contentActivityService.Add(content_id, new ContentActivity()
                    {
                        contentId = content_id,
                        type = Operations.RECURRING_MEETING_SUBSCRIBER_UPDATE,
                        date = DateTime.UtcNow,
                        activityUser = _currentUser,
                        processed = !recurrenceShouldNotify.Value,
                        contentData = JsonConvert.SerializeObject(new { type = SubItems.SUBSCRIBER, ids_added = new int[] { }, ids_removed = users_remove.Select(o => o.userId) })
                    });
                }
                else
                {
                    contentActivityService.Add(content_id, new ContentActivity()
                    {
                        contentId = content_id,
                        type = Operations.SUBSCRIBER_UPDATE,
                        date = DateTime.UtcNow,
                        activityUser = _currentUser,
                        processed = false,
                        contentData = JsonConvert.SerializeObject(new { type = SubItems.SUBSCRIBER, ids_added = users_add.Select(o => o.userId), ids_removed = users_remove.Select(o => o.userId) })
                    });
                }

                return 1;
            }
            else
            {
                return 0;
            }
        }

        public async Task<bool> Update(List<ContentSubscriberDTO> list, bool? recurrenceShouldNotify = null)
        {
            //1. user sends the whole list
            //2. validate permissions to current user update
            //3. loop through the list to check if
            //  3.1. Check if user is in content's workgroup
            //  3.2. Check if user can be unsubscribed (if created, its assigned or its reviewer of the content)
            var users_add = new List<ContentSubscriber>();
            var users_remove = new List<ContentSubscriber>();

            //check permissions
            if (!(await _repo.CheckPermissionsForWorkgroup(Operations.SUBSCRIBER_UPDATE, _contentUuid)))
            {
                //todo: logar tentativa de acesso
                throw new SecurityException("Unauthorized attempt to add comment into a content in workgroup.");
            }
            Content _currentContent = await _repo.Get(_contentUuid);

            if (_currentContent.status.ToLower() == "closed")
            {
                throw new InvalidOperationException("Cannot update subscribers list, when task is already closed");
            }

            foreach (var subscriber in list)
            {
                subscriber.contentUuid = _contentUuid;
                subscriber.contentId = _currentContent.contentId;
            }

            if (_currentContent.type == ContentTypes.Task && _currentContent.parent_status == ContentMeetingStatus.CANCELLED)
            {
                throw new SecurityException("STATUS_INVALID");
            }

            List<User> _wkg_users_allowed = await new WorkgroupService(_currentUser).GetUsers(_currentContent.workgroupId);
            var _currentSubscribers = _currentContent.ContentSubscriber.ToList();
            var _currentPermissions = _currentContent.ContentPermission.ToList();

            if (_currentPermissions.Count() == 0)
            {
                _currentPermissions = _repo.List_Permissions(_currentContent.contentId);
            }

            if (_currentContent.type == ContentTypes.Poll)
            {
                if (_currentContent.status != "OPEN" || _currentContent.status != "AWAITING_VOTES")
                {
                    // S-48 Poll Modal, Edit button is no longer needed, subscribers can be added/removed individually via VoterManager in the frontend
                    // The rule remains the same though: If there are already any votes, even if it is deleted, there will be no possiblity of managing subscribers

                    Poll poll = _currentContent.Poll.FirstOrDefault();

                    if (poll.Votes.Any())
                    {
                        throw new SecurityException("STATUS_INVALID");
                    }
                }
            }

            //if meeting, and closed, prevent subscribers to be changed
            if (_currentContent.type == ContentTypes.Meeting && MeetingRestrictedParticipantsStatus.Contains(_currentContent.status))
            {
                throw new SecurityException("STATUS_INVALID");
            }

            //if form is closed prevent subscribers to be changed
            if (_currentContent.type == ContentTypes.Form && _currentContent.status == "CLOSED")
            {
                throw new SecurityException("STATUS_INVALID");
            }

            foreach (var item in list)
            {
                //check if user is allowed in the content's workgroup
                if (!_wkg_users_allowed.Select(o => o.userId).Contains(item.userId) && item.isChecked)
                {
                    // if not allowed in the workgroup, skip it
                    continue;
                }

                // determine if the item should be added or removed of the current subscribers list
                if (_currentSubscribers.Select(o => o.userId).Contains(item.userId))
                {
                    if (!item.isChecked) //if exists and its UNCHECKED.... remove it
                    {
                        users_remove.Add(new ContentSubscriber
                        {
                            contentUuid = item.contentUuid,
                            userId = item.userId,
                            contentId = item.contentId
                        });
                    }
                }
                else
                {
                    if (item.isChecked) //if not exists and its CHECKED... Add it
                    {
                        var newSubscriber = new ContentSubscriber
                        {
                            contentUuid = item.contentUuid,
                            userId = item.userId,
                            contentId = item.contentId,
                            createDate = DateTime.UtcNow
                        };

                        users_add.Add(newSubscriber);

                        if (!_currentPermissions.Select(o => o.userId).Contains(item.userId))
                        {
                            _repo.Add_Permission(_currentContent.contentId, new ContentPermission()
                            {
                                contentUuid = item.contentUuid,
                                contentId = _currentContent.contentId,
                                userId = item.userId,
                                createDate = DateTime.UtcNow,
                                createUser = _currentUser,
                                allowed = true
                            });
                        }
                    }
                }
            }

            if (_repo.Set_Subscribers(_currentContent.contentId, users_add, users_remove) > 0)
            {
                if (_currentContent.type == "Pipeline" || _currentContent.type == "Meeting")
                {
                    //esse caso é pra reuniões passadas apenas,(conselheiro adicionado no board depois)
                    var users_to_add_permission = users_add.Select(o => o.userId).Where(o => !_currentPermissions.Select(a => a.userId).Contains(o)).ToList();
                    _repo.UpdateChildPermissions(_currentContent, users_to_add_permission, new List<int>());
                }

                if (_currentContent.type == ContentTypes.Form)
                {
                    var formOwnerList = _currentContent.ContentOwner;
                    foreach (var removeUserId in users_remove.Select(user => user.userId))
                    {
                        if (_currentPermissions.Select(o => o.userId).Contains(removeUserId) &&
                            !formOwnerList.Any(o => o.userId == removeUserId))
                        {
                            _repo.Delete_Permission(content_id, removeUserId);
                        }
                    }
                }

                var contentActivityService = new ContentActivityService(_currentUser);
                if (recurrenceShouldNotify.HasValue)
                {
                    contentActivityService.Add(content_id, _contentUuid, new ContentActivity()
                    {
                        contentId = content_id,
                        type = Operations.RECURRING_MEETING_SUBSCRIBER_UPDATE,
                        date = DateTime.UtcNow,
                        activityUser = _currentUser,
                        processed = !recurrenceShouldNotify.Value,
                        contentData = JsonConvert.SerializeObject(new { type = SubItems.SUBSCRIBER, ids_added = new int[] { }, ids_removed = users_remove.Select(o => o.userId) })
                    }, _userAgent);
                }
                else
                {
                    contentActivityService.Add(content_id, _contentUuid, new ContentActivity()
                    {
                        contentId = content_id,
                        type = Operations.SUBSCRIBER_UPDATE,
                        date = DateTime.UtcNow,
                        activityUser = _currentUser,
                        processed = false,
                        contentData = JsonConvert.SerializeObject(new { type = SubItems.SUBSCRIBER, ids_added = users_add.Select(o => o.userId), ids_removed = users_remove.Select(o => o.userId) })
                    }, _userAgent);
                }

                return true;
            }
            else
            {
                return false;
            }
        }

        public async Task<bool> SetRead(DateTime? readDate)
        {

            AtlasModelCore _md = new AtlasModelCore();
            var res = await _md.ContentSubscriber.Where(o => o.contentUuid == _contentUuid && o.userId == _currentUser).FirstOrDefaultAsync();

            if (res == null || res.isRead == true)
            {
                return false;
            }

            res.isRead = true;
            res.readDate = readDate;

            _md.Entry(res).State = EntityState.Modified;
            await _md.SaveChangesAsync();

            return true;
        }

        public async Task<List<ContentPermission>> GetSubscribersInfo()
        {

            ContentService contentService = new ContentService(_currentUser);
            Content content = await contentService.Get(_contentUuid);

            List<ContentPermission> permissions = content.ContentPermission.ToList();

            if (content.type == ContentTypes.Meeting)
            {
                // If Meeting we filter the permissions according to the subscribers in the meeting
                permissions = permissions.Where(cp => content.ContentSubscriber.Any(cs => cs.userId == cp.userId)).ToList();


            }
            else if (content.type == ContentTypes.Poll)
            {
                // S52 - Poll Report
                // Only voters who already voted can be selected for signing

                Poll poll = content.Poll.FirstOrDefault();
                if (poll == null) return new List<ContentPermission>();
                permissions = permissions.Where(cp => content.ContentSubscriber.Any(cs => cs.userId == cp.userId &&
                                                poll.Votes.Any(pv => pv.deleted != true && pv.userId == cs.userId)))
                                        .ToList();
            }

            // We should create a viewmodel for this in the future
            foreach (ContentPermission contentPermission in permissions)
            {
                contentPermission.User.shouldSerializeCpf = true;
                contentPermission.User.shouldSerializeMobile = true;
            }

            return permissions;

        }
        public async Task<bool> RSVPResponse(bool? confirm)

        {
            AtlasModelCore _md = new AtlasModelCore();
            var res = await _md.ContentSubscriber.Where(o => o.contentId == content_id && o.userId == _currentUser).FirstOrDefaultAsync();

            if (res == null)
            {
                return false;
            }

            if (confirm.HasValue)
            {
                res.rsvp = confirm;
                res.rsvpDate = DateTime.UtcNow;
            }
            else
            {
                res.rsvp = null;
                res.rsvpDate = null;
            }

            _md.Entry(res).State = EntityState.Modified;
            await _md.SaveChangesAsync();

            return true;
        }

        public async Task<bool> RSVPResponseUuId(bool? confirm)

        {
            AtlasModelCore _md = new AtlasModelCore();
            var res = await _md.ContentSubscriber.Where(o => o.contentUuid == _contentUuid && o.userId == _currentUser).FirstOrDefaultAsync();

            if (res == null)
            {
                return false;
            }

            if (confirm.HasValue)
            {
                res.rsvp = confirm;
                res.rsvpDate = DateTime.UtcNow;
            }
            else
            {
                res.rsvp = null;
                res.rsvpDate = null;
            }

            _md.Entry(res).State = EntityState.Modified;
            await _md.SaveChangesAsync();

            return true;
        }

        private static SubscriberListViewModel SubscribeChangeRules(User current_usr, Content _content, SubscriberListViewModel item, bool updating)
        {
            var new_item = new SubscriberListViewModel();
            if (item.ContentSubscriber.userId == _content.createUser)
            {
                if (item.ContentSubscriber.userId == current_usr.userId)
                {
                    //the creator user can unsubscribe himself, so, we'll just trow a warning
                    new_item.warning = "CREATED";
                }
                else
                {
                    if (item.Checked)//block change only when it is checked
                    {
                        new_item.blockChange = true;
                        new_item.blockReason = "CREATED";
                    }
                }
            }
            if (_content.type == ContentTypes.Task)
            {
                if (item.ContentSubscriber.userId == _content.assignedUser)
                {
                    if (item.ContentSubscriber.userId == current_usr.userId)
                    {
                        //the ASSIGNED user can unsubscribe himself, so, we'll just trow a warning
                        new_item.warning = "ASSIGNED";
                    }
                    else
                    {
                        if (item.Checked && !updating)//block change only when it is checked, and the validation is not occouring during update
                        {
                            new_item.blockChange = true;
                            new_item.blockReason = "ASSIGNED";
                        }
                    }
                }

                if (item.ContentSubscriber.userId == _content.reviewerUser)
                {
                    if (item.ContentSubscriber.userId == current_usr.userId)
                    {
                        //the reviewer user can unsubscribe himself, so, we'll just trow a warning
                        new_item.warning = "REVIEWER";
                    }
                    else
                    {
                        if (item.Checked && !updating)//block change only when it is checked, and the validation is not occouring during update
                        {
                            new_item.blockChange = true;
                            new_item.blockReason = "REVIEWER";
                        }
                    }
                }
            }
            return new_item;
        }

        public int Add(int newSubscriber_userId, bool createInvite = false, bool grantedByAdmin = false, Content content = null)
        {


            //if granted by admin, we should ignore these additional checks
            if (!grantedByAdmin)
            {
                //check permissions
                if (!_repo.CheckPermissionsForWorkgroup(Operations.SUBSCRIBER_ADD, content_id))
                {
                    //todo: logar tentativa de acesso
                    throw new SecurityException("Unauthorized attempt to add a subscriber into a content in workgroup.");
                }
            }

            var t = System.Threading.Tasks.Task.Run(() => _repo.Get(content_id));
            t.Wait();

            //This rule is necessary for the meeting type.
            //BUG 8158
            if (t.Result.type == "Meeting" &&
                   t.Result.Meeting.Where(x => x.date < DateTime.UtcNow).Any())
            {
                if (!grantedByAdmin)
                {

                    return 0;
                }
                else
                {
                    createInvite = false;
                }
            }
            //--------------------------


            ContentSubscriber c = new ContentSubscriber();
            c.contentId = this.content_id;
            c.contentUuid = content.contentUuid;
            c.userId = newSubscriber_userId;
            c.createDate = DateTime.UtcNow;

            int added_key = 0;
            if (_repo.Add_Subscriber(content.contentUuid, c, out added_key, createInvite) > 0)
            {
                if (!grantedByAdmin) //if grantedByAdmin, it will be described in another Audit Log
                {
                    var contentData = content?.type == ContentTypes.Meeting ?
                        JsonConvert.SerializeObject(new { type = SubItems.SUBSCRIBER, id = added_key, c.userId, ids_added = new int[] { c.userId }, ids_removed = new int[] { } }) :
                        JsonConvert.SerializeObject(new { type = SubItems.SUBSCRIBER, id = added_key, c.userId });

                    var subItemId = content?.type == ContentTypes.Meeting ? c.userId : added_key;

                    new ContentActivityService(_currentUser).Add(content.contentId, content.contentUuid, new ContentActivity()
                    {
                        contentId = content_id,
                        date = DateTime.UtcNow,
                        processed = false,
                        type = Operations.SUBSCRIBER_ADD,
                        activityUser = _currentUser,
                        contentData = contentData,
                        subItemId = subItemId,
                        subItemType = SubItems.SUBSCRIBER
                    }, _userAgent);
                }

                return added_key;
            }
            else
            {
                return 0;
            }
        }

        public async Task<bool> SendRSVP(int contentId)
        {
            try
            {

                AtlasModelCore _md = new AtlasModelCore();
                var subscribers = _md.Content.Where(o => o.contentId == contentId).SelectMany(o => o.ContentSubscriber).Include(o => o.User).ToList();


                foreach (var item in subscribers)
                {
                    if (item.rsvp != null)
                    {
                        continue;
                    }

                    // pingas mail
                    //Service.MailNotification.MailNotification mail = new Service.MailNotification.MailNotification();
                    //await mail.atlasv2_rsvp(item.User, contentId);
                }

                return true;
            }
            catch (Exception ex)
            {

                throw;
            }
        }

        public async Task<bool> SendRSVP()
        {
            try
            {

                AtlasModelCore _md = new AtlasModelCore();
                var subscribers = _md.Content.Where(o => o.contentUuid == _contentUuid).SelectMany(o => o.ContentSubscriber).Include(o => o.User).ToList();


                foreach (var item in subscribers)
                {
                    if (item.rsvp != null)
                    {
                        continue;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {

                throw;
            }
        }
    }
}
