using Atlas.Business.Helpers;
using Atlas.Business.Helpers.Queue;
using Atlas.Business.ViewModels;
using Atlas.CrossCutting.AppEnums;
using Atlas.CrossCutting.Helpers;
using Atlas.CrossCutting.Helpers.PersonalData;
using Atlas.CrossCutting.Settings;
using Atlas.Data.Entities;
using Atlas.Data.Repository;
using i18next_net;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using OfficeOpenXml;
using Sentry;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TimeZoneConverter;

namespace Atlas.Business
{
    public class ActivityService
    {
        public ActivityRepository act_repo = null;
        UserRoleRepository ur_repo = null;

        private static readonly string[] hiddenActivities =
        {
            "UPDATED_CLIENT_TAXNUMBER",
            "UPDATED_CLIENT_COUNTRY",
            "UPDATED_CLIENT_FULLNAME",
            "UPDATED_CLIENT_NAME",
            "UPDATED_CLIENT_STATUS",
            "USER_ADDED_NEW_CLIENT",
            "USER_SESSION_DATA_EXPORT"
        };


        public int _currentUser;

        public ActivityService(int _currentUser, bool readOnly = false)
        {
            this._currentUser = _currentUser;
            act_repo = new ActivityRepository(_currentUser, readOnly);
            ur_repo = new UserRoleRepository(_currentUser);
        }

        public List<Activity> Get(int userId, int meetingId = 0, string type = null)
        {
            if (type != null)
            {
                List<Activity> acts = new List<Activity>();
                var activities = act_repo.GetFromUser(userId).Where(act => act.activityType == type).ToList();

                foreach (Activity act in activities)
                {
                    var json = JObject.Parse(act.activityMsg);
                    var meeting = json["meetingId"].ToString();

                    if (Convert.ToInt32(meeting) == meetingId)
                    {
                        acts.Add(act);
                    }
                }

                return acts;
            }
            else
            {
                return act_repo.GetFromUser(userId);
            }
        }

        //Traz todos os activities e contentActivities
        public async Task<List<AuditlogViewModel>> FetchAllActivities(ContentRequestFilter filters, List<UserRole> userRoles = null)
        {
            if (userRoles == null)
            {
                // gets roles for current user
                UserRoleRepository usrRepo = new UserRoleRepository(_currentUser);
                userRoles = usrRepo.GetUserRolesDetails(_currentUser);
            }

            // Checks if it is an Admin
            if (userRoles.Any(ur => ur.Role.name == "CLIENT_ADMIN" || ur.Role.name == "SUPER_ADMIN" || ur.Role.name == "SUPPORT_ADMIN"))
            {
                // Quickier queries
                var resAct = (await act_repo.FetchAll(filters, userRoles));

                var resAct_vm = resAct.Select(o => new AuditlogViewModel()
                {
                    logId = o.activityId,
                    activityType = o.activityType,
                    details = GetAuditDetails(o),
                    userId = o.activityUserId,
                    userName = o.User?.name,
                    clientId = o.clientId,
                    date = o.date,
                    workgroupColor = o.Workgroup?.bulletColor,
                    workgroupName = o.Workgroup?.name,
                    workgroupId = o.Workgroup?.workgroupId,
                    logType = "ACCESS_LOG",
                    contentId = null,
                    IPAddress = o.IPAddress,
                    sessionKey = o.sessionKey,
                    externalSignerActivity = o.activityMsg?.Contains("externalSignerActivity"),
                    hidden = o.hidden,
                    device = o.device
                }).ToList();

                return resAct_vm;
            }
            else
            {
                throw new Exception("NOT_ADMIN");
            }
        }

        public List<AuditlogViewModel> PrepareActivities(List<Activity> activities)
        {
            var resAct_vm = activities.Select(o => new AuditlogViewModel()
            {
                logId = o.activityId,
                activityType = o.activityType,
                details = GetAuditDetails(o),
                userId = o.activityUserId,
                userName = o.User?.name,
                clientId = o.clientId,
                date = o.date,
                workgroupColor = o.Workgroup?.bulletColor,
                workgroupName = o.Workgroup?.name,
                workgroupId = o.Workgroup?.workgroupId,
                logType = "ACCESS_LOG",
                contentId = null,
                IPAddress = o.IPAddress,
                sessionKey = o.sessionKey,
                externalSignerActivity = o.activityMsg?.Contains("externalSignerActivity"),
                hidden = o.hidden,
                device = o.device
            }).ToList();

            return resAct_vm;
        }

        private string GetAuditDetails(Activity activity)
        {

            try
            {
                if (activity.activityMsg != null && activity.activityMsg.Contains("externalSignerActivity"))
                {
                    var activityMsgData = (dynamic)JsonConvert.DeserializeObject(activity.activityMsg ?? "{}");
                    string details = $"name: {activityMsgData.name},\n" +
                                     $"email: {activityMsgData.email}";

                    if (!string.IsNullOrEmpty(activityMsgData.contentId))
                    {
                        details += $"\ncontentId: {activityMsgData.contentId}";
                    }

                    return details;
                }
                else
                {
                    return activity.activityMsg;
                }
            }
            catch (Exception)
            {
                return activity.activityMsg;
            }
        }

        public async Task<List<AuditlogViewModel>> FetchAuditLogData(ContentRequestFilter filters, bool maskSensitiveData = true)
        {
            try
            {
                List<AuditlogViewModel> resultUnion = new List<AuditlogViewModel>();
                FeatureManagerService featureManagerService = new FeatureManagerService(_currentUser);

                var userRoles = ur_repo.GetUserRolesDetails(_currentUser);

                bool isAuditEnabled = await featureManagerService.isEnabledByClient(PlanFeatureNames.AUDIT_LOG, (int)filters.clientId);

                if (filters.pageNumber == null)
                {
                    filters.pageNumber = 0;
                    filters.pageSize = 50;
                }

                // The user with Support Admin privileges can view the audit log even its feature plan was disabled.
                if (!isAuditEnabled &&
                    !userRoles.Any(ur => (ur.Role.name == "SUPER_ADMIN" || ur.Role.name == Roles.SupportAdmin)))
                {
                    throw new InvalidOperationException("FEATURE_NOT_INCLUDED");
                }

                AtlasModelCore _md = new AtlasModelCore();
                User usr = _md.User.Find(_currentUser);
                i18next i18n = new i18next(new InitOptions()
                {
                    defaultNS = "common",
                    localeFileType = LocaleFileTypeEnum.Path,
                    fallbackLng = "en"
                });

                string lang = (usr.defaultLanguage ?? "pt").ToLower();
                i18n.changeLanguage(lang);

                bool isSearchingForSpecificContent = filters.contentId > 0;

                ContentActivityService casv = new ContentActivityService(_currentUser, usr);

                var resConAct = await casv.FetchContentActivities(filters, userRoles);

                var resAct = isSearchingForSpecificContent ? new List<AuditlogViewModel>() : await FetchAllActivities(filters, userRoles);

                resultUnion.AddRange(resAct);
                resultUnion.AddRange(resConAct);

                resultUnion = resultUnion.OrderByDescending(o => o.date).ToList();

                if (filters.pageSize != 0)
                {
                    resultUnion = resultUnion.Skip(filters.pageNumber.Value * filters.pageSize.Value).Take(filters.pageSize.Value).ToList();
                }

                resultUnion.ForEach(p => p.workgroupName = (p.workgroupName == "Workgroup ROOT" ? i18n.t("activities:auditLog.KB_SHARED_FOLDERS") : p.workgroupName));

                if (maskSensitiveData)
                {
                    resultUnion.ForEach(p =>
                    {
                        p.details = PersonalDataHelper.MaskSensitiveInformation(p.details, maskPhoneBeginning: true);
                    });
                }

                return resultUnion;
            }
            catch (Exception ex)
            {
                throw ex;
            }

        }

        /// <summary>Adds the specified activity type for many clients when a user is member of more than one board in different clients.</summary>
        /// <param name="activityType">Type of the activity.</param>
        /// <param name="clientId">The client identifier.</param>
        /// <param name="userId">The user identifier.</param>
        /// <param name="message">The message.</param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException">nor clientId or workgroupId should be specified</exception>
        /// 
        [Obsolete("Use AddNew instead. This method will be removed in future versions.")]
        public static int Add(string activityType, List<int> clientId, int? userId = null, string message = null, bool sendOnce = false, string providedSessionKey = null, string providedIP = null)
        {
            if (!string.IsNullOrWhiteSpace(message))
                message = ActivityMessageTruncate(message);

            string ipAddress = providedIP;
            string sessionKey = providedSessionKey;
            if (string.IsNullOrEmpty(providedIP))
            {
                ExtractConnectionInfo(out ipAddress, out sessionKey, providedSessionKey);
            }

            int activity_addedId = 0;
            int index = 0;

            string device = "";
            try
            {
                string userAgent = ""; //System.Web.HttpContext.Current?.Request?.Headers?.GetValues("User-Agent")?.FirstOrDefault();
                if (!string.IsNullOrEmpty(userAgent))
                {
                    device = new DeviceDetectorHelper(userAgent).GetUserAgentDeviceOrEmpty();
                }
            }
            catch (Exception)
            {
                // Ignore as we've the default device to be used.
            }

            // DO NOT instantiate ActivityRepository inside foreach, preventing a DbContext creation for each client
            using (var ar = new ActivityRepository(userId))
            {
                foreach (int client in clientId)
                {
                    try
                    {
                        Activity activity = new Activity();
                        activity.clientId = client;
                        activity.workgroupId = null; // This method is oriented to activities related to multiclient actions like login
                        activity.activityUserId = userId;
                        activity.activityType = activityType;
                        activity.date = DateTime.UtcNow;
                        activity.processed = false;
                        activity.activityMsg = message;
                        activity.IPAddress = ipAddress;
                        activity.sessionKey = sessionKey;
                        activity.device = device;

                        if (sendOnce && index > 0)
                        {
                            activity.processed = true;
                        }

                        activity_addedId = ar.AddActivity(activity);
                        if (activity_addedId > 0)
                        {
                            try
                            {
                                //envia para fila do activity / Twilio Segment 
                                String queueName2 = "activity";
                                activity.Workgroup = null;
                                activity.Client = null;
                                activity.General_Activity_Notifications = null;
                                activity.User = null;
                                var objJson2 = JsonConvert.SerializeObject(activity, Formatting.None,
                                new JsonSerializerSettings
                                {
                                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                                });
                                //Save Message
                                MessageSender ms2 = new MessageSender(queueName2);
                                ms2.SendMessage(objJson2);

                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine("ERROR WHILE SERIALIZING ACTIVITIY: " + ex.Message);
                            }

                            SendToAzFunctionsSchedulersOnCompanyActivityAdd(activity);
                        }

                        index++;
                    }
                    catch (Exception ex)
                    {
                        using (SentrySdk.Init("https://ab28683383204d07a3d36712ce88a5a0:<EMAIL>/162538"))
                        {
                            SentrySdk.CaptureException(ex);
                            SentrySdk.CaptureMessage(string.Format("ERROR AddActivity: activityType={0};clientId={1};userId={2}", activityType, clientId.ToString(), userId));
                        }
                        return activity_addedId;
                    }

                }
            }

            return activity_addedId;
        }

        [Obsolete("Use AddNew instead. This method will be removed in future versions.")]
        public static bool Add(string activityType, int? clientId = null, int? workgroupId = null, int? userId = null, string message = null, string providedSessionKey = null)
        {
            if (!string.IsNullOrWhiteSpace(message))
                message = ActivityMessageTruncate(message);

            string ipAddress, sessionKey;
            ExtractConnectionInfo(out ipAddress, out sessionKey, providedSessionKey);

            string device = "";
            try
            {
                string userAgent = ""; //System.Web.HttpContext.Current?.Request?.Headers?.GetValues("User-Agent")?.FirstOrDefault();
                if (!string.IsNullOrEmpty(userAgent))
                {
                    device = new DeviceDetectorHelper(userAgent).GetUserAgentDeviceOrEmpty();
                }
            }
            catch (Exception)
            {
                // Ignore as we've the default device to be used.
            }

            try
            {

                if (!clientId.HasValue && !workgroupId.HasValue)
                {
                    throw new ArgumentNullException("nor clientId or workgroupId should be specified");
                }

                Activity activity = new Activity();
                activity.clientId = clientId;
                activity.workgroupId = workgroupId;
                activity.activityUserId = userId;
                activity.activityType = activityType;
                activity.date = DateTime.UtcNow;
                activity.processed = false;
                activity.activityMsg = message;
                activity.IPAddress = ipAddress;
                activity.sessionKey = sessionKey;
                activity.device = device;


                ActivityRepository ar = new ActivityRepository(userId);
                if (ar.AddActivity(activity) > 0)
                {
                    if (activityType == "WORKGROUP_ARCHIVED" || activityType == "WORKGROUP_RESTORED")
                    {
                        String queueName = "quota-activity";
                        //serializar objeto obj / 0
                        var objJson = JsonConvert.SerializeObject(activity);

                        //Save Message
                        MessageSender ms = new MessageSender(queueName);
                        bool message_result = ms.SendMessage(objJson);
                    }

                    SendToAzFunctionsSchedulersOnCompanyActivityAdd(activity);

                    return true;
                }
                else
                {

                    return false;
                }
            }
            catch (Exception ex)
            {
                using (SentrySdk.Init("https://ab28683383204d07a3d36712ce88a5a0:<EMAIL>/162538"))
                {
                    SentrySdk.CaptureException(ex);
                    SentrySdk.CaptureMessage(string.Format("ERROR AddActivity: activityType={0};clientId={1};workgroupId={2};userId={3}", activityType, clientId, workgroupId, userId));
                }
                return false;
            }
        }

        public static bool AddNew(StorageSettings storageSettings, string activityType, string userAgent, int? clientId = null, int? workgroupId = null, int? userId = null, string message = null)
        {
            if (!string.IsNullOrWhiteSpace(message))
                message = ActivityMessageTruncate(message);

            string ipAddress, sessionKey;
            ExtractConnectionInfo(out ipAddress, out sessionKey);

            string device = "";
            try
            {
                if (!string.IsNullOrEmpty(userAgent))
                {
                    device = new DeviceDetectorHelper(userAgent).GetUserAgentDeviceOrEmpty();
                }
            }
            catch (Exception)
            {
                // Ignore as we've the default device to be used.
            }

            try
            {

                if (!clientId.HasValue && !workgroupId.HasValue)
                {
                    throw new ArgumentNullException("nor clientId or workgroupId should be specified");
                }

                Activity activity = new Activity();
                activity.clientId = clientId;
                activity.workgroupId = workgroupId;
                activity.activityUserId = userId;
                activity.activityType = activityType;
                activity.date = DateTime.UtcNow;
                activity.processed = false;
                activity.activityMsg = message;
                activity.IPAddress = ipAddress;
                activity.sessionKey = sessionKey;
                activity.device = device;

                ActivityRepository ar = new ActivityRepository(userId);
                if (ar.AddActivity(activity) > 0)
                {
                    if (activityType == "WORKGROUP_ARCHIVED" || activityType == "WORKGROUP_RESTORED")
                    {
                        String queueName = "quota-activity";
                        //serializar objeto obj / 0
                        var objJson = JsonConvert.SerializeObject(activity);

                        //Save Message
                        MessageSender ms = new MessageSender(queueName);
                        bool message_result = ms.SendMessage(objJson);
                    }

                    SendToAzFunctionsSchedulersOnCompanyActivityAddNew(storageSettings, activity);

                    return true;
                }
                else
                {

                    return false;
                }
            }
            catch (Exception ex)
            {
                using (SentrySdk.Init("https://ab28683383204d07a3d36712ce88a5a0:<EMAIL>/162538"))
                {
                    SentrySdk.CaptureException(ex);
                    SentrySdk.CaptureMessage(string.Format("ERROR AddActivity: activityType={0};clientId={1};workgroupId={2};userId={3}", activityType, clientId, workgroupId, userId));
                }
                return false;
            }
        }

        public static int AddNew(StorageSettings storageSettings, string activityType, List<int> clientId, string userAgent, int? userId = null, string message = null, bool sendOnce = false)
        {
            if (!string.IsNullOrWhiteSpace(message))
                message = ActivityMessageTruncate(message);

            string ipAddress, sessionKey;
            ExtractConnectionInfo(out ipAddress, out sessionKey);

            int activity_addedId = 0;
            int index = 0;

            string device = "";
            try
            {
                if (!string.IsNullOrEmpty(userAgent))
                {
                    device = new DeviceDetectorHelper(userAgent).GetUserAgentDeviceOrEmpty();
                }
            }
            catch (Exception)
            {
                // Ignore as we've the default device to be used.
            }

            foreach (int client in clientId)
            {
                try
                {

                    Activity activity = new Activity();
                    activity.clientId = client;
                    activity.workgroupId = null; // This method is oriented to activities related to multiclient actions like login
                    activity.activityUserId = userId;
                    activity.activityType = activityType;
                    activity.date = DateTime.UtcNow;
                    activity.processed = false;
                    activity.activityMsg = message;
                    activity.IPAddress = ipAddress;
                    activity.sessionKey = sessionKey;
                    activity.device = device;


                    if (sendOnce && index > 0)
                    {
                        activity.processed = true;
                    }

                    ActivityRepository ar = new ActivityRepository(userId);
                    activity_addedId = ar.AddActivity(activity);
                    if (activity_addedId > 0)
                    {
                        try
                        {
                            //envia para fila do activity / Twilio Segment 
                            String queueName2 = "activity";
                            activity.Workgroup = null;
                            activity.Client = null;
                            activity.General_Activity_Notifications = null;
                            activity.User = null;
                            var objJson2 = JsonConvert.SerializeObject(activity, Formatting.None,
                            new JsonSerializerSettings
                            {
                                ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                            });
                            //Save Message
                            MessageSender ms2 = new MessageSender(storageSettings, queueName2);
                            ms2.SendMessage(objJson2);

                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine("ERROR WHILE SERIALIZING ACTIVITIY: " + ex.Message);
                        }

                        SendToAzFunctionsSchedulersOnCompanyActivityAddNew(storageSettings, activity);
                    }

                    index++;
                }
                catch (Exception ex)
                {
                    using (SentrySdk.Init("https://ab28683383204d07a3d36712ce88a5a0:<EMAIL>/162538"))
                    {
                        SentrySdk.CaptureException(ex);
                        SentrySdk.CaptureMessage(string.Format("ERROR AddActivity: activityType={0};clientId={1};userId={2}", activityType, clientId.ToString(), userId));
                    }
                    return activity_addedId;
                }

            }

            return activity_addedId;
        }

        private static void SendToAzFunctionsSchedulersOnCompanyActivityAddNew(StorageSettings storageSettings, Activity activity)
        {
            try
            {
                //envia para fila do activity / Twilio Segment 
                String queueName2 = "activity";
                activity.Workgroup = null;
                activity.Client = null;
                activity.General_Activity_Notifications = null;
                activity.User = null;
                var objJson2 = JsonConvert.SerializeObject(activity, Formatting.None,
                new JsonSerializerSettings
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                });
                //Save Message
                MessageSender ms2 = new MessageSender(storageSettings, queueName2);
                ms2.SendMessage(objJson2);

            }
            catch (Exception ex)
            {
                Console.WriteLine("ERROR WHILE SERIALIZING ACTIVITIY: " + ex.Message);
            }
        }

        [Obsolete("Use SendToAzFunctionsSchedulersOnCompanyActivityAdd instead. This method will be removed in future versions.")]
        private static void SendToAzFunctionsSchedulersOnCompanyActivityAdd(Activity activity)
        {
            try
            {
                //envia para fila do activity / Twilio Segment 
                String queueName2 = "activity";
                activity.Workgroup = null;
                activity.Client = null;
                activity.General_Activity_Notifications = null;
                activity.User = null;
                var objJson2 = JsonConvert.SerializeObject(activity, Formatting.None,
                new JsonSerializerSettings
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                });
                //Save Message
                MessageSender ms2 = new MessageSender(queueName2);
                ms2.SendMessage(objJson2);

            }
            catch (Exception ex)
            {
                Console.WriteLine("ERROR WHILE SERIALIZING ACTIVITIY: " + ex.Message);
            }
        }

        private static void ExtractConnectionInfo(out string ipAddress, out string sessionKey, string providedSessionKey = null)
        {
            ipAddress = "";
            sessionKey = "";
            /* PINGAS try
            {
                // Get current IP address - True-Client-IP FIRST
                if (!string.IsNullOrEmpty(HttpContext.Current.Request.Headers["True-Client-IP"]))
                    ipAddress = HttpContext.Current.Request.Headers["True-Client-IP"];

                // Get current IP address - CLOUDFLARE FIRST
                if (string.IsNullOrEmpty(ipAddress))
                    ipAddress = System.Web.HttpContext.Current.Request.Headers["CF-CONNECTING-IP"];

                // Get current IP address
                if (string.IsNullOrEmpty(ipAddress))
                    ipAddress = System.Web.HttpContext.Current.Request.Headers["X-FORWARDED-FOR"];

                if (string.IsNullOrEmpty(ipAddress))
                {
                    ipAddress = System.Web.HttpContext.Current.Request.ServerVariables["REMOTE_ADDR"];
                }
                else
                {
                    //X-FORWARDED-FOR is a list of IP address, where the first one should be the user's IP address.
                    ipAddress = ipAddress.Split(',').FirstOrDefault();
                }

                string _ENVIRONMENT = ConfigurationManager.AppSettings["Environment"];
                if (_ENVIRONMENT == "BETA" || _ENVIRONMENT == "STAGING")
                {
                    string email = System.Threading.Thread.CurrentPrincipal.Identity.Name;
                    System.Diagnostics.Trace.TraceWarning("Connection data: " + JsonConvert.SerializeObject(new
                    {
                        email,
                        ipAddress,
                        trueClientIp = System.Web.HttpContext.Current.Request.Headers["True-Client-IP"],
                        cfConnectingIp = System.Web.HttpContext.Current.Request.Headers["CF-CONNECTING-IP"],
                        xForwardedForServer = System.Web.HttpContext.Current.Request.ServerVariables["X-FORWARDED-FOR"],
                        xForwardedForHeader = System.Web.HttpContext.Current.Request.Headers["X-FORWARDED-FOR"],
                        remoteAddr = System.Web.HttpContext.Current.Request.ServerVariables["REMOTE_ADDR"]
                    }));
                }

            }
            catch
            {
                ipAddress = "UNKNOWN";
            }

            try
            {
                // Get current session key
                if (!string.IsNullOrEmpty(providedSessionKey))
                    sessionKey = providedSessionKey;
                else
                {
                    var session_key_claim = ((System.Security.Claims.ClaimsIdentity)System.Threading.Thread.CurrentPrincipal.Identity).Claims.FirstOrDefault(o => o.Type == "session_key");
                    if (session_key_claim != null)
                    {
                        sessionKey = session_key_claim.Value;
                    }
                    else
                    {
                        sessionKey = "UNKNOWN";
                    }
                }
            }
            catch
            {
                sessionKey = "UNKNOWN";
            }*/

        }

        [Obsolete("Use AddNewAsync instead. This method will be removed in future versions.")]
        public static async Task<bool> AddAsync(string activityType, int? clientId = null, int? workgroupId = null, int? userId = null, string message = null, string device = "")
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(message))
                    message = ActivityMessageTruncate(message);

                string ipAddress, sessionKey;
                ExtractConnectionInfo(out ipAddress, out sessionKey);

                if (!clientId.HasValue && !workgroupId.HasValue)
                {
                    throw new ArgumentNullException("nor clientId or workgroupId should be specified");
                }

                Activity activity = new Activity();
                activity.clientId = clientId;
                activity.workgroupId = workgroupId;
                activity.activityUserId = userId;
                activity.activityType = activityType;
                activity.date = DateTime.UtcNow;
                activity.processed = false;
                activity.activityMsg = message;
                activity.IPAddress = ipAddress;
                activity.sessionKey = sessionKey;
                activity.hidden = hiddenActivities.Contains(activityType);
                activity.device = device;

                ActivityRepository ar = new ActivityRepository(userId);
                if (await ar.AddActivityAsync(activity) > 0)
                {
                    if (activityType == "WORKGROUP_ARCHIVED" || activityType == "WORKGROUP_RESTORED")
                    {
                        String queueName = "quota-activity";
                        //serializar objeto obj / 0
                        var objJson = JsonConvert.SerializeObject(activity);

                        //Save Message
                        MessageSender ms = new MessageSender(queueName);
                        bool message_result = ms.SendMessage(objJson);
                    }

                    SendToAzFunctionsSchedulersOnCompanyActivityAdd(activity);

                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                using (SentrySdk.Init("https://ab28683383204d07a3d36712ce88a5a0:<EMAIL>/162538"))
                {
                    SentrySdk.CaptureException(ex);
                    SentrySdk.CaptureMessage(string.Format("ERROR AddActivityAsync: activityType={0};clientId={1};workgroupId={2};userId={3}", activityType, clientId, workgroupId, userId));
                }
                return false;
            }
        }

        public static async Task<bool> AddNewAsync(StorageSettings storageSettings, string activityType, int? clientId = null, int? workgroupId = null, int? userId = null, string message = null, string userAgent = null)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(message))
                    message = ActivityMessageTruncate(message);

                string ipAddress, sessionKey;
                ExtractConnectionInfo(out ipAddress, out sessionKey);

                string device = "UNKNOWN";
                try
                {
                    if (!string.IsNullOrEmpty(userAgent))
                    {
                        device = new DeviceDetectorHelper(userAgent).GetUserAgentDeviceOrEmpty();
                    }
                }
                catch
                {
                    // Ignore as we've the default device to be used.
                }

                if (!clientId.HasValue && !workgroupId.HasValue)
                {
                    throw new ArgumentNullException("nor clientId or workgroupId should be specified");
                }

                Activity activity = new Activity();
                activity.clientId = clientId;
                activity.workgroupId = workgroupId;
                activity.activityUserId = userId;
                activity.activityType = activityType;
                activity.date = DateTime.UtcNow;
                activity.processed = false;
                activity.activityMsg = message;
                activity.IPAddress = ipAddress;
                activity.sessionKey = sessionKey;
                activity.hidden = hiddenActivities.Contains(activityType);
                activity.device = device;

                ActivityRepository ar = new ActivityRepository(userId);
                if (await ar.AddActivityAsync(activity) > 0)
                {
                    if (activityType == "WORKGROUP_ARCHIVED" || activityType == "WORKGROUP_RESTORED")
                    {
                        String queueName = "quota-activity";
                        //serializar objeto obj / 0
                        var objJson = JsonConvert.SerializeObject(activity);

                        //Save Message
                        MessageSender ms = new MessageSender(storageSettings, queueName);
                        bool message_result = ms.SendMessage(objJson);
                    }

                    SendToAzFunctionsSchedulersOnCompanyActivityAddNew(storageSettings, activity);

                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                using (SentrySdk.Init("https://ab28683383204d07a3d36712ce88a5a0:<EMAIL>/162538"))
                {
                    SentrySdk.CaptureException(ex);
                    SentrySdk.CaptureMessage(string.Format("ERROR AddActivityAsync: activityType={0};clientId={1};workgroupId={2};userId={3}", activityType, clientId, workgroupId, userId));
                }
                return false;
            }
        }

        private static string ActivityMessageTruncate(string message)
        {
            int maxLength = 1024;

            if (message.Length > maxLength)
            {
                return message.Substring(0, maxLength);
            }

            return message;
        }

        public async Task<byte[]> GenerateCsv(List<AuditlogViewModel> _list, string _language, string _tempPath)
        {
            StringBuilder sb = new StringBuilder();


            //ta igual para os dois idiomas, por enquanto
            var header = "ID;UserId;UserName;Type;Timespan;WorkgroupId;WorkgroupName;Msg";

            switch (_language)
            {
                case "en":
                    header = "ID;UserId;UserName;Type;Timespan;WorkgroupId;WorkgroupName;Msg";
                    break;
                default:
                    break;
            }

            sb.AppendLine(header);

            int i = 0;
            foreach (var item in _list)
            {
                sb.AppendLine(String.Format("{0};{1};{2};{3};{4};{5};{6};{7};", item.logId, item.userId, item.userName, item.activityType, item.date, item.workgroupId, item.workgroupName, item.details));
                i++;
            }
            return Encoding.UTF8.GetBytes(sb.ToString());
        }

        public async Task<byte[]> exportDataToExcel(ExportDataViewModel exportConfig)
        {
            using (var _md = new AtlasModelCore())
            {
                User usr = _md.User.FirstOrDefault(o => o.userId == _currentUser);

                var i18n = new i18next(new InitOptions
                {
                    defaultNS = "common",
                    localeFileType = LocaleFileTypeEnum.Path,
                    fallbackLng = "en"
                });

                i18n.changeLanguage((usr.defaultLanguage ?? "pt").ToLower());

                using (var stream = new MemoryStream())
                using (ExcelPackage package = new ExcelPackage(stream))
                {
                    if (exportConfig.exportActivities)
                    {
                        var worksheet = package.Workbook.Worksheets.Add(i18n.t("activities:export.log"));
                        await addActivitiesToExcel(worksheet, exportConfig);
                        Add("AUDIT_LOG_EXPORTED", exportConfig.clientId, null, _currentUser);
                    }

                    //For local testing
                    //package.SaveAs(new FileInfo(@"c:\workbooks\myworkbook.xlsx"));
                    package.Save();
                    return stream.ToArray();
                }
            }
        }

        public async Task<byte[]> ExportUserSessionsDataToExcel(ExportUserSessionsDataViewModel exportData, bool isClientAdminOnly = true)
        {
            using (var _md = new AtlasModelCore())
            {
                User usr = _md.User.FirstOrDefault(o => o.userId == _currentUser);
                var i18n = new i18next(new InitOptions
                {
                    defaultNS = "common",
                    localeFileType = LocaleFileTypeEnum.Path,
                    fallbackLng = "en"
                });
                i18n.changeLanguage((usr.defaultLanguage ?? "pt").ToLower());
                using (var stream = new MemoryStream())
                using (ExcelPackage package = new ExcelPackage(stream))
                {
                    var worksheet = package.Workbook.Worksheets.Add(i18n.t("activities:export.log_user_session"));
                    await AddUserSessionsToExcel(worksheet, exportData, isClientAdminOnly);

                    var activityType = isClientAdminOnly ? Operations.CLIENT_USER_SESSION_DATA_EXPORT : Operations.USER_SESSION_DATA_EXPORT;

                    await AddAsync(activityType, exportData.clientId, userId: _currentUser);

                    //For local testing
                    //package.SaveAs(new FileInfo(@"c:\testeeeee\myworkbook.xlsx"));
                    package.Save();
                    return stream.ToArray();
                }
            }
        }

        private async System.Threading.Tasks.Task addActivitiesToExcel(ExcelWorksheet worksheet, ExportDataViewModel exportConfig)
        {

            AtlasModelCore _md = new AtlasModelCore();

            //FeatureManagerService featureManagerService = new FeatureManagerService(_currentUser);
            //bool isAuditLogEnabled = await featureManagerService.isEnabledByClient(PlanFeatureNames.AUDIT_LOG, exportConfig.clientId);

            ContentRequestFilter filter = new ContentRequestFilter
            {
                contentId = exportConfig.contentId,
                workgroups = exportConfig.workgroups,
                createDateMin = exportConfig.fromDate,
                createDateMax = exportConfig.toDate,
                pageSize = 0,
                pageNumber = 0,
                types = exportConfig.types,
                actionUsers = exportConfig.actionUsers,
                clientId = exportConfig.clientId
            };

            var activitiesList = await FetchAuditLogData(filter, maskSensitiveData: false);

            var i18n = new i18next(new InitOptions()
            {
                defaultNS = "common",
                localeFileType = LocaleFileTypeEnum.Path,
                fallbackLng = "en"
            });

            var user = _md.User.Where(u => u.userId == _currentUser).FirstOrDefault();
            string tz = "";
            try
            {
                tz = TZConvert.IanaToWindows(user.defaultTimezone);

            }
            catch (InvalidTimeZoneException)
            {
                tz = "E. South America Standard Time";
            }

            TimeZoneInfo timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(tz);

            string lang = (user.defaultLanguage ?? "pt").ToLower();
            i18n.changeLanguage(lang);

            string sheetName = i18n.t("activities:export.log");
            worksheet.Workbook.Worksheets[sheetName].Cells["A1"].Value = i18n.t("activities:export.logId");
            worksheet.Workbook.Worksheets[sheetName].Cells["B1"].Value = i18n.t("activities:export.userId");
            worksheet.Workbook.Worksheets[sheetName].Cells["C1"].Value = i18n.t("activities:export.username");
            worksheet.Workbook.Worksheets[sheetName].Cells["D1"].Value = i18n.t("activities:export.date");
            worksheet.Workbook.Worksheets[sheetName].Cells["E1"].Value = i18n.t("activities:export.time");
            worksheet.Workbook.Worksheets[sheetName].Cells["F1"].Value = i18n.t("activities:export.action");
            worksheet.Workbook.Worksheets[sheetName].Cells["G1"].Value = i18n.t("activities:export.contentType");
            worksheet.Workbook.Worksheets[sheetName].Cells["H1"].Value = i18n.t("activities:export.contentId");
            worksheet.Workbook.Worksheets[sheetName].Cells["I1"].Value = i18n.t("activities:export.workgroupId");
            worksheet.Workbook.Worksheets[sheetName].Cells["J1"].Value = i18n.t("activities:export.workgroupName");
            worksheet.Workbook.Worksheets[sheetName].Cells["K1"].Value = i18n.t("activities:export.details");
            worksheet.Workbook.Worksheets[sheetName].Cells["L1"].Value = i18n.t("activities:export.logType");
            worksheet.Workbook.Worksheets[sheetName].Cells["M1"].Value = i18n.t("activities:export.ip");
            worksheet.Workbook.Worksheets[sheetName].Cells["N1"].Value = i18n.t("activities:export.activityDevice");
            worksheet.Workbook.Worksheets[sheetName].Cells["A1:N1"].Style.Font.Bold = true;

            if (activitiesList.Any())
            {
                List<AuditlogViewModel> activitiesRows = new List<AuditlogViewModel>();
                int i = 1;

                foreach (AuditlogViewModel act in activitiesList)
                {
                    AuditlogViewModel row = new AuditlogViewModel();
                    row.logId = act.logId;
                    if (act.externalSignerActivity == true)
                    {
                        row.userId = i18n.t("activities:export.externalSigner");
                    }
                    else
                    {
                        row.userId = act.userId;
                    }
                    row.userName = UserUtils.ExtractFirstName(act.userName);

                    row.date = TimeZoneInfo.ConvertTimeFromUtc(act.date, timeZoneInfo);
                    worksheet.Cells[(i + 1), 4].Style.Numberformat.Format = i18n.t("activities:export.dateFormat");

                    row.time = TimeZoneInfo.ConvertTimeFromUtc(act.date, timeZoneInfo);
                    worksheet.Cells[(i + 1), 5].Style.Numberformat.Format = i18n.t("activities:export.timeFormat");

                    try
                    {
                        row.activityType = i18n.t("activities:auditLog." + act.activityType);
                    }
                    catch (Exception)
                    {
                        row.activityType = act.activityType;
                    }

                    row.contentType = act.contentType != null ? i18n.t("activities:contentType." + act.contentType) : "";
                    row.contentId = act.contentId;
                    row.workgroupId = act.workgroupId;
                    row.workgroupName = act.workgroupName;
                    row.details = act.details;

                    try
                    {
                        row.logType = i18n.t("activities:list." + act.logType);
                    }
                    catch (Exception)
                    {
                        row.logType = act.logType;
                    }

                    if (!string.IsNullOrEmpty(act.IPAddress) && act.IPAddress.Contains(":"))
                    {
                        row.IPAddress = act.IPAddress.Split(':').FirstOrDefault();
                    }
                    else
                    {
                        row.IPAddress = act.IPAddress;
                    }

                    if (string.IsNullOrEmpty(act.device) || act.device == "UNKNOWN")
                    {
                        act.device = "";
                    }

                    row.device = act.device;

                    activitiesRows.Add(row);
                    i++;

                }

                worksheet.Workbook.Worksheets[sheetName].Cells["A2"].LoadFromCollection(activitiesRows);
                worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
            }
        }

        private async System.Threading.Tasks.Task AddUserSessionsToExcel(ExcelWorksheet worksheet, ExportUserSessionsDataViewModel exportData, bool isClientAdminOnly = true)
        {
            AtlasModelCore _md = new AtlasModelCore();

            var userSessionsList = await FetchUserSessionsData(exportData, isClientAdminOnly);

            var i18n = new i18next(new InitOptions()
            {
                defaultNS = "common",
                localeFileType = LocaleFileTypeEnum.Path,
                fallbackLng = "en"
            });

            var user = _md.User.Where(u => u.userId == _currentUser).FirstOrDefault();

            string tz = "";
            try
            {
                tz = TZConvert.IanaToWindows(user.defaultTimezone);

            }
            catch (InvalidTimeZoneException)
            {
                tz = "E. South America Standard Time";
            }

            TimeZoneInfo timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(tz);

            string lang = (user.defaultLanguage ?? "pt").ToLower();
            i18n.changeLanguage(lang);

            string sheetName = i18n.t("activities:export.log_user_session");
            worksheet.Workbook.Worksheets[sheetName].Cells["A1"].Value = i18n.t("activities:export.userSessionId");
            worksheet.Workbook.Worksheets[sheetName].Cells["B1"].Value = i18n.t("activities:export.userId");
            worksheet.Workbook.Worksheets[sheetName].Cells["C1"].Value = i18n.t("activities:export.session");
            worksheet.Workbook.Worksheets[sheetName].Cells["D1"].Value = i18n.t("activities:export.startDate");
            worksheet.Workbook.Worksheets[sheetName].Cells["E1"].Value = i18n.t("activities:export.startDateTime");
            worksheet.Workbook.Worksheets[sheetName].Cells["F1"].Value = i18n.t("activities:export.expireDate");
            worksheet.Workbook.Worksheets[sheetName].Cells["G1"].Value = i18n.t("activities:export.expireDateTime");
            worksheet.Workbook.Worksheets[sheetName].Cells["H1"].Value = i18n.t("activities:export.ip");
            worksheet.Workbook.Worksheets[sheetName].Cells["I1"].Value = i18n.t("activities:export.userAgent");
            worksheet.Workbook.Worksheets[sheetName].Cells["J1"].Value = i18n.t("activities:export.location");
            worksheet.Workbook.Worksheets[sheetName].Cells["K1"].Value = i18n.t("activities:export.name");
            worksheet.Workbook.Worksheets[sheetName].Cells["L1"].Value = i18n.t("activities:export.revoked");
            worksheet.Workbook.Worksheets[sheetName].Cells["M1"].Value = i18n.t("activities:export.verifiedByToken");
            worksheet.Workbook.Worksheets[sheetName].Cells["N1"].Value = i18n.t("activities:export.lastActivity");
            worksheet.Workbook.Worksheets[sheetName].Cells["O1"].Value = i18n.t("activities:export.lastActivityTime");
            worksheet.Workbook.Worksheets[sheetName].Cells["P1"].Value = i18n.t("activities:export.browserFingerprint");
            worksheet.Workbook.Worksheets[sheetName].Cells["Q1"].Value = i18n.t("activities:export.revokeTicket");
            worksheet.Workbook.Worksheets[sheetName].Cells["R1"].Value = i18n.t("activities:export.revokeDate");
            worksheet.Workbook.Worksheets[sheetName].Cells["S1"].Value = i18n.t("activities:export.revokeDateTime");
            worksheet.Workbook.Worksheets[sheetName].Cells["T1"].Value = i18n.t("activities:export.revokeUserId");
            worksheet.Workbook.Worksheets[sheetName].Cells["U1"].Value = i18n.t("activities:export.deviceId");
            worksheet.Workbook.Worksheets[sheetName].Cells["V1"].Value = i18n.t("activities:export.userIntegrationId");
            worksheet.Workbook.Worksheets[sheetName].Cells["W1"].Value = i18n.t("activities:export.lastIP");
            worksheet.Workbook.Worksheets[sheetName].Cells["X1"].Value = i18n.t("activities:export.lastLocation");
            worksheet.Workbook.Worksheets[sheetName].Cells["Y1"].Value = i18n.t("activities:export.lastUserAgent");
            worksheet.Workbook.Worksheets[sheetName].Cells["A1:Y1"].Style.Font.Bold = true;

            string dateFormat = "dd/MM/yyyy";
            string timeFormat = "HH:mm:ss";
            IFormatProvider formatProvider = CultureInfo.InvariantCulture;
            if (lang == "en")
            {
                dateFormat = "MM/dd/yyyy";
                timeFormat = "hh:mm:ss tt";
            }

            if (userSessionsList.Any())
            {
                int row = 2;
                foreach (var userSession in userSessionsList)
                {
                    worksheet.Cells[row, 1].Value = userSession.userSessionId;
                    worksheet.Cells[row, 2].Value = userSession.userId;
                    worksheet.Cells[row, 3].Value = userSession.session;
                    worksheet.Cells[row, 4].Value = userSession.startDate.HasValue ?
                                                                        TimeZoneInfo.ConvertTimeFromUtc(userSession.startDate.Value, timeZoneInfo).ToString(dateFormat, formatProvider)
                                                                    : string.Empty;

                    worksheet.Cells[row, 5].Value = userSession.startDate.HasValue ?
                                                                        TimeZoneInfo.ConvertTimeFromUtc(userSession.startDate.Value, timeZoneInfo).ToString(timeFormat, formatProvider)
                                                                    : string.Empty;

                    worksheet.Cells[row, 6].Value = userSession.expireDate.HasValue ?
                                                                        TimeZoneInfo.ConvertTimeFromUtc(userSession.expireDate.Value, timeZoneInfo).ToString(dateFormat, formatProvider)
                                                                    : string.Empty;

                    worksheet.Cells[row, 7].Value = userSession.expireDate.HasValue ?
                                                                        TimeZoneInfo.ConvertTimeFromUtc(userSession.expireDate.Value, timeZoneInfo).ToString(timeFormat, formatProvider)
                                                                    : string.Empty;
                    worksheet.Cells[row, 8].Value = userSession.IPAddress;
                    worksheet.Cells[row, 9].Value = userSession.userAgent;
                    worksheet.Cells[row, 10].Value = userSession.location;
                    worksheet.Cells[row, 11].Value = userSession.name;
                    worksheet.Cells[row, 12].Value = userSession.revoked ? i18n.t("export:yes") : i18n.t("export:no");
                    worksheet.Cells[row, 13].Value = userSession.verifiedByToken ? i18n.t("export:yes") : i18n.t("export:no");
                    worksheet.Cells[row, 14].Value = userSession.lastActivity.HasValue ?
                                                                        TimeZoneInfo.ConvertTimeFromUtc(userSession.lastActivity.Value, timeZoneInfo).ToString(dateFormat, formatProvider)
                                                                    : string.Empty;

                    worksheet.Cells[row, 15].Value = userSession.lastActivity.HasValue ?
                                                                        TimeZoneInfo.ConvertTimeFromUtc(userSession.lastActivity.Value, timeZoneInfo).ToString(timeFormat, formatProvider)
                                                                    : string.Empty;
                    worksheet.Cells[row, 16].Value = userSession.browserFingerprint;
                    worksheet.Cells[row, 17].Value = userSession.revokeTicket;
                    worksheet.Cells[row, 18].Value = userSession.revokeDate.HasValue ?
                                                                        TimeZoneInfo.ConvertTimeFromUtc(userSession.revokeDate.Value, timeZoneInfo).ToString(dateFormat, formatProvider)
                                                                    : string.Empty;

                    worksheet.Cells[row, 19].Value = userSession.revokeDate.HasValue ?
                                                                        TimeZoneInfo.ConvertTimeFromUtc(userSession.revokeDate.Value, timeZoneInfo).ToString(timeFormat, formatProvider)
                                                                    : string.Empty;
                    worksheet.Cells[row, 20].Value = userSession.revokeUserId;
                    worksheet.Cells[row, 21].Value = userSession.deviceId;
                    worksheet.Cells[row, 22].Value = userSession.userIntegrationId;
                    worksheet.Cells[row, 23].Value = userSession.lastIP;
                    worksheet.Cells[row, 24].Value = userSession.lastLocation;
                    worksheet.Cells[row, 25].Value = userSession.lastUserAgent;
                    row++;
                }

                worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
            }
        }

        public async Task<List<UserSessionLogViewModel>> FetchUserSessionsData(ExportUserSessionsDataViewModel exportData, bool isClientAdminOnly = true)
        {
            int clientId = exportData.clientId;
            var startDate = exportData.startDate;
            var endDate = exportData.endDate;
            using (var _md = new AtlasModelCore())
            {
                var sqlQuery = @"
                            SELECT DISTINCT
                                US.userSessionId, 
                                US.userId, 
                                US.session, 
                                US.startDate, 
                                US.expireDate, 
                                US.IP AS [IPAddress], 
                                US.userAgent, 
                                US.location, 
                                U.name, 
                                US.revoked, 
                                US.verifiedByToken, 
                                US.lastActivity, 
                                US.browserFingerprint, 
                                US.revokeTicket,
                                US.revokeDate, 
                                US.revokeUser AS [revokeUserId], 
                                US.deviceId, 
                                US.userIntegrationId, 
                                US.lastIP, 
                                US.lastLocation, 
                                US.lastUserAgent
                            FROM UserSession US
                            JOIN UserRole UR ON UR.userId = US.userId
                            JOIN [User] U ON US.userId = U.userId
                            ";
                var whereClauses = new List<string>();
                var parameters = new List<SqlParameter>();
                if (isClientAdminOnly)
                {
                    whereClauses.Add("UR.clientId = @clientId");
                    parameters.Add(new SqlParameter("@clientId", clientId));
                }
                if (startDate.HasValue)
                {
                    whereClauses.Add("US.startDate >= @startDate");
                    parameters.Add(new SqlParameter("@startDate", startDate.Value));
                }
                if (endDate.HasValue)
                {
                    whereClauses.Add("US.startDate <= @endDate");
                    parameters.Add(new SqlParameter("@endDate", endDate.Value));
                }
                if (whereClauses.Any())
                {
                    sqlQuery += " WHERE " + string.Join(" AND ", whereClauses);
                }
                sqlQuery += " ORDER BY US.userSessionId DESC";
                var userSessions = await _md.Database.SqlQueryRaw<UserSessionLogViewModel>(sqlQuery, parameters.ToArray()).ToListAsync();
                return userSessions;
            }
        }
    }
}
