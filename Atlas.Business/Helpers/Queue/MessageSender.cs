using System;
using Atlas.CrossCutting.Settings;
using Microsoft.WindowsAzure.Storage; // Namespace for CloudStorageAccount
using Microsoft.WindowsAzure.Storage.Queue; // Namespace for Queue storage types

namespace Atlas.Business.Helpers.Queue
{
    public class MessageSender
    {
        const string ServiceBusConnectionString = "<your_connection_string>";
        const string TopicName = "<your_topic_name>";
        public CloudStorageAccount storageAccount { get; set; }
        public CloudQueue queue { get; set; }
        public CloudQueueMessage message { get; set; }
        public CloudQueueClient queueClient { get; set; }

        public MessageSender(StorageSettings storageSettings, string queuename)
        {
            // Parse the connection string and return a reference to the storage account.
            storageAccount = CloudStorageAccount.Parse(
                storageSettings.StorageQueueConnectionString
            );

            // Instantiates the client
            queueClient = storageAccount.CreateCloudQueueClient();
            // Retrieve a reference to a container.
            queue = queueClient.GetQueueReference(queuename);
            // Create the queue if it doesn't already exist
            queue.CreateIfNotExistsAsync(); // pingas async
        }

        public MessageSender(string queuename)
        {
            // Parse the connection string and return a reference to the storage account.
            storageAccount = CloudStorageAccount.Parse(
                Environment.GetEnvironmentVariable("StorageQueueConnectionString")
            );

            // Instantiates the client
            queueClient = storageAccount.CreateCloudQueueClient();
            // Retrieve a reference to a container.
            queue = queueClient.GetQueueReference(queuename);
            // Create the queue if it doesn't already exist
            queue.CreateIfNotExistsAsync(); // pingas async
        }

        public bool SendMessage(string msg)
        {
            try
            {
                // CreateS a message and add it to the queue.
                message = new CloudQueueMessage(msg);
                queue.AddMessageAsync(message); // pingas async (not)

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                return false;
            }
        }
    }
}
