using Atlas.CrossCutting.DTO;
using Atlas.Data.Entities;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using System;
using System.Linq;
using System.Net.Http;
using System.Net;
using System.Threading.Tasks;

namespace Atlas.Business.Helpers
{
    public static class MailParse
    {
        private static string[] acceptedStrings = new[]
        {
            "Accepted",
            "<PERSON>pta<PERSON>",
            "Accept<PERSON>",
            "Accettato",
            "Zugesagt",
            "Aceito", //google fdp
            "Aceite",
            "<PERSON><PERSON>",
            "Aceptado",
            "Evento aceito"
        };

        private static string[] tentativeStrings = new[]
        {
            "Tentative",
            "Provisional",
            "Provisoire",
            "Provvisorio",
            "Mit Vorbehalt",
            "Tentat<PERSON>",
            "<PERSON>lvez",
            "Provisória",
            "Tentatively Accepted",
            "Provisório",
            "Aceito provisoriamente"
        };

        private static string[] declinedStrings = new[]
        {
            "Declined",
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>nt",
            "<PERSON>cusado", //google denovo pqp
            "Recus<PERSON>",
            "<PERSON>chazado"
        };

        private static string[] newTimeStrings = new[]
        {
            "New Time Proposed",
            "Nueva hora propuesta",
            "Nouvelle heure proposée",
            "Nuova ora proposta",
            "Vorgeschlagene Zeit",
            "Nova hora proposta",
            "Novo horário proposto",
            "Nueva hora propuesta"
        };

        public static async Task<bool> Parse_RSVP(CrossCutting.DTO.ParseMail mail, JObject json)
        {
            string mail_command = (mail.Subject ?? "").Split(':').FirstOrDefault().TrimEnd();
            var contentId = Convert.ToInt32(json["content"].ToString());
            var mail_from = mail.From.Split('<').LastOrDefault().Split('>').FirstOrDefault();

            AtlasModelCore _md = new AtlasModelCore();
            var content = _md.Content.Find(contentId);

            await ProcessContentGuest(mail_command, mail_from, contentId);
            await ProcessMeetingAgendaItem(mail_command, mail_from, content.parentContentId);
            await ProcessContentSubscriber(mail_command, mail_from, contentId);

            return true;
        }

        public static async Task<bool> ParseRSVPByContentId(CrossCutting.DTO.ParseMail mail, int contentId)
        {
            string mail_command = (mail.Subject ?? "").Split(':').FirstOrDefault().TrimEnd();
            var mail_from = mail.From.Split('<').LastOrDefault().Split('>').FirstOrDefault();

            AtlasModelCore _md = new AtlasModelCore();
            var content = _md.Content.FirstOrDefault(p => p.contentId == contentId);

            await ProcessContentGuest(mail_command, mail_from, contentId);
            await ProcessMeetingAgendaItem(mail_command, mail_from, contentId);
            await ProcessContentSubscriber(mail_command, mail_from, contentId);

            return true;
        }
        public static async Task<bool> ParseRecurringRSVP(CrossCutting.DTO.ParseMail mail, int contentId)
        {
            string mail_command = (mail.Subject ?? "").Split(':').FirstOrDefault().TrimEnd();
            var mail_from = mail.From.Split('<').LastOrDefault().Split('>').FirstOrDefault();

            AtlasModelCore _md = new AtlasModelCore();
            var content = _md.Content.Find(contentId);

            if (!content.recurringMeetingId.HasValue)
            {
                await ProcessContentGuest(mail_command, mail_from, contentId);
                await ProcessMeetingAgendaItem(mail_command, mail_from, contentId);
                await ProcessContentSubscriber(mail_command, mail_from, contentId);
            }
            else
            {
                var futureMeetings = await _md.Content
                    .Where(c => c.recurringMeetingId == content.recurringMeetingId && c.deleted != true && c.Meeting.FirstOrDefault().date > DateTime.UtcNow)
                    .ToArrayAsync();


                var currentUser = _md.User.Where(o => o.email == mail_from).FirstOrDefault();
                ContentService svc = new ContentService(currentUser.userId);

                foreach (var meeting in futureMeetings)
                {
                    await ProcessContentSubscriber(mail_command, currentUser, meeting.contentId, svc);
                }
            }
            return true;
        }

        private static async Task<bool> ProcessContentSubscriber(string mail_command, User currentUser, int contentId, ContentService contentService)
        {
            if (currentUser == null)
            {
                return false;
            }

            //check if user has permission and is participant
            var content = await contentService.Get(contentId);

            if (!content.ContentSubscriber.Select(o => o.userId).Contains(currentUser.userId))
            {
                return false;
            }

            ContentSubscriberService csSvc = new ContentSubscriberService(currentUser.userId, content.contentId);

            bool? rsvp = false;
            bool validCommand = false;

            foreach (var item in acceptedStrings)
            {
                if (mail_command.Contains(item))
                {
                    //Accepted
                    rsvp = true;
                    validCommand = true;
                    break;
                }
            }
            foreach (var item in tentativeStrings)
            {
                if (mail_command.Contains(item))
                {
                    //tentative
                    rsvp = null;
                    validCommand = true;
                    break;
                }
            }
            foreach (var item in declinedStrings)
            {
                if (mail_command.Contains(item))
                {
                    //declined
                    rsvp = false;
                    validCommand = true;
                    break;
                }
            }

            if (validCommand)
            {
                // Try to compute the response for a valid command.
                return await csSvc.RSVPResponse(rsvp);
            }

            // The other commands should be ignored.
            // For instante the "Meeting Forward Notification" command.
            return false;
        }

        public static async Task<bool> ProcessContentSubscriber(string mail_command, string mail_from, int contentId)
        {
            AtlasModelCore _md = new AtlasModelCore();

            var currentUser = _md.User.Where(o => o.email == mail_from).FirstOrDefault();
            if (currentUser == null)
            {
                return false;
            }


            //get the content
            ContentService svc = new ContentService(currentUser.userId);

            //check if user has permission and is participant
            var content = await svc.Get(contentId);

            if (!content.ContentSubscriber.Select(o => o.userId).Contains(currentUser.userId))
            {
                return false;
            }

            ContentSubscriberService csSvc = new ContentSubscriberService(currentUser.userId, content.contentId);

            bool? rsvp = false;
            bool validCommand = false;

            foreach (var item in acceptedStrings)
            {
                if (mail_command.Contains(item))
                {
                    //Accepted
                    rsvp = true;
                    validCommand = true;
                    break;
                }
            }
            foreach (var item in tentativeStrings)
            {
                if (mail_command.Contains(item))
                {
                    //tentative
                    rsvp = null;
                    validCommand = true;
                    break;
                }
            }
            foreach (var item in declinedStrings)
            {
                if (mail_command.Contains(item))
                {
                    //declined
                    rsvp = false;
                    validCommand = true;
                    break;
                }
            }
            foreach (var item in newTimeStrings)
            {
                if (mail_command.Contains(item))
                {
                    // New Time Proposed
                    //todo: alert createUser that particpant proposed a new time
                }
            }

            if (validCommand)
            {
                // Try to compute the response for a valid command.
                return await csSvc.RSVPResponse(rsvp);
            }

            // The other commands should be ignored.
            // For instante the "Meeting Forward Notification" command.
            return false;
        }

        private static async Task<bool> ProcessContentGuest(string mail_command, string mail_from, int? contentId)
        {
            if (!contentId.HasValue)
                return false;

            bool? rsvp = false;
            bool validCommand = false;

            foreach (var item in acceptedStrings)
            {
                if (mail_command.Contains(item))
                {
                    //Accepted
                    rsvp = true;
                    validCommand = true;
                    break;
                }
            }
            foreach (var item in tentativeStrings)
            {
                if (mail_command.Contains(item))
                {
                    //tentative
                    rsvp = null;
                    validCommand = true;
                    break;
                }
            }
            foreach (var item in declinedStrings)
            {
                if (mail_command.Contains(item))
                {
                    //declined
                    rsvp = false;
                    validCommand = true;
                    break;
                }
            }
            foreach (var item in newTimeStrings)
            {
                if (mail_command.Contains(item))
                {
                    // New Time Proposed
                    //todo: alert createUser that particpant proposed a new time
                }
            }

            if (!validCommand) { return false; }


            using (AtlasModelCore _md = new AtlasModelCore())
            {
                foreach (var cg in _md.ContentGuest.Where(p => p.guestMail == mail_from && (p.contentId == contentId || (p.Content.parentContentId.HasValue && p.Content.parentContentId == contentId))).ToList())
                {
                    cg.rsvp = rsvp;
                    cg.rsvpDate = DateTime.UtcNow;
                    _md.Entry(cg).State = EntityState.Modified;
                }

                await _md.SaveChangesAsync();
            }

            return true;
        }

        private static async Task<bool> ProcessMeetingAgendaItem(string mail_command, string mail_from, int? contentId)
        {
            if (!contentId.HasValue)
                return false;

            bool? rsvp = false;
            bool validCommand = false;

            foreach (var item in acceptedStrings)
            {
                if (mail_command.Contains(item))
                {
                    //Accepted
                    rsvp = true;
                    validCommand = true;
                    break;
                }
            }
            foreach (var item in tentativeStrings)
            {
                if (mail_command.Contains(item))
                {
                    //tentative
                    rsvp = null;
                    validCommand = true;
                    break;
                }
            }
            foreach (var item in declinedStrings)
            {
                if (mail_command.Contains(item))
                {
                    //declined
                    rsvp = false;
                    validCommand = true;
                    break;
                }
            }
            foreach (var item in newTimeStrings)
            {
                if (mail_command.Contains(item))
                {
                    // New Time Proposed
                    //todo: alert createUser that particpant proposed a new time
                }
            }

            if (!validCommand) { return false; }

            using (AtlasModelCore _md = new AtlasModelCore())
            {
                foreach (var mai in _md.MeetingAgendaItem.Where(p => p.guestMail == mail_from && (p.contentId == contentId || p.Content.parentContentId == contentId)).ToList())
                {
                    mai.guestRsvp = rsvp;
                    mai.guestRsvpDate = DateTime.UtcNow;

                    _md.Entry(mai).State = EntityState.Modified;
                }

                await _md.SaveChangesAsync();
            }

            return true;
        }
        public static async System.Threading.Tasks.Task<HttpResponseMessage> ExecuteOldParseMailStandard(string contentId_string, bool hasRRULE, bool hasRecurrenceId, ParseMail email)
        {
            int contentId;
            if (Int32.TryParse(contentId_string, out contentId))
            {
                if (hasRRULE && !hasRecurrenceId)
                {
                    // Respond to a master recurring meeting invite
                    await ParseRecurringRSVP(email, contentId);
                }
                else
                {
                    await ParseRSVPByContentId(email, contentId);
                }
                return new HttpResponseMessage(HttpStatusCode.OK);
            }
            return new HttpResponseMessage(HttpStatusCode.BadRequest);
        }
        public static async System.Threading.Tasks.Task<HttpResponseMessage> ExecuteNewParseMailStandard(string contentId_string, ParseMail email)
        {
            int childContentId;
            var lastId = contentId_string.Split('_').LastOrDefault();
            if (Int32.TryParse(lastId, out childContentId))
            {
                await ParseRSVPByContentId(email, childContentId);
                return new HttpResponseMessage(HttpStatusCode.OK);
            }
            return new HttpResponseMessage(HttpStatusCode.BadRequest);
        }
    }
}
