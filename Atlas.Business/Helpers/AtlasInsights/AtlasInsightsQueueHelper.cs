using Atlas.CrossCutting.DTO.AtlasInsights;
using Atlas.Data.Entities.AtlasInsights;
using Atlas.Data.Repository;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Atlas.Business.Helpers.AtlasInsights
{
    public class AtlasInsightsQueueHelper
    {
        private readonly ILogger _logger;
        private readonly InsightsRepository _insightsRepository;
        private readonly Dictionary<(string ActivityType, string ContentType), Func<AtlasInsightsActivityPayload, Task>> _activityProcessors;
        private const int initialViewCount = 1;

        private static class ActivityTypes
        {
            public const string Created = "CREATED";
            public const string Updated = "UPDATED";
            public const string Deleted = "DELETED";
            public const string StatusReady = "STATUS_READY";
            public const string StatusClosed = "STATUS_CLOSED";
            public const string CommentAdd = "COMMENT_ADD";
            public const string CommentDelete = "COMMENT_DELETE";
            public const string CommentUndelete = "COMMENT_UNDELETE";
            public const string BluebookView = "BLUEBOOK_VIEW";
            public const string SubscribersUpdated = "SUBSCRIBER_UPDATE";
            public const string SubscriberAdd = "SUBSCRIBER_ADD";
            public const string BluebookPagesUpdated = "BLUEBOOK_PAGES_UPDATE";
            public const string AttachmentDelete = "ATTACHMENT_DELETE";
            public const string AttachmentUndelete = "ATTACHMENT_UNDELETE";
            public const string AttachmentView = "ATTACHMENT_VIEW";
            public const string PollReportView = "POLL_REPORT_VIEW";
            public const string ESignatureRequested = "ESIGNATURE_REQUEST";
            public const string ESignatureClosed = "ESIGNATURE_REQUEST_CLOSED";
            public const string DSignatureRequested = "DIGITAL_SIGNATURE_REQUEST";
            public const string DSignatureClosed = "DIGITAL_SIGNATURE_REQUEST_CLOSED";
            public const string SignatureCancelled = "SIGNATURE_REQUEST_CANCELLED";
            public const string Published = "STATUS_PUBLISHED";
            public const string Unpublished = "STATUS_UNPUBLISHED";
        }

        private static class ContentTypes
        {
            public const string Meeting = "MEETING";
            public const string Poll = "POLL";
            public const string MeetingAgendaItem = "MEETINGAGENDAITEM";
            public const string MeetingMinute = "MEETINGMINUTE";
        }

        private static class SignatureStatuses
        {
            public const string Open = "OPEN";
            public const string Closed = "CLOSED";
            public const string Cancelled = "CANCELLED";
        }

        private static class LogMessages
        {
            public const string ProcessingActivity = "Processing activity: Type={ActivityType}, ContentType={ContentType}, ContentId={ContentId}";
            public const string ActivityProcessed = "Activity processed successfully: Type={ActivityType}, ContentType={ContentType}, ContentId={ContentId}";
            public const string NoActivityMapping = "Could not map operation for activity type '{ActivityType}' and content type '{ContentType}'. Skipping it...";
            public const string EntityNotFound = "{EntityType} not found for Id={EntityId}";
            public const string MethodEntry = "Entering method {MethodName} with ContentId={ContentId}";
            public const string MethodExit = "Exiting method {MethodName} with ContentId={ContentId}";
            public const string ExceptionOccurred = "Exception occurred in {MethodName}: {ExceptionMessage}";
            public const string EntityCreated = "Created {EntityType} with Id={EntityId}";
            public const string EntityUpdated = "Updated {EntityType} with Id={EntityId}";
            public const string EntityDeleted = "Deleted {EntityType} with Id={EntityId}";
            public const string ViewCountUpdated = "Updated view count for {EntityType} with Id={EntityId}, New Count={ViewCount}";
            public const string InvalidOperationDetected = "Invalid operation detected: {Message}";
        }

        public AtlasInsightsQueueHelper(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _logger.LogInformation("Initializing AtlasInsightsQueueHelper");

            _insightsRepository = new InsightsRepository();

            _activityProcessors = new Dictionary<(string, string), Func<AtlasInsightsActivityPayload, Task>>
                        {
                            // Meeting
                            { (ActivityTypes.Created, ContentTypes.Meeting), ProcessMeetingCreatedAsync },
                            { (ActivityTypes.Updated, ContentTypes.Meeting), ProcessMeetingUpdatedAsync },
                            { (ActivityTypes.Deleted, ContentTypes.Meeting), ProcessMeetingDeletedAsync }, // review meeting minute implications
                            { (ActivityTypes.StatusReady, ContentTypes.Meeting), ProcessMeetingUpdatedAsync },
                            { (ActivityTypes.StatusClosed, ContentTypes.Meeting), ProcessMeetingUpdatedAsync },
                            { (ActivityTypes.SubscribersUpdated, ContentTypes.Meeting), ProcessMeetingUpdatedAsync },
                            { (ActivityTypes.SubscriberAdd, ContentTypes.Meeting), ProcessMeetingUpdatedAsync },
                            { (ActivityTypes.BluebookPagesUpdated, ContentTypes.Meeting), ProcessMeetingBluebookUpdatedAsync },
                            { (ActivityTypes.BluebookView, ContentTypes.Meeting), ProcessBluebookViewAsync },
                            // Resolution
                            { (ActivityTypes.Created, ContentTypes.Poll), ProcessResolutionCreatedAsync },
                            { (ActivityTypes.Updated, ContentTypes.Poll), ProcessResolutionUpdatedAsync },
                            { (ActivityTypes.Deleted, ContentTypes.Poll), ProcessResolutionDeletedAsync },
                            { (ActivityTypes.AttachmentView, ContentTypes.Poll), ProcessMaterialViewAsync },
                            { (ActivityTypes.PollReportView, ContentTypes.Poll), ProcessMaterialViewAsync },
                            { (ActivityTypes.AttachmentDelete, ContentTypes.Poll), payload => ProcessMaterialViewStatusAsync(payload, true) },
                            { (ActivityTypes.AttachmentUndelete, ContentTypes.Poll), payload => ProcessMaterialViewStatusAsync(payload, false) },
                            { (ActivityTypes.CommentAdd, ContentTypes.Poll), ProcessContentCommentUpdate },
                            { (ActivityTypes.CommentDelete, ContentTypes.Poll), ProcessContentCommentUpdate },
                            { (ActivityTypes.CommentUndelete, ContentTypes.Poll), ProcessContentCommentUpdate },
                            // Agenda
                            { (ActivityTypes.Created, ContentTypes.MeetingAgendaItem), ProcessMeetingUpdatedAsync },
                            { (ActivityTypes.Updated, ContentTypes.MeetingAgendaItem), ProcessMeetingUpdatedAsync },
                            { (ActivityTypes.Deleted, ContentTypes.MeetingAgendaItem), ProcessMeetingUpdatedAsync },
                            { (ActivityTypes.AttachmentView, ContentTypes.MeetingAgendaItem), ProcessMaterialViewAsync },
                            { (ActivityTypes.AttachmentDelete, ContentTypes.MeetingAgendaItem), payload => ProcessMaterialViewStatusAsync(payload, true)},
                            { (ActivityTypes.AttachmentUndelete, ContentTypes.MeetingAgendaItem), payload => ProcessMaterialViewStatusAsync(payload, false)},
                            { (ActivityTypes.SubscribersUpdated, ContentTypes.MeetingAgendaItem), ProcessMeetingUpdatedAsync },
                            { (ActivityTypes.CommentAdd, ContentTypes.MeetingAgendaItem), ProcessContentCommentUpdate },
                            { (ActivityTypes.CommentDelete, ContentTypes.MeetingAgendaItem), ProcessContentCommentUpdate },
                            { (ActivityTypes.CommentUndelete, ContentTypes.MeetingAgendaItem), ProcessContentCommentUpdate },
                            // Minute
                            { (ActivityTypes.Published, ContentTypes.MeetingMinute), ProcessMeetingMinuteUpdate },
                            { (ActivityTypes.Unpublished, ContentTypes.MeetingMinute), ProcessMeetingMinuteUpdate },
                            { (ActivityTypes.ESignatureRequested, ContentTypes.MeetingMinute), ProcessMinuteSignatureStatusChange },
                            { (ActivityTypes.ESignatureClosed, ContentTypes.MeetingMinute), ProcessMinuteSignatureStatusChange },
                            { (ActivityTypes.DSignatureRequested, ContentTypes.MeetingMinute), ProcessMinuteSignatureStatusChange },
                            { (ActivityTypes.DSignatureClosed, ContentTypes.MeetingMinute), ProcessMinuteSignatureStatusChange },
                            { (ActivityTypes.SignatureCancelled, ContentTypes.MeetingMinute), ProcessMinuteSignatureStatusChange },
                        };

            _logger.LogInformation("AtlasInsightsQueueHelper initialized with {ProcessorCount} activity processors", _activityProcessors.Count);
        }

        public async Task ProcessActivityAsync(AtlasInsightsActivityPayload payload)
        {
            if (payload == null)
            {
                _logger.LogError("Cannot process null activity payload");
                return;
            }

            var activityType = payload.ActivityType?.ToUpperInvariant() ?? string.Empty;
            var contentType = payload.ContentType?.ToUpperInvariant() ?? string.Empty;
            var contentId = payload.ContentId;

            _logger.LogInformation(LogMessages.ProcessingActivity, activityType, contentType, contentId);

            try
            {
                var tupleOperationKey = (activityType, contentType);

                if (_activityProcessors.TryGetValue(tupleOperationKey, out var processor))
                {
                    await processor(payload);
                    _logger.LogInformation(LogMessages.ActivityProcessed, activityType, contentType, contentId);
                }
                else
                {
                    _logger.LogWarning(LogMessages.NoActivityMapping, activityType, contentType);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, LogMessages.ExceptionOccurred, nameof(ProcessActivityAsync), ex.Message);
                _logger.LogError("Additional context: ActivityType={ActivityType}, ContentType={ContentType}, ContentId={ContentId}",
                    activityType, contentType, contentId);
                throw;
            }
        }

        //
        // Meeting
        //

        private async Task ProcessMeetingCreatedAsync(AtlasInsightsActivityPayload payload)
        {
            var methodName = nameof(ProcessMeetingCreatedAsync);
            int meetingId = payload.ContentId;
            _logger.LogInformation(LogMessages.MethodEntry, methodName, meetingId);

            try
            {
                var meeting = await _insightsRepository.GetMeetingInfo(meetingId);

                if (meeting == null)
                {
                    _logger.LogWarning(LogMessages.EntityNotFound, "Meeting", meetingId);
                    return;
                }

                _logger.LogDebug("Retrieved meeting info: MeetingId={MeetingId}, WorkgroupId={WorkgroupId}, Duration={Duration}, Participants={Participants}",
                    meeting.ContentId, meeting.WorkgroupId, meeting.MeetingDuration, meeting.TotalParticipants);

                int totalDurationMinutes = meeting.MeetingDuration;
                int totalParticipants = meeting.TotalParticipants;
                int totalAgendas = meeting.TotalChildAgendas;

                var insightsMeeting = new InsightsMeeting
                {
                    ContentId = meeting.ContentId,
                    WorkgroupId = meeting.WorkgroupId,
                    MeetingDate = meeting.MeetingDate,
                    TotalDurationMinutes = totalDurationMinutes,
                    TotalParticipants = totalParticipants,
                    TotalAgendas = totalAgendas,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                await _insightsRepository.AddInsightsMeetingAsync(insightsMeeting);
                _logger.LogInformation(LogMessages.EntityCreated, "InsightsMeeting", insightsMeeting.ContentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, LogMessages.ExceptionOccurred, methodName, ex.Message);
                _logger.LogError("Failed to process meeting creation for MeetingId={MeetingId}", meetingId);
                throw;
            }
            finally
            {
                _logger.LogInformation(LogMessages.MethodExit, methodName, meetingId);
            }
        }

        private async Task ProcessMeetingUpdatedAsync(AtlasInsightsActivityPayload payload)
        {
            var methodName = nameof(ProcessMeetingUpdatedAsync);
            int meetingId = payload.ContentType.ToUpper() == ContentTypes.MeetingAgendaItem ?
                payload.ParentContentId ?? payload.ContentId :
                payload.ContentId;

            _logger.LogInformation(LogMessages.MethodEntry, methodName, meetingId);
            _logger.LogDebug("Determined meetingId={MeetingId} from payload ContentType={ContentType}, ContentId={ContentId}, ParentContentId={ParentContentId}",
                meetingId, payload.ContentType, payload.ContentId, payload.ParentContentId);

            try
            {
                var insightsMeeting = await _insightsRepository.GetInsightsMeeting(meetingId);

                if (insightsMeeting == null)
                {
                    _logger.LogWarning(LogMessages.EntityNotFound, "InsightsMeeting", meetingId);
                    return;
                }

                var meeting = await _insightsRepository.GetMeetingInfo(insightsMeeting.ContentId);

                if (meeting == null)
                {
                    _logger.LogWarning(LogMessages.EntityNotFound, "Meeting", insightsMeeting.ContentId);
                    return;
                }

                _logger.LogDebug("Before update: InsightsMeeting with Id={MeetingId}, MeetingDate={MeetingDate}, Duration={Duration}, Participants={Participants}",
                    insightsMeeting.ContentId, insightsMeeting.MeetingDate, insightsMeeting.TotalDurationMinutes, insightsMeeting.TotalParticipants);

                insightsMeeting.MeetingDate = meeting.MeetingDate;
                insightsMeeting.TotalDurationMinutes = meeting.MeetingDuration;
                insightsMeeting.TotalParticipants = meeting.TotalParticipants;
                insightsMeeting.TotalAgendas = meeting.TotalChildAgendas;
                insightsMeeting.UpdatedAt = DateTime.UtcNow;

                await _insightsRepository.UpdateInsightsMeetingAsync(insightsMeeting);

                _logger.LogInformation(LogMessages.EntityUpdated, "InsightsMeeting", insightsMeeting.ContentId);
                _logger.LogDebug("After update: InsightsMeeting with Id={MeetingId}, MeetingDate={MeetingDate}, Duration={Duration}, Participants={Participants}",
                    insightsMeeting.ContentId, insightsMeeting.MeetingDate, insightsMeeting.TotalDurationMinutes, insightsMeeting.TotalParticipants);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, LogMessages.ExceptionOccurred, methodName, ex.Message);
                _logger.LogError("Failed to process meeting update for MeetingId={MeetingId}", meetingId);
                throw;
            }
            finally
            {
                _logger.LogInformation(LogMessages.MethodExit, methodName, meetingId);
            }
        }

        private async Task ProcessMeetingBluebookUpdatedAsync(AtlasInsightsActivityPayload payload)
        {
            var methodName = nameof(ProcessMeetingBluebookUpdatedAsync);
            int meetingId = payload.ContentId;
            _logger.LogInformation(LogMessages.MethodEntry, methodName, meetingId);

            try
            {
                var insightsMeeting = await _insightsRepository.GetInsightsMeeting(meetingId);

                if (insightsMeeting == null)
                {
                    _logger.LogWarning(LogMessages.EntityNotFound, "InsightsMeeting", meetingId);
                    return;
                }

                _logger.LogDebug("Retrieved insights meeting: MeetingId={MeetingId}, CurrentBlueBookPages={CurrentPages}",
                    insightsMeeting.ContentId, insightsMeeting.TotalBlueBookPages);

                var bluebookData = JObject.Parse(payload.ContentData);
                var bluebookTotalPages = bluebookData.GetValue("bluebookTotalPages")?.ToObject<int>() ?? 0;

                _logger.LogDebug("Updating bluebook pages: MeetingId={MeetingId}, OldPages={OldPages}, NewPages={NewPages}",
                    insightsMeeting.ContentId, insightsMeeting.TotalBlueBookPages, bluebookTotalPages);

                insightsMeeting.TotalBlueBookPages = bluebookTotalPages;
                await _insightsRepository.UpdateInsightsMeetingAsync(insightsMeeting);

                _logger.LogInformation(LogMessages.EntityUpdated, "InsightsMeeting", insightsMeeting.ContentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, LogMessages.ExceptionOccurred, methodName, ex.Message);
                _logger.LogError("Failed to process bluebook update for MeetingId={MeetingId}", meetingId);
                throw;
            }
            finally
            {
                _logger.LogInformation(LogMessages.MethodExit, methodName, meetingId);
            }
        }

        private async Task ProcessMeetingDeletedAsync(AtlasInsightsActivityPayload payload)
        {
            var methodName = nameof(ProcessMeetingDeletedAsync);
            int meetingId = payload.ContentId;
            _logger.LogInformation(LogMessages.MethodEntry, methodName, meetingId);

            try
            {
                var insightsMeeting = await _insightsRepository.GetInsightsMeeting(payload.ContentId);

                if (insightsMeeting == null)
                {
                    _logger.LogWarning(LogMessages.EntityNotFound, "InsightsMeeting", meetingId);
                    return;
                }

                _logger.LogDebug("Deleting InsightsMeeting: Id={MeetingId}, WorkgroupId={WorkgroupId}",
                    insightsMeeting.ContentId, insightsMeeting.WorkgroupId);

                await _insightsRepository.DeleteInsightsMeetingAsync(insightsMeeting);
                _logger.LogInformation(LogMessages.EntityDeleted, "InsightsMeeting", insightsMeeting.ContentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, LogMessages.ExceptionOccurred, methodName, ex.Message);
                _logger.LogError("Failed to process meeting deletion for MeetingId={MeetingId}", meetingId);
                throw;
            }
            finally
            {
                _logger.LogInformation(LogMessages.MethodExit, methodName, meetingId);
            }
        }

        //
        // Resolution
        //

        private async Task ProcessResolutionCreatedAsync(AtlasInsightsActivityPayload payload)
        {
            var methodName = nameof(ProcessResolutionCreatedAsync);
            int resolutionId = payload.ContentId;
            _logger.LogInformation(LogMessages.MethodEntry, methodName, resolutionId);

            try
            {
                var resolutionInfo = await _insightsRepository.GetResolutionInfo(resolutionId);

                if (resolutionInfo is null)
                {
                    _logger.LogWarning(LogMessages.EntityNotFound, "ResolutionInfo", resolutionId);
                    return;
                }

                _logger.LogDebug("Retrieved resolution info: ResolutionId={ResolutionId}, WorkgroupId={WorkgroupId}, MeetingId={MeetingId}",
                    resolutionInfo.ContentId, resolutionInfo.WorkgroupId, resolutionInfo.InsightsMeetingId);

                var insightsResolution = new InsightsResolution
                {
                    ContentId = resolutionInfo.ContentId,
                    WorkgroupId = resolutionInfo.WorkgroupId,
                    InsightsMeetingId = resolutionInfo.InsightsMeetingId,
                    ResolutionDate = resolutionInfo.DueDate,
                    CreatedAt = DateTime.UtcNow
                };

                await _insightsRepository.AddInsightsResolutionAsync(insightsResolution);
                _logger.LogInformation(LogMessages.EntityCreated, "InsightsResolution", insightsResolution.ContentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, LogMessages.ExceptionOccurred, methodName, ex.Message);
                _logger.LogError("Failed to process resolution creation for ResolutionId={ResolutionId}", resolutionId);
                throw;
            }
            finally
            {
                _logger.LogInformation(LogMessages.MethodExit, methodName, resolutionId);
            }
        }

        private async Task ProcessResolutionUpdatedAsync(AtlasInsightsActivityPayload payload)
        {
            var methodName = nameof(ProcessResolutionUpdatedAsync);
            int resolutionId = payload.ContentId;
            _logger.LogInformation(LogMessages.MethodEntry, methodName, resolutionId);

            try
            {
                var insightsResolution = await _insightsRepository.GetInsightsResolutionByContentId(resolutionId);

                if (insightsResolution == null)
                {
                    _logger.LogWarning(LogMessages.EntityNotFound, "InsightsResolution", resolutionId);
                    return;
                }

                var resolutionInfo = await _insightsRepository.GetResolutionInfo(resolutionId);
                if (resolutionInfo == null)
                {
                    _logger.LogWarning(LogMessages.EntityNotFound, "ResolutionInfo", resolutionId);
                    return;
                }

                if (resolutionInfo.DueDate != insightsResolution.ResolutionDate)
                {
                    _logger.LogDebug("Resolution date changed: Old={OldDate}, New={NewDate}",
                        insightsResolution.ResolutionDate, resolutionInfo.DueDate);

                    insightsResolution.ResolutionDate = resolutionInfo.DueDate;
                    insightsResolution.UpdatedAt = DateTime.UtcNow;

                    await _insightsRepository.UpdateInsightsResolutionAsync(insightsResolution);
                    _logger.LogInformation(LogMessages.EntityUpdated, "InsightsResolution", insightsResolution.ContentId);
                }
                else
                {
                    _logger.LogDebug("No changes detected for ResolutionId={ResolutionId}, skipping update", resolutionId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, LogMessages.ExceptionOccurred, methodName, ex.Message);
                _logger.LogError("Failed to process resolution update for ResolutionId={ResolutionId}", resolutionId);
                throw;
            }
            finally
            {
                _logger.LogInformation(LogMessages.MethodExit, methodName, resolutionId);
            }
        }

        private async Task ProcessResolutionDeletedAsync(AtlasInsightsActivityPayload payload)
        {
            var methodName = nameof(ProcessResolutionDeletedAsync);
            int resolutionId = payload.ContentId;
            _logger.LogInformation(LogMessages.MethodEntry, methodName, resolutionId);

            try
            {
                var insightsResolution = await _insightsRepository.GetInsightsResolutionByContentId(resolutionId);

                if (insightsResolution is null)
                {
                    _logger.LogWarning(LogMessages.EntityNotFound, "InsightsResolution", resolutionId);
                    return;
                }

                _logger.LogDebug("Deleting resolution: ResolutionId={ResolutionId}, WorkgroupId={WorkgroupId}",
                    insightsResolution.ContentId, insightsResolution.WorkgroupId);

                await _insightsRepository.ResolutionDeleteAsync(insightsResolution);
                _logger.LogInformation(LogMessages.EntityDeleted, "InsightsResolution", insightsResolution.ContentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, LogMessages.ExceptionOccurred, methodName, ex.Message);
                _logger.LogError("Failed to process resolution deletion for ResolutionId={ResolutionId}", resolutionId);
                throw;
            }
            finally
            {
                _logger.LogInformation(LogMessages.MethodExit, methodName, resolutionId);
            }
        }

        //
        // Bluebook Views
        //

        private async Task ProcessBluebookViewAsync(AtlasInsightsActivityPayload payload)
        {
            var methodName = nameof(ProcessBluebookViewAsync);
            int bluebookContentId = payload.ContentId;
            int userId = payload.ActivityUserId;

            _logger.LogInformation(LogMessages.MethodEntry, methodName, bluebookContentId);
            _logger.LogDebug("Processing bluebook view: BluebookId={BluebookId}, UserId={UserId}", bluebookContentId, userId);

            try
            {
                BluebookInfoDTO insightsBluebookFreshData = await _insightsRepository.GetInsightsBlueBookInfo(bluebookContentId, userId);

                if (insightsBluebookFreshData == null)
                {
                    _logger.LogError(LogMessages.EntityNotFound, "BlueBookInfo", bluebookContentId);
                    _logger.LogError("Cannot process bluebook view without bluebook info");
                    return;
                }

                var isNewEntryOperation = false;
                var currrentInsightsBluebook = await _insightsRepository.GetInsightsBlueBookViews(userId, bluebookContentId);

                if (currrentInsightsBluebook == null)
                {
                    _logger.LogDebug("No existing bluebook view record found, creating new one");
                    isNewEntryOperation = true;
                }

                var newTotalViewsCount = insightsBluebookFreshData.TotalViews > 0 ? insightsBluebookFreshData.TotalViews : initialViewCount;

                if (isNewEntryOperation)
                {
                    currrentInsightsBluebook = new InsightsBlueBookViews()
                    {
                        ContentId = insightsBluebookFreshData.ContentId,
                        WorkgroupId = insightsBluebookFreshData.WorkgroupId,
                        InsightsMeetingId = insightsBluebookFreshData.InsightsMeetingId,
                        UserId = userId,
                        TotalViews = newTotalViewsCount,
                        CreatedAt = DateTime.UtcNow
                    };

                    await _insightsRepository.AddInsightsBlueBookViews(currrentInsightsBluebook);
                    _logger.LogInformation(LogMessages.EntityCreated, "InsightsBlueBookViews", currrentInsightsBluebook.ContentId);
                    _logger.LogDebug("Initial view count set to {ViewCount}", currrentInsightsBluebook.TotalViews);
                    return;
                }

                currrentInsightsBluebook.TotalViews = newTotalViewsCount;
                await _insightsRepository.UpdateInsightsBlueBookViews(currrentInsightsBluebook);
                _logger.LogInformation(LogMessages.ViewCountUpdated, "BlueBook", bluebookContentId, newTotalViewsCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, LogMessages.ExceptionOccurred, methodName, ex.Message);
                _logger.LogError("Failed to process bluebook view for BluebookId={BluebookId}, UserId={UserId}", bluebookContentId, userId);
                throw;
            }
            finally
            {
                _logger.LogInformation(LogMessages.MethodExit, methodName, bluebookContentId);
            }
        }

        //
        // Material View
        //

        private async Task ProcessMaterialViewAsync(AtlasInsightsActivityPayload payload)
        {
            var methodName = nameof(ProcessMaterialViewAsync);
            int contentId = payload.ContentId;
            int userId = payload.ActivityUserId;
            int attachmentId = payload.AttachmentId ?? 0;
            string contentType = payload.ContentType;

            _logger.LogInformation(LogMessages.MethodEntry, methodName, contentId);
            _logger.LogDebug("Processing material view: ContentId={ContentId}, UserId={UserId}, AttachmentId={AttachmentId}, ContentType={ContentType}",
                contentId, userId, attachmentId, contentType);

            try
            {
                if (!payload.AttachmentId.HasValue)
                {
                    _logger.LogWarning("Material view processing requires an attachment ID, but none was provided. ContentId={ContentId}", contentId);
                    return;
                }

                MaterialViewLogsInfoDTO insightsMaterialViewsFreshData = await _insightsRepository.GetInsightsMaterialViewLogsInfo(contentId, userId, attachmentId, contentType);

                if (insightsMaterialViewsFreshData == null)
                {
                    _logger.LogError(LogMessages.EntityNotFound, "MaterialViewInfo", contentId);
                    _logger.LogError("Cannot process material view without material view info for ContentId={ContentId}, AttachmentId={AttachmentId}",
                        contentId, attachmentId);
                    return;
                }

                bool isNewEntryOperation = false;
                InsightsMaterialViewLogs currentInsightsMaterialView = await _insightsRepository.GetInsightsMaterialViewLogs(userId, contentId, attachmentId);

                if (currentInsightsMaterialView == null)
                {
                    _logger.LogDebug("No existing material view record found, creating new one");
                    isNewEntryOperation = true;
                }

                if (isNewEntryOperation)
                {
                    currentInsightsMaterialView = new InsightsMaterialViewLogs()
                    {
                        WorkgroupId = insightsMaterialViewsFreshData.WorkgroupId,
                        InsightsMeetingId = insightsMaterialViewsFreshData.InsightsMeetingId,
                        UserId = userId,
                        ParentContentId = contentId,
                        AttachmentId = insightsMaterialViewsFreshData.AttachmentId,
                        TotalViews = insightsMaterialViewsFreshData.TotalViews > 0 ? insightsMaterialViewsFreshData.TotalViews : initialViewCount,
                        CreatedAt = DateTime.UtcNow
                    };

                    await _insightsRepository.AddInsightsMaterialViewLog(currentInsightsMaterialView);
                    _logger.LogInformation(LogMessages.EntityCreated, "InsightsMaterialViewLogs", currentInsightsMaterialView.ParentContentId);
                    _logger.LogDebug("Total views count set to {ViewCount}", currentInsightsMaterialView.TotalViews);
                    return;
                }

                currentInsightsMaterialView.TotalViews = insightsMaterialViewsFreshData.TotalViews;
                _logger.LogDebug("Incrementing view count for material: ContentId={ContentId}, AttachmentId={AttachmentId}, UserId={UserId}, NewCount={ViewCount}",
                    contentId, attachmentId, userId, currentInsightsMaterialView.TotalViews);

                await _insightsRepository.UpdateInsightsMaterialViewViews(currentInsightsMaterialView);
                _logger.LogInformation(LogMessages.ViewCountUpdated, "Material", currentInsightsMaterialView.ParentContentId, currentInsightsMaterialView.TotalViews);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, LogMessages.ExceptionOccurred, methodName, ex.Message);
                _logger.LogError("Failed to process material view for ContentId={ContentId}, AttachmentId={AttachmentId}, UserId={UserId}",
                    contentId, attachmentId, userId);
                throw;
            }
            finally
            {
                _logger.LogInformation(LogMessages.MethodExit, methodName, contentId);
            }
        }

        private async Task ProcessMaterialViewStatusAsync(AtlasInsightsActivityPayload payload, bool setAsDeleted)
        {
            var operationType = setAsDeleted ? "deleted" : "restored";
            var methodName = $"ProcessMaterialViewStatusAsync({operationType})";
            int contentId = payload.ContentId;
            int userId = payload.ActivityUserId;
            int attachmentId = payload.AttachmentId ?? 0;

            _logger.LogInformation(LogMessages.MethodEntry, methodName, contentId);
            _logger.LogDebug($"Processing {operationType} material view: ContentId={{ContentId}}, UserId={{UserId}}, AttachmentId={{AttachmentId}}",
                contentId, userId, attachmentId);

            try
            {
                if (!payload.AttachmentId.HasValue)
                {
                    _logger.LogWarning("Material view processing requires an attachment ID, but none was provided. ContentId={ContentId}", contentId);
                    return;
                }

                var insightsMaterialViewList = await _insightsRepository.GetInsightsMaterialViewLogsList(attachmentId, setAsDeleted);

                if (insightsMaterialViewList == null || !insightsMaterialViewList.Any())
                {
                    _logger.LogWarning(LogMessages.EntityNotFound, "InsightsMaterialViewLogs", contentId);
                    return;
                }

                insightsMaterialViewList.ForEach(imv => imv.Deleted = setAsDeleted);
                await _insightsRepository.UpdateBulkInsightsMaterialViewLogs(insightsMaterialViewList);

                var logMessage = setAsDeleted ? LogMessages.EntityDeleted : LogMessages.EntityUpdated;
                insightsMaterialViewList.ForEach(o => _logger.LogInformation(logMessage, "InsightsMaterialViewLogs", o.ParentContentId));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, LogMessages.ExceptionOccurred, methodName, ex.Message);
                _logger.LogError($"Failed to process {operationType} material view for ContentId={{ContentId}}, AttachmentId={{AttachmentId}}, UserId={{UserId}}",
                    contentId, attachmentId, userId);
                throw;
            }
            finally
            {
                _logger.LogInformation(LogMessages.MethodExit, methodName, contentId);
            }
        }

        //
        // Comment
        //

        private async Task ProcessContentCommentUpdate(AtlasInsightsActivityPayload payload)
        {
            var methodName = nameof(ProcessContentCommentUpdate);
            int commentedResourceContentId = payload.ContentId;
            _logger.LogInformation(LogMessages.MethodEntry, methodName, commentedResourceContentId);

            try
            {
                bool isMeetingContext = payload.ParentContentId.HasValue;

                int? insightsMeetingId = null;

                if (isMeetingContext)
                {
                    int meetingContentId = payload.ParentContentId.Value;

                    InsightsMeeting insightsMeeting = await _insightsRepository.GetInsightsMeeting(meetingContentId);

                    if (insightsMeeting == null)
                    {
                        _logger.LogError(LogMessages.EntityNotFound, "InsightsMeeting", meetingContentId);
                        return;
                    }

                    insightsMeetingId = insightsMeeting.InsightsMeetingId;
                }

                var content = await _insightsRepository.GetContent(commentedResourceContentId);

                if (content == null)
                {
                    _logger.LogError(LogMessages.EntityNotFound, "Content", commentedResourceContentId);
                    return;
                }

                var workgroupId = content.workgroupId;
                var insightCommentEntry = await _insightsRepository.GetInsightsComment(workgroupId, insightsMeetingId, commentedResourceContentId);
                var isNewInsightCommentEntryOperation = insightCommentEntry == null;

                var contentCommentsCount = await _insightsRepository.GetContentCommentsCount(commentedResourceContentId);

                if (isNewInsightCommentEntryOperation)
                {
                    _logger.LogInformation("Insights Comment entry does not exist yet. Creating a new one...");

                    var newInsightCommentEntry = new InsightsComments
                    {
                        CreatedAt = DateTime.UtcNow,
                        InsightsMeetingId = insightsMeetingId,
                        ParentContentId = commentedResourceContentId,
                        TotalComments = contentCommentsCount,
                        WorkgroupId = workgroupId,
                    };

                    await _insightsRepository.AddInsightsContentCommentAsync(newInsightCommentEntry);

                    _logger.LogInformation("Added InsightsContentComment for ContentId={ContentId}", commentedResourceContentId);
                }
                else
                {
                    _logger.LogInformation("Insighs Comment entry already exists. Updating object...");

                    insightCommentEntry.TotalComments = contentCommentsCount;

                    await _insightsRepository.UpdateInsightsContentCommentAsync(insightCommentEntry);

                    _logger.LogInformation("Updated InsightsContentComment for ContentId={ContentId}", commentedResourceContentId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, LogMessages.ExceptionOccurred, methodName, ex.Message);
                _logger.LogError("Failed to process content comment add for ContentId={ContentId}", commentedResourceContentId);
                throw;
            }
            finally
            {
                _logger.LogInformation(LogMessages.MethodExit, methodName, commentedResourceContentId);
            }
        }

        //
        // Minute (within Meeting)
        //

        private async Task ProcessMeetingMinuteUpdate(AtlasInsightsActivityPayload payload)
        {
            var methodName = nameof(ProcessMeetingMinuteUpdate);
            int? meetingContentId = payload.ParentContentId;
            _logger.LogInformation(LogMessages.MethodEntry, methodName, meetingContentId);

            if (!meetingContentId.HasValue)
            {
                _logger.LogError("Cannot process meeting minute update without parent content ID");
                return;
            }

            try
            {
                var insightsMeeting = await _insightsRepository.GetInsightsMeeting(meetingContentId.Value);

                if (insightsMeeting == null)
                {
                    _logger.LogError(LogMessages.EntityNotFound, "InsightsMeeting", meetingContentId.Value);
                    return;
                }

                _logger.LogDebug("Retrieved insights meeting: MeetingId={MeetingId}, WorkgroupId={WorkgroupId}",
                    insightsMeeting.ContentId, insightsMeeting.WorkgroupId);

                var isPublished = await _insightsRepository.IsMeetingMinutePublished(insightsMeeting.ContentId);

                _logger.LogDebug("Setting TotalMinutesPublished={newData} for MeetingId={MeetingId}", isPublished, insightsMeeting.ContentId);
                insightsMeeting.HasPublishedMinute = isPublished;
                insightsMeeting.HasSignedMinute = isPublished ? insightsMeeting.HasSignedMinute : false;
                insightsMeeting.HasPendingSignature = isPublished ? insightsMeeting.HasPendingSignature : false;

                await _insightsRepository.UpdateInsightsMeetingAsync(insightsMeeting);
                _logger.LogInformation("Updated minutes published status for MeetingId={MeetingId}", insightsMeeting.ContentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, LogMessages.ExceptionOccurred, methodName, ex.Message);
                _logger.LogError("Failed to process minute update for MeetingContentID={MeetingContentID}", meetingContentId);
                throw;
            }
            finally
            {
                _logger.LogInformation(LogMessages.MethodExit, methodName, meetingContentId);
            }
        }

        private async Task ProcessMinuteSignatureStatusChange(AtlasInsightsActivityPayload payload)
        {
            var methodName = nameof(ProcessMinuteSignatureStatusChange);
            int? meetingContentId = payload.ParentContentId;

            _logger.LogInformation(LogMessages.MethodEntry, methodName, meetingContentId);
            _logger.LogDebug("Processing signature status change: MeetingContentId={MeetingContentId}", meetingContentId);

            if (!meetingContentId.HasValue)
            {
                _logger.LogError("Cannot process minute signature status change without parent content ID (meeting content ID)");
                return;
            }

            try
            {
                var insightsMeeting = await _insightsRepository.GetInsightsMeeting(meetingContentId.Value);

                if (insightsMeeting == null)
                {
                    _logger.LogError(LogMessages.EntityNotFound, "InsightsMeeting", meetingContentId.Value);
                    return;
                }

                _logger.LogDebug("Retrieved insights meeting: MeetingId={MeetingId}, WorkgroupId={WorkgroupId}",
                    insightsMeeting.ContentId, insightsMeeting.WorkgroupId);

                var signatureRequest = await _insightsRepository.GetMinutesSignatureRequest(insightsMeeting.ContentId);

                insightsMeeting.MinuteSignatureType = signatureRequest.signatureType;
                var updatedSignatureStatus = signatureRequest.status;

                switch (updatedSignatureStatus)
                {
                    case SignatureStatuses.Closed:
                        _logger.LogDebug("Setting signature status to Closed for MeetingId={MeetingId}", insightsMeeting.ContentId);
                        insightsMeeting.HasPendingSignature = false;
                        insightsMeeting.HasSignedMinute = true;
                        insightsMeeting.HasPublishedMinute = false;
                        break;

                    case SignatureStatuses.Open:
                        _logger.LogDebug("Setting signature status to Open for MeetingId={MeetingId}", insightsMeeting.ContentId);
                        insightsMeeting.HasPublishedMinute = false;
                        insightsMeeting.HasSignedMinute = false;
                        insightsMeeting.HasPendingSignature = true;
                        break;

                    case SignatureStatuses.Cancelled:
                        _logger.LogDebug("Setting signature status to Cancelled for MeetingId={MeetingId}", insightsMeeting.ContentId);
                        insightsMeeting.HasPendingSignature = false;
                        insightsMeeting.HasSignedMinute = false;
                        insightsMeeting.HasPublishedMinute = true;
                        break;

                    default:
                        string errorMessage = $"Unhandled signature status: {updatedSignatureStatus}";
                        _logger.LogError(LogMessages.InvalidOperationDetected, errorMessage);
                        throw new InvalidOperationException(errorMessage);
                }

                await _insightsRepository.UpdateInsightsMeetingAsync(insightsMeeting);
                _logger.LogInformation("Updated minutes signature status to {Status} for MeetingId={MeetingId}",
                    updatedSignatureStatus, insightsMeeting.ContentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, LogMessages.ExceptionOccurred, methodName, ex.Message);
                _logger.LogError("Failed to process minute signature status change for ParentContentId={ParentContentId}", meetingContentId);
                throw;
            }
            finally
            {
                _logger.LogInformation(LogMessages.MethodExit, methodName, meetingContentId);
            }
        }
    }
}