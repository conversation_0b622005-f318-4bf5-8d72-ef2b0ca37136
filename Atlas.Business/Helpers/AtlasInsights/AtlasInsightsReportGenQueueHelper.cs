using Atlas.Data.Entities.AtlasInsights;
using Atlas.Data.Repository;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Atlas.Business.Helpers.AtlasInsights
{
    public class AtlasInsightsReportGenQueueHelper
    {
        private readonly ILogger _logger;
        private readonly InsightsRepository _insightsRepository;

        private const int MaxRetryAttempts = 10;
        private const int DefaultMaxDegreeOfParallelism = 3;

        // Status constants
        public const string StatusQueued = "Queued";
        public const string StatusProcessing = "Processing";
        public const string StatusCompleted = "Completed";
        public const string StatusFailed = "Failed";

        public AtlasInsightsReportGenQueueHelper(ILogger logger)
        {
            _logger = logger;
            _insightsRepository = new InsightsRepository();
        }

        private void LogInfoWithID(string msg, int clientId, int workgroupId)
        {
            _logger.LogInformation($"[{clientId}/{workgroupId}] {msg}");
        }

        private async Task<int[]> GetWorkgroupIdsToProcess(int clientId, int[] workgroupIdsFilter, bool isOnlyActiveWorkgroups)
        {
            int[] _targetProcessingWorkgroupIds = { };

            var clientAllWorkgroupIds = await _insightsRepository.GetWorkgroupList(clientId, isOnlyActiveWorkgroups);

            if (workgroupIdsFilter != null && workgroupIdsFilter.Any())
            {
                _targetProcessingWorkgroupIds = clientAllWorkgroupIds
                    .Where(w => workgroupIdsFilter.Contains(w))
                    .ToArray();
            }
            else
            {
                _targetProcessingWorkgroupIds = clientAllWorkgroupIds;
            }

            return _targetProcessingWorkgroupIds;
        }

        // Implement retry logic with exponential backoff
        private async Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, int clientId, int workgroupId, string operationName)
        {
            int retryCount = 0;
            while (true)
            {
                try
                {
                    return await operation();
                }
                catch (Exception ex) when (IsTransientException(ex) && retryCount < MaxRetryAttempts)
                {
                    retryCount++;
                    int delayMs = (int)Math.Pow(2, retryCount) * 500; // Exponential backoff

                    LogInfoWithID($"Transient error during {operationName}, retry {retryCount}/{MaxRetryAttempts} after {delayMs}ms: {ex.Message}",
                        clientId, workgroupId);

                    await Task.Delay(delayMs);
                }
            }
        }

        // Helper method to identify transient SQL exceptions
        private bool IsTransientException(Exception ex)
        {
            if (ex is System.Data.Entity.Core.EntityException ||
                ex is System.Data.SqlClient.SqlException ||
                ex is System.Data.Entity.Infrastructure.DbUpdateException)
            {
                // Look for deadlock error numbers (1205), connection issues, etc.
                return true;
            }

            if (ex.InnerException != null)
            {
                return IsTransientException(ex.InnerException);
            }

            return false;
        }

        // Update an existing InsightsReportRequest record
        private async Task UpdateReportRequest(int requestId, int clientId, int workgroupId, string status, bool processed = false, string errorMessage = null)
        {
            // Truncate error message if it exceeds 250 characters
            if (errorMessage != null && errorMessage.Length > 250)
            {
                errorMessage = errorMessage.Substring(0, 247) + "...";
            }

            var reportRequest = new InsightsReportRequest
            {
                RequestId = requestId,
                ClientId = clientId,
                WorkgroupId = workgroupId,
                Processed = processed,
                Status = status,
                ProcessedDate = processed ? (DateTime?)DateTime.UtcNow : null,
                ErrorMessage = errorMessage,
                HasError = !string.IsNullOrEmpty(errorMessage)
            };

            await ExecuteWithRetryAsync(
                () => _insightsRepository.UpdateInsightsReportRequest(reportRequest),
                clientId, workgroupId, "updating report request");
        }

        private async Task HandleReconcileWorkgroupData(int clientId, int workgroupId, int requestId, string[] reconcileResourceFilter)
        {
            int[] workgroupIds = { workgroupId };
            int cId = clientId;
            int wId = workgroupId;

            var meetingsToUpdate = new List<InsightsMeeting>();
            var meetingsToInsert = new List<InsightsMeeting>();
            var meetingsToDelete = new List<InsightsMeeting>();

            // (comment, resolution, bbView, matView)
            var shouldReconcileComments = reconcileResourceFilter.Contains("comment");
            var shouldReconcileResolutions = reconcileResourceFilter.Contains("resolution");
            var shouldReconcileBlueBookViews = reconcileResourceFilter.Contains("bbView");
            var shouldReconcileMaterialViews = reconcileResourceFilter.Contains("matView");

            try
            {
                await UpdateReportRequest(requestId, clientId, workgroupId, StatusProcessing);

                //
                // Fetch Phase
                //

                LogInfoWithID("Getting existing insight meetings...", cId, wId);
                var existingInsightMeetings = await ExecuteWithRetryAsync(
                    () => _insightsRepository.GetInsightsMeetings(workgroupIds),
                    cId, wId, "getting existing insight meetings");

                LogInfoWithID("Collecting fresh meeting data...", cId, wId);
                var freshInsightMeetingsData = await ExecuteWithRetryAsync(
                    () => _insightsRepository.GetMeetingInfoForReportGen(workgroupIds),
                    cId, wId, "collecting fresh meeting data");

                IEnumerable<InsightsResolution> resolutionFreshEntries = new List<InsightsResolution>();
                IEnumerable<InsightsComments> commentFreshEntries = new List<InsightsComments>();
                IEnumerable<InsightsBlueBookViews> blueBookViewFreshEntries = new List<InsightsBlueBookViews>();
                IEnumerable<InsightsMaterialViewLogs> materialViewFreshEntries = new List<InsightsMaterialViewLogs>();

                LogInfoWithID("Collecting non-root reconciliating data...", cId, wId);

                if (shouldReconcileResolutions)
                {
                    var resolutionFreshData = await ExecuteWithRetryAsync(
                        () => _insightsRepository.GetResolutionInfoForReportGen(workgroupIds),
                        cId, wId, "collecting resolution data");

                    resolutionFreshEntries = resolutionFreshData.Select(r => new InsightsResolution()
                    {
                        WorkgroupId = r.WorkgroupId,
                        ContentId = r.ContentId,
                        InsightsMeetingId = r.InsightsMeetingId,
                        ResolutionDate = r.DueDate,
                        CreatedAt = DateTime.UtcNow
                    }).ToArray();
                }

                if (shouldReconcileComments)
                {
                    var commentFreshData = await ExecuteWithRetryAsync(
                        () => _insightsRepository.GetCommentInfoForReportGen(workgroupIds),
                        cId, wId, "collecting comment data");

                    commentFreshEntries = commentFreshData.Select(c => new InsightsComments()
                    {
                        WorkgroupId = c.WorkgroupId,
                        InsightsMeetingId = c.InsightsMeetingId,
                        ParentContentId = c.ParentContentId,
                        TotalComments = c.TotalComments,
                        CreatedAt = DateTime.UtcNow
                    }).ToArray();
                }

                if (shouldReconcileBlueBookViews)
                {
                    var blueBookViewFreshData = await ExecuteWithRetryAsync(
                        () => _insightsRepository.GetBlueBookViewsInfoForReportGen(workgroupIds),
                        cId, wId, "collecting bluebook views");

                    blueBookViewFreshEntries = blueBookViewFreshData.Select(b => new InsightsBlueBookViews()
                    {
                        WorkgroupId = b.WorkgroupId,
                        InsightsMeetingId = b.InsightsMeetingId,
                        UserId = b.UserId,
                        ContentId = b.ContentId,
                        TotalViews = b.TotalViews,
                        CreatedAt = DateTime.UtcNow
                    }).ToArray();
                }

                if (shouldReconcileMaterialViews)
                {
                    var materialViewFreshData = await ExecuteWithRetryAsync(
                        () => _insightsRepository.GetMaterialViewLogsInfoForReportGen(workgroupIds),
                        cId, wId, "collecting material views");

                    materialViewFreshEntries = materialViewFreshData.Select(m => new InsightsMaterialViewLogs()
                    {
                        WorkgroupId = m.WorkgroupId,
                        InsightsMeetingId = m.InsightsMeetingId,
                        UserId = m.UserId,
                        ParentContentId = m.ParentContentId,
                        AttachmentId = m.AttachmentId,
                        TotalViews = m.TotalViews,
                        CreatedAt = DateTime.UtcNow
                    }).ToArray();
                }

                LogInfoWithID("All required reconciliating data collected successfully", cId, wId);

                //
                // Operational Phase
                //

                foreach (var existing in existingInsightMeetings)
                {
                    var matchingFreshMeeting = freshInsightMeetingsData
                        .FirstOrDefault(fresh => fresh.ContentId == existing.ContentId);

                    if (matchingFreshMeeting != null)
                    {
                        // Update existing meeting with fresh data
                        existing.MeetingDate = matchingFreshMeeting.MeetingDate;
                        existing.TotalDurationMinutes = matchingFreshMeeting.MeetingDuration ?? 0;
                        existing.TotalParticipants = matchingFreshMeeting.TotalParticipants;
                        existing.TotalAgendas = matchingFreshMeeting.TotalAgendas;
                        existing.TotalBlueBookPages = matchingFreshMeeting.TotalBluebookPages;
                        existing.HasPublishedMinute = matchingFreshMeeting.HasPublishedMinute && !matchingFreshMeeting.HasPendingMinuteSignature && !matchingFreshMeeting.HasSignedMinute;
                        existing.HasPendingSignature = matchingFreshMeeting.HasPendingMinuteSignature;
                        existing.HasSignedMinute = matchingFreshMeeting.HasSignedMinute;
                        existing.MinuteSignatureType = matchingFreshMeeting.MinuteSignatureType;
                        existing.UpdatedAt = DateTime.UtcNow;

                        meetingsToUpdate.Add(existing);
                    }
                    else
                    {
                        meetingsToDelete.Add(existing);
                    }
                }

                foreach (var fresh in freshInsightMeetingsData)
                {
                    var matchingExistingMeeting = existingInsightMeetings
                        .FirstOrDefault(existing => existing.ContentId == fresh.ContentId);

                    if (matchingExistingMeeting == null)
                    {
                        // Insert new meeting
                        var newMeeting = new InsightsMeeting()
                        {
                            WorkgroupId = fresh.WorkgroupId,
                            ContentId = fresh.ContentId,
                            MeetingDate = fresh.MeetingDate,
                            TotalDurationMinutes = fresh.MeetingDuration ?? 0,
                            TotalParticipants = fresh.TotalParticipants,
                            TotalAgendas = fresh.TotalAgendas,
                            TotalBlueBookPages = fresh.TotalBluebookPages,
                            HasPublishedMinute = fresh.HasPublishedMinute && !fresh.HasPendingMinuteSignature && !fresh.HasSignedMinute,
                            HasPendingSignature = fresh.HasPendingMinuteSignature,
                            HasSignedMinute = fresh.HasSignedMinute,
                            MinuteSignatureType = fresh.MinuteSignatureType,
                            CreatedAt = DateTime.UtcNow
                        };

                        meetingsToInsert.Add(newMeeting);
                    }
                }

                if (meetingsToDelete.Any())
                {
                    LogInfoWithID("Deleting meetings that do not longer exist...", cId, wId);
                    await ExecuteWithRetryAsync(
                        () => _insightsRepository.DeleteInsightsForMeetings(meetingsToDelete.Select(m => m.InsightsMeetingId).ToArray()),
                        cId, wId, "deleting meetings that no longer exist");
                }
                else
                {
                    LogInfoWithID("No meetings to delete", cId, wId);
                }

                if (meetingsToUpdate.Any())
                {
                    LogInfoWithID("Updating existing meetings...", cId, wId);
                    await ExecuteWithRetryAsync(
                        () => _insightsRepository.UpdateInsightMeetings(meetingsToUpdate),
                        cId, wId, "updating existing meetings");
                }
                else
                {
                    LogInfoWithID("No meetings to update", cId, wId);
                }

                if (meetingsToInsert.Any())
                {
                    LogInfoWithID("Inserting new meetings...", cId, wId);
                    await ExecuteWithRetryAsync(
                        () => _insightsRepository.SaveInsightMeetings(meetingsToInsert),
                        cId, wId, "inserting new meetings");
                }
                else
                {
                    LogInfoWithID("No meetings to insert", cId, wId);
                }

                LogInfoWithID("Deleting previous stored non-root insights data...", cId, wId);
                await ExecuteWithRetryAsync(
                    () => _insightsRepository.DeletePreviousReportData(workgroupIds, reconcileResourceFilter),
                    cId, wId, "deleting previous data");

                if (!resolutionFreshEntries.Any() && !commentFreshEntries.Any() && !blueBookViewFreshEntries.Any() && !materialViewFreshEntries.Any())
                {
                    LogInfoWithID("No insights data to save. Skipping workgroup...", cId, wId);

                    // Even if no data, consider this a successful completion
                    await UpdateReportRequest(requestId, clientId, workgroupId, StatusCompleted, true);

                    return;
                }

                LogInfoWithID("Saving insights data...", cId, wId);
                await ExecuteWithRetryAsync(
                    () => _insightsRepository.SaveInsights(resolutionFreshEntries, commentFreshEntries, blueBookViewFreshEntries, materialViewFreshEntries),
                    cId, wId, "saving insights data");

                LogInfoWithID("Insights data saved successfully", cId, wId);

                // Update status to Completed
                await UpdateReportRequest(requestId, clientId, workgroupId, StatusCompleted, true);
            }
            catch (Exception ex)
            {
                // Update status to Failed with potentially truncated error message
                await UpdateReportRequest(requestId, clientId, workgroupId, StatusFailed, true, ex.Message);

                _logger.LogError($"[{cId}/{wId}] Error processing insights data: {ex.Message}");
                throw new Exception($"Error processing insights data: {ex.Message}", ex);
            }
        }

        private async Task HandleProcessEntireWorkgroup(int clientId, int workgroupId, int requestId)
        {
            int[] workgroupIds = { workgroupId };
            int cId = clientId;
            int wId = workgroupId;

            try
            {
                // Update status to Processing
                await UpdateReportRequest(requestId, clientId, workgroupId, StatusProcessing);

                LogInfoWithID("Collecting meeting data...", cId, wId);
                var meetingInfo = await ExecuteWithRetryAsync(
                    () => _insightsRepository.GetMeetingInfoForReportGen(workgroupIds),
                    cId, wId, "collecting meeting data");

                LogInfoWithID("Meeting data collected", cId, wId);

                var insightsMeetings = meetingInfo.Select(m => new InsightsMeeting()
                {
                    WorkgroupId = m.WorkgroupId,
                    ContentId = m.ContentId,
                    MeetingDate = m.MeetingDate,
                    TotalDurationMinutes = m.MeetingDuration.HasValue ? m.MeetingDuration.Value : 0,
                    TotalParticipants = m.TotalParticipants,
                    TotalAgendas = m.TotalAgendas,
                    TotalBlueBookPages = m.TotalBluebookPages,
                    HasPublishedMinute = m.HasPublishedMinute && !m.HasPendingMinuteSignature && !m.HasSignedMinute,
                    HasPendingSignature = m.HasPendingMinuteSignature,
                    HasSignedMinute = m.HasSignedMinute,
                    MinuteSignatureType = m.MinuteSignatureType,
                    CreatedAt = DateTime.UtcNow
                }).ToArray();

                LogInfoWithID("Deleting previous stored insights data...", cId, wId);
                await ExecuteWithRetryAsync(
                    () => _insightsRepository.DeletePreviousReportData(workgroupIds),
                    cId, wId, "deleting previous data");

                LogInfoWithID("Previous data deleted", cId, wId);

                LogInfoWithID("Saving initial root insight meeting entity...", cId, wId);
                var insightMeetingsInitialSaveSuccess = await ExecuteWithRetryAsync(
                    () => _insightsRepository.SaveInsightMeetings(insightsMeetings),
                    cId, wId, "saving insight meetings");

                LogInfoWithID("Insight Meeting saved successfully", cId, wId);

                // Collect all necessary data before performing more database operations
                LogInfoWithID("Collecting additional data...", cId, wId);

                var resolutionInfo = await ExecuteWithRetryAsync(
                    () => _insightsRepository.GetResolutionInfoForReportGen(workgroupIds),
                    cId, wId, "collecting resolution data");

                var commentInfo = await ExecuteWithRetryAsync(
                    () => _insightsRepository.GetCommentInfoForReportGen(workgroupIds),
                    cId, wId, "collecting comment data");

                var blueBookViewInfo = await ExecuteWithRetryAsync(
                    () => _insightsRepository.GetBlueBookViewsInfoForReportGen(workgroupIds),
                    cId, wId, "collecting bluebook views");

                var materialLogsInfo = await ExecuteWithRetryAsync(
                    () => _insightsRepository.GetMaterialViewLogsInfoForReportGen(workgroupIds),
                    cId, wId, "collecting material views");

                LogInfoWithID("All data collected successfully", cId, wId);

                var insightsResolution = resolutionInfo.Select(r => new InsightsResolution()
                {
                    WorkgroupId = r.WorkgroupId,
                    ContentId = r.ContentId,
                    InsightsMeetingId = r.InsightsMeetingId,
                    ResolutionDate = r.DueDate,
                    CreatedAt = DateTime.UtcNow
                }).ToArray();

                var insightsComment = commentInfo.Select(c => new InsightsComments()
                {
                    WorkgroupId = c.WorkgroupId,
                    InsightsMeetingId = c.InsightsMeetingId,
                    ParentContentId = c.ParentContentId,
                    TotalComments = c.TotalComments,
                    CreatedAt = DateTime.UtcNow
                }).ToArray();

                var insightsBlueBook = blueBookViewInfo.Select(b => new InsightsBlueBookViews()
                {
                    WorkgroupId = b.WorkgroupId,
                    InsightsMeetingId = b.InsightsMeetingId,
                    UserId = b.UserId,
                    ContentId = b.ContentId,
                    TotalViews = b.TotalViews,
                    CreatedAt = DateTime.UtcNow
                }).ToArray();

                var insightsMaterialLogs = materialLogsInfo.Select(m => new InsightsMaterialViewLogs()
                {
                    WorkgroupId = m.WorkgroupId,
                    InsightsMeetingId = m.InsightsMeetingId,
                    UserId = m.UserId,
                    ParentContentId = m.ParentContentId,
                    AttachmentId = m.AttachmentId,
                    TotalViews = m.TotalViews,
                    CreatedAt = DateTime.UtcNow
                }).ToArray();

                if (!insightsResolution.Any() && !insightsComment.Any() && !insightsBlueBook.Any() && !insightsMaterialLogs.Any())
                {
                    LogInfoWithID("No insights data to save. Skipping workgroup...", cId, wId);

                    // Even if no data, we consider this a successful completion
                    await UpdateReportRequest(requestId, clientId, workgroupId, StatusCompleted, true);
                    return;
                }

                LogInfoWithID("Saving insights data...", cId, wId);
                await ExecuteWithRetryAsync(
                    () => _insightsRepository.SaveInsights(insightsResolution, insightsComment, insightsBlueBook, insightsMaterialLogs),
                    cId, wId, "saving insights data");

                LogInfoWithID("Insights data saved successfully", cId, wId);

                // Update status to Completed
                await UpdateReportRequest(requestId, clientId, workgroupId, StatusCompleted, true);
            }
            catch (Exception ex)
            {
                // Update status to Failed with potentially truncated error message
                await UpdateReportRequest(requestId, clientId, workgroupId, StatusFailed, true, ex.Message);

                _logger.LogError($"[{cId}/{wId}] Error processing insights data: {ex.Message}");
                throw new Exception($"Error processing insights data: {ex.Message}", ex);
            }
        }

        private IEnumerable<int[]> BatchWorkgroups(int[] workgroupIds, int batchSize)
        {
            for (int i = 0; i < workgroupIds.Length; i += batchSize)
            {
                yield return workgroupIds.Skip(i).Take(batchSize).ToArray();
            }
        }

        public async Task HandleProcessClientInsightsData(
            string invocationId,
            int clientId,
            int[] workgroupIdsFilter,
            bool isOnlyActiveWorkgroups,
            bool isReconcileOnlyOperation,
            string[] reconcileResourceFilter,
            int maxDegreeOfParallelism = DefaultMaxDegreeOfParallelism)
        {
            var workgroupIds = await GetWorkgroupIdsToProcess(clientId, workgroupIdsFilter, isOnlyActiveWorkgroups);

            if (workgroupIds == null || !workgroupIds.Any())
            {
                _logger.LogWarning($"[{clientId}] No workgroups found for client ID {clientId} " +
                                   $"with filter: {string.Join(", ", workgroupIdsFilter ?? Array.Empty<int>())} " +
                                   $"(only active = {isOnlyActiveWorkgroups})");
                return;
            }

            int total = workgroupIds.Length;
            _logger.LogInformation($"[{clientId}] Processing {total} work‑groups (only active = {isOnlyActiveWorkgroups}) " +
                                   $"for invocation {invocationId}");
            _logger.LogInformation($"[{clientId}] Work‑groups to process: {string.Join(", ", workgroupIds)}");

            // progress counters
            int processed = 0; // successes + failures so far
            int succeeded = 0; // successful completions
            int failed = 0; // failures so far

            void LogProgress(string extra = "")
            {
                // Compute once to avoid races between reads
                var done = Volatile.Read(ref processed);
                var ok = Volatile.Read(ref succeeded);
                var ko = Volatile.Read(ref failed);
                double pct = total == 0 ? 100 : done * 100d / total;
                _logger.LogInformation(
                    $"[{clientId}] Progress {done}/{total} ({pct:F1} %) – OK:{ok} | KO:{ko}{extra}");
            }

            var failures = new ConcurrentBag<(int WorkgroupId, Exception Ex)>();
            var sem = new SemaphoreSlim(maxDegreeOfParallelism);
            var requestIds = new ConcurrentDictionary<int, int>(); // workgroupId -> requestId

            // Create all InsightsReportRequest entries in bulk
            _logger.LogInformation($"[{clientId}] Creating bulk report requests for {total} work‑groups");

            try
            {
                var reportRequests = workgroupIds.Select(wgId => new InsightsReportRequest
                {
                    InvocationTitle = invocationId,
                    ClientId = clientId,
                    WorkgroupId = wgId,
                    Processed = false,
                    Status = StatusQueued,
                    IsReconcileOnlyOperation = isReconcileOnlyOperation,
                    ReconcileOnlyTargetResources = string.Join(",", reconcileResourceFilter),
                    RequestDate = DateTime.UtcNow,
                    ProcessedDate = null,
                    ErrorMessage = null,
                    HasError = false
                }).ToList();

                var workgroupToRequestIdMap = await ExecuteWithRetryAsync(
                    () => _insightsRepository.SaveBulkInsightsReportRequests(reportRequests),
                    clientId, 0, "creating bulk report requests");

                foreach (var kvp in workgroupToRequestIdMap)
                    requestIds[kvp.Key] = kvp.Value;

                _logger.LogInformation($"[{clientId}] Successfully created {requestIds.Count} report requests");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{clientId}] Failed to create bulk report requests: {ex.Message}");
                throw new Exception("Failed to create report requests", ex);
            }

            // Process work‑groups that have successful request entries
            foreach (var batch in BatchWorkgroups(workgroupIds.Where(wId => requestIds.ContainsKey(wId)).ToArray(),
                                                  maxDegreeOfParallelism))
            {
                var tasks = batch.Select(async wgId =>
                {
                    await sem.WaitAsync();
                    try
                    {
                        int requestId = requestIds[wgId];
                        _logger.LogInformation($"[{clientId}/{wgId}] Starting…");

                        if (isReconcileOnlyOperation)
                        {
                            await HandleReconcileWorkgroupData(clientId, wgId, requestId, reconcileResourceFilter);
                        }
                        else
                        {
                            await HandleProcessEntireWorkgroup(clientId, wgId, requestId);
                        }

                        Interlocked.Increment(ref succeeded);
                        _logger.LogInformation($"[{clientId}/{wgId}] Finished successfully");
                    }
                    catch (Exception ex)
                    {
                        failures.Add((wgId, ex));
                        Interlocked.Increment(ref failed);
                        _logger.LogError(ex, $"[{clientId}/{wgId}] Failed: {ex.Message}");
                    }
                    finally
                    {
                        // Update overall progress and release the slot
                        Interlocked.Increment(ref processed);
                        LogProgress();
                        sem.Release();
                    }
                });

                await Task.WhenAll(tasks);
            }

            if (!failures.IsEmpty)
            {
                var summary = $"[{clientId}] Finished with {failures.Count} failure(s): " +
                              string.Join(", ", failures.Select(f => $"{f.WorkgroupId} ({f.Ex.Message})"));
                _logger.LogWarning(summary);

                return;
            }

            LogProgress(" – All work‑groups processed successfully");
        }
    }
}
