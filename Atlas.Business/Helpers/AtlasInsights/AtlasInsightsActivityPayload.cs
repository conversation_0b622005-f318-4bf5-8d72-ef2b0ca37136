using System;

namespace Atlas.Business.Helpers.AtlasInsights
{
    public class AtlasInsightsActivityPayload
    {
        public int ContentId { get; set; }
        public Guid ContentKey { get; set; }
        public string ActivityType { get; set; }
        public string ContentType { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public int ActivityUserId { get; set; }
        public string ContentData { get; set; }
        public int? ParentContentId { get; set; }
        public int? AttachmentId { get; set; }
    }
}
