namespace Atlas.Business.Helpers.AtlasInsights
{
    public class AtlasInsightsReportGenRequestEvent
    {
        public int ClientId { get; set; }
        public int[] WorkgroupIdsFilter { get; set; }
        public string InvocationTitle { get; set; }
        public bool? IsOnlyActiveWorkgroups { get; set; }

        // Allow reconciling only specific resources
        public bool? IsReconcileOnlyOperation { get; set; }

        // At least one of: (comment, resolution, bbView, matView) OBS: Meeting is always re-syncronized
        public string[] ReconcileResourceFilter { get; set; }
    }
}
