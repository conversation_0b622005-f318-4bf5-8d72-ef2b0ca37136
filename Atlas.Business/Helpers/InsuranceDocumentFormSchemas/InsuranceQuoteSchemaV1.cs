namespace Atlas.Business.Helpers.InsuranceDocumentFormSchemas
{
    public class InsuranceQuoteSchemaV1
    {
        public InsuranceFileData formFileData { get; set; }
        public InsuranceFileData[] financialStatementFilesData { get; set; }
        public InsuranceFileData[] bylawsSocialContractFilesData { get; set; }
        public InsuranceFileData[] otherDocumentsFilesData { get; set; }
    }


    public class InsuranceFileData
    {
        public int insDocId { get; set; }
        public int clientId { get; set; }
        public int createUserId { get; set; }
        public string insuranceDocumentTitle { get; set; }
        public string type { get; set; }
        public string documentType { get; set; }
        public string formInsuranceType { get; set; }
        public string attachmentFilename { get; set; }
        public int[] formWorkgroupIds { get; set; }
    }
}
