namespace Atlas.Business.Helpers.InsuranceDocumentFormSchemas.SchemaHelperClasses
{
    public class NetWorthRevenueLoansV1
    {
        private decimal? _netRevenue { get; set; }
        private decimal? _loans { get; set; }
        private decimal? _netWorth { get; set; }

        public decimal? netRevenue
        {
            get => _netRevenue ?? 0;
            set => _netRevenue = value is null ? 0 : value;
        }
        public decimal? loans
        {
            get => _loans ?? 0;
            set => _loans = value is null ? 0 : value;
        }
        public decimal? netWorth
        {
            get => _netWorth ?? 0;
            set => _netWorth = value is null ? 0 : value;
        }
        public string desiredWarrantLimit { get; set; }
    }
}
