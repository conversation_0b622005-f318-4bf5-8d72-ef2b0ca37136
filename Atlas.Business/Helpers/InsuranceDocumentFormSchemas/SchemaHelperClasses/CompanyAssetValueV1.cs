namespace Atlas.Business.Helpers.InsuranceDocumentFormSchemas.SchemaHelperClasses
{
    public class CompanyAssetValueV1
    {
        private decimal? _revenue;
        private decimal? _loans;
        private decimal? _netWorth;

        public decimal? revenue
        {
            get => _revenue ?? 0;
            set => _revenue = value is null ? 0 : value;
        }

        public CurrencyCode currencyCodeRevenue { get; set; }

        public decimal? loans
        {
            get => _loans ?? 0;
            set => _loans = value is null ? 0 : value;
        }

        public CurrencyCode currencyCodeLoans { get; set; }

        public decimal? netWorth
        {
            get => _netWorth ?? 0;
            set => _netWorth = value is null ? 0 : value;
        }

        public CurrencyCode currencyCodeNetWorth { get; set; }
        public string warrantyLimit { get; set; }
    }
}
