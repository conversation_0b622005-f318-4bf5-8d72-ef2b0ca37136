using iText.Signatures;
using System;
using System.Security.Cryptography;
using Azure.Security.KeyVault.Secrets;
using Azure.Security.KeyVault.Certificates;
using Azure.Identity;
using Azure;

namespace Atlas.Business.Helpers
{
    public class KeyVaultDigitalSignature : IExternalSignature
    {
        private readonly SecretClient secretClient;
        private readonly CertificateClient certificateClient;
        private readonly string certificateName;
        private readonly string vaultUri;

        public KeyVaultDigitalSignature(string vaultUrl, string certName)
        {
            vaultUri = vaultUrl;
            certificateName = certName;
            var credential = new DefaultAzureCredential();
            secretClient = new SecretClient(new Uri(vaultUri), credential);
            certificateClient = new CertificateClient(new Uri(vaultUri), credential);
        }

        public string GetEncryptionAlgorithm()
        {
            return "RSA";
        }

        public string GetHashAlgorithm()
        {
            return "SHA-256";
        }

        public byte[] Sign(byte[] message)
        {
            try
            {
                using var sha256hasher = new SHA256CryptoServiceProvider();
                byte[] digest = sha256hasher.ComputeHash(message);

                // Get the certificate version
                var certResponse = certificateClient.GetCertificate(certificateName);
                var cert = certResponse.Value;

                // Get the key ID from the certificate
                var keyId = cert.KeyId;

                // Create a CryptographyClient to perform the signing operation
                var cryptoClient = new Azure.Security.KeyVault.Keys.Cryptography.CryptographyClient(
                    keyId, 
                    new DefaultAzureCredential());

                // Sign the digest
                var signResult = cryptoClient.SignData(
                    Azure.Security.KeyVault.Keys.Cryptography.SignatureAlgorithm.RS256,
                    digest);

                return signResult.Signature;
            }
            catch (RequestFailedException ex)
            {
                throw new InvalidOperationException($"Key Vault signing operation failed: {ex.Message}", ex);
            }
        }
    }
}
