using ImageMagick;
using iText.IO.Image;
using Newtonsoft.Json;
using SharpRaven.Data;
using SharpRaven;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Xml.Linq;
using iText.Kernel.Geom;

namespace Atlas.Business.Helpers
{
    public class ImageUtils
    {
        private static readonly XNamespace ns = "http://ns.adobe.com/xfdf/";

        public static bool IsImageInvisible(byte[] imageBytes)
        {

            MagickImage image = new MagickImage(imageBytes);

            MagickColor white = MagickColors.White;
            MagickColor transparent = MagickColors.Transparent;

            bool isInvisible = true;
            //List<string> colors = new List<string>();

            using (var pixels = image.GetPixels())
            {
                foreach (var pixel in pixels)
                {
                    MagickColor color = (MagickColor)pixel.ToColor();

                    /*
                     * Debug Only
                     * 
                     * if (!colors.Any(c => c == color.ToString()))
                    {
                        colors.Add(color.ToString());
                        Console.WriteLine(color.ToString());

                    }*/

                    if (color != white && color != transparent)
                    {
                        isInvisible = false;
                        break;
                    }
                }
            }


            return isInvisible;
        }

        public static bool HasCorruptImages(string xfdfString)
        {
            byte[] xfdfBytes = Encoding.ASCII.GetBytes(xfdfString);
            StreamReader reader = new StreamReader(new MemoryStream(xfdfBytes), Encoding.GetEncoding("iso-8859-1"));
            // We need to read the xfdf as an XDocument to fix encoding problems
            XDocument xDoc = XDocument.Load(reader);
            //MemoryStream xfdfStream = new MemoryStream();
            //xDocMain.Save(xfdfStream);
            IEnumerable<XElement> annots = xDoc.Descendants(ns + "annots").ToList();
            // Stamps = Signature and Initials Images in Base64 (plus info about the user and coordinates)
            IEnumerable<XElement> stamps = annots.Descendants(ns + "stamp").ToList();
            IEnumerable<XElement> stampCustomDatas = stamps.Descendants(ns + "trn-custom-data").ToList();
            foreach (XElement stampCustomData in stampCustomDatas)
            {
                string data = stampCustomData.Attribute("bytes").Value;
                XFDFCustomData xfdfCustomData = JsonConvert.DeserializeObject<XFDFCustomData>(data);
                XElement stamp = stampCustomData.Parent;
                XAttribute rectAttribute = stamp.Attribute("rect");
                if (rectAttribute != null)
                {
                    Rectangle rectangle = ParseRect(rectAttribute);
                    if (rectangle != null)
                    {
                        XAttribute nameAttribute = stamp.Attribute("name");
                        if (!string.IsNullOrEmpty(stamp.Value) && stamp.Value.Trim().StartsWith("data:image/png;base64"))
                        {
                            string imageElementValue = stamp.Value.Trim();
                            try
                            {
                                string base64ImageString = imageElementValue.Split(',').LastOrDefault();
                                byte[] base64ImageBytes = Convert.FromBase64String(base64ImageString);
                                // Checks that the image is in a regular state
                                ImageDataFactory.Create(base64ImageBytes);
                            }
                            catch (Exception e)
                            {
                                RavenClient ravenClient = new RavenClient("https://6aa2f8bcac3e41e198dc414d24d19e45:<EMAIL>/162539");
                                SentryEvent sentryEvent = new SharpRaven.Data.SentryEvent(new SentryMessage("XFDF_CORRUPT_BASE64"));
                                sentryEvent.Extra = new { e, imageElementValue };
                                ravenClient.Capture(sentryEvent);
                                return true;
                            }
                        }
                    }
                }
            }
            return false;
        }
        private static Rectangle ParseRect(XAttribute rectAttribute)
        {
            string value = rectAttribute.Value;
            string[] values = value.Split(',');
            if (values.Count() == 4)
            {
                var x1 = Convert.ToDecimal(values[0]);
                var y1 = Convert.ToDecimal(values[1]);
                var x2 = Convert.ToDecimal(values[2]);
                var y2 = Convert.ToDecimal(values[3]);
                Rectangle rect = new Rectangle((float)x1, (float)y1, (float)(x2 - x1), (float)(y2 - y1));
                return rect;
            }
            return null;
        }

    }
}
