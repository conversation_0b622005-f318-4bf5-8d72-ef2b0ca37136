using System;
using iText.IO.Image;
using iText.Layout;
using iText.Layout.Element;
using iText.Layout.Properties;
using iText.Layout.Renderer;

namespace Atlas.Business.Helpers
{
    class InitialsImageFrameRenderer : CellRenderer
    {

        private Image initialsImage;
        private Image frameImage;
        private ImageData imageData;
        private string bottomRightText;
        private bool isDigital;

        public InitialsImageFrameRenderer(Cell modelElement, Image signatureImage, Image frameImage, String bottomRightText, ImageData imageData, bool isDigital = false)
        : base(modelElement)
        {
            this.initialsImage = signatureImage;
            this.frameImage = frameImage;
            this.bottomRightText = bottomRightText;
            this.imageData = imageData;
            this.isDigital = isDigital;
        }

        public override void DrawBackground(DrawContext drawContext)
        {

            // Initials Frame placement

            float frameWidth = 25;
            float signatureWidth = 60;

            // Initials frame positions
            float frameImageX = GetOccupiedAreaBBox().GetX();
            float frameImageY = GetOccupiedAreaBBox().GetY() - 25;

            // Initials image positions
            float initialsImageX = GetOccupiedAreaBBox().GetX(); //+ 3
            float initialsImageY = GetOccupiedAreaBBox().GetY() - 30;

            if (isDigital)
            {
                frameWidth = frameWidth - 5;
                frameImageY = GetOccupiedAreaBBox().GetY() - 30;
            }

            drawContext.GetCanvas().AddXObject(frameImage.GetXObject(), frameImageX, frameImageY, frameWidth);
            //drawContext.GetCanvas().AddXObject(initialsImage.GetXObject(), initialsImageX, initialsImageY, signatureWidth);

            iText.Kernel.Geom.Rectangle rectangle = new iText.Kernel.Geom.Rectangle(width : signatureWidth,  height : 40);
            rectangle.SetX(initialsImageX);
            rectangle.SetY(initialsImageY);
            drawContext.GetCanvas().AddImageFittedIntoRectangle(imageData, rectangle, true);

            // BottomRight Text (userId) placement
            //float textX = GetOccupiedAreaBBox().GetRight() - 3;
            //float textY = GetOccupiedAreaBBox().GetBottom() + 3;

            //float textX = GetOccupiedAreaBBox().GetX() + 42;
            float textX = GetOccupiedAreaBBox().GetX() + 27;


            float textY = GetOccupiedAreaBBox().GetY() - 28;

            if (isDigital)
            {
                //textX = GetOccupiedAreaBBox().GetX() + 23;
                textX = GetOccupiedAreaBBox().GetX() + 9;


                textY = GetOccupiedAreaBBox().GetY() - 29;
            }


            TextAlignment alignment = TextAlignment.LEFT;
            Paragraph textParagraph = new Paragraph(bottomRightText);
            textParagraph.SetFontSize(6);

            new Canvas(drawContext.GetCanvas(), drawContext.GetDocument(), GetOccupiedAreaBBox()).ShowTextAligned(textParagraph, textX, textY, alignment);
        }

    }
}
