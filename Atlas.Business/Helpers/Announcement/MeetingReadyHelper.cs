using Atlas.Data.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Microsoft.EntityFrameworkCore;

namespace Atlas.Business.Helpers.Announcement
{
    public class MeetingReadyHelper
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="contentId">contentId da reunião</param>
        /// <returns>string com o JSON</returns>
        public string GenerateJson(int contentId)
        {
            MeetingReadyViewModel result = new MeetingReadyViewModel();
            //1. vai no banco de dados e traz os dados da reuniao, sem usar a contentService :(
            AtlasModelCore _md = new AtlasModelCore();
            var content = _md.Content.Include(o => o.Meeting).Where(o => o.contentId == contentId).FirstOrDefault();
            var meeting = content.Meeting.FirstOrDefault();
            var agendas = _md.Content
                .Include(o => o.MeetingAgendaItem)
                .Where(o => o.parentContentId == contentId && (o.deleted ?? false) == false && o.type == "MeetingAgendaItem")
                .ToList();

            var accumulatedTime = 0;
            var sortedList = agendas.SelectMany(o => o.MeetingAgendaItem).OrderBy(o => o.itemOrder).Select(o => o.Content).ToList();

            //2. popula os dados da reuniao, pautas e as devidas permissoes usando os viewmodels abaixo
            result = new MeetingReadyViewModel()
            {
                title = content.title,
                location = meeting.location,
                date = meeting.date,
                duration = meeting.duration,
                permChecked = false,
                conferenceURL = meeting.conferenceURL
            };

            result.agenda = new List<MeetingReadyAgendaViewModel>();

            foreach (var content_agenda in sortedList)
            {
                var agenda = content_agenda.MeetingAgendaItem.FirstOrDefault();
                var permissions = _md.ContentPermission.Where(o => o.contentId == agenda.contentId).ToList();

                result.agenda.Add(new MeetingReadyAgendaViewModel()
                {
                    title = agenda.title,
                    time = agenda.time,
                    agendaItemType = agenda.agendaItemType,
                    permissions = permissions.Select(o => o.userId).ToArray(),
                    startTime = result.date.AddMinutes(accumulatedTime),
                    endTime = result.date.AddMinutes(accumulatedTime + agenda.time),

                });

                accumulatedTime += agenda.time;
            }

            result.endMeetingTime = result.date.AddMinutes(accumulatedTime);


            //3. gera o JSON com base no viewModel, nao no Content inteiro 
            return JsonConvert.SerializeObject(result);
        }

        public string CheckJsonPermissions(string json, int currentUserId)
        {
            var vm = JsonConvert.DeserializeObject<MeetingReadyViewModel>(json);

            foreach (var item in vm.agenda)
            {
                //se o usuario nao tem permissao naquele content, segundo o proprio array informado no MeetingReadyAgendaViewModel
                if (!item.permissions.Contains(currentUserId))
                {
                    item.title = "RESTRICTED_AGENDA";
                    item.agendaItemType = "RESTRICTED";
                }
            }

            vm.permChecked = true;
            return JsonConvert.SerializeObject(vm);
        }

        public MeetingReadyViewModel Parse(string json, int currentUserId)
        {
            var jsonChecked = this.CheckJsonPermissions(json, currentUserId);
            var vm = JsonConvert.DeserializeObject<MeetingReadyViewModel>(jsonChecked);

            return vm;
        }
    }


    public class MeetingReadyViewModel
    {
        public string title { get; set; }
        public string location { get; set; }
        public DateTime date { get; set; }
        public int? duration { get; set; }

        public List<MeetingReadyAgendaViewModel> agenda { get; set; }

        public bool permChecked { get; set; }
        public DateTime endMeetingTime { get; set; }

        public string conferenceURL { get; set; }

    }
    public class MeetingReadyAgendaViewModel
    {
        public string title { get; set; }
        public string agendaItemType { get; set; }
        public int time { get; set; }
        public DateTime startTime { get; set; }
        public DateTime endTime { get; set; }
        public int[] permissions { get; set; }


    }

}
