using Atlas.Data.Entities;
using i18next_net;
using System;
using System.Text;

namespace Atlas.Business.Helpers
{
    public class KnowledgeBaseHelper
    {
        public static string GetContentGuid(string entity, object contentId)
        {
            byte[] textoAsBytes = Encoding.ASCII.GetBytes(String.Concat(entity, "_", contentId));
            return Convert.ToBase64String(textoAsBytes);
        }

        public static string GetWorkgroupGuid(string entity, int workgroupId)
        {
            byte[] textoAsBytes = Encoding.ASCII.GetBytes(String.Concat(entity, "_", workgroupId));
            return Convert.ToBase64String(textoAsBytes);
        }

        public string GetBackupUrl(string entity, int? contentId, int? workgroupId, int resultId)
        {
            string url = "";
            AtlasModelCore _md = new AtlasModelCore();
            string workgroupGuid;
            string contentGuid;

            var urlPrefix = this.getDefaultUrlPrefix();

            switch (entity)
            {
                case "workgroup"://workgroupId
                case "workgroupRoot": //workgroupId
                    var wk = _md.Workgroup.Find(workgroupId.Value);
                    workgroupGuid = GetWorkgroupGuid(wk.type == "ROOT" ? "workgroupRoot" : "workgroup", workgroupId.Value);
                    url = $"https://{urlPrefix}atlasgov.com/knowledge/folderId/" + workgroupGuid + "/" + entity + "/" + workgroupId.Value + "?resultId=" + resultId;
                    break;
                case "knowledgeDirectory": //contentId
                    contentGuid = GetContentGuid(entity, contentId.Value);
                    url = $"https://{urlPrefix}atlasgov.com/knowledge/folderId/" + contentGuid + "/" + entity + "/" + contentId.Value + "?resultId=" + resultId;
                    break;
                case "knowledgeBase": //workgroupId
                case "meetingMaterials": //workgroupId
                case "meetingMinutes": //workgroupId
                case "workgroupActions": //workgroupId
                    workgroupGuid = GetWorkgroupGuid(entity, workgroupId.Value);
                    url = $"https://{urlPrefix}atlasgov.com/knowledge/folderId/" + workgroupGuid + "/" + entity + "/" + workgroupId.Value + "?resultId=" + resultId;
                    break;
                default:
                    break;
            }

            return url;
        }

        public string BuildZipName(int backupRequestId, string currentName, int resultFileNumber)
        {
            AtlasModelCore _md = new AtlasModelCore();
            //ClientBackupFiltersViewModel request = new ClientBackupFiltersViewModel();
            var bakRequest = _md.ClientBackupRequest.Find(backupRequestId);
            var user = _md.User.Find(bakRequest.requestUserId);

            if (!string.IsNullOrEmpty(bakRequest.requestFilters))
            {
                try
                {
                    var filters = Newtonsoft.Json.JsonConvert.DeserializeObject<Business.ViewModels.ClientBackup.ClientBackupFiltersViewModel>(bakRequest.requestFilters);

                    i18next_net.i18next i18n = new i18next_net.i18next(new i18next_net.InitOptions()
                    {
                        defaultNS = "common",
                        localeFileType = LocaleFileTypeEnum.Path,
                        fallbackLng = "en"
                    });

                    var langCode = user.defaultLanguage ?? "pt".ToLower();

                    i18n.changeLanguage(langCode);

                    if (filters.contentId.HasValue)
                    {
                        var objContent = _md.Content.Find(filters.contentId.Value);
                        var nameFolder = objContent.title.Length > 40 ? objContent.title.Substring(0, 40) : objContent.title;
                        return "Dwl " + nameFolder + "_part_" + resultFileNumber + ".zip";
                    }
                    else if (filters.workgroupId.HasValue)
                    {
                        var objWorkgroup = _md.Workgroup.Find(filters.workgroupId.Value);
                        var nameFolder = objWorkgroup.name.Length > 40 ? objWorkgroup.name.Substring(0, 40) : objWorkgroup.name;
                        nameFolder = objWorkgroup.type == "ROOT" ? i18n.t("knowledgeBase:companyFolders") : nameFolder;
                        return "Dwl " + nameFolder + "_part_" + resultFileNumber + ".zip";
                    }
                    else
                    {
                        return currentName;
                    }

                }
                catch
                {
                    Console.WriteLine("Unable to parse requestFilters JSON: " + backupRequestId);
                    return currentName;
                }
            }
            else
            {
                return currentName;
            }
        }

        private string getDefaultUrlPrefix()
        {
            var environment = Environment.GetEnvironmentVariable("ENVIRONMENT");
            
            string default_url_prefix;
            switch (environment.ToUpper())
            {
                case "STAGING":
                    default_url_prefix = "staging.";
                    break;
                case "BETA":
                    default_url_prefix = "beta.";
                    break;
                default:
                    default_url_prefix = "www.";
                    break;
            }

            return default_url_prefix;
        }
    }
}
