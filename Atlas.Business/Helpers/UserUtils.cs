using System;
using System.Text.RegularExpressions;

namespace Atlas.Business.Helpers
{
    public class UserUtils
    {

        /// <summary>
        /// Extracts the first name from a full name string using a regular expression.
        /// </summary>
        /// <param name="fullName">The full name string.</param>
        /// <returns>The extracted first name or an empty string if extraction is unsuccessful.</returns>
        public static string ExtractFirstName(string fullName)
        {
            try
            {
                // Use a regular expression to match the first word
                Regex regex = new Regex(@"\b\w+\b");
                Match match = regex.Match(fullName);

                if (match.Success)
                {
                    // Get the first name
                    return match.Value;
                }

                // Return the original fullname if extraction is unsuccessful.
                return fullName;
            }
            catch (Exception e) when (e is ArgumentException || e is RegexMatchTimeoutException)
            {
                // Handle any exceptions that may occur during the extraction process
                // Console.WriteLine($"Error extracting first name: {ex.Message}");
                return string.Empty;
            }
        }

        public static bool validateCPF(string cpf)
        {

            if (cpf == "00000000000") return false;

            int[] multiplicador1 = new int[9] { 10, 9, 8, 7, 6, 5, 4, 3, 2 };
            int[] multiplicador2 = new int[10] { 11, 10, 9, 8, 7, 6, 5, 4, 3, 2 };
            string tempCpf;
            string digito;
            int soma;
            int resto;
            cpf = cpf.Trim();
            cpf = cpf.Replace(".", "").Replace("-", "");
            if (cpf.Length != 11)
                return false;
            tempCpf = cpf.Substring(0, 9);
            soma = 0;

            for (int i = 0; i < 9; i++)
                soma += int.Parse(tempCpf[i].ToString()) * multiplicador1[i];
            resto = soma % 11;
            if (resto < 2)
                resto = 0;
            else
                resto = 11 - resto;
            digito = resto.ToString();
            tempCpf = tempCpf + digito;
            soma = 0;
            for (int i = 0; i < 10; i++)
                soma += int.Parse(tempCpf[i].ToString()) * multiplicador2[i];
            resto = soma % 11;
            if (resto < 2)
                resto = 0;
            else
                resto = 11 - resto;
            digito = digito + resto.ToString();
            return cpf.EndsWith(digito);
        }


    }
}
