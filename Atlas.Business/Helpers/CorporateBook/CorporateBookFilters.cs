using System;

namespace Atlas.Business.Helpers.CorporateBook
{
    public class CorporateBookFilters
    {
        public int[] WorkgroupId { get; set; } = Array.Empty<int>();
        public int Page { get; set; }
        public int ItemsPerPage { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
    }
}
