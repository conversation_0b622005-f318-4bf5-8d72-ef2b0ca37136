//using MigraDoc.DocumentObjectModel;
//using MigraDoc.DocumentObjectModel.Shapes;
//using MigraDoc.DocumentObjectModel.Tables;
//using MigraDoc.Rendering;
//using PdfSharp.Pdf;
//using PdfSharp.Pdf.IO;
using System;
using System.Collections.Generic;
using System.Globalization;

namespace Atlas.Business.Helpers.CorporateBook.PDF_Generator
{
    public class CorporateBookPDFHelper
    {
        public ColumnsSize _columnsSize;
        public CorporateBookDocument _docPages = new CorporateBookDocument();
        public IEnumerable<Workgroup> _workgroups;

        readonly CultureInfo _cultureInfo;
        readonly Translator _translator;

        public CorporateBookPDFHelper
            (IEnumerable<CBDocumentStream> cbDocPages, IEnumerable<Workgroup> workgroups, CultureInfo cultureInfo, Translator translator)
        {
            _docPages.Header = new Header();
            _docPages.CBDocPages = cbDocPages;
            _workgroups = workgroups;
            _cultureInfo = cultureInfo;
            _translator = translator;
        }

        //public byte[] Generate()
        //{
        //    try
        //    {
        //        // Creating "Migradoc" document
        //        Document document = new Document();
        //        SetFontsAndColors2(document);
        //        SetColumnsSize(document);
        //        SetPageSettings(document);

        //        // Starting content section
        //        Section section = document.AddSection();

        //        BuildHeader(section);
        //        AddLineBreak(section);
        //        Tuple<Table, Row[]> summary_tuple = BuildSummaryHeaderTable(section);

        //        SetHeaderByte(document);

        //        PDFReader();

        //        BuildSummaryHeaderPagination(section, summary_tuple.Item1, summary_tuple.Item2);

        //        SetHeaderByte(document);

        //        var documentBytes = PDFCreator();

        //        var printedDocument = PrintDocument(documentBytes);

        //        printedDocument = StampAllDocumentPages(printedDocument);

        //        return printedDocument;
        //    }
        //    catch (Exception ex)
        //    {
        //        Console.WriteLine(ex.Message);
        //        throw;
        //    }

        //}

        //private byte[] StampAllDocumentPages(byte[] sourceDocument)
        //{
        //    var baseFont = iTextSharp.text.pdf.BaseFont.CreateFont(iTextSharp.text.pdf.BaseFont.HELVETICA, iTextSharp.text.pdf.BaseFont.CP1252, iTextSharp.text.pdf.BaseFont.NOT_EMBEDDED);

        //    using (iTextSharp.text.pdf.PdfReader reader = new iTextSharp.text.pdf.PdfReader(sourceDocument))
        //    {
        //        using (var memoryStream = new MemoryStream(50 * 1024))
        //        {
        //            using (var stamper = new iTextSharp.text.pdf.PdfStamper(reader, memoryStream))
        //            {
        //                int TotalPages = reader.NumberOfPages;

        //                for (int currentPage = 1; currentPage <= TotalPages; currentPage++)
        //                {
        //                    var documentContent = stamper.GetOverContent(currentPage);

        //                    AddPagingBox(documentContent, currentPage + "/" + TotalPages, TotalPages, reader.GetPageSizeWithRotation(currentPage), null, stamper);
        //                }

        //                try
        //                {
        //                    stamper.Close();
        //                }
        //                finally
        //                {

        //                }
        //            }

        //            return memoryStream.ToArray();
        //        }
        //    }
        //}

        //private static void AddPagingBox(iTextSharp.text.pdf.PdfContentByte documentContent, string count, int totalPages, iTextSharp.text.Rectangle realPageSize, iTextSharp.text.Rectangle rect, iTextSharp.text.pdf.PdfStamper stamper)
        //{
        //    var pageSize = rect ?? realPageSize;

        //    var initialBoxHorizontal = (pageSize.Right + pageSize.Left);

        //    if (totalPages <= 99)
        //    {
        //        initialBoxHorizontal -= 49;
        //    }
        //    else if (totalPages > 99 && totalPages <= 999)
        //    {
        //        initialBoxHorizontal -= 70;
        //    }
        //    else if (totalPages > 999 && totalPages <= 9999)
        //    {
        //        initialBoxHorizontal -= 90;
        //    }
        //    else
        //    {
        //        initialBoxHorizontal -= 100;
        //    }

        //    var initialBoxVertical = (pageSize.Bottom + pageSize.Top) / 5;

        //    var gstate = new iTextSharp.text.pdf.PdfGState { FillOpacity = 0.5f, StrokeOpacity = 0.5f, BlendMode = new iTextSharp.text.pdf.PdfName("BM_COLORDODGE") };

        //    documentContent.SetGState(gstate);

        //    documentContent.SaveState();

        //    documentContent.SetColorStroke(new iTextSharp.text.BaseColor(63, 207, 178));

        //    documentContent.SetColorFill(new iTextSharp.text.BaseColor(63, 207, 178));

        //    documentContent.MoveTo(initialBoxHorizontal - 20, initialBoxVertical);

        //    if (totalPages > 999 && totalPages < 9999)
        //    {
        //        documentContent.LineTo(initialBoxHorizontal + 90, initialBoxVertical);

        //        documentContent.LineTo(initialBoxHorizontal + 90, initialBoxVertical + 30);

        //        documentContent.LineTo(initialBoxHorizontal - 20, initialBoxVertical + 30);

        //    }
        //    else if (totalPages <= 999)
        //    {
        //        documentContent.LineTo(initialBoxHorizontal + 70, initialBoxVertical);

        //        documentContent.LineTo(initialBoxHorizontal + 70, initialBoxVertical + 30);

        //        documentContent.LineTo(initialBoxHorizontal - 20, initialBoxVertical + 30);
        //    }
        //    else if (totalPages > 9999)
        //    {
        //        documentContent.LineTo(initialBoxHorizontal + 100, initialBoxVertical);

        //        documentContent.LineTo(initialBoxHorizontal + 100, initialBoxVertical + 30);

        //        documentContent.LineTo(initialBoxHorizontal - 20, initialBoxVertical + 30);
        //    }

        //    documentContent.ClosePathFillStroke();

        //    var gstate2 = new iTextSharp.text.pdf.PdfGState { FillOpacity = 0.7f, StrokeOpacity = 0.7f, BlendMode = new iTextSharp.text.pdf.PdfName("BM_SCREEN") };
        //    documentContent.SetGState(gstate2);
        //    documentContent.SaveState();
        //    documentContent.SetColorFill(new iTextSharp.text.BaseColor(254, 254, 254));
        //    documentContent.BeginText();
        //    documentContent.SetFontAndSize(iTextSharp.text.pdf.BaseFont.CreateFont(iTextSharp.text.pdf.BaseFont.HELVETICA, iTextSharp.text.pdf.BaseFont.CP1252, iTextSharp.text.pdf.BaseFont.NOT_EMBEDDED), 14);

        //    if (totalPages <= 99)
        //    {
        //        documentContent.ShowTextAligned(iTextSharp.text.Element.ALIGN_CENTER, count, initialBoxHorizontal + 25, initialBoxVertical + 10, 0);
        //    }
        //    else if (totalPages > 99 && totalPages <= 999)
        //    {
        //        documentContent.ShowTextAligned(iTextSharp.text.Element.ALIGN_CENTER, count, initialBoxHorizontal + 33, initialBoxVertical + 10, 0);
        //    }
        //    else if (totalPages > 999 && totalPages <= 9999)
        //    {
        //        documentContent.ShowTextAligned(iTextSharp.text.Element.ALIGN_CENTER, count, initialBoxHorizontal + 40, initialBoxVertical + 10, 0);
        //    }
        //    else if (totalPages > 9999)
        //    {
        //        documentContent.ShowTextAligned(iTextSharp.text.Element.ALIGN_CENTER, count, initialBoxHorizontal + 50, initialBoxVertical + 10, 0);
        //    }

        //    documentContent.EndText();
        //    documentContent.RestoreState();

        //    var gstate3 = new iTextSharp.text.pdf.PdfGState { FillOpacity = 0.4f, StrokeOpacity = 0.0f, BlendMode = new iTextSharp.text.pdf.PdfName("BM_SCREEN") };
        //    documentContent.SetGState(gstate3);
        //    documentContent.SaveState();

        //    var logoImageFilename = GetDefaultAtlasLogoWhite();
        //    iTextSharp.text.Image image = iTextSharp.text.Image.GetInstance(logoImageFilename);
        //    image.ScaleAbsoluteWidth(20);
        //    image.ScaleAbsoluteHeight(20);
        //    image.SetAbsolutePosition(initialBoxHorizontal - 15, initialBoxVertical + 5);
        //    documentContent.AddImage(image);
        //}

        //private void SetColumnsSize(Document document)
        //{
        //    float sectionWidth = document.DefaultPageSetup.PageWidth - document.DefaultPageSetup.LeftMargin - document.DefaultPageSetup.RightMargin;

        //    _columnsSize = new ColumnsSize()
        //    {
        //        Size01 = (sectionWidth / 12) * 1,
        //        Size02 = (sectionWidth / 12) * 2,
        //        Size03 = (sectionWidth / 12) * 3,
        //        Size04 = (sectionWidth / 12) * 4,
        //        Size05 = (sectionWidth / 12) * 5,
        //        Size06 = (sectionWidth / 12) * 6,
        //        Size07 = (sectionWidth / 12) * 7,
        //        Size08 = (sectionWidth / 12) * 8,
        //        Size09 = (sectionWidth / 12) * 9,
        //        Size10 = (sectionWidth / 12) * 10,
        //        Size11 = (sectionWidth / 12) * 11,
        //        Size12 = (sectionWidth / 12) * 12
        //    };
        //}

        //private void SetFontsAndColors(Document document)
        //{
        //    document.UseCmykColor = false;

        //    //// Font styles
        //    Style defaultFont = document.Styles["Normal"];
        //    defaultFont.Font.Name = "Roboto";
        //    defaultFont.Font.Size = "9pt";
        //    defaultFont.Font.Color = Color.FromRgb(58, 64, 77);
        //    defaultFont.ParagraphFormat.LineSpacingRule = LineSpacingRule.AtLeast;
        //    defaultFont.ParagraphFormat.LineSpacing = 18;
        //    document.Add(defaultFont);

        //    Style h1Titles = new Style("H1", "Normal");
        //    h1Titles.Font.Size = "8pt";
        //    h1Titles.Font.Bold = true;
        //    document.Add(h1Titles);

        //    Style h2Titles = new Style("H2", "Normal");
        //    h2Titles.Font.Bold = true;
        //    h2Titles.Font.Size = "8pt";
        //    document.Add(h2Titles);

        //    Style h3Titles = new Style("H3", "Normal");
        //    h3Titles.Font.Size = "8pt";
        //    document.Add(h3Titles);
        //}

        //private void SetFontsAndColors2(Document document)
        //{
        //    document.UseCmykColor = false;

        //    // Font styles
        //    Style style = document.Styles["Normal"];
        //    style.Font.Name = "Roboto";
        //    style.Font.Size = "10pt";
        //    style.Font.Color = new Color(58, 64, 77);
        //    document.Add(style);

        //    // H1
        //    Style styleH1 = new Style("H1", "Normal");
        //    styleH1.Font.Size = "14pt";
        //    styleH1.Font.Bold = true;
        //    document.Add(styleH1);

        //    // Muted
        //    Style styleMuted = new Style("muted", "Normal");
        //    styleMuted.Font.Color = new Color(199, 199, 199);
        //    document.Add(styleMuted);

        //    // Details Label
        //    Style styleDetailsLabel = new Style("detailsLabel", "Normal");
        //    styleDetailsLabel.Font.Size = "8.8pt";
        //    styleDetailsLabel.Font.Bold = true;
        //    styleDetailsLabel.Font.Color = new Color(35, 32, 50);
        //    styleDetailsLabel.Font.Name = "Arial";
        //    document.Add(styleDetailsLabel);

        //    // Details Value
        //    Style styleDetailsValue = new Style("detailsValue", "Normal");
        //    styleDetailsValue.Font.Size = "8.8pt";
        //    styleDetailsValue.Font.Name = "Arial";
        //    document.Add(styleDetailsValue);

        //    // Details Label Winner
        //    Style styleDetailsLabelWinner = new Style("detailsLabelWinner", "Normal");
        //    styleDetailsLabelWinner.Font.Size = "8.8pt";
        //    styleDetailsLabelWinner.Font.Bold = true;
        //    styleDetailsLabelWinner.Font.Color = new Color(71, 187, 158);
        //    document.Add(styleDetailsLabelWinner);

        //    // Details Value Winner
        //    Style styleDetailsValueWinner = new Style("detailsValueWinner", "Normal");
        //    styleDetailsValueWinner.Font.Size = "8.8pt";
        //    styleDetailsValueWinner.Font.Bold = true;
        //    styleDetailsValueWinner.Font.Color = new Color(71, 187, 158);
        //    document.Add(styleDetailsValueWinner);

        //    Style textBold = new Style("Bold", "Normal");
        //    textBold.Font.Bold = true;
        //    textBold.Font.Size = "8pt";
        //    document.Add(textBold);

        //    Style textItalic = new Style("Italic", "Normal");
        //    textItalic.Font.Italic = true;
        //    textItalic.Font.Size = "8pt";
        //    document.Add(textItalic);

        //    Style textUnderline = new Style("Underline", "Normal");
        //    textUnderline.Font.Underline = Underline.Words;
        //    textUnderline.Font.Size = "8pt";
        //    document.Add(textUnderline);

        //    Style SmallHyperlink = new Style("SmallHyperlink", "Normal");
        //    SmallHyperlink.Font.Size = "8pt";
        //    SmallHyperlink.Font.Color = new Color(255, 0, 111, 238);
        //    document.Add(SmallHyperlink);
        //}

        //private void SetPageSettings(Document document)
        //{
        //    document.DefaultPageSetup.PageFormat = PageFormat.A4;
        //    document.DefaultPageSetup.BottomMargin = 60;
        //    document.DefaultPageSetup.LeftMargin = 30;
        //    document.DefaultPageSetup.RightMargin = 30;
        //    document.DefaultPageSetup.TopMargin = 30;
        //}

        //private void AddLineBreak(Section section)
        //{
        //    Paragraph line_break_paragraph = new Paragraph();
        //    section.Add(line_break_paragraph);
        //}

        //private void BuildHeader(Section section)
        //{
        //    BuildLogoHeader(section);

        //    Paragraph divider = new Paragraph();
        //    divider.Format.Borders.Top.Width = 1;
        //    divider.Format.Borders.Top.Color = Color.FromRgb(210, 210, 210);
        //    divider.Format.SpaceBefore = 10;
        //    section.Add(divider);

        //    BuildWorkgroupsHeaderTable(section);
        //}

        //private void BuildLogoHeader(Section section)
        //{
        //    Table headerTable = new Table();
        //    headerTable.AddColumn(_columnsSize.Size02);
        //    headerTable.AddColumn(_columnsSize.Size08);
        //    headerTable.AddColumn(_columnsSize.Size02);

        //    Row headerRow = headerTable.AddRow();
        //    headerRow.Format.Alignment = ParagraphAlignment.Center;

        //    headerRow.Cells[0].Format.Alignment = ParagraphAlignment.Center;
        //    headerRow.Cells[1].Format.Alignment = ParagraphAlignment.Center;

        //    headerRow.Cells[0].VerticalAlignment = VerticalAlignment.Center;
        //    headerRow.Cells[1].VerticalAlignment = VerticalAlignment.Center;

        //    // Atlas logo
        //    Paragraph logoParagraph = new Paragraph();
        //    logoParagraph.Format.Alignment = ParagraphAlignment.Center;
        //    string logoImageFileName = GetDefaultAtlasLogo();
        //    Image logo = logoParagraph.AddImage(logoImageFileName);

        //    logo.LockAspectRatio = true;
        //    logo.Width = 40;

        //    // Title "Atas de reunioes"
        //    Paragraph header_title_paragraph = new Paragraph();
        //    header_title_paragraph.Format.Alignment = ParagraphAlignment.Center;
        //    header_title_paragraph.AddFormattedText(_translator.Translate("minutesBook:general.meetingMinutes"), "H1");

        //    headerRow[0].Add(logoParagraph);
        //    headerRow[1].Add(header_title_paragraph);

        //    section.Add(headerTable);
        //}

        //private void BuildWorkgroupsHeaderTable(Section section)
        //{
        //    Paragraph workgroups_title_paragraph = new Paragraph();
        //    workgroups_title_paragraph.Format.Font.Bold = true;
        //    workgroups_title_paragraph.Format.Font.Size = 11;
        //    workgroups_title_paragraph.Format.SpaceAfter = 6;
        //    workgroups_title_paragraph.Format.SpaceBefore = 15;
        //    workgroups_title_paragraph.AddFormattedText(_translator.Translate("minutesBook:general.workgroupsSelected"));
        //    section.Add(workgroups_title_paragraph);

        //    // setting up the WK table
        //    Table workgroups_table = new Table();
        //    Column workgroups_first_column = workgroups_table.AddColumn(_columnsSize.Size06);
        //    Column workgroups_second_column = workgroups_table.AddColumn(_columnsSize.Size06);

        //    Row workgroups_row = workgroups_table.AddRow();

        //    foreach (var item in _workgroups.Select((wk, index) => new { wk, index }))
        //    {
        //        var workgroup = item.wk;
        //        int index = item.index;

        //        bool isEven = (index % 2 == 0);

        //        Paragraph workgroup_paragraph = new Paragraph();
        //        workgroup_paragraph.Format.Font.Size = 10;
        //        workgroup_paragraph.Format.FirstLineIndent = "0.8cm";

        //        FormattedText bullet = workgroup_paragraph.AddFormattedText("\u2022 ");
        //        bullet.Size = 30;
        //        bullet.Subscript = true;
        //        bullet.Font.Size = 22;
        //        bullet.Color = Color.Parse(workgroup.BulletColor.Replace("#", "0xff"));

        //        string title = $"{workgroup.Name}".Trim();

        //        if (title.Length > 30)
        //        {
        //            title = title.Substring(0, 29);
        //            title = $"{title}...";
        //        }

        //        workgroup_paragraph.AddFormattedText($"{title}");

        //        int left_table_side = 0;
        //        int right_table_side = 1;
        //        var table_cell_side = isEven ? left_table_side : right_table_side;

        //        workgroups_row.Cells[table_cell_side].Add(workgroup_paragraph);
        //    }

        //    section.Add(workgroups_table);
        //}

        //private Tuple<Table, Row[]> BuildSummaryHeaderTable(Section section)
        //{
        //    Paragraph summary_title_paragraph = new Paragraph();
        //    summary_title_paragraph.Format.Font.Bold = true;
        //    summary_title_paragraph.Format.Font.Size = 11;
        //    summary_title_paragraph.Format.SpaceBefore = 15;
        //    summary_title_paragraph.Format.SpaceAfter = 12;
        //    summary_title_paragraph.AddText(_translator.Translate("minutesBook:general.selectedDocuments"));

        //    section.Add(summary_title_paragraph);

        //    // setting up the Summary table
        //    Table summary_table = new Table();
        //    summary_table.KeepTogether = false;
        //    summary_table.Format.KeepTogether = false;

        //    Column summary_doc_title_column = summary_table.AddColumn(_columnsSize.Size10);
        //    summary_doc_title_column.Format.KeepTogether = false;
        //    summary_doc_title_column.Format.KeepWithNext = true;

        //    Column summary_pagination_number_column = summary_table.AddColumn(_columnsSize.Size02);
        //    summary_pagination_number_column.Format.KeepTogether = false;
        //    summary_pagination_number_column.Format.KeepWithNext = true;

        //    _docPages.CBDocPages = _docPages.CBDocPages.OrderBy(cb => cb.ItemOrder);

        //    Row[] summary_pagination_rows = new Row[_docPages.CBDocPages.Count()];

        //    foreach (var item in _docPages.CBDocPages.Select((document, index) => new { document, index }))
        //    {
        //        Row summary_pagination_number_row = summary_table.AddRow();
        //        summary_pagination_number_row.Format.KeepWithNext = true;

        //        summary_pagination_rows[item.index] = summary_pagination_number_row;

        //        CBDocumentStream document = item.document;

        //        Paragraph summary_doc_title_paragraph = new Paragraph();

        //        summary_doc_title_paragraph.Format.Font.Size = 10;
        //        summary_doc_title_paragraph.Format.FirstLineIndent = "0.8cm";
        //        summary_doc_title_paragraph.Format.LineSpacingRule = LineSpacingRule.OnePtFive;

        //        DateTime dateStart = TimeZoneInfo.ConvertTimeFromUtc(document.DocumentDate, document.DocTimeZone);
        //        var date = dateStart.ToString(_cultureInfo.DateTimeFormat.ShortDatePattern, _cultureInfo);

        //        bool isExternalDoc = document.Type == ContentTypes.ExternalCorpBookDoc;
        //        string externalDocInfo = isExternalDoc ? $" {_translator.Translate("minutesBook:general.externalDoc")}" : "";
        //        date = !isExternalDoc ? date : "";

        //        if (string.IsNullOrWhiteSpace(document.Title))
        //        {
        //            document.Title = _translator.Translate("minutesBook:general.noTitle");
        //        }

        //        if (document.Title.Length > 100)
        //        {
        //            document.Title = document.Title.Substring(0, (80 - $"{date}{externalDocInfo} ".Length));
        //            document.Title = $"{document.Title}...";
        //        }

        //        var formattedTitle = summary_doc_title_paragraph.AddFormattedText($"{document.Title} - ");
        //        formattedTitle.Bold = true;

        //        summary_doc_title_paragraph.AddText($"{date}{externalDocInfo} ");

        //        summary_pagination_number_row.Cells[0].Add(summary_doc_title_paragraph);
        //    }

        //    section.Add(summary_table);

        //    return new Tuple<Table, Row[]>(summary_table, summary_pagination_rows);
        //}

        //private string Capitalize(string input)
        //{
        //    try
        //    {
        //        return input[0].ToString().ToUpper() + input.Substring(1);
        //    }
        //    catch (Exception)
        //    {
        //        return input;
        //    }
        //}

        //private string GimmeTheDottedLine(int numberOfDots)
        //{
        //    StringBuilder sb = new StringBuilder();

        //    for (int i = 0; i < numberOfDots; i++)
        //    {
        //        sb.Append('_');
        //    }

        //    string dottedLine = sb.ToString();
        //    return dottedLine;
        //}

        //private void BuildSummaryHeaderPagination(Section section, Table summary_table, Row[] summary_pagination_rows)
        //{
        //    _docPages.CBDocPages = _docPages.CBDocPages.OrderBy(cb => cb.ItemOrder);

        //    foreach (var item in _docPages.CBDocPages.Select((document, index) => new { document, index }))
        //    {
        //        CBDocumentStream document = item.document;

        //        Paragraph summary_pagination_number_paragraph = new Paragraph();
        //        summary_pagination_number_paragraph.Format.LineSpacingRule = LineSpacingRule.OnePtFive;
        //        summary_pagination_number_paragraph.Format.Alignment = ParagraphAlignment.Left;
        //        summary_pagination_number_paragraph.AddFormattedText($"{_translator.Translate("minutesBook:general.page")} {document.StartAtPage}");

        //        summary_pagination_rows[item.index].Cells[1].Add(summary_pagination_number_paragraph);
        //    }
        //}

        //private void BuildBody(Section section)
        //{
        //    BuildAtlasGovTitle(section);
        //    // BuildBodyComponents(section);
        //}

        //private void BuildAtlasGovTitle(Section section)
        //{
        //    Table table = section.AddTable();
        //    table.AddColumn(_columnsSize.Size12);
        //    table.Style = "Table";

        //    table.Borders.Width = 0;
        //    table.Borders.Left.Width = 0;
        //    table.Borders.Right.Width = 0;

        //    Row row = table.AddRow();

        //    row.Shading.Color = Color.FromArgb(255, 25, 192, 160);
        //    row.Format.Font.Color = Color.FromArgb(255, 255, 255, 255);

        //    row.HeadingFormat = true;
        //    row.Format.Font.Bold = true;

        //    row.VerticalAlignment = VerticalAlignment.Center;
        //    row.Format.SpaceBefore = 10;
        //    row.Format.SpaceAfter = 10;
        //    row.Format.Font.Size = 14;

        //    //reportHeaderRow.Cells[0].AddParagraph(i18n.t(""));
        //    row.Cells[0].AddParagraph("Atlas Governance");
        //    row.Cells[0].Format.Alignment = ParagraphAlignment.Center;
        //}

        //private void BuildBodyComponents(Section section)
        //{
        //    foreach (var page in _docPages.CBDocPages)
        //    {
        //        Paragraph filename_paragraph = section.AddParagraph();
        //        filename_paragraph.Format.Shading.Color = Color.FromRgb(230, 230, 230);
        //        filename_paragraph.Format.LineSpacingRule = LineSpacingRule.AtLeast;
        //        filename_paragraph.Format.LineSpacing = 15;
        //        //filename_paragraph.AddFormattedText($"{page.FileName}");
        //        filename_paragraph.AddFormattedText("ATA DE COMITE DE CULTURA");

        //        Paragraph linebreak_paragraph_01 = section.AddParagraph();
        //        linebreak_paragraph_01.Format.Shading.Color = Color.FromRgb(230, 230, 230);
        //        linebreak_paragraph_01.AddLineBreak();

        //        Paragraph date_paragraph = section.AddParagraph();
        //        date_paragraph.Format.Shading.Color = Color.FromRgb(240, 240, 240);
        //        date_paragraph.Format.LineSpacingRule = LineSpacingRule.AtLeast;
        //        date_paragraph.Format.LineSpacing = 15;
        //        date_paragraph.AddFormattedText($"page.Date");

        //        Paragraph linebreak_paragraph_02 = section.AddParagraph();
        //        linebreak_paragraph_02.Format.Shading.Color = Color.FromRgb(240, 240, 240);
        //        linebreak_paragraph_02.AddLineBreak();

        //        string names = string.Join("; ", "page.Subscribers.Select(subs => subs.Name)");
        //        Paragraph subscribers_paragraph = section.AddParagraph();
        //        subscribers_paragraph.Format.Shading.Color = Color.FromRgb(230, 230, 230);
        //        subscribers_paragraph.Format.LineSpacingRule = LineSpacingRule.AtLeast;
        //        subscribers_paragraph.Format.LineSpacing = 15;
        //        subscribers_paragraph.AddFormattedText($"{names}");

        //        Paragraph linebreak_paragraph_03 = section.AddParagraph();
        //        linebreak_paragraph_03.Format.Shading.Color = Color.FromRgb(230, 230, 230);
        //        linebreak_paragraph_03.AddLineBreak();

        //        Paragraph description_paragraph = section.AddParagraph();
        //        description_paragraph.Format.Shading.Color = Color.FromRgb(240, 240, 240);
        //        description_paragraph.Format.LineSpacingRule = LineSpacingRule.AtLeast;
        //        description_paragraph.Format.LineSpacing = 15;
        //        description_paragraph.AddFormattedText($"page.Description");

        //        Paragraph linebreak_paragraph_04 = section.AddParagraph();
        //        linebreak_paragraph_04.Format.Shading.Color = Color.FromRgb(240, 240, 240);
        //        linebreak_paragraph_04.AddLineBreak();
        //    }

        //}

        //private void SetHeaderByte(Document document)
        //{
        //    _docPages.Header.ByteStream = ToByteArray(document);
        //}

        //private byte[] PrintDocument(byte[] document)
        //{
        //    PdfDocument outputDocument = new PdfDocument();
        //    MemoryStream documentStream = new MemoryStream(document);
        //    PdfDocument inputDocument = PdfReader.Open(documentStream, PdfDocumentOpenMode.Import);

        //    for (int idx = 1; idx <= inputDocument.PageCount; idx++)
        //    {
        //        PdfPage page = inputDocument.Pages[idx - 1];

        //        page.Annotations.Clear();

        //        outputDocument.AddPage(page);
        //    }

        //    MemoryStream outputStream = new MemoryStream();
        //    outputDocument.Save(outputStream, false);
        //    byte[] bytes = outputStream.ToArray();
        //    return bytes;
        //}

        //private static string GetDefaultAtlasLogo()
        //{
        //    /*System.Drawing.ImageConverter converter = new System.Drawing.ImageConverter();
        //    byte[] logo_array = (byte[])converter.ConvertTo(Resources.ReportResource.logo_green_v3, typeof(byte[]));

        //    string logoImageFilename = "base64:" +
        //           Convert.ToBase64String(logo_array);

        //    return logoImageFilename;*/

        //    return "";
        //}

        //private static byte[] GetDefaultAtlasLogoWhite()
        //{
        //    /*
        //     * pingas wpf
        //     * System.Drawing.ImageConverter converter = new System.Drawing.ImageConverter();
        //    return (byte[])converter.ConvertTo(Resources.ReportResource.logo_white, typeof(byte[]));*/

        //    return new byte[] { };
        //}



        //private byte[] ToByteArray(Document document)
        //{
        //    const bool unicode = false;

        //    // const PdfFontEmbedding embedding = PdfFontEmbedding.Always;
        //    // PdfDocumentRenderer pdf_renderer = new PdfDocumentRenderer(unicode, embedding)

        //    PdfDocumentRenderer pdf_renderer = new PdfDocumentRenderer(unicode)
        //    {
        //        Document = document.Clone()
        //    };

        //    pdf_renderer.RenderDocument();

        //    MemoryStream ms = new MemoryStream();
        //    pdf_renderer.PdfDocument.Save(ms);

        //    return ms.ToArray();
        //}

        //private void PDFReader()
        //{
        //    _docPages.CBDocPages = _docPages.CBDocPages.OrderBy(cb => cb.ItemOrder);

        //    iTextSharp.text.pdf.PdfReader header = new iTextSharp.text.pdf.PdfReader(_docPages.Header.ByteStream);

        //    if (header.NumberOfPages >= 1)
        //    {
        //        _docPages.TotalPages = header.NumberOfPages;

        //        _docPages.Header.StartAtPage = 1;
        //        _docPages.Header.EndAtPage = header.NumberOfPages;
        //        _docPages.Header.TotalPages = header.NumberOfPages;

        //        //_docPages.CBDocPages.First().StartAtPage = 1;
        //        //_docPages.CBDocPages.First().EndAtPage = header.NumberOfPages;
        //        //_docPages.CBDocPages.First().TotalPages = header.NumberOfPages;
        //    }

        //    foreach (var cbDocument in _docPages.CBDocPages)
        //    {
        //        try
        //        {
        //            using (iTextSharp.text.pdf.PdfReader reader = new iTextSharp.text.pdf.PdfReader(cbDocument.ByteStream))
        //            {
        //                try
        //                {
        //                    //---- HOTFIX S28 - Arquivos encriptados mas sem senha
        //                    var rfl_type = reader.GetType();
        //                    var rfl_field = rfl_type.GetField("encrypted");
        //                    if (rfl_field == null)
        //                    {
        //                        rfl_field = rfl_type.GetField("encrypted", BindingFlags.NonPublic | BindingFlags.Instance);
        //                    }
        //                    rfl_field.SetValue(reader, false);
        //                }
        //                catch (Exception ex)
        //                {
        //                    throw;
        //                }

        //                if (reader.NumberOfPages >= 1)
        //                {
        //                    cbDocument.StartAtPage = _docPages.TotalPages + 1;
        //                    cbDocument.EndAtPage = _docPages.TotalPages + reader.NumberOfPages + 1;
        //                    cbDocument.TotalPages = reader.NumberOfPages;
        //                }


        //                for (int i = 1; i <= reader.NumberOfPages; i++)
        //                {
        //                    _docPages.TotalPages++;
        //                }


        //            }

        //        }
        //        catch (Exception ex)
        //        {
        //            Console.WriteLine(ex.Message);
        //            using (MemoryStream ms = new MemoryStream())
        //            {
        //                iTextSharp.text.Document error_document = new iTextSharp.text.Document();
        //                iTextSharp.text.pdf.PdfWriter error_writer = iTextSharp.text.pdf.PdfWriter.GetInstance(error_document, ms);

        //                error_document.Open();
        //                error_document.Add(new iTextSharp.text.Paragraph("INVALID FILE. " + cbDocument.FileName));

        //                try
        //                {
        //                    error_document.Close();
        //                }
        //                finally
        //                {
        //                    //this is to prevent "Already closed" System.InvalidOperationException exception
        //                }

        //            }
        //        }
        //        finally
        //        {
        //            // resultado.MergedDocuments.Add(docData);
        //        }
        //    }


        //}

        //private byte[] PDFCreator()
        //{
        //    _docPages.CBDocPages = _docPages.CBDocPages.OrderBy(cb => cb.ItemOrder);

        //    using (var mergedPagesStream = new MemoryStream())
        //    {
        //        iTextSharp.text.Document document = new iTextSharp.text.Document();

        //        using (iTextSharp.text.pdf.PdfCopy writer = new iTextSharp.text.pdf.PdfCopy(document, mergedPagesStream))
        //        {
        //            if (writer == null)
        //            {
        //                return null;
        //            }

        //            document.Open();

        //            iTextSharp.text.pdf.PdfReader header = new iTextSharp.text.pdf.PdfReader(_docPages.Header.ByteStream);

        //            if (header.NumberOfPages >= 1)
        //            {
        //                _docPages.TotalPages = header.NumberOfPages;

        //                _docPages.Header.StartAtPage = 1;
        //                _docPages.Header.EndAtPage = header.NumberOfPages;
        //                _docPages.Header.TotalPages = header.NumberOfPages;
        //            }


        //            for (int i = 1; i <= header.NumberOfPages; i++)
        //            {
        //                iTextSharp.text.pdf.PdfImportedPage page = writer.GetImportedPage(header, i);
        //                writer.AddPage(page);
        //            }


        //            foreach (var cbDocument in _docPages.CBDocPages)
        //            {
        //                try
        //                {
        //                    using (iTextSharp.text.pdf.PdfReader reader = new iTextSharp.text.pdf.PdfReader(cbDocument.ByteStream))
        //                    {
        //                        try
        //                        {
        //                            //---- HOTFIX S28 - Arquivos encriptados mas sem senha
        //                            var rfl_type = reader.GetType();
        //                            var rfl_field = rfl_type.GetField("encrypted");
        //                            if (rfl_field == null)
        //                            {
        //                                rfl_field = rfl_type.GetField("encrypted", BindingFlags.NonPublic | BindingFlags.Instance);
        //                            }
        //                            rfl_field.SetValue(reader, false);
        //                        }
        //                        catch (Exception ex)
        //                        {
        //                            throw;
        //                        }

        //                        if (reader.NumberOfPages >= 1)
        //                        {
        //                            cbDocument.StartAtPage = _docPages.TotalPages + 1;
        //                            cbDocument.EndAtPage = _docPages.TotalPages + reader.NumberOfPages + 1;
        //                            cbDocument.TotalPages = reader.NumberOfPages;
        //                        }


        //                        for (int i = 1; i <= reader.NumberOfPages; i++)
        //                        {
        //                            _docPages.TotalPages++;
        //                        }


        //                        for (int i = 1; i <= reader.NumberOfPages; i++)
        //                        {
        //                            iTextSharp.text.pdf.PdfImportedPage page = writer.GetImportedPage(reader, i);
        //                            writer.AddPage(page);
        //                        }

        //                        // Now we don't need the Close method
        //                        //reader.Close();
        //                    }

        //                }
        //                catch (Exception ex)
        //                {
        //                    Console.WriteLine(ex.Message);
        //                    using (MemoryStream ms = new MemoryStream())
        //                    {
        //                        iTextSharp.text.Document error_document = new iTextSharp.text.Document();
        //                        iTextSharp.text.pdf.PdfWriter error_writer = iTextSharp.text.pdf.PdfWriter.GetInstance(error_document, ms);

        //                        error_document.Open();
        //                        error_document.Add(new iTextSharp.text.Paragraph("INVALID FILE. " + cbDocument.FileName));

        //                        try
        //                        {
        //                            error_document.Close();
        //                        }
        //                        finally
        //                        {
        //                            //this is to prevent "Already closed" System.InvalidOperationException exception
        //                        }


        //                        iTextSharp.text.pdf.PdfReader error_reader = new iTextSharp.text.pdf.PdfReader(ms.ToArray());
        //                        iTextSharp.text.pdf.PdfImportedPage page = writer.GetImportedPage(error_reader, 1);
        //                        writer.AddPage(page);

        //                        //atualiza o tamanho da pagina pra + 1, para evitar conflitos
        //                        // docData.pageLen = 1;

        //                    }
        //                }
        //                finally
        //                {
        //                    // resultado.MergedDocuments.Add(docData);
        //                }
        //            }

        //            writer.Close();
        //            return mergedPagesStream.ToArray();
        //        }
        //    }
        //}

        //private async Task<Stream> GetDocumentAsync(string key)
        //{
        //    AzureHelper azureHelper = new AzureHelper();
        //    byte[] bytes = await azureHelper.ParallelDownloadAsync(key);
        //    return new MemoryStream(bytes);
        //}

        public class ColumnsSize
        {
            public float Size01 { get; set; }
            public float Size02 { get; set; }
            public float Size03 { get; set; }
            public float Size04 { get; set; }
            public float Size05 { get; set; }
            public float Size06 { get; set; }
            public float Size07 { get; set; }
            public float Size08 { get; set; }
            public float Size09 { get; set; }
            public float Size10 { get; set; }
            public float Size11 { get; set; }
            public float Size12 { get; set; }
        }

        public class CorporateBookDocument
        {
            public int TotalPages { get; set; }
            public Header Header { get; set; }
            public IEnumerable<CBDocumentStream> CBDocPages = new List<CBDocumentStream>();
        }

        public class Header : CBDocumentStream
        {
        }

        public class Workgroup
        {
            public string BulletColor { get; set; }
            public string Name { get; set; }
            public string ClientName { get; set; }
        }

        public class CBDocumentStream
        {
            public int Id { get; set; }
            public int AttachmentId { get; set; }
            public DateTime UploadDate { get; set; }
            public DateTime DocumentDate { get; set; }
            public TimeZoneInfo DocTimeZone { get; set; }
            public string Title { get; set; }
            public string FileName { get; set; }
            public string Type { get; set; }
            public int StartAtPage { get; set; }
            public int EndAtPage { get; set; }
            public int TotalPages { get; set; }
            public int ItemOrder { get; set; }
            public string DownloadKey { get; set; }
            public byte[] ByteStream { get; set; }
        }
    }
}
