using Atlas.Data.Entities;
using System;
using System.Globalization;
using TimeZoneConverter;

namespace Atlas.Business.Helpers.CorporateBook.PDF_Generator
{
    public static class CorporateBookPDFUtils
    {
        public static CultureInfo GetCultureInfo(User User)
        {
            try
            {
                switch ((User.defaultLanguage ?? "pt").ToLower())
                {
                    case "pt":
                        return new CultureInfo("pt-BR");
                    case "en":
                        return new CultureInfo("en-US");
                    case "es":
                        return new CultureInfo("es-ES");

                    default:
                        return new CultureInfo("pt-BR");
                }
            }
            catch (Exception)
            {
                return new CultureInfo("pt-BR");
            }
        }

        public static TimeZoneInfo GetTimeZone(User User)
        {
            string tz = "";

            try
            {
                string db_fuso = string.IsNullOrWhiteSpace(User.defaultTimezone) ? "America/Sao_Paulo" : User.defaultTimezone;
                tz = TZConvert.IanaToWindows(db_fuso);
            }
            catch (InvalidTimeZoneException)
            {
                tz = "E. South America Standard Time";
            }

            return TimeZoneInfo.FindSystemTimeZoneById(tz);
        }

        public static Translator GetTranslator(string language)
        {
            return new Translator(language);
        }
    }
}
