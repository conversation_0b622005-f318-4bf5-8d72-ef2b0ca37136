using i18next_net;

namespace Atlas.Business.Helpers.CorporateBook.PDF_Generator
{
    public class Translator
    {
        readonly i18next i18Next;

        public Translator(string language)
        {
            this.i18Next = new i18next(new InitOptions()
            {
                defaultNS = "common",
                localeFileType = LocaleFileTypeEnum.Path,
                fallbackLng = "en"
            });

            i18Next.changeLanguage(language);
        }

        public void SetLanguage(string language)
        {
            i18Next.changeLanguage(language);
        }

        public string Translate(string key)
        {
            return i18Next.t(key);
        }
    }
}
