using Atlas.CrossCutting.AppEnums;
using Atlas.CrossCutting.Exceptions;
using Atlas.CrossCutting.Helpers.Notetaker;
using Atlas.Data.Entities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Atlas.Business.Helpers.Notetaker
{
    public class NotetakerHelper
    {
        public class NotetakerFinishMeetingResult
        {
            public int MeetingContentId { get; set; }
            public Guid TranscriptionId { get; set; }
            public Guid SummaryId { get; set; }
        }

        public static async System.Threading.Tasks.Task<NotetakerFinishMeetingResult> FinishMeeting(NotetakerFinishRequest request)
        {
            var requestGuid = new Guid(request.meetingId);

            MeetingTranscription mt = new MeetingTranscription();

            var summaryJson = Newtonsoft.Json.JsonConvert.DeserializeObject<MeetingSummaryJson>(request.summaryJson);

            mt.externalId = requestGuid;
            mt.status = "COMPLETED";
            mt.statusDate = DateTime.UtcNow;
            mt.transcriptionJson = request.transcriptionJson;
            mt.createdAt = DateTime.UtcNow;

            MeetingTranscriptionSummary mts = new MeetingTranscriptionSummary();
            mts.MeetingTranscription = mt;
            mts.SummaryJson = Newtonsoft.Json.JsonConvert.SerializeObject(summaryJson.summaryJson);
            mts.SummaryJsonDetailed = Newtonsoft.Json.JsonConvert.SerializeObject(summaryJson.summaryDetailedJson);
            mts.CurrentSummaryType = NotetakerMeetingSummaryType.CONCISE;

            List<MeetingTranscriptionTask> mt_tasks = new List<MeetingTranscriptionTask>();
            try
            {
                //parse summaryJson to get the tasks
                MeetingSummaryPayload meetingSummaryPayload = summaryJson.summaryDetailedJson;

                if (meetingSummaryPayload.actionPlan != null)
                {
                    foreach (var item in meetingSummaryPayload.actionPlan)
                    {
                        // prevent issues with wrong date formats
                        DateTime? parsedPossibleDeadline = null;
                        if (DateTime.TryParse(item.possibleDeadline, out DateTime parsed))
                        {
                            parsedPossibleDeadline = parsed;
                        }

                        mt_tasks.Add(new MeetingTranscriptionTask()
                        {
                            MeetingTranscription = mt,
                            userName = item.responsible.name ?? "Unknown",
                            title = item.action,
                            suggestDate = parsedPossibleDeadline,
                            createdAt = DateTime.UtcNow

                        }); ;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error parsing meetingSummaryPayload: {ex.Message}");
            }

            await SaveTranscription(requestGuid, mt, mts, mt_tasks);

            return new NotetakerFinishMeetingResult
            {
                MeetingContentId = mt.contentId,
                TranscriptionId = mt.transcriptionId,
                SummaryId = mts.summaryId
            };

        }

        private static async System.Threading.Tasks.Task<bool> SaveTranscription(Guid contentKey, MeetingTranscription mt, MeetingTranscriptionSummary mts, List<MeetingTranscriptionTask> mt_tasks)
        {
            try
            {
                AtlasModelCore _md = new AtlasModelCore();
                var content = await (from c in _md.Content
                                     join m in _md.Meeting on c.contentId equals m.contentId
                                     where c.contentKey == contentKey
                                     select c).Include(o => o.MeetingTranscriptions).FirstOrDefaultAsync();
                if (content == null)
                {
                    return false;
                }

                mt.contentId = content.contentId;

                foreach (var item in mt_tasks) // set contentid
                {
                    item.contentId = content.contentId;
                }

                mt.MeetingTranscriptionTasks = mt_tasks;

                mts.CreatedAt = DateTime.UtcNow;
                mts.ContentId = content.contentId;

                mt.MeetingTranscriptionSummaries.Add(mts);

                var added = _md.MeetingTranscription.Add(mt);
                _md.SaveChanges();
                return true;
            }
            catch (Exception e)
            {
                Console.WriteLine($"Error saving transcription: {e.Message}");

                throw new Exception("Error saving transcription", e);
            }
        }

        public static async System.Threading.Tasks.Task<bool> UpdateMeetingNotetakerStatus(NotetakerStatusUpdateRequest request)
        {
            if (request == null)
            {
                throw new ArgumentNullException(nameof(request), "The request object cannot be null.");
            }

            var contentKey = new Guid(request.meetingId);
            var meetingNotetakerStatus = request.status;
            var isStatusValid = AINotetakerFlowStatus.IsValid(meetingNotetakerStatus);

            if (!isStatusValid)
            {
                throw new BadRequestCustomException($"Invalid status: {meetingNotetakerStatus}: {request.status}");
            }

            return await UpdateMeetingNotetakerStatus(contentKey, meetingNotetakerStatus);
        }

        private static async System.Threading.Tasks.Task<bool> UpdateMeetingNotetakerStatus(Guid contentKey, string status)
        {
            AtlasModelCore _md = new AtlasModelCore();

            var content = await (from c in _md.Content
                                 join m in _md.Meeting on c.contentId equals m.contentId
                                 where c.contentKey == contentKey
                                 select c)
                                 .Include(o => o.Meeting)
                                 .FirstOrDefaultAsync();

            if (content == null)
            {
                throw new BadRequestCustomException($"No content found for the specified key: {contentKey}");
            }

            var meeting = content.Meeting.FirstOrDefault() ?? throw new InvalidOperationException("No associated meeting found for the specified content.");

            meeting.AINotetakerFlowStatus = status;

            var entries = await _md.SaveChangesAsync();

            return entries > 0;
        }

        public static async System.Threading.Tasks.Task<NotetakerMeeting> GetNotetakerMeetingByActivity(string message)
        {
            //obtem Content do tipo meeting e mapeia no NotetakerMeeting
            ContentActivity ca = Newtonsoft.Json.JsonConvert.DeserializeObject<ContentActivity>(message);
            ContentService cs = new ContentService(ca.activityUser.Value);
            var content = await cs.Get(ca.contentId);
            var meeting = content.Meeting.First();

            return ContentMeetingToNotetaker(ca, content, meeting);
        }

        public static async System.Threading.Tasks.Task<NotetakerMeeting> GetNotetakerDeletedMeetingByActivity(string message)
        {
            ContentActivity ca = Newtonsoft.Json.JsonConvert.DeserializeObject<ContentActivity>(message);

            Content content;

            using (var _md = new AtlasModelCore()) // In the future use AtlasModelReadyOnly
            {
                content = await _md.Content
                    .Include(c => c.Meeting)
                    .Include(c => c.User_Create)
                    .Where(c => c.contentId == ca.contentId)
                    .FirstOrDefaultAsync();
            }

            if (content == null)
            {
                throw new Exception($"Content with ID {ca.contentId} not found. Cannot map to NotetakerMeeting.");
            }

            return ContentMeetingToNotetaker(ca, content, content.Meeting.FirstOrDefault());
        }

        public static async System.Threading.Tasks.Task<NotetakerMeeting> GetNotetakerMeetingByChildActivity(string message)
        {
            //obtem Content do tipo meeting e mapeia no NotetakerMeeting
            ContentActivity ca = Newtonsoft.Json.JsonConvert.DeserializeObject<ContentActivity>(message);
            ContentService cs = new ContentService(ca.activityUser.Value);
            var childContent = await cs.Get(ca.contentId);
            var content = await cs.Get(childContent.parentContentId.Value);
            var meeting = content.Meeting.First();

            return ContentMeetingToNotetaker(ca, content, meeting);
        }

        static string GetLanguage(Content content)
        {
            string lang = content?.User_Create?.defaultLanguage ?? "";

            if (string.IsNullOrEmpty(lang))
            {
                AtlasModelCore _md = new AtlasModelCore();

                // workgroup may be null depending on how the content was loaded from the database
                int? clientId = content.Workgroup?.clientId ?? null;

                if (!clientId.HasValue)
                {
                    //load workgroup
                    var workgroup = _md.Workgroup.Find(content.workgroupId);
                    clientId = workgroup.clientId;
                }

                var cli = _md.Client.Find(clientId);
                lang = cli.language ?? "";
            }

            return string.IsNullOrEmpty(lang) ? "pt" : lang;
        }

        public static NotetakerMeeting ContentMeetingToNotetaker(ContentActivity ca, Content content, Meeting meeting)
        {
            //meeting do tipo NotetakerMeeting
            NotetakerMeeting notetakerMeeting = new NotetakerMeeting();

            notetakerMeeting.userId = (ca.activityUser ?? 0).ToString();
            notetakerMeeting.customMeetingId = content.contentKey.ToString();
            notetakerMeeting.tenantId = "02a5ec76-fd38-4554-a6b4-33b61fcb4083";// (ca.activityTenant ?? 0).ToString();
            notetakerMeeting.customTenantId = "02a5ec76-fd38-4554-a6b4-33b61fcb4083";// (ca.activityTenant ?? 0).ToString();
            notetakerMeeting.scheduledTime = meeting.date;

            var contentMeetingCultureCodeMapper = new
            {
                pt = MeetingCultureCode.pt_BR,
                es = MeetingCultureCode.es_ES,
                en = MeetingCultureCode.en_US
            };

            var contentCreationUserDefaultLang = GetLanguage(content);

            MeetingCultureCode targetCultureCode = MeetingCultureCode.pt_BR;
            try
            {
                targetCultureCode = contentMeetingCultureCodeMapper
                    .GetType()
                    .GetProperty(contentCreationUserDefaultLang)
                    .GetValue(contentMeetingCultureCodeMapper) as MeetingCultureCode?
                    ?? MeetingCultureCode.pt_BR;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Using default culture code (pt_BR) for meeting. Reason - error getting culture code for meeting: {ex.Message}");
            }

            notetakerMeeting.cultureCode = new
            System.Collections.Generic.List<MeetingCultureCode>
            {
                targetCultureCode
            };

            SetUpdatedMeetingDuration(meeting, content);                // get the updated duration from the activity triggered on SetStep
                                                                        // -> STATUS is READY -> duration from agendas
                                                                        // STATUS OPEN or other -> Original meeting duration
            notetakerMeeting.duration = meeting.duration ?? 60;
            notetakerMeeting.providerData = new MeetingProviderData()
            {
                providerType = MeetingProviderType.teams,
                data = new MeetingProviderDataDetails()
                {
                    meetingLink = meeting.conferenceURL
                },
            };
            return notetakerMeeting;
        }

        private static void SetUpdatedMeetingDuration(Meeting meeting, Content content)
        {
            if (content.type == ContentTypes.Meeting && content.status == "READY")
            {
                meeting.duration = content.Child_Content.Where(o => o.type == ContentTypes.MeetingAgendaItem).SelectMany(o => o.MeetingAgendaItem).Sum(m => m.time);
            }
        }
    }
}
