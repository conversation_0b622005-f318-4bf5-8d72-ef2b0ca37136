using Atlas.Data.Entities;
//using ;
//using MigraDoc.DocumentObjectModel.Shapes;
//using MigraDoc.DocumentObjectModel.Tables;
//using MigraDoc.Rendering;
//using PdfSharp.Pdf;
//using PdfSharp.Pdf.IO;
//using iTextSharp.text.pdf;
//using iTextSharp.text;

namespace Atlas.Business.Helpers
{
    public class PDFGenerator
    {
        User _currentUser;

        public PDFGenerator(User currentUser)
        {
            _currentUser = currentUser;
        }

        //public byte[] ToByteArray(MigraDoc.DocumentObjectModel.Document document)
        //{

        //    //Document document = CreateDocument();
        //    document.UseCmykColor = true;

        //    // ===== Unicode encoding and font program embedding in MigraDoc is demonstrated here =====

        //    // A flag indicating whether to create a Unicode PDF or a WinAnsi PDF file.
        //    // This setting applies to all fonts used in the PDF document.
        //    // This setting has no effect on the RTF renderer.
        //    const bool unicode = true;

        //    // An enum indicating whether to embed fonts or not.
        //    // This setting applies to all font programs used in the document.
        //    // This setting has no effect on the RTF renderer.
        //    // (The term 'font program' is used by Adobe for a file containing a font. Technically a 'font file'
        //    // is a collection of small programs and each program renders the glyph of a character when executed.
        //    // Using a font in PDFsharp may lead to the embedding of one or more font programms, because each outline
        //    // (regular, bold, italic, bold+italic, ...) has its own fontprogram)
        //    //const PdfSharp.Pdf.PdfFontEmbedding embedding = PdfSharp.Pdf.PdfFontEmbedding.Always;

        //    // ========================================================================================

        //    // Create a renderer for the MigraDoc document.
        //    PdfDocumentRenderer pdfRenderer = new PdfDocumentRenderer(unicode);

        //    // Associate the MigraDoc document with a renderer
        //    pdfRenderer.Document = document;

        //    // Layout and render document to PDF
        //    pdfRenderer.RenderDocument();

        //    // Save the document...
        //    MemoryStream ms = new MemoryStream();
        //    pdfRenderer.PdfDocument.Save(ms);

        //    return ms.ToArray();

        //}
        ///// <summary>
        ///// Creates an absolutely minimalistic document.
        ///// </summary>
        //public MigraDoc.DocumentObjectModel.Document Bluebook_GenerateHeader(Content obj, List<Attachment> attachments, List<MeetingAgendaItem> pautas)
        //{
        //    Meeting meeting = obj.Meeting.FirstOrDefault();

        //    //converting time according to timezone
        //    string db_fuso = string.IsNullOrWhiteSpace(_currentUser.defaultTimezone) ? "America/Sao_Paulo" : _currentUser.defaultTimezone;
        //    string tz = "";

        //    try
        //    {
        //        tz = TZConvert.IanaToWindows(db_fuso);

        //    }
        //    catch (InvalidTimeZoneException)
        //    {
        //        tz = "E. South America Standard Time";
        //    }

        //    TimeZoneInfo userZone = TimeZoneInfo.FindSystemTimeZoneById(tz);
        //    var meeting_date = TimeZoneInfo.ConvertTimeFromUtc(meeting.date, userZone);



        //    string footer_string = string.Format("Generated by Atlas Governance requested by {0}/{1} at {2}", _currentUser.name, _currentUser.email, TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, userZone));



        //    // Create a new MigraDoc document
        //    MigraDoc.DocumentObjectModel.Document document = new MigraDoc.DocumentObjectModel.Document();

        //    MigraDoc.DocumentObjectModel.Section section = document.AddSection();
        //    section.PageSetup.BottomMargin = 60;
        //    section.PageSetup.LeftMargin = 30;
        //    section.PageSetup.RightMargin = 30;
        //    section.PageSetup.TopMargin = 30;


        //    MigraDoc.DocumentObjectModel.Style style = document.Styles["Normal"];
        //    // Because all styles are derived from Normal, the next line changes the 
        //    // font of the whole document. Or, more exactly, it changes the font of
        //    // all styles and paragraphs that do not redefine the font.
        //    style.Font.Name = "Arial";
        //    style.Font.Size = "10pt";

        //    //muted
        //    MigraDoc.DocumentObjectModel.Style styleMuted = new MigraDoc.DocumentObjectModel.Style("muted", "Normal");
        //    styleMuted.Font.Color = new MigraDoc.DocumentObjectModel.Color(199, 199, 199);
        //    //styleMuted.Font.Size = "10pt";
        //    document.Add(styleMuted);

        //    MigraDoc.DocumentObjectModel.Style styleSemiMuted = new MigraDoc.DocumentObjectModel.Style("semimuted", "Normal");
        //    styleSemiMuted.Font.Color = new MigraDoc.DocumentObjectModel.Color(130, 130, 130);
        //    //styleMuted.Font.Size = "10pt";
        //    document.Add(styleSemiMuted);

        //    MigraDoc.DocumentObjectModel.Style textBold = new MigraDoc.DocumentObjectModel.Style("Bold", "Normal");
        //    textBold.Font.Bold = true;
        //    document.Add(textBold);

        //    MigraDoc.DocumentObjectModel.Style styleH1 = new MigraDoc.DocumentObjectModel.Style("H1", "Normal");
        //    styleH1.Font.Size = "20pt";
        //    styleH1.Font.Bold = true;
        //    document.Add(styleH1);

        //    MigraDoc.DocumentObjectModel.Style styleH2 = new MigraDoc.DocumentObjectModel.Style("H2", "Normal");
        //    styleH2.Font.Size = "16pt";
        //    styleH2.Font.Bold = true;
        //    document.Add(styleH2);

        //    MigraDoc.DocumentObjectModel.Style styleH3 = new MigraDoc.DocumentObjectModel.Style("H3", "Normal");
        //    styleH3.Font.Size = "14pt";
        //    styleH3.Font.Bold = true;
        //    document.Add(styleH3);

        //    MigraDoc.DocumentObjectModel.Style styleH4 = new MigraDoc.DocumentObjectModel.Style("H4", "Normal");
        //    styleH4.Font.Size = "12pt";
        //    styleH4.Font.Bold = true;
        //    document.Add(styleH4);


        //    //var hr = document.AddStyle("HorizontalRule", "Normal");
        //    //var hrBorder = new Border();
        //    //hrBorder.Width = "1pt";
        //    //hrBorder.Color = Colors.LightGray;
        //    //hr.ParagraphFormat.Borders.Bottom = hrBorder;
        //    //hr.ParagraphFormat.LineSpacing = 0;
        //    //hr.ParagraphFormat.SpaceBefore = 15;

        //    ////cabeçalho
        //    //var header = new Paragraph();
        //    //header.AddFormattedText(string.Format("Generated by Atlas Governance requested by {0}/{1} at {2}(UTC)", _currentUser.name, _currentUser.email, DateTime.UtcNow.ToString())
        //    //    , "muted");
        //    //section.Headers.Primary.Add(header);

        //    //rodape
        //    var footer = new MigraDoc.DocumentObjectModel.Paragraph();
        //    footer.AddFormattedText(footer_string, "muted");
        //    section.Footers.Primary.Add(footer);

        //    //main content
        //    var mainTitle = new MigraDoc.DocumentObjectModel.Paragraph();
        //    //main.AddText(meeting.title == "" ? "Meeting" : meeting.title);
        //    //main.AddText("Location: " + (meeting.location == "" ? "Not specified" : meeting.location));
        //    mainTitle.AddFormattedText("Meeting Report", "H1");




        //    //first page header table
        //    var h_table = new Table();
        //    h_table.AddColumn(70);
        //    h_table.AddColumn(200);
        //    var h_row = h_table.AddRow();
        //    h_row.VerticalAlignment = VerticalAlignment.Center;
        //    //fp header end



        //    //logo
        //    if (obj.Workgroup?.Client.defaultLogo == null)
        //    {
        //        string logoImageFilename = GetDefaultAtlasLogo();

        //        var logo = h_row[0].AddImage(logoImageFilename);
        //        logo.LockAspectRatio = true;
        //        logo.Width = 66;
        //        //fim logo
        //    }

        //    else
        //    {
        //        try
        //        {

        //            var imageToDownload = obj.Workgroup?.Client.defaultLogoBlack ?? obj.Workgroup?.Client.defaultLogo;

        //            var logo = h_row[0].AddImage(GetImageFromUrl(imageToDownload));
        //            logo.LockAspectRatio = true;
        //            logo.Width = obj.Workgroup?.Client.defaultLogoMaxWidth ?? 66;

        //        }
        //        catch (Exception ex)
        //        {
        //            string logoImageFilename = GetDefaultAtlasLogo();

        //            var logo = h_row[0].AddImage(logoImageFilename);
        //            logo.LockAspectRatio = true;
        //            logo.Width = 66;
        //        }
        //    }

        //    mainTitle.AddLineBreak();
        //    mainTitle.AddLineBreak();

        //    h_row[1].Add(mainTitle);
        //    section.Add(h_table);


        //    var details = new MigraDoc.DocumentObjectModel.Paragraph();

        //    details.AddLineBreak();
        //    details.AddLineBreak();

        //    if (!string.IsNullOrWhiteSpace(meeting.title))
        //    {
        //        details.AddFormattedText("Title: ", "Bold");
        //        details.AddFormattedText(meeting.title, "Normal");
        //        details.AddLineBreak();
        //    }

        //    details.AddFormattedText("Group: ", "Bold");
        //    details.AddFormattedText(obj.Workgroup.name, "Normal");
        //    details.AddLineBreak();

        //    details.AddFormattedText("Date: ", "Bold");
        //    details.AddFormattedText(meeting_date.ToString("f"), "Normal");
        //    details.AddLineBreak();

        //    details.AddFormattedText("Location: ", "Bold");
        //    details.AddFormattedText(meeting.location, "Normal");
        //    details.AddLineBreak();
        //    details.AddLineBreak();
        //    details.AddLineBreak();

        //    //details.AddFormattedText("", "HorizontalRule");

        //    //var hr = MigraDoc.DocumentObjectModel.

        //    //details.Format.Borders.Color = Colors.LightGray;
        //    details.Format.Borders.Bottom = new MigraDoc.DocumentObjectModel.Border { Style = MigraDoc.DocumentObjectModel.BorderStyle.Single, Color = new MigraDoc.DocumentObjectModel.Color(238, 238, 238) };



        //    section.Add(details);


        //    var agenda_title = new MigraDoc.DocumentObjectModel.Paragraph();

        //    agenda_title.AddLineBreak();
        //    agenda_title.AddFormattedText("Agenda", "H2");
        //    agenda_title.AddLineBreak();
        //    agenda_title.AddLineBreak();

        //    section.Add(agenda_title);

        //    //Section pautas = document.AddSection();
        //    var table = section.AddTable();
        //    table.Style = "Table";
        //    table.Borders.Width = 0;
        //    table.Borders.Left.Width = 0;
        //    table.Borders.Right.Width = 0;
        //    table.Rows.LeftIndent = 0;

        //    // Before you can add a row, you must define the columns
        //    Column column = table.AddColumn(50);
        //    column.Format.Alignment = MigraDoc.DocumentObjectModel.ParagraphAlignment.Left;

        //    column = table.AddColumn(300);
        //    column.Format.Alignment = MigraDoc.DocumentObjectModel.ParagraphAlignment.Left;


        //    int accumulatedTime = 0;

        //    //table.SetEdge(0, 0, 6, 2, Edge.Box, BorderStyle.Single, 0.75, Color.Empty);
        //    //var pautas = obj.Child_Content.Where(o => o.type == CrossCutting.AppEnums.ContentTypes.MeetingAgendaItem).SelectMany(o => o.MeetingAgendaItem).OrderBy(o => o.itemOrder).ToList();

        //    var hasComments = false;
        //    foreach (var pauta in pautas)
        //    {

        //        var item = pauta.Content;

        //        var time_start = meeting_date.AddMinutes(accumulatedTime);
        //        var time_end = meeting_date.AddMinutes(accumulatedTime + pauta.time);
        //        accumulatedTime += pauta.time;

        //        var par_pauta = new MigraDoc.DocumentObjectModel.Paragraph();

        //        // Create the header of the table
        //        Row row = table.AddRow();
        //        row.HeadingFormat = true;
        //        row.Format.Alignment = MigraDoc.DocumentObjectModel.ParagraphAlignment.Center;
        //        row.Format.Font.Bold = true;
        //        row.Cells[0].Format.Alignment = MigraDoc.DocumentObjectModel.ParagraphAlignment.Left;
        //        row.Cells[0].VerticalAlignment = VerticalAlignment.Top;

        //        //par_pauta.AddLineBreak();   

        //        string profilePic = item.User_Assigned.profilePic;
        //        if (string.IsNullOrWhiteSpace(profilePic))
        //        {
        //            profilePic = "https://atlasgovprod.blob.core.windows.net/atlasgovprod-pub/profile/empty-o.png";
        //        }

        //        var image = row.Cells[0].AddImage(GetImageFromUrl(profilePic));
        //        image.LockAspectRatio = true;
        //        image.Width = 33;
        //        image.WrapFormat.Style = WrapStyle.TopBottom;
        //        //image.WrapFormat = new WrapFormat().


        //        par_pauta.AddFormattedText("Title: ", "Bold");
        //        par_pauta.AddFormattedText(pauta.title, "Normal");
        //        par_pauta.AddLineBreak();

        //        par_pauta.AddFormattedText("Duration: ", "Bold");
        //        par_pauta.AddFormattedText(pauta.time.ToString() + " minutes", "Normal");
        //        par_pauta.AddFormattedText(" - " + time_start.ToShortTimeString() + " ~ " + time_end.ToShortTimeString(), "Normal");


        //        par_pauta.AddLineBreak();

        //        par_pauta.AddFormattedText("Presented by: ", "Bold");
        //        par_pauta.AddFormattedText(item.User_Assigned.name, "Normal");
        //        par_pauta.AddLineBreak();

        //        //var anexos = item.ContentAttachment.Where(o => (o.deleted ?? false) == false);
        //        //if (anexos.Count() > 0)
        //        //{
        //        //    par_pauta.AddFormattedText("Attachments: ", "Bold");
        //        //    foreach (var cta in anexos)
        //        //    {
        //        //        par_pauta.AddFormattedText(cta.Attachment.fileName, "Normal");

        //        //    }
        //        //    par_pauta.AddLineBreak();

        //        //}

        //        var anexos_ja_adicionados = new List<int>();

        //        var comentarios = item.ContentComment.ToList();
        //        if (comentarios.Count() > 0)
        //        {
        //            hasComments = true;
        //        }


        //        par_pauta.AddLineBreak();
        //        row.Cells[1].Format.Alignment = MigraDoc.DocumentObjectModel.ParagraphAlignment.Left;
        //        row.Cells[1].VerticalAlignment = VerticalAlignment.Center;

        //        row.Cells[1].Add(par_pauta);

        //        //par_pauta.Format.Borders.Bottom = new Border { Style = BorderStyle.Single, Color = Colors.LightGray };

        //        //section.Add(par_pauta);

        //        //details.AddFormattedText("", "HorizontalRule");
        //    }


        //    /////////////////////////////////////////////////////
        //    //COMMENTS
        //    ////////////////////////////////////////////////////
        //    var spacer_paragraph3 = new MigraDoc.DocumentObjectModel.Paragraph();
        //    spacer_paragraph3.AddLineBreak();
        //    spacer_paragraph3.Format.Borders.Bottom = new MigraDoc.DocumentObjectModel.Border { Style = MigraDoc.DocumentObjectModel.BorderStyle.Single, Color = new MigraDoc.DocumentObjectModel.Color(238, 238, 238) };

        //    section.Add(spacer_paragraph3);

        //    var comments_title = new MigraDoc.DocumentObjectModel.Paragraph();

        //    comments_title.AddLineBreak();
        //    comments_title.AddFormattedText("Comments", "H2");
        //    comments_title.AddLineBreak();
        //    comments_title.AddLineBreak();

        //    section.Add(comments_title);
        //    foreach (var pauta in pautas)
        //    {
        //        var item = pauta.Content;
        //        var comentarios = item.ContentComment.ToList();
        //        if (comentarios.Count() > 0)
        //        {
        //            var par_pauta = new MigraDoc.DocumentObjectModel.Paragraph();

        //            par_pauta.AddFormattedText(pauta.title + ": ", "H3");
        //            par_pauta.AddLineBreak();
        //            par_pauta.AddLineBreak();
        //            section.Add(par_pauta);

        //            foreach (var comm in comentarios)
        //            {
        //                var par_comm = new MigraDoc.DocumentObjectModel.Paragraph();
        //                par_comm.Format.LeftIndent = 10;
        //                par_comm.AddFormattedText(comm.User.name + ": ", MigraDoc.DocumentObjectModel.TextFormat.Italic | MigraDoc.DocumentObjectModel.TextFormat.Bold);


        //                //using (var htmlWorker = new iTextSharp.text.html.simpleparser.HTMLWorker(document))
        //                //{

        //                //}

        //                par_comm.AddFormattedText(HtmlToPlainText(comm.text), MigraDoc.DocumentObjectModel.TextFormat.NotBold);
        //                par_comm.AddLineBreak();
        //                par_comm.AddFormattedText(TimeZoneInfo.ConvertTimeFromUtc(comm.date, userZone).ToString(), "semimuted");
        //                par_comm.AddLineBreak();
        //                par_comm.AddLineBreak();

        //                section.Add(par_comm);
        //            }
        //        }
        //    }

        //    /////////////////////////////////////////////////////
        //    //ATTACHMENTS
        //    /////////////////////////////////////////////////////
        //    var spacer_paragraph = new MigraDoc.DocumentObjectModel.Paragraph();
        //    spacer_paragraph.AddLineBreak();
        //    spacer_paragraph.Format.Borders.Bottom = new MigraDoc.DocumentObjectModel.Border { Style = MigraDoc.DocumentObjectModel.BorderStyle.Single, Color = new MigraDoc.DocumentObjectModel.Color(238, 238, 238) };

        //    section.Add(spacer_paragraph);

        //    var attachments_title = new MigraDoc.DocumentObjectModel.Paragraph();

        //    attachments_title.AddLineBreak();
        //    attachments_title.AddFormattedText("Attachments", "H2");
        //    attachments_title.AddLineBreak();
        //    attachments_title.AddFormattedText("List of included attachments:", "Normal");
        //    attachments_title.AddLineBreak();
        //    attachments_title.AddLineBreak();

        //    section.Add(attachments_title);

        //    //PdfDocumentRenderer renderer = new PdfDocumentRenderer(true, PdfSharp.Pdf.PdfFontEmbedding.Always);
        //    //renderer.Document = document;

        //    //table

        //    var attachment_ordered = pautas.SelectMany(o => o.Content.ContentAttachment).Where(o => (o.deleted ?? false) == false).Select(a => a.Attachment).ToList();

        //    if (attachment_ordered.Where(o => (o.extension ?? "").ToLower() == "pdf" || (o.processedExtension ?? "").ToLower() == "pdf").Count() > 0)
        //    {

        //        var attachments_table = section.AddTable();
        //        attachments_table.Style = "Table";
        //        attachments_table.Borders.Width = 0;
        //        attachments_table.Borders.Left.Width = 0;
        //        attachments_table.Borders.Right.Width = 0;
        //        attachments_table.Rows.LeftIndent = 0;

        //        // Before you can add a row, you must define the columns
        //        var attcolumn1 = attachments_table.AddColumn(50);
        //        attcolumn1.Format.Alignment = MigraDoc.DocumentObjectModel.ParagraphAlignment.Left;

        //        var attcolumn2 = attachments_table.AddColumn(300);
        //        attcolumn2.Format.Alignment = MigraDoc.DocumentObjectModel.ParagraphAlignment.Left;


        //        foreach (var item in attachment_ordered)
        //        {
        //            var row = attachments_table.AddRow();
        //            var att_par = row[1].AddParagraph();

        //            //att_par.AddFormattedText(item.fileName, "Normal");
        //            //att_par.AddLineBreak();

        //            //att_par.AddFormattedText(item, "Normal");
        //            //att_par.AddLineBreak();

        //            var hasConversion = !string.IsNullOrWhiteSpace(item.processedExtension) && item.processedExtension.ToLower() == "pdf";

        //            var ext = item.extension.ToLower();
        //            var path = item.path;

        //            if (hasConversion)
        //            {
        //                ext = item.processedExtension;
        //                path = item.processedUrl;
        //            }


        //            if (ext.ToLower() == "pdf")
        //            {
        //                //PdfDocument inputDocument = PdfReader.Open(GetDocument(obj.contentId, path), PdfDocumentOpenMode.Modify);

        //                att_par.AddFormattedText(item.fileName, "Normal");
        //                att_par.AddLineBreak();
        //            }
        //            else
        //            {
        //                //att_par.AddFormattedText("NOT SUPPORTED FILETYPE", "Normal");
        //                //att_par.AddLineBreak();
        //                //not supported
        //            }

        //        }

        //        //return document;
        //    }
        //    else
        //    {
        //        var no_attachments = new MigraDoc.DocumentObjectModel.Paragraph();
        //        no_attachments.AddLineBreak();
        //        no_attachments.AddFormattedText("No attachments found.", "Normal");
        //        no_attachments.AddLineBreak();
        //        section.Add(no_attachments);

        //    }

        //    //renderer.RenderDocument();
        //    return document;
        //}
        //public MigraDoc.DocumentObjectModel.Document Bluebook_GenerateHeader2(Content obj, List<Attachment> attachments, List<MeetingAgendaItem> pautas)
        //{
        //    Meeting meeting = obj.Meeting.FirstOrDefault();

        //    //converting time according to timezone
        //    string db_fuso = string.IsNullOrWhiteSpace(_currentUser.defaultTimezone) ? "America/Sao_Paulo" : _currentUser.defaultTimezone;
        //    string tz = "";

        //    try
        //    {
        //        tz = TZConvert.IanaToWindows(db_fuso);

        //    }
        //    catch (InvalidTimeZoneException)
        //    {
        //        tz = "E. South America Standard Time";
        //    }

        //    TimeZoneInfo userZone = TimeZoneInfo.FindSystemTimeZoneById(tz);
        //    var meeting_date = TimeZoneInfo.ConvertTimeFromUtc(meeting.date, userZone);



        //    string footer_string = string.Format("Generated by Atlas Governance requested by {0}/{1} at {2}", _currentUser.name, _currentUser.email, TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, userZone));



        //    // Create a new MigraDoc document
        //    MigraDoc.DocumentObjectModel.Document document = new MigraDoc.DocumentObjectModel.Document();

        //    MigraDoc.DocumentObjectModel.Section section = document.AddSection();
        //    //section.PageSetup.PageFormat = MigraDoc.DocumentObjectModel.PageFormat.A4;
        //    document.DefaultPageSetup.PageFormat = MigraDoc.DocumentObjectModel.PageFormat.A4;

        //    document.DefaultPageSetup.BottomMargin = 60;
        //    document.DefaultPageSetup.LeftMargin = 30;
        //    document.DefaultPageSetup.RightMargin = 30;
        //    document.DefaultPageSetup.TopMargin = 30;

        //    float sectionWidth = document.DefaultPageSetup.PageWidth - document.DefaultPageSetup.LeftMargin - document.DefaultPageSetup.RightMargin;
        //    float col12 = sectionWidth;
        //    float col11 = (sectionWidth / 12) * 11;
        //    float col10 = (sectionWidth / 12) * 10;
        //    float col9 = (sectionWidth / 12) * 9;
        //    float col8 = (sectionWidth / 12) * 8;
        //    float col7 = (sectionWidth / 12) * 7;
        //    float col6 = (sectionWidth / 12) * 6;
        //    float col5 = (sectionWidth / 12) * 5;
        //    float col4 = (sectionWidth / 12) * 4;
        //    float col3 = (sectionWidth / 12) * 3;
        //    float col2 = (sectionWidth / 12) * 2;
        //    float col1 = (sectionWidth / 12) * 1;


        //    MigraDoc.DocumentObjectModel.Style style = document.Styles["Normal"];
        //    // Because all styles are derived from Normal, the next line changes the 
        //    // font of the whole document. Or, more exactly, it changes the font of
        //    // all styles and paragraphs that do not redefine the font.
        //    style.Font.Name = "Helvetica";
        //    style.Font.Size = "10pt";



        //    //muted
        //    MigraDoc.DocumentObjectModel.Style styleMuted = new MigraDoc.DocumentObjectModel.Style("muted", "Normal");
        //    styleMuted.Font.Color = new MigraDoc.DocumentObjectModel.Color(199, 199, 199);
        //    //styleMuted.Font.Size = "10pt";
        //    document.Add(styleMuted);

        //    MigraDoc.DocumentObjectModel.Style styleSemiMuted = new MigraDoc.DocumentObjectModel.Style("semimuted", "Normal");
        //    styleSemiMuted.Font.Color = new MigraDoc.DocumentObjectModel.Color(130, 130, 130);
        //    //styleMuted.Font.Size = "10pt";
        //    document.Add(styleSemiMuted);

        //    MigraDoc.DocumentObjectModel.Style textBold = new MigraDoc.DocumentObjectModel.Style("Bold", "Normal");
        //    textBold.Font.Bold = true;
        //    document.Add(textBold);

        //    MigraDoc.DocumentObjectModel.Style styleH1 = new MigraDoc.DocumentObjectModel.Style("H1", "Normal");
        //    styleH1.Font.Size = "20pt";
        //    styleH1.Font.Bold = true;
        //    document.Add(styleH1);

        //    MigraDoc.DocumentObjectModel.Style styleH2 = new MigraDoc.DocumentObjectModel.Style("H2", "Normal");
        //    styleH2.Font.Size = "16pt";
        //    styleH2.Font.Bold = false;
        //    document.Add(styleH2);

        //    MigraDoc.DocumentObjectModel.Style styleH2B = new MigraDoc.DocumentObjectModel.Style("H2B", "Normal");
        //    styleH2B.Font.Size = "16pt";
        //    styleH2B.Font.Bold = true;
        //    document.Add(styleH2B);

        //    MigraDoc.DocumentObjectModel.Style styleH3 = new MigraDoc.DocumentObjectModel.Style("H3", "Normal");
        //    styleH3.Font.Size = "14pt";
        //    styleH3.Font.Bold = true;
        //    document.Add(styleH3);

        //    MigraDoc.DocumentObjectModel.Style styleH4 = new MigraDoc.DocumentObjectModel.Style("H4", "Normal");
        //    styleH4.Font.Size = "12pt";
        //    styleH4.Font.Bold = true;
        //    document.Add(styleH4);

        //    MigraDoc.DocumentObjectModel.Style styleSmall1 = new MigraDoc.DocumentObjectModel.Style("Small1", "Normal");
        //    styleSmall1.Font.Size = "9pt";
        //    document.Add(styleSmall1);

        //    MigraDoc.DocumentObjectModel.Style styleSmall1muted = new MigraDoc.DocumentObjectModel.Style("Small1Muted", "muted");
        //    styleSmall1muted.Font.Size = "9pt";
        //    document.Add(styleSmall1muted);

        //    MigraDoc.DocumentObjectModel.Style styleSmall2 = new MigraDoc.DocumentObjectModel.Style("Small2", "Normal");
        //    styleSmall2.Font.Size = "8pt";
        //    document.Add(styleSmall2);

        //    MigraDoc.DocumentObjectModel.Style styleSmall2muted = new MigraDoc.DocumentObjectModel.Style("Small2Muted", "muted");
        //    styleSmall2muted.Font.Size = "8pt";
        //    document.Add(styleSmall2muted);


        //    //var hr = document.AddStyle("HorizontalRule", "Normal");
        //    //var hrBorder = new Border();
        //    //hrBorder.Width = "1pt";
        //    //hrBorder.Color = Colors.LightGray;
        //    //hr.ParagraphFormat.Borders.Bottom = hrBorder;
        //    //hr.ParagraphFormat.LineSpacing = 0;
        //    //hr.ParagraphFormat.SpaceBefore = 15;

        //    ////cabeçalho
        //    //var header = new Paragraph();
        //    //header.AddFormattedText(string.Format("Generated by Atlas Governance requested by {0}/{1} at {2}(UTC)", _currentUser.name, _currentUser.email, DateTime.UtcNow.ToString())
        //    //    , "muted");
        //    //section.Headers.Primary.Add(header);

        //    //rodape
        //    var footer = new MigraDoc.DocumentObjectModel.Paragraph();
        //    footer.AddFormattedText(footer_string, "muted");
        //    section.Footers.Primary.Add(footer);

        //    //main content
        //    var mainTitle = new MigraDoc.DocumentObjectModel.Paragraph();
        //    //main.AddText(meeting.title == "" ? "Meeting" : meeting.title);
        //    //main.AddText("Location: " + (meeting.location == "" ? "Not specified" : meeting.location));
        //    mainTitle.Format.Alignment = MigraDoc.DocumentObjectModel.ParagraphAlignment.Center;
        //    mainTitle.AddFormattedText("Meeting Report", "H1");
        //    if (!string.IsNullOrEmpty(meeting.title))
        //    {
        //        mainTitle.AddLineBreak();
        //        mainTitle.AddFormattedText("Meeting Report", "H2");

        //    }




        //    //first page header table
        //    var h_table = new Table();
        //    h_table.AddColumn(col2);
        //    h_table.AddColumn(col8);
        //    var h_row = h_table.AddRow();
        //    h_row.VerticalAlignment = VerticalAlignment.Center;
        //    //fp header end



        //    //logo
        //    if (obj.Workgroup?.Client.defaultLogo == null)
        //    {
        //        string logoImageFilename = GetDefaultAtlasLogo();

        //        var logo = h_row[0].AddImage(logoImageFilename);
        //        logo.LockAspectRatio = true;
        //        logo.Width = 66;
        //        //fim logo
        //    }

        //    else
        //    {
        //        try
        //        {

        //            var imageToDownload = obj.Workgroup?.Client.defaultLogoBlack ?? obj.Workgroup?.Client.defaultLogo;

        //            var logo = h_row[0].AddImage(GetImageFromUrl(imageToDownload));
        //            logo.LockAspectRatio = true;
        //            logo.Width = obj.Workgroup?.Client.defaultLogoMaxWidth ?? 66;

        //        }
        //        catch (Exception ex)
        //        {
        //            string logoImageFilename = GetDefaultAtlasLogo();

        //            var logo = h_row[0].AddImage(logoImageFilename);
        //            logo.LockAspectRatio = true;
        //            logo.Width = 66;
        //        }
        //    }

        //    mainTitle.AddLineBreak();
        //    mainTitle.AddLineBreak();

        //    h_row[1].Add(mainTitle);
        //    section.Add(h_table);

        //    var containerTable = new MigraDoc.DocumentObjectModel.Tables.Table();

        //    var colunaDetails = containerTable.AddColumn(col6);
        //    var attendeesTable = containerTable.AddColumn(col6);

        //    var details = new MigraDoc.DocumentObjectModel.Paragraph();

        //    details.AddLineBreak();
        //    details.AddLineBreak();

        //    if (!string.IsNullOrWhiteSpace(meeting.title))
        //    {
        //        details.AddFormattedText("Title: ", "Bold");
        //        details.AddFormattedText(meeting.title, "Normal");
        //        details.AddLineBreak();
        //    }

        //    details.AddFormattedText("Group: ", "Bold");
        //    details.AddFormattedText(obj.Workgroup.name, "Normal");
        //    details.AddLineBreak();

        //    details.AddFormattedText("Date: ", "Bold");
        //    details.AddFormattedText(meeting_date.ToString("f"), "Normal");
        //    details.AddLineBreak();

        //    details.AddFormattedText("Location: ", "Bold");
        //    details.AddFormattedText(meeting.location, "Normal");
        //    details.AddLineBreak();
        //    details.AddLineBreak();
        //    details.AddLineBreak();

        //    //details.AddFormattedText("", "HorizontalRule");

        //    //var hr = MigraDoc.DocumentObjectModel.

        //    //details.Format.Borders.Color = Colors.LightGray;
        //    details.Format.Borders.Bottom = new MigraDoc.DocumentObjectModel.Border { Style = MigraDoc.DocumentObjectModel.BorderStyle.Single, Color = new MigraDoc.DocumentObjectModel.Color(238, 238, 238) };


        //    var r1 = containerTable.AddRow();
        //    r1[0].Add(details);


        //    //subscribers

        //    var subscribersPar = new MigraDoc.DocumentObjectModel.Paragraph();
        //    subscribersPar.AddFormattedText("Subscribers: ", "Bold");
        //    subscribersPar.AddLineBreak();

        //    foreach (var item in obj.ContentSubscriber)
        //    {
        //        subscribersPar.AddFormattedText("- " + item.User.name, "Small1");
        //        subscribersPar.AddLineBreak();

        //    }

        //    r1[1].Add(subscribersPar);

        //    section.Add(containerTable);


        //    var agenda_title = new MigraDoc.DocumentObjectModel.Paragraph();

        //    agenda_title.AddLineBreak();
        //    agenda_title.AddFormattedText("Agenda", "H2B");
        //    agenda_title.AddLineBreak();
        //    agenda_title.AddLineBreak();

        //    section.Add(agenda_title);

        //    //Section pautas = document.AddSection();
        //    var agendaTable = section.AddTable();
        //    agendaTable.Style = "Table";
        //    agendaTable.Borders.Width = 0;
        //    agendaTable.Borders.Left.Width = 0;
        //    agendaTable.Borders.Right.Width = 0;
        //    agendaTable.Rows.LeftIndent = 0;

        //    // Before you can add a row, you must define the columns
        //    var agenda_titleColumn = agendaTable.AddColumn(col4);
        //    agenda_titleColumn.Format.Alignment = MigraDoc.DocumentObjectModel.ParagraphAlignment.Left;

        //    var agenda_timeColumn = agendaTable.AddColumn(col2);
        //    agenda_timeColumn.Format.Alignment = MigraDoc.DocumentObjectModel.ParagraphAlignment.Left;

        //    var agenda_userColumn = agendaTable.AddColumn(col1);
        //    agenda_userColumn.Format.Alignment = MigraDoc.DocumentObjectModel.ParagraphAlignment.Left;

        //    var agenda_attachmentColumn = agendaTable.AddColumn(col5);
        //    agenda_attachmentColumn.Format.Alignment = MigraDoc.DocumentObjectModel.ParagraphAlignment.Left;

        //    //var agenda_pageColumn = agendaTable.AddColumn(col1);
        //    //agenda_pageColumn.Format.Alignment = MigraDoc.DocumentObjectModel.ParagraphAlignment.Left;

        //    int accumulatedTime = 0;

        //    //table.SetEdge(0, 0, 6, 2, Edge.Box, BorderStyle.Single, 0.75, Color.Empty);
        //    //var pautas = obj.Child_Content.Where(o => o.type == CrossCutting.AppEnums.ContentTypes.MeetingAgendaItem).SelectMany(o => o.MeetingAgendaItem).OrderBy(o => o.itemOrder).ToList();

        //    var hasComments = false;
        //    int pautaCount = 0;
        //    foreach (var pauta in pautas)
        //    {
        //        pautaCount += 1;

        //        var item = pauta.Content;

        //        var time_start = meeting_date.AddMinutes(accumulatedTime);
        //        var time_end = meeting_date.AddMinutes(accumulatedTime + pauta.time);
        //        accumulatedTime += pauta.time;


        //        // Create the header of the table
        //        Row agendaRow = agendaTable.AddRow();
        //        agendaRow.HeadingFormat = true;
        //        agendaRow.Format.Alignment = MigraDoc.DocumentObjectModel.ParagraphAlignment.Center;
        //        agendaRow.Format.Font.Bold = true;
        //        agendaRow.Cells[0].Format.Alignment = MigraDoc.DocumentObjectModel.ParagraphAlignment.Left;
        //        agendaRow.Cells[0].VerticalAlignment = VerticalAlignment.Top;

        //        //par_pauta.AddLineBreak();   

        //        string profilePic = item.User_Assigned.profilePic;
        //        if (string.IsNullOrWhiteSpace(profilePic))
        //        {
        //            profilePic = "https://atlasgovprod.blob.core.windows.net/atlasgovprod-pub/profile/empty-o.png";
        //        }




        //        var title_par = new MigraDoc.DocumentObjectModel.Paragraph();

        //        //par_pauta.AddFormattedText("Title: ", "Bold");
        //        title_par.AddFormattedText(pauta.title, "Bold");
        //        title_par.AddLineBreak();

        //        agendaRow.Cells[0].Add(title_par);
        //        agendaRow.Cells[0].Format.Alignment = MigraDoc.DocumentObjectModel.ParagraphAlignment.Left;
        //        agendaRow.Cells[0].VerticalAlignment = VerticalAlignment.Top;

        //        var time_par = new MigraDoc.DocumentObjectModel.Paragraph();

        //        //par_pauta.AddFormattedText("Duration: ", "Bold");
        //        time_par.AddFormattedText(time_start.ToShortTimeString() + " ~ " + time_end.ToShortTimeString(), "Small2");
        //        time_par.AddLineBreak();
        //        time_par.AddFormattedText(pauta.time.ToString() + " minutes", "Small2");
        //        time_par.AddLineBreak();
        //        agendaRow.Cells[1].Add(time_par);

        //        var user_par = new MigraDoc.DocumentObjectModel.Paragraph();

        //        var image = user_par.AddImage(GetImageFromUrl(profilePic));
        //        image.LockAspectRatio = true;
        //        image.Width = 24;
        //        image.WrapFormat.Style = WrapStyle.TopBottom;
        //        user_par.AddLineBreak();

        //        user_par.AddFormattedText(item.User_Assigned.name, "Small2");
        //        user_par.AddLineBreak();

        //        agendaRow.Cells[2].Add(user_par);



        //        //ATTACHMENTS
        //        var agendaAttachmentsOrdered = item.ContentAttachment.Where(o => (o.deleted ?? false) == false).Select(a => a.Attachment).ToList();
        //        var countAttachments = agendaAttachmentsOrdered.Where(o => (o.extension ?? "").ToLower() == "pdf" || (o.processedExtension ?? "").ToLower() == "pdf").Count();
        //        if (countAttachments > 0)
        //        {
        //            agendaRow.Cells[3].Format.Alignment = MigraDoc.DocumentObjectModel.ParagraphAlignment.Left;
        //            var textFrame = new TextFrame();
        //            //textFrame.Height = countAttachments * 25;

        //            var attachmentTable = textFrame.AddTable();

        //            attachmentTable.AddColumn((agendaRow.Cells[3].Column.Width / 12) * 10);
        //            attachmentTable.AddColumn((agendaRow.Cells[3].Column.Width / 12) * 2);

        //            agendaRow[3].Add(textFrame);

        //            foreach (var att in agendaAttachmentsOrdered)
        //            {
        //                var row = attachmentTable.AddRow();
        //                var attachmentPar = new MigraDoc.DocumentObjectModel.Paragraph();
        //                attachmentPar.AddFormattedText(att.fileName, "Small1");
        //                attachmentPar.AddLineBreak();
        //                row[0].Add(attachmentPar);
        //                var pageNumberPar = row[1].AddParagraph("1");
        //                pageNumberPar.Format.Alignment = MigraDoc.DocumentObjectModel.ParagraphAlignment.Right;
        //                row.BottomPadding = 5;

        //            }
        //            agendaRow[3].Row.HeightRule = RowHeightRule.Auto;
        //            //agendaRow.Cells[3].Add(attachmentPar);
        //        }


        //        //par_pauta.Format.Borders.Bottom = new Border { Style = BorderStyle.Single, Color = Colors.LightGray };

        //        //section.Add(par_pauta);

        //        //details.AddFormattedText("", "HorizontalRule");
        //        agendaRow.TopPadding = 20;
        //        agendaRow.BottomPadding = 20;
        //        if (pautaCount != pautas.Count)
        //        {
        //            var bottomBorder = new MigraDoc.DocumentObjectModel.Border();
        //            bottomBorder.Color = new MigraDoc.DocumentObjectModel.Color(240, 240, 240);
        //            bottomBorder.Style = MigraDoc.DocumentObjectModel.BorderStyle.Single;
        //            bottomBorder.Width = 1;
        //            agendaRow.Borders.Bottom = bottomBorder;
        //        }
        //    }


        //    /////////////////////////////////////////////////////
        //    //COMMENTS
        //    ////////////////////////////////////////////////////
        //    var spacer_paragraph3 = new MigraDoc.DocumentObjectModel.Paragraph();
        //    spacer_paragraph3.AddLineBreak();
        //    spacer_paragraph3.Format.Borders.Bottom = new MigraDoc.DocumentObjectModel.Border { Style = MigraDoc.DocumentObjectModel.BorderStyle.Single, Color = new MigraDoc.DocumentObjectModel.Color(238, 238, 238) };

        //    section.Add(spacer_paragraph3);

        //    var comments_title = new MigraDoc.DocumentObjectModel.Paragraph();

        //    comments_title.AddLineBreak();
        //    comments_title.AddFormattedText("Comments", "H2B");
        //    comments_title.AddLineBreak();
        //    comments_title.AddLineBreak();

        //    section.Add(comments_title);
        //    foreach (var pauta in pautas)
        //    {
        //        var item = pauta.Content;
        //        var comentarios = item.ContentComment.ToList();
        //        if (comentarios.Count() > 0)
        //        {
        //            var par_pauta = new MigraDoc.DocumentObjectModel.Paragraph();

        //            par_pauta.AddFormattedText(pauta.title + ": ", "H3");
        //            par_pauta.AddLineBreak();
        //            par_pauta.AddLineBreak();
        //            section.Add(par_pauta);

        //            foreach (var comm in comentarios)
        //            {
        //                var par_comm = new MigraDoc.DocumentObjectModel.Paragraph();
        //                par_comm.Format.LeftIndent = 10;
        //                par_comm.AddFormattedText(comm.User.name + ": ", MigraDoc.DocumentObjectModel.TextFormat.Italic | MigraDoc.DocumentObjectModel.TextFormat.Bold);


        //                //using (var htmlWorker = new iTextSharp.text.html.simpleparser.HTMLWorker(document))
        //                //{

        //                //}

        //                par_comm.AddFormattedText(HtmlToPlainText(comm.text), MigraDoc.DocumentObjectModel.TextFormat.NotBold);
        //                par_comm.AddLineBreak();
        //                par_comm.AddFormattedText(TimeZoneInfo.ConvertTimeFromUtc(comm.date, userZone).ToString(), "semimuted");
        //                par_comm.AddLineBreak();
        //                par_comm.AddLineBreak();

        //                section.Add(par_comm);
        //            }
        //        }
        //    }

        //    /////////////////////////////////////////////////////
        //    //ATTACHMENTS
        //    /////////////////////////////////////////////////////
        //    var spacer_paragraph = new MigraDoc.DocumentObjectModel.Paragraph();
        //    spacer_paragraph.AddLineBreak();
        //    spacer_paragraph.Format.Borders.Bottom = new MigraDoc.DocumentObjectModel.Border { Style = MigraDoc.DocumentObjectModel.BorderStyle.Single, Color = new MigraDoc.DocumentObjectModel.Color(238, 238, 238) };

        //    section.Add(spacer_paragraph);

        //    var attachments_title = new MigraDoc.DocumentObjectModel.Paragraph();

        //    attachments_title.AddLineBreak();
        //    attachments_title.AddFormattedText("Attachments", "H2B");
        //    attachments_title.AddLineBreak();
        //    attachments_title.AddFormattedText("List of included attachments:", "Normal");
        //    attachments_title.AddLineBreak();
        //    attachments_title.AddLineBreak();

        //    section.Add(attachments_title);

        //    //PdfDocumentRenderer renderer = new PdfDocumentRenderer(true, PdfSharp.Pdf.PdfFontEmbedding.Always);
        //    //renderer.Document = document;

        //    //table

        //    var attachment_ordered = pautas.SelectMany(o => o.Content.ContentAttachment).Where(o => (o.deleted ?? false) == false).Select(a => a.Attachment).ToList();

        //    if (attachment_ordered.Where(o => (o.extension ?? "").ToLower() == "pdf" || (o.processedExtension ?? "").ToLower() == "pdf").Count() > 0)
        //    {

        //        var attachments_table = section.AddTable();
        //        attachments_table.Style = "Table";
        //        attachments_table.Borders.Width = 0;
        //        attachments_table.Borders.Left.Width = 0;
        //        attachments_table.Borders.Right.Width = 0;
        //        attachments_table.Rows.LeftIndent = 0;

        //        // Before you can add a row, you must define the columns
        //        var attcolumn1 = attachments_table.AddColumn(50);
        //        attcolumn1.Format.Alignment = MigraDoc.DocumentObjectModel.ParagraphAlignment.Left;

        //        var attcolumn2 = attachments_table.AddColumn(300);
        //        attcolumn2.Format.Alignment = MigraDoc.DocumentObjectModel.ParagraphAlignment.Left;


        //        foreach (var item in attachment_ordered)
        //        {
        //            var row = attachments_table.AddRow();
        //            var att_par = row[1].AddParagraph();

        //            //att_par.AddFormattedText(item.fileName, "Normal");
        //            //att_par.AddLineBreak();

        //            //att_par.AddFormattedText(item, "Normal");
        //            //att_par.AddLineBreak();

        //            var hasConversion = !string.IsNullOrWhiteSpace(item.processedExtension) && item.processedExtension.ToLower() == "pdf";

        //            var ext = item.extension.ToLower();
        //            var path = item.path;

        //            if (hasConversion)
        //            {
        //                ext = item.processedExtension;
        //                path = item.processedUrl;
        //            }


        //            if (ext.ToLower() == "pdf")
        //            {
        //                //PdfDocument inputDocument = PdfReader.Open(GetDocument(obj.contentId, path), PdfDocumentOpenMode.Modify);

        //                att_par.AddFormattedText(item.fileName, "Normal");
        //                att_par.AddLineBreak();
        //            }
        //            else
        //            {
        //                //att_par.AddFormattedText("NOT SUPPORTED FILETYPE", "Normal");
        //                //att_par.AddLineBreak();
        //                //not supported
        //            }

        //        }

        //        //return document;
        //    }
        //    else
        //    {
        //        var no_attachments = new MigraDoc.DocumentObjectModel.Paragraph();
        //        no_attachments.AddLineBreak();
        //        no_attachments.AddFormattedText("No attachments found.", "Normal");
        //        no_attachments.AddLineBreak();
        //        section.Add(no_attachments);

        //    }

        //    //renderer.RenderDocument();
        //    return document;
        //}

        //private static string GetDefaultAtlasLogo()
        //{
        //    /*
        //     * pingas wpf
        //     * byte[] logo_array = null;

        //    System.Drawing.ImageConverter converter = new System.Drawing.ImageConverter();
        //    logo_array = (byte[])converter.ConvertTo(Atlas.Business.Resources.ReportResource.logo_green, typeof(byte[]));

        //    string logoImageFilename = MigraDocFilenameFromByteArray(logo_array);
        //    return logoImageFilename;*/
        //    return "";

        //}

        //private static byte[] GetDefaultAtlasLogoWhite()
        //{
        //    /*
        //     * pingas wpf
        //     * 
        //     * byte[] logo_array = null;

        //    System.Drawing.ImageConverter converter = new System.Drawing.ImageConverter();
        //    logo_array = (byte[])converter.ConvertTo(Atlas.Business.Resources.ReportResource.logo_white, typeof(byte[]));

        //    return logo_array;*/

        //    return new byte[] { };
        //}

        //public byte[] Bluebook_GenerateDocument(Content obj, List<Attachment> attachments)
        //{

        //    AtlasModelCore _md = new AtlasModelCore();

        //    //get data for Bluebook Generation
        //    var pautas = _md.MeetingAgendaItem
        //            .Where(c => c.Content.parentContentId == obj.contentId && c.Content.type == "MeetingAgendaItem" && c.Content.deleted != true)
        //            .Include(o => o.Content)
        //            .Include(o => o.Content.ContentSubscriber)
        //            .Include(o => o.Content.ContentSubscriber.Select(a => a.User))
        //            .ToList().OrderBy(o => o.itemOrder).ThenBy(o => o.Content.createDate).ToList();

        //    //get Comments and attachments for each Agenda Item
        //    foreach (var item in pautas)
        //    {
        //        item.Content.ContentComment = _md.ContentComment.Where(o => o.contentId == item.contentId).Include(o => o.User).ToList();
        //        item.Content.ContentAttachment = _md.ContentAttachment.Where(o => o.contentId == item.contentId).Include(o => o.Attachment).ToList();
        //        item.Content.User_Assigned = _md.User.Where(o => o.userId == item.Content.assignedUser.Value).FirstOrDefault();
        //    }

        //    //Generate header for this bluebook
        //    MigraDoc.DocumentObjectModel.Document headerDoc = null;

        //    headerDoc = this.Bluebook_GenerateHeader(obj, attachments, pautas);
            
        //    //convert it to ByteArray
        //    var headerBytes = this.ToByteArray(headerDoc);

        //    // bundle it all together merging the documents
        //    return Bluebook_MergeDocuments(obj, pautas, headerBytes);
        //}

        //private byte[] Bluebook_MergeDocuments(Content obj, List<MeetingAgendaItem> pautas, byte[] headerBytes)
        //{

        //    //memory stream to hold bluebook data
        //    var mergedPagesStream = new MemoryStream();

        //    // step 1: creation of a document-object
        //    iTextSharp.text.Document document = new iTextSharp.text.Document();

        //    // step 2: we create a writer that listens to the document
        //    iTextSharp.text.pdf.PdfCopy writer = new iTextSharp.text.pdf.PdfCopy(document, mergedPagesStream);
        //    if (writer == null)
        //    {
        //        return null;
        //    }

        //    // step 3: we open the document
        //    document.Open();

        //    iTextSharp.text.pdf.PdfReader Hdreader = new iTextSharp.text.pdf.PdfReader(headerBytes);
        //    Hdreader.ConsolidateNamedDestinations();

        //    // step 4: we add content
        //    for (int i = 1; i <= Hdreader.NumberOfPages; i++)
        //    {
        //        iTextSharp.text.pdf.PdfImportedPage page = writer.GetImportedPage(Hdreader, i);
        //        writer.AddPage(page);
        //    }

        //    var attachment_ordered = pautas.SelectMany(o => o.Content.ContentAttachment).Where(o => (o.deleted ?? false) == false).Select(a => a.Attachment).ToList();
        //    //var attachment_ordered = attachments.Where(o => o.extension == "pdf" || o.processedExtension == "pdf").OrderBy(o => o.ContentAttachment.Select(a => a.Content.MeetingAgendaItem.Select(x => (x.itemOrder ?? 0))));
        //    foreach (var file in attachment_ordered.Where(o => (o.extension ?? "").ToLower() == "pdf" || (o.processedExtension ?? "").ToLower() == "pdf"))
        //    {
        //        try
        //        {


        //            var path = "";

        //            if (!string.IsNullOrWhiteSpace(file.processedExtension))
        //            {
        //                path = file.processedUrl;
        //            }
        //            else
        //            {
        //                path = file.path;
        //            }

        //            //1. download
        //            var str = GetDocument(obj.contentId, path);

        //            // we create a reader for a certain document
        //            iTextSharp.text.pdf.PdfReader reader = new iTextSharp.text.pdf.PdfReader(str);
        //            reader.ConsolidateNamedDestinations();

        //            // step 4: we add content
        //            for (int i = 1; i <= reader.NumberOfPages; i++)
        //            {
        //                iTextSharp.text.pdf.PdfImportedPage page = writer.GetImportedPage(reader, i);
        //                writer.AddPage(page);
        //            }

        //            //iTextSharp.text.pdf.PRAcroForm form = reader.AcroForm;
        //            //if (form != null)
        //            //{
        //            //    writer.CopyAcroForm(reader);
        //            //}

        //            reader.Close();
        //        }
        //        catch (Exception ex)
        //        {
        //            using (System.IO.MemoryStream ms = new System.IO.MemoryStream())
        //            {
        //                iTextSharp.text.Document error_document = new iTextSharp.text.Document();
        //                iTextSharp.text.pdf.PdfWriter error_writer = iTextSharp.text.pdf.PdfWriter.GetInstance(error_document, ms);

        //                error_document.Open();
        //                error_document.Add(new iTextSharp.text.Paragraph("INVALID FILE. " + file.fileName));
        //                error_document.Close();

        //                iTextSharp.text.pdf.PdfReader error_reader = new iTextSharp.text.pdf.PdfReader(ms.ToArray());
        //                error_reader.ConsolidateNamedDestinations();
        //                iTextSharp.text.pdf.PdfImportedPage page = writer.GetImportedPage(error_reader, 1);
        //                writer.AddPage(page);
        //            }
        //            //iTextSharp.text.pdf.PdfReader error_reader = new iTextSharp.text.pdf.PdfReader();
        //            //writer.AddPage(error_document);

        //            //

        //        }
        //    }

        //    writer.Close();

        //    return mergedPagesStream.ToArray();
        //}

        //public byte[] WriteWatermarkAllPages(byte[] source, string bkg_text)
        //{
        //    byte[] modified = null;
        //    var bf = BaseFont.CreateFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED);

        //    using (iTextSharp.text.pdf.PdfReader reader = new iTextSharp.text.pdf.PdfReader(source))
        //    {
        //        reader.ConsolidateNamedDestinations();
        //        using (var ms = new MemoryStream(50 * 1024))
        //        {
        //            using (var stamper = new PdfStamper(reader, ms))
        //            {
        //                int times = reader.NumberOfPages;
        //                for (int i = 1; i <= times; i++)
        //                {
        //                    var dc = stamper.GetOverContent(i);
        //                    AddWaterMark(dc, bkg_text, bf, 28, 35, new BaseColor(70, 70, 255), reader.GetPageSizeWithRotation(i));

        //                    AddPagingBox(dc, i + "/" + times, reader.GetPageSizeWithRotation(i), null, stamper);

        //                }
        //                stamper.Close();
        //            }
        //            return ms.ToArray();
        //        }
        //    }
        //}

        //private static void AddWaterMark(PdfContentByte dc, string text, BaseFont font, float fontSize, float angle, BaseColor color, Rectangle realPageSize, Rectangle rect = null)
        //{
        //    var gstate = new PdfGState { FillOpacity = 0.1f, StrokeOpacity = 0.3f };
        //    dc.SaveState();
        //    dc.SetGState(gstate);
        //    dc.SetColorFill(color);
        //    dc.BeginText();
        //    dc.SetFontAndSize(font, fontSize);
        //    var ps = rect ?? realPageSize; /*dc.PdfDocument.PageSize is not always correct*/
        //    var x = (ps.Right + ps.Left) / 2;
        //    var y = (ps.Bottom + ps.Top) / 2;
        //    dc.ShowTextAligned(Element.ALIGN_CENTER, text, x, y, angle);
        //    dc.EndText();
        //    dc.RestoreState();
        //}

        //private static void AddPagingBox(PdfContentByte cb, string count, Rectangle realPageSize, Rectangle rect = null, PdfStamper stamper = null)
        //{

        //    var ps = rect ?? realPageSize; /*dc.PdfDocument.PageSize is not always correct*/
        //    var initialBoxX = (ps.Right + ps.Left) - 49;
        //    var initialBoxY = (ps.Bottom + ps.Top) / 5;



        //    //////////////


        //    var gstate = new PdfGState { FillOpacity = 0.5f, StrokeOpacity = 0.5f, BlendMode = new PdfName("BM_COLORDODGE") };
        //    cb.SetGState(gstate);
        //    cb.SaveState();

        //    cb.SetColorStroke(new BaseColor(63, 207, 178));

        //    cb.SetColorFill(new BaseColor(63, 207, 178));


        //    cb.MoveTo(initialBoxX - 20, initialBoxY);



        //    cb.LineTo(initialBoxX + 70, initialBoxY);

        //    cb.LineTo(initialBoxX + 70, initialBoxY + 30);

        //    cb.LineTo(initialBoxX - 20, initialBoxY + 30);

        //    //Path closed, stroked and filled

        //    cb.ClosePathFillStroke();

        //    ////////////////////

        //    //cb.MoveTo(initialBoxX+25, initialBoxY+25);

        //    var gstate2 = new PdfGState { FillOpacity = 0.7f, StrokeOpacity = 0.7f, BlendMode = new PdfName("BM_SCREEN") };
        //    cb.SetGState(gstate2);
        //    cb.SaveState();
        //    cb.SetColorFill(new BaseColor(254, 254, 254));
        //    cb.BeginText();
        //    cb.SetFontAndSize(BaseFont.CreateFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED), 14);
        //    cb.ShowTextAligned(Element.ALIGN_CENTER, count, initialBoxX + 25, initialBoxY + 10, 0);
        //    cb.EndText();
        //    cb.RestoreState();


        //    /// logotipo
        //    /// 

        //    var gstate3 = new PdfGState { FillOpacity = 0.4f, StrokeOpacity = 0.0f, BlendMode = new PdfName("BM_SCREEN") };
        //    cb.SetGState(gstate3);
        //    cb.SaveState();

        //    var logoImageFilename = GetDefaultAtlasLogoWhite();
        //    iTextSharp.text.Image image = iTextSharp.text.Image.GetInstance(logoImageFilename);
        //    image.ScaleAbsoluteWidth(20);
        //    image.ScaleAbsoluteHeight(20);
        //    image.SetAbsolutePosition(initialBoxX - 15, initialBoxY + 5);
        //    cb.AddImage(image);


        //}

        //public string GetImageFromUrl(string url)
        //{
        //    using (WebClient client = new WebClient())
        //    {
        //        byte[] response = new System.Net.WebClient().DownloadData(url);

        //        return "base64:" + Convert.ToBase64String(response);
        //    }
        //}

        //public Stream GetDocument(int content_id, string key)
        //{
        //    using (WebClient client = new WebClient())
        //    {
        //        ContentAttachmentService svc = new ContentAttachmentService(_currentUser.userId, content_id);
        //        string url = svc.Get(key);

        //        byte[] response = new System.Net.WebClient().DownloadData(url);
        //        return new MemoryStream(response);

        //        //return "base64:" + Convert.ToBase64String(response);
        //    }
        //}
        //public byte[] LoadImage(string name)
        //{
        //    var assembly = Assembly.GetExecutingAssembly();

        //    using (Stream stream = assembly.GetManifestResourceStream(name))
        //    {
        //        if (stream == null)
        //            throw new ArgumentException("No resource with name " + name);

        //        int count = (int)stream.Length;
        //        byte[] data = new byte[count];
        //        stream.Read(data, 0, count);
        //        return data;
        //    }
        //}
        //static string MigraDocFilenameFromByteArray(byte[] image)
        //{
        //    return "base64:" +
        //           Convert.ToBase64String(image);
        //}
        //private static string HtmlToPlainText(string html)
        //{
        //    const string tagWhiteSpace = @"(>|$)(\W|\n|\r)+<";//matches one or more (white space or line breaks) between '>' and '<'
        //    const string stripFormatting = @"<[^>]*(>|$)";//match any character between '<' and '>', even when end tag is missing
        //    const string lineBreak = @"<(br|BR)\s{0,1}\/{0,1}>";//matches: <br>,<br/>,<br />,<BR>,<BR/>,<BR />
        //    var lineBreakRegex = new Regex(lineBreak, RegexOptions.Multiline);
        //    var stripFormattingRegex = new Regex(stripFormatting, RegexOptions.Multiline);
        //    var tagWhiteSpaceRegex = new Regex(tagWhiteSpace, RegexOptions.Multiline);

        //    var text = html;

        //    //transforma as listas em bullets.
        //    text = text.Replace("<li>", "<li>● ");


        //    //Remove tag whitespace/line breaks
        //    text = tagWhiteSpaceRegex.Replace(text, "><");
        //    //Replace <br /> with line breaks
        //    text = lineBreakRegex.Replace(text, Environment.NewLine);
        //    //Strip formatting
        //    text = stripFormattingRegex.Replace(text, string.Empty);

        //    //Decode html specific characters
        //    text = System.Net.WebUtility.HtmlDecode(text);


        //    return text;
        //}
    }
}


