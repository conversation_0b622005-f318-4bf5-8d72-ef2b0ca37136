using Atlas.Data.Entities;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Security;
using System.Text;
using System.Threading.Tasks;
using System.Configuration;
using Atlas.CrossCutting.AppEnums;
using System.Text.RegularExpressions;
using Atlas.Business.ViewModels.ClientBackup;
using TimeZoneConverter;
using i18next_net;
using Atlas.CrossCutting.DTO;
using Atlas.Data.Repository;
using Microsoft.EntityFrameworkCore;
using Microsoft.Data.SqlClient;
using System.Data;

namespace Atlas.Business.Helpers
{
    public class DataExport
    {
        public int _currentUser { get; set; }
        public int _currentClient { get; set; }
        public DataExport(int currentUser, int managedClient)
        {
            _currentUser = currentUser;
            _currentClient = managedClient;
        }

        public async Task<ClientBackupResultingFileViewModel> GetExportUserDataFilePart(ClientBackupResult bakResPar, bool lastPart)
        {
            AtlasModelCore _md = new AtlasModelCore();
            _md.Database.SetCommandTimeout(6000);
            var currentUserObj = _md.User.Find(_currentUser);
            var bakRes = _md.ClientBackupResult.FirstOrDefault(p => p.bakResId == bakResPar.bakResId);
            var bakRequest = _md.ClientBackupRequest.Find(bakResPar.bakReqId);

            var cs = ConfigurationManager.ConnectionStrings["AtlasV2ContextReadOnly"].ConnectionString;

            ClientBackupFiltersViewModel filters = null;

            if (!string.IsNullOrEmpty(bakRequest.requestFilters))
            {
                try
                {
                    filters = Newtonsoft.Json.JsonConvert.DeserializeObject<ClientBackupFiltersViewModel>(bakRequest.requestFilters);
                }
                catch
                {
                    Console.WriteLine("Unable to parse requestFilters JSON: " + bakRequest.requestFilters);
                    filters = null;
                }
            }



            SqlCommand cmd = new SqlCommand("sp_user_exportData", new SqlConnection(cs));
            cmd.CommandTimeout = 6000;
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.Parameters.Add(new SqlParameter("@clientId", bakRes.clientId));
            cmd.Parameters.Add(new SqlParameter("@attachmentRankMin", bakRes.attachmentRankMin));
            cmd.Parameters.Add(new SqlParameter("@attachmentRankMax", bakRes.attachmentRankMax));
            cmd.Parameters.Add(new SqlParameter("@attachmentIdStart", bakRes.attachmentIdStart));
            cmd.Parameters.Add(new SqlParameter("@attachmentIdEnd", bakRes.attachmentIdEnd));
            cmd.Parameters.Add(new SqlParameter("@workgroupId", filters?.workgroupId));
            cmd.Parameters.Add(new SqlParameter("@userId", bakRequest.requestUserId));
            if (filters != null && filters.startDate.HasValue)
            {
                cmd.Parameters.Add(new SqlParameter("@dataCreationStart", filters.startDate.Value));
            }
            if (filters != null && filters.endDate.HasValue)
            {
                cmd.Parameters.Add(new SqlParameter("@dataCreationEnd", filters.endDate.Value));
            }
            cmd.Connection.Open();

            DataSet ds = new DataSet();
            SqlDataAdapter adapter = new SqlDataAdapter();
            adapter.SelectCommand = cmd;
            adapter.Fill(ds);
            var attachment_table = ds.Tables[0];
            var notes_table = ds.Tables[1];

            bakRes.fileCountTotal = attachment_table.Rows.Count;
            bakRes.fileCountProcessed = 0;
            _md.Entry(bakRes).State = EntityState.Modified;
            _md.SaveChanges();
#if DEBUG
            var tempFileName = Path.Combine("", Guid.NewGuid().ToString().Replace(" -", "") + ".zip");
#else
            var tempFileName = Path.Combine("D:\\local\\Temp", Guid.NewGuid().ToString().Replace(" -", "") + ".zip");
#endif
            string db_fuso = string.IsNullOrWhiteSpace(currentUserObj.defaultTimezone) ? "America/Sao_Paulo" : currentUserObj.defaultTimezone;
            string tz = "";

            try
            {
                tz = TZConvert.IanaToWindows(db_fuso);

            }
            catch (InvalidTimeZoneException)
            {
                tz = "E. South America Standard Time";
            }


            TimeZoneInfo userZone = TimeZoneInfo.FindSystemTimeZoneById(tz);

            DateTime utcNow = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, userZone);

            var culture = new System.Globalization.CultureInfo("pt-BR");

            i18next i18n = new i18next(new InitOptions()
            {
                defaultNS = "common",
                localeFileType = LocaleFileTypeEnum.Path,
                fallbackLng = "en"
            });

            var langCode = currentUserObj.defaultLanguage ?? "pt".ToLower();

            i18n.changeLanguage(langCode);

            switch (langCode)
            {
                case "pt":
                    culture = new System.Globalization.CultureInfo("pt-BR");
                    break;
                case "en":
                    culture = new System.Globalization.CultureInfo("en-US");
                    break;
                case "es":
                    culture = new System.Globalization.CultureInfo("es-ES");
                    break;
                default:
                    break;
            }

            string utcNowCultureFormat = utcNow.ToString(culture);

            string watermarkText = currentUserObj.name + " " + utcNowCultureFormat;
            string watermarkEmail = currentUserObj.email;

            KnowledgeBaseRepository kbRepo = new KnowledgeBaseRepository();
            var kbFolderTree = await kbRepo.GetKnowledgeBaseBackupFolderTree(bakRes.clientId, filters?.workgroupId);

            using (FileStream compressedFileStream = File.Create(tempFileName))
            {
                //Create an archive and store the stream in memory.
                using (var zipArchive = new ZipArchive(compressedFileStream, ZipArchiveMode.Create, false))
                {
                    attachment_table.Columns.Add("Export_status");
                    attachment_table.Columns.Add("Export_path");
                    int count = attachment_table.Rows.Count;
                    int i = 0;
                    foreach (DataRow item in attachment_table.Rows)
                    {
                        i++;

                        //check if request has been cancelled EVERY 10 files processed. to prevent to check every time and overload the DB
                        if (i % 10 == 0 && i != 0)
                        {
                            var req = _md.ClientBackupRequest.Where(o => o.bakReqId == bakRes.bakReqId).FirstOrDefault();
                            if (req.status == "CANCELLED")
                            {
                                return new ClientBackupResultingFileViewModel() { fileGenerated = "CANCELLED" };
                            }
                        }


                        try
                        {
                            //download
                            string path = item["processedUrl"].ToString();
                            string fileName = item["fileName"].ToString();
                            string attachmentId = item["attachmentId"].ToString();
                            Console.WriteLine($"Download " + i + " de " + count + " " + item["attachmentId"]);

                            string fileExtension = item["processedExtension"].ToString();
                            string fileNameWithoutExtension = fileName.Substring(0, fileName.LastIndexOf("."));

                            byte[] file_content = null;

                            using (System.Net.WebClient client = new System.Net.WebClient())
                            {
                                ContentAttachmentService svc = new ContentAttachmentService(_currentUser, 0);
                                string url = svc.Get(path);

                                file_content = new System.Net.WebClient().DownloadData(url);
                            }

                            //FORMATANDO O DIRETORIO DESTINO DO ARQUIVO NO ZIP

                            string export_path = "attachments/others/" + attachmentId + "/" + fileName;

                            //variaveis possiveis para formatar o caminho do arquivo
                            string contentId = item["contentId"].ToString();
                            string contentType = item["contentType"].ToString();

                            string contentTitle = item["contentTitle"].ToString();
                            string parentContentTitle = item["parentContentTitle"].ToString();

                            if (!string.IsNullOrEmpty(contentId))
                            {
                                string regexSearch = new string(Path.GetInvalidFileNameChars());

                                /*****************/
                                //formatting contentTitle (titulo do objeto content)
                                /*****************/
                                //reduce size
                                contentTitle = contentTitle.Length > 40 ? contentTitle.Substring(0, 40) : contentTitle;
                                //remove forbidden characters
                                Regex rgxPathClear = new Regex(string.Format("[{0}]", Regex.Escape(regexSearch)));

                                contentTitle = rgxPathClear.Replace(contentTitle, "");
                                contentTitle = contentTitle.Trim();

                                parentContentTitle = (!String.IsNullOrEmpty(parentContentTitle) ? parentContentTitle.Replace("/", "-") : parentContentTitle);
                                contentTitle = (!String.IsNullOrEmpty(contentTitle) ? contentTitle.Replace("/", "-") : contentTitle);
                                fileNameWithoutExtension = (!String.IsNullOrEmpty(fileNameWithoutExtension) ? fileNameWithoutExtension.Replace("/", "-") : fileNameWithoutExtension);

                                if (item["showWaterMark"].ToString() == "0")
                                {
                                    // Signed files with a custom filename prefix
                                    fileNameWithoutExtension = fileNameWithoutExtension.StartsWith("Signed_") ?
                                        fileNameWithoutExtension :
                                        "Signed_" + fileNameWithoutExtension;
                                }

                                if (contentType == ContentTypes.MeetingMinute)
                                {
                                    string meetingDate = ((DateTime)item["meetingDate"]).ToString("dd-MM-yyyy");
                                    parentContentTitle = rgxPathClear.Replace(parentContentTitle, "");
                                    parentContentTitle = parentContentTitle.Trim();
                                    parentContentTitle = !string.IsNullOrEmpty(parentContentTitle) ? parentContentTitle : "";

                                    export_path = $"MeetingMinutes/{parentContentTitle} - {meetingDate} - {fileNameWithoutExtension}.{fileExtension}";
                                }
                                else if (contentType == ContentTypes.KnowledgeDirectory)
                                {
                                    int.TryParse(contentId, out int folderId);
                                    var folderPath = GetKbFolderPath(kbFolderTree, folderId);
                                    export_path = $"KnowledgeBase/{folderPath}/{fileNameWithoutExtension} ({attachmentId}).{fileExtension}";
                                }
                                else if (contentType == ContentTypes.KnowledgeArticle)
                                {
                                    parentContentTitle = rgxPathClear.Replace(parentContentTitle, "");
                                    parentContentTitle = parentContentTitle.Trim();
                                    parentContentTitle = !string.IsNullOrEmpty(parentContentTitle) ? parentContentTitle : "";
                                    export_path = $"KB/{parentContentTitle}/{contentTitle}/{fileNameWithoutExtension}.{fileExtension}";
                                }
                                else if (contentType == "Bluebook")
                                {
                                    string meetingDate = ((DateTime)item["meetingDate"]).ToString("dd-MM-yyyy");
                                    parentContentTitle = rgxPathClear.Replace(parentContentTitle, "");
                                    parentContentTitle = parentContentTitle.Trim();
                                    parentContentTitle = !string.IsNullOrEmpty(parentContentTitle) ? parentContentTitle : "";
                                    parentContentTitle = parentContentTitle.Length > 40 ? parentContentTitle.Substring(0, 40) : parentContentTitle;
                                    export_path = $"Bluebooks/{parentContentTitle} - {meetingDate} - {fileNameWithoutExtension}.{fileExtension}";
                                }
                                else
                                {
                                    continue;
                                }
                            }

                            var zipEntry = zipArchive.CreateEntry(export_path);

                            var fileToExport = file_content;

                            if (item["showWaterMark"].ToString() == "1")
                            {
                                PDFGenerator_v2 pdfGenerator = new PDFGenerator_v2(currentUserObj);
                                try
                                {
                                    //MATHEUS
                                    //fileToExport = pdfGenerator.WriteWatermarkAllPages(file_content, watermarkText, watermarkEmail, paging: false);
                                }
                                catch (Exception)
                                {
                                }
                            }

                            //Get the stream of the attachment
                            using (var originalFileStream = new MemoryStream(fileToExport))
                            {
                                using (var zipEntryStream = zipEntry.Open())
                                {
                                    //Copy the attachment stream to the zip entry stream
                                    originalFileStream.CopyTo(zipEntryStream);
                                }
                            }
                            item["Export_status"] = "Ok";
                            item["Export_path"] = export_path;
                        }
                        catch (Exception ex)
                        {
                            item["Export_status"] = "Error";
                        }
                        GC.Collect();

                        try
                        {
                            bakRes.fileCountProcessed = i;
                            _md.Entry(bakRes).State = EntityState.Modified;
                            _md.SaveChanges();
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine(ex.Message);
                        }
                    }

                    if (lastPart)
                    {
                        foreach (DataRow item in notes_table.Rows)
                        {
                            try
                            {
                                var contentId = Convert.ToInt32(item["contentId"].ToString());
                                string html = item["text"].ToString();
                                var html_bytes = Encoding.UTF8.GetBytes(html);

                                var noteTitle = item["title"].ToString();

                                if (string.IsNullOrEmpty(noteTitle))
                                {
                                    noteTitle = item["contentId"].ToString();
                                }
                                else
                                {
                                    string regexSearch = new string(Path.GetInvalidFileNameChars());
                                    Regex regexPathClear = new Regex(string.Format("[{0}]", Regex.Escape(regexSearch)));

                                    noteTitle = regexPathClear.Replace(noteTitle, "");
                                    noteTitle = noteTitle.Trim();
                                }

                                var export_path = $"Notes/{noteTitle} - {((DateTime)item["createDate"]).ToString("dd-MM-yyyy")}.html";

                                var zipEntry = zipArchive.CreateEntry(export_path);

                                //Get the stream of the attachment
                                using (var originalFileStream = new MemoryStream(html_bytes))
                                {
                                    using (var zipEntryStream = zipEntry.Open())
                                    {
                                        //Copy the attachment stream to the zip entry stream
                                        originalFileStream.CopyTo(zipEntryStream);
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                            }
                            GC.Collect();
                        }
                    }

                    GC.Collect();
                }

                compressedFileStream.Close();
                return new ClientBackupResultingFileViewModel() { fileGenerated = tempFileName, filters = filters };
            }
        }

        public async Task<ClientBackupResultingFileViewModel> GetExportClientDataFilePart(ClientBackupResult bakResPar)
        {
            var isAdmin = await new UserRoleService(_currentUser).IsAdminOnClient(this._currentClient);

            if (!isAdmin)
            {
                throw new SecurityException();
            }
            AtlasModelCore _md = new AtlasModelCore();
            _md.Database.SetCommandTimeout(6000);
            var currentUserObj = _md.User.Find(_currentUser);
            var bakRes = _md.ClientBackupResult.Find(bakResPar.bakResId);
            var bakRequest = _md.ClientBackupRequest.Find(bakResPar.bakReqId);

            var cs = ConfigurationManager.ConnectionStrings["AtlasV2ContextReadOnly"].ConnectionString;


            ClientBackupFiltersViewModel filters = null;

            if (!string.IsNullOrEmpty(bakRequest.requestFilters))
            {
                try
                {
                    filters = Newtonsoft.Json.JsonConvert.DeserializeObject<ClientBackupFiltersViewModel>(bakRequest.requestFilters);
                }
                catch
                {
                    Console.WriteLine("Unable to parse requestFilters JSON: " + bakRequest.requestFilters);
                    filters = null;
                }
            }

            SqlCommand cmd = new SqlCommand("sp_client_exportData", new SqlConnection(cs));
            cmd.CommandTimeout = 6000;
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.Parameters.Add(new SqlParameter("@clientId", bakRes.clientId));
            cmd.Parameters.Add(new SqlParameter("@attachmentRankMin", bakRes.attachmentRankMin));
            cmd.Parameters.Add(new SqlParameter("@attachmentRankMax", bakRes.attachmentRankMax));
            cmd.Parameters.Add(new SqlParameter("@attachmentIdStart", bakRes.attachmentIdStart));
            cmd.Parameters.Add(new SqlParameter("@attachmentIdEnd", bakRes.attachmentIdEnd));
            if (filters != null && filters.startDate.HasValue)
            {
                cmd.Parameters.Add(new SqlParameter("@dataCreationStart", filters.startDate.Value));
            }
            if (filters != null && filters.endDate.HasValue)
            {
                cmd.Parameters.Add(new SqlParameter("@dataCreationEnd", filters.endDate.Value));
            }

            cmd.Connection.Open();

            DataSet ds = new DataSet();
            SqlDataAdapter adapter = new SqlDataAdapter();
            adapter.SelectCommand = cmd;
            adapter.Fill(ds);
            var attachment_table = ds.Tables[6];

            bakRes.fileCountTotal = attachment_table.Rows.Count;
            bakRes.fileCountProcessed = 0;
            _md.Entry(bakRes).State = EntityState.Modified;
            _md.SaveChanges();

            string tempFileName;
            if (System.Diagnostics.Debugger.IsAttached)
            {
                tempFileName = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString().Replace(" -", "") + ".zip");
            }
            else
            {
                tempFileName = Path.Combine("D:\\local\\Temp", Guid.NewGuid().ToString().Replace(" -", "") + ".zip");
            }

            KnowledgeBaseRepository kbRepo = new KnowledgeBaseRepository();
            var kbFolderTree = await kbRepo.GetKnowledgeBaseBackupFolderTree(bakRes.clientId);

            i18next i18n = new i18next(new i18next_net.InitOptions()
            {
                defaultNS = "common",
                localeFileType = LocaleFileTypeEnum.Path,
                fallbackLng = "en"
            });
            i18n.changeLanguage("en");

            using (FileStream compressedFileStream = File.Create(tempFileName))
            {
                //Create an archive and store the stream in memory.
                using (var zipArchive = new ZipArchive(compressedFileStream, ZipArchiveMode.Create, false))
                {
                    attachment_table.Columns.Add("Export_status");
                    attachment_table.Columns.Add("Export_path");
                    int count = attachment_table.Rows.Count;
                    int i = 0;
                    foreach (DataRow item in attachment_table.Rows)
                    {
                        i++;

                        //check if request has been cancelled EVERY 10 files processed. to prevent to check every time and overload the DB
                        if (i % 10 == 0 && i != 0)
                        {
                            var req = _md.ClientBackupRequest.Where(o => o.bakReqId == bakRes.bakReqId).FirstOrDefault();
                            if (req.status == "CANCELLED")
                            {
                                return new ClientBackupResultingFileViewModel() { fileGenerated = "CANCELLED" };
                            }
                        }


                        try
                        {
                            //download
                            string path = item["path"].ToString();
                            string fileName = item["fileName"].ToString();
                            string attachmentId = item["attachmentId"].ToString();
                            Console.WriteLine($"Download " + i + " de " + count + " " + item["attachmentId"]);

                            string fileExtension = item["extension"].ToString();
                            string fileNameWithoutExtension = fileName.Substring(0, fileName.Length - fileExtension.Length - 1);

                            byte[] file_content = null;

                            using (System.Net.WebClient client = new System.Net.WebClient())
                            {
                                ContentAttachmentService svc = new ContentAttachmentService(_currentUser, 0);
                                string url = svc.Get(path);

                                file_content = new System.Net.WebClient().DownloadData(url);
                            }

                            //FORMATANDO O DIRETORIO DESTINO DO ARQUIVO NO ZIP

                            string export_path = "attachments/others/" + attachmentId + "/" + fileName;

                            //variaveis possiveis para formatar o caminho do arquivo
                            string workgroupId = item["workgroupId"].ToString();
                            string contentId = item["contentId"].ToString();
                            string parentContentId = item["parentContentId"].ToString();
                            string workgroupName = item["workgroupName"].ToString();
                            string workgroupType = item["workgroupType"].ToString();
                            string contentType = item["contentType"].ToString();

                            string contentTitle = item["contentTitle"].ToString();
                            string parentContentTitle = item["parentContentTitle"].ToString();

                            if (!string.IsNullOrEmpty(item["signedAttachmentId"].ToString()))
                            {
                                workgroupId = item["signWorkgroupId"].ToString();
                                contentId = item["signContentId"].ToString();
                                parentContentId = item["signParentContentId"].ToString();
                                workgroupName = item["signWorkgroupName"].ToString();
                                workgroupType = item["signWorkgroupType"].ToString();
                                contentType = item["signContentType"].ToString();
                                contentTitle = item["signContentTitle"].ToString();
                                parentContentTitle = item["signParentContentTitle"].ToString();

                                fileNameWithoutExtension = fileNameWithoutExtension.StartsWith("Signed_") ?
                                    fileNameWithoutExtension : "Signed_" + fileNameWithoutExtension;
                            }

                            if (!string.IsNullOrEmpty(contentId))
                            {
                                string regexSearch = new string(Path.GetInvalidFileNameChars());

                                /*****************/
                                //formatting contentTitle (titulo do objeto content)
                                /*****************/
                                //reduce size
                                contentTitle = contentTitle.Length > 15 ? contentTitle.Substring(0, 15) : contentTitle;
                                //remove forbidden characters
                                string regexInvalidPathChars = new string(Path.GetInvalidFileNameChars());
                                Regex rgxPathClear = new Regex(string.Format("[{0}]", Regex.Escape(regexSearch)));

                                contentTitle = rgxPathClear.Replace(contentTitle, "");
                                contentTitle = contentTitle.Trim();
                                contentTitle = !string.IsNullOrEmpty(contentTitle) ? " - " + contentTitle : "";

                                string kbFolderName = "";
                                if (workgroupType == WorkgroupTypes.ROOT)
                                {
                                    workgroupName = i18n.t("knowledgeBase:companyFolders");
                                }
                                else
                                {
                                    workgroupName = rgxPathClear.Replace(workgroupName, "");
                                    workgroupName = workgroupName.Trim();
                                    kbFolderName = i18n.t("export:knowledgeBase") + "/";
                                }

                                if (contentType == ContentTypes.MeetingAgendaItem)
                                {
                                    /*****************/
                                    //formatting parentContentTitle (titulo da reuniao)
                                    /*****************/
                                    //reduce size
                                    parentContentTitle = parentContentTitle.Length > 15 ? parentContentTitle.Substring(0, 15) : parentContentTitle;
                                    //remove forbidden characters
                                    parentContentTitle = rgxPathClear.Replace(parentContentTitle, "");
                                    //remove spaces from start and end of the string
                                    parentContentTitle = parentContentTitle.Trim();
                                    parentContentTitle = !string.IsNullOrEmpty(parentContentTitle) ? " - " + parentContentTitle : "";

                                    export_path = $"attachments/{workgroupId} - {workgroupName}/Meeting {parentContentId}{parentContentTitle}/Agenda {contentId}{contentTitle}/{fileNameWithoutExtension} ({attachmentId}).{fileExtension}";
                                }
                                else if (contentType == ContentTypes.KnowledgeDirectory)
                                {
                                    int.TryParse(contentId, out int folderId);
                                    var folderPath = GetKbFolderPath(kbFolderTree, folderId);
                                    export_path = $"attachments/{workgroupId} - {workgroupName}/{kbFolderName}{folderPath}/{fileNameWithoutExtension} ({attachmentId}).{fileExtension}";
                                }
                                else
                                {
                                    export_path = $"attachments/{workgroupId} - {workgroupName}/{contentType} {contentId}{contentTitle}/{fileNameWithoutExtension} ({attachmentId}).{fileExtension}";
                                }

                            }

                            var zipEntry = zipArchive.CreateEntry(export_path);

                            //Get the stream of the attachment
                            using (var originalFileStream = new MemoryStream(file_content))
                            {
                                using (var zipEntryStream = zipEntry.Open())
                                {
                                    //Copy the attachment stream to the zip entry stream
                                    originalFileStream.CopyTo(zipEntryStream);
                                }
                            }
                            item["Export_status"] = "Ok";
                            item["Export_path"] = export_path;
                        }
                        catch (Exception ex)
                        {
                            item["Export_status"] = "Error";
                        }
                        GC.Collect();

                        try
                        {
                            bakRes.fileCountProcessed = i;
                            _md.Entry(bakRes).State = EntityState.Modified;
                            _md.SaveChanges();
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine(ex.Message);
                        }


                    }
                    GC.Collect();
                    //only add CSV file to the first 

                    int index = 0;
                    foreach (DataTable table in ds.Tables)
                    {
                        //criar excel
                        if (bakRes.fileNumber == 1 || index == 6) //if is the first file (will contain all CSVs) or the attachment table
                        {
                            byte[] csv = Encoding.UTF8.GetBytes(DataTableToCSV(table));
                            string nome = GetTableName(index);

                            //Create a zip entry for each attachment
                            var zipEntry = zipArchive.CreateEntry(nome);

                            //Get the stream of the attachment
                            using (var originalFileStream = new MemoryStream(csv))
                            {
                                using (var zipEntryStream = zipEntry.Open())
                                {
                                    //Copy the attachment stream to the zip entry stream
                                    originalFileStream.CopyTo(zipEntryStream);
                                }
                            }
                        }

                        index = index + 1;



                    }


                }

                compressedFileStream.Close();
                return new ClientBackupResultingFileViewModel() { fileGenerated = tempFileName, filters = filters };
            }
        }

        public async Task<ClientBackupResultingFileViewModel> GetExportKBData(ClientBackupResult bakResPar, string zipFolderName)
        {
            AtlasModelCore _md = new AtlasModelCore();
            _md.Database.SetCommandTimeout(6000);
            var currentUserObj = _md.User.Find(_currentUser);
            var bakRes = _md.ClientBackupResult.Find(bakResPar.bakResId);
            var bakRequest = _md.ClientBackupRequest.Find(bakResPar.bakReqId);

            i18next_net.i18next i18n = new i18next_net.i18next(new i18next_net.InitOptions()
            {
                defaultNS = "common",
                localeFileType = LocaleFileTypeEnum.Path,
                fallbackLng = "en"
            });

            var langCode = currentUserObj.defaultLanguage ?? "pt".ToLower();

            i18n.changeLanguage(langCode);

            string db_fuso = string.IsNullOrWhiteSpace(currentUserObj.defaultTimezone) ? "America/Sao_Paulo" : currentUserObj.defaultTimezone;
            string tz = "";

            try
            {
                tz = TZConvert.IanaToWindows(db_fuso);

            }
            catch (InvalidTimeZoneException)
            {
                tz = "E. South America Standard Time";
            }


            TimeZoneInfo userZone = TimeZoneInfo.FindSystemTimeZoneById(tz);

            DateTime utcNow = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, userZone);

            var culture = new System.Globalization.CultureInfo("pt-BR");

            switch (langCode)
            {
                case "pt":
                    culture = new System.Globalization.CultureInfo("pt-BR");
                    break;
                case "en":
                    culture = new System.Globalization.CultureInfo("en-US");
                    break;
                case "es":
                    culture = new System.Globalization.CultureInfo("es-ES");
                    break;
                default:
                    break;
            }

            string utcNowCultureFormat = utcNow.ToString(culture);

            string watermarkText = currentUserObj.name + " " + utcNowCultureFormat;
            string watermarkEmail = currentUserObj.email;


            var connection = ConfigurationManager.ConnectionStrings["AtlasV2ContextReadOnly"].ConnectionString;

            Business.ViewModels.ClientBackup.ClientBackupFiltersViewModel filters = null;

            if (!string.IsNullOrEmpty(bakRequest.requestFilters))
            {
                try
                {
                    filters = Newtonsoft.Json.JsonConvert.DeserializeObject<Business.ViewModels.ClientBackup.ClientBackupFiltersViewModel>(bakRequest.requestFilters);
                }
                catch
                {
                    Console.WriteLine("Unable to parse requestFilters JSON: " + bakRequest.requestFilters);
                    filters = null;
                }
            }

            try
            {

                using (var dbContext = new AtlasModelCore())
                {
                    KnowledgeBaseRepository kbRepo = new KnowledgeBaseRepository();
                    List<KnowledgeBaseBackupResult> result = new List<KnowledgeBaseBackupResult>();

                    var showKbDefaultFoldersEnabled = dbContext.Client.Find(bakRequest.clientId).showKbDefaultFoldersEnabled;

                    switch (filters.entity)
                    {
                        case "workgroup"://workgroupId
                            result.AddRange(kbRepo.GetKBBackupQuery(filters.workgroupId, filters.contentId, _currentUser, filters.entity, false));

                            if (showKbDefaultFoldersEnabled == true)
                            {
                                result.AddRange(kbRepo.GetBoardTasks(_currentUser, filters.workgroupId.Value));

                                result.AddRange(kbRepo.GetMeetingMaterials(_currentUser, filters.workgroupId.Value));

                                result.AddRange(kbRepo.GetMeetingMinutes(_currentUser, filters.workgroupId.Value));
                            }
                            
                            break;
                        case "knowledgeDirectory": //contentId
                        case "knowledgeBase": //workgroupId
                        case "workgroupRoot": //workgroupId
                            result.AddRange(kbRepo.GetKBBackupQuery(filters.workgroupId, filters.contentId, _currentUser, filters.entity, false));
                            break;
                        case "meetingMaterials": //workgroupId
                            if (showKbDefaultFoldersEnabled == true)
                            {
                                result.AddRange(kbRepo.GetMeetingMaterials(_currentUser, filters.workgroupId.Value));
                            }
                            break;
                        case "meetingMinutes": //workgroupId
                            if (showKbDefaultFoldersEnabled == true)
                            {
                                result.AddRange(kbRepo.GetMeetingMinutes(_currentUser, filters.workgroupId.Value));
                            }
                            break;
                        case "workgroupActions": //workgroupId
                            if (showKbDefaultFoldersEnabled == true)
                            {
                                result.AddRange(kbRepo.GetBoardTasks(_currentUser, filters.workgroupId.Value, bakResPar.attachmentIdStart, bakResPar.attachmentIdEnd));
                            }
                            break;
                        default:
                            break;
                    }

                    var resultsFull = result.ToList();
                    result = resultsFull.Where(p => p.contentAttachmentId != null).Skip(Convert.ToInt32(bakResPar.attachmentRankMin.Value) - 1).Take(Convert.ToInt32(bakResPar.attachmentRankMax.Value - bakResPar.attachmentRankMin.Value) + 1).ToList();

                    bakRes.fileCountTotal = result.Count;
                    bakRes.fileCountProcessed = 0;
                    _md.Entry(bakRes).State = EntityState.Modified;
                    _md.SaveChanges();

#if DEBUG
                    var tempFileName = Path.Combine(Guid.NewGuid().ToString().Replace(" -", "").Replace("-", "") + ".zip");
#else
    var tempFileName = Path.Combine("D:\\local\\Temp", Guid.NewGuid().ToString().Replace(" -", "").Replace("-", "") + ".zip");
#endif

                    using (FileStream compressedFileStream = File.Create(tempFileName))
                    {
                        //Create an archive and store the stream in memory.
                        using (var zipArchive = new ZipArchive(compressedFileStream, ZipArchiveMode.Create, false))
                        {
                            int count = result.Count;
                            int i = 0;
                            foreach (var item in result)
                            {
                                if (item.contentAttachmentId != null)
                                {
                                    i++;

                                    //check if request has been cancelled EVERY 10 files processed. to prevent to check every time and overload the DB
                                    if (i % 10 == 0 && i != 0)
                                    {
                                        var req = _md.ClientBackupRequest.Where(o => o.bakReqId == bakRes.bakReqId).FirstOrDefault();
                                        if (req.status == "CANCELLED")
                                        {
                                            return new ClientBackupResultingFileViewModel() { fileGenerated = "CANCELLED" };
                                        }
                                    }


                                    try
                                    {
                                        //download
                                        string path = item.processedUrl != null && item.watermarkEnabled.Value == true ? item.processedUrl : item.path;
                                        string fileName = item.fileName;
                                        int contentAttachmentId = item.contentAttachmentId.Value;
                                        string folderName = result.Where(o => o.contentId == item.parentContentId).Select(p => p.folderName).FirstOrDefault();
                                        Console.WriteLine($"Download " + i + " de " + count + " " + contentAttachmentId.ToString());

                                        string fileExtension = item.processedExtension != null && item.watermarkEnabled.Value == true ? item.processedExtension : item.extension;
                                        string fileNameWithoutExtension = fileName.Substring(0, fileName.Length - fileExtension.Length - 1);
                                        fileNameWithoutExtension = fileNameWithoutExtension.Length > 40 ? fileNameWithoutExtension.Substring(0, 40) : fileNameWithoutExtension;

                                        byte[] file_content = null;

                                        using (System.Net.WebClient client = new System.Net.WebClient())
                                        {
                                            ContentAttachmentService svc = new ContentAttachmentService(_currentUser, 0);
                                            string url = svc.Get(path);

                                            file_content = new System.Net.WebClient().DownloadData(url);
                                        }

                                        //FORMATANDO O DIRETORIO DESTINO DO ARQUIVO NO ZIP
                                        //remove forbidden characters
                                        string regexSearch = new string(Path.GetInvalidFileNameChars()) + new string(Path.GetInvalidPathChars());
                                        Regex r = new Regex(string.Format("[{0}]", Regex.Escape(regexSearch)));

                                        string export_path = folderName + "/" + fileName;

                                        string workgroupId = item.workgroupId.ToString();
                                        string contentId = item.contentId.ToString();
                                        string parentContentId = item.parentContentId.ToString();

                                        if (!string.IsNullOrEmpty(contentId))
                                        {

                                            /*****************/
                                            //formatting contentTitle (titulo do objeto content)
                                            /*****************/
                                            //reduce size
                                            var contentTitle = fileNameWithoutExtension.Length > 40 ? fileNameWithoutExtension.Substring(0, 40) : fileNameWithoutExtension;                                        

                                            if (item.originAttachment == "contentFolders")
                                            {
                                                if (item.parentContentId.HasValue)
                                                {
                                                    var folderPath = GetFolderKBHierarchy(resultsFull, item.parentContentId.Value);
                                                    export_path = $"{folderPath}/{fileNameWithoutExtension} ({contentAttachmentId}).{fileExtension}";
                                                }
                                                else
                                                {
                                                    export_path = $"{fileNameWithoutExtension} ({contentAttachmentId}).{fileExtension}";
                                                }

                                            }
                                            else if (item.originAttachment == "workgroupFolders")
                                            {
                                                var folderPath = GetFolderKBHierarchy(resultsFull, item.parentContentId.Value);

                                                var workgroup = _md.Workgroup.Find(item.workgroupId);

                                                string textTranslated = workgroup.type == "PROJECT" ? i18n.t("knowledgeBase:projectfolders") : i18n.t("knowledgeBase:boardFolders");

                                                export_path = $"{textTranslated}/{folderPath}/{fileNameWithoutExtension} ({contentAttachmentId}).{fileExtension}";
                                            }
                                            else if (item.originAttachment == "tasks")
                                            {
                                                ContentService contentService = new ContentService(_currentUser);
                                                var content = await contentService.Get(item.contentId);

                                                string title = content.title.Length > 15 ? content.title.Substring(0, 15) : content.title;
                                                string userAssigned = content.User_Assigned.name.Length > 15 ? content.User_Assigned.name.Substring(0, 15) : content.User_Assigned.name;

                                                StringBuilder builder = new StringBuilder();
                                                builder.Append(content.Task.FirstOrDefault()?.dueDate.ToString("yyyy-MM-dd") ?? "");
                                                builder.AppendFormat(" {1} - {0}", title, userAssigned);
                                                builder.ToString();

                                                string textTranslated = content.Workgroup.type == "PROJECT" ? i18n.t("knowledgeBase:projectTasks") : i18n.t("knowledgeBase:boardTasks");

                                                export_path = $"{textTranslated}/{builder} {content.contentId}/{fileNameWithoutExtension} ({contentAttachmentId}).{fileExtension}";
                                            }
                                            else if (item.originAttachment == "meetingMinutes")
                                            {
                                                ContentService contentService = new ContentService(_currentUser);
                                                var content = await contentService.Get(item.parentContentId.Value);
                                                string meetingDate = content.Meeting.FirstOrDefault()?.date.ToString("yyyy-MM-dd ") ?? "";
                                                string title = content.title.Length > 35 ? content.title.Substring(0, 35) : content.title;
                                                title = r.Replace(title, "");

                                                export_path = $"{i18n.t("knowledgeBase:meetingMinutes")}/{meetingDate} {title} {content.contentId}/{fileNameWithoutExtension} ({contentAttachmentId}).{fileExtension}";

                                            }
                                            else
                                            {
                                                ContentService contentService = new ContentService(_currentUser);
                                                var content = await contentService.Get(item.parentContentId.Value);
                                                string meetingDate = content.Meeting.FirstOrDefault()?.date.ToString("yyyy-MM-dd ") ?? "";
                                                string title = content.title.Length > 35 ? content.title.Substring(0, 35) : content.title;
                                                title = r.Replace(title, "");

                                                export_path = $"{i18n.t("knowledgeBase:meetingMaterials")}/{meetingDate} {title} {content.contentId}/{fileNameWithoutExtension} ({contentAttachmentId}).{fileExtension}";

                                            }
                                        }

                                        if (export_path.Length > 240)
                                        {
                                            string[] pathSplit = export_path.Split('/');
                                            string newPath = "";

                                            for (int index = pathSplit.Length - 1; index >= 0; index--)
                                            {
                                                if (pathSplit[index].Length + newPath.Length + zipFolderName.Length <= 240)
                                                {
                                                    newPath = index < pathSplit.Length - 1 ? pathSplit[index] + "/" + newPath : pathSplit[index];
                                                }
                                                else
                                                {
                                                    break;
                                                }
                                            }

                                            export_path = newPath;
                                        }

                                        var zipEntry = zipArchive.CreateEntry(export_path);

                                        var fileToExport = file_content;

                                        if (item.watermarkEnabled.HasValue && item.watermarkEnabled.Value == true && (path.ToLower().EndsWith(".pdf")))
                                        {
                                            PDFGenerator_v2 pdfGenerator = new PDFGenerator_v2(currentUserObj);
                                            try
                                            {
                                                //MATHEUS
                                                //fileToExport = pdfGenerator.WriteWatermarkAllPages(file_content, watermarkText, watermarkEmail, paging: false);
                                            }
                                            catch (Exception)
                                            {

                                            }
                                        }

                                        //Get the stream of the attachment
                                        using (var originalFileStream = new MemoryStream(fileToExport))
                                        {
                                            using (var zipEntryStream = zipEntry.Open())
                                            {
                                                //Copy the attachment stream to the zip entry stream
                                                originalFileStream.CopyTo(zipEntryStream);
                                            }
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        Console.WriteLine(ex.Message + ", ContentAttachmentId:" + item.contentAttachmentId);
                                    }
                                    GC.Collect();

                                    try
                                    {
                                        bakRes.fileCountProcessed = i;
                                        _md.Entry(bakRes).State =   EntityState.Modified;
                                        _md.SaveChanges();
                                    }
                                    catch (Exception ex)
                                    {
                                        Console.WriteLine(ex.Message + ", ContentAttachmentId:" + item.contentAttachmentId);
                                    }


                                }
                                GC.Collect();

                            }
                        }

                        compressedFileStream.Close();
                        return new ClientBackupResultingFileViewModel() { fileGenerated = tempFileName, filters = filters };
                    }

                }
            }
            catch (Exception e)
            {
                throw e;
            }

        }

        public string GetKbFolderPath(Dictionary<int, DirectoryNode> kbFolderTree, int parentFolderId)
        {
            string folderPath = "";
            try
            {
                var parentNode = kbFolderTree[parentFolderId];
                while (parentNode != null)
                {
                    var folderName = parentNode.folderName;
                    var currentName = $"{(folderName.Length > 35 ? folderName.Substring(0, 35) : folderName)} {parentNode.contentId}";

                    folderPath = currentName + (String.IsNullOrEmpty(folderPath) ? "" : $"/{folderPath}");

                    if (parentNode.parentContentId.HasValue)
                    {
                        parentNode = kbFolderTree[parentNode.parentContentId.Value];
                    }
                    else
                    {
                        parentNode = null;
                    }
                }
            }
            catch (KeyNotFoundException) { /* Specific error catched to return the latest folder path */ }
            catch (ArgumentNullException) { /* Specific error catched to return the latest folder path */ }
            catch (InvalidOperationException) { /* Specific error catched to return the latest folder path */ }

            return folderPath;
        }

        public string GetFolderKBHierarchy(List<KnowledgeBaseBackupResult> listKbResult, int contentId)
        {
            string path = "";
            var list = listKbResult.Where(k => k.fileName == null).ToList();
            var folder = list.Where(o => o.contentId == contentId).FirstOrDefault();

            if (folder != null)
            {
                path = folder.folderName;
                path = path.Length > 35 ? path.Substring(0, 35) + " " + contentId : path + " " + contentId;

            }
            else
            {
                return "";
            }

            bool hasParentFolder = false;
            var parentContentId = folder.parentContentId;

            if (folder.parentContentId != null)
            {
                hasParentFolder = true;
            }


            while (hasParentFolder)
            {
                var folderParent = list.Where(o => o.contentId == parentContentId).FirstOrDefault();

                if (folderParent == null || parentContentId == null)
                {
                    hasParentFolder = false;
                }
                else
                {
                    parentContentId = folderParent.parentContentId;
                    var folderName = folderParent.folderName.Length > 35 ? folderParent.folderName.Substring(0, 35) + " " + folderParent.contentId : folderParent.folderName + " " + folderParent.contentId;
                    path = folderName + "/" + path;

                }

            }

            return path;

        }



        public async Task<ClientBackupRequest> GetEstimatedBackupResults(ClientBackupRequest bakReq)
        {
            var isAdmin = await new UserRoleService(_currentUser).IsAdminOnClient(this._currentClient);

            if (!isAdmin && bakReq.type == "CLIENT")
            {
                throw new SecurityException();
            }

            ClientBackupFiltersViewModel filters = null;

            if (!string.IsNullOrEmpty(bakReq.requestFilters))
            {
                try
                {
                    filters = Newtonsoft.Json.JsonConvert.DeserializeObject<ClientBackupFiltersViewModel>(bakReq.requestFilters);
                }
                catch
                {
                    Console.WriteLine("Unable to parse requestFilters JSON: " + bakReq.requestFilters);
                    filters = null;
                }
            }

            var cs = ConfigurationManager.ConnectionStrings["AtlasV2ContextReadOnly"].ConnectionString;

            var spName = string.Concat("sp_", bakReq.type.ToLower(), "_exportData");

            SqlCommand cmd = new SqlCommand(spName, new SqlConnection(cs));
            cmd.CommandTimeout = 6000;
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.Parameters.AddWithValue("@attOnly", 1);
            cmd.Parameters.AddWithValue("@clientId", bakReq.clientId);

            if (filters != null && filters.workgroupId.HasValue && bakReq.type == "USER")
            {
                cmd.Parameters.AddWithValue("@workgroupId", filters.workgroupId.Value);
                cmd.Parameters.Add(new SqlParameter("@userId", bakReq.requestUserId));
            }
            if (filters != null && filters.startDate.HasValue)
            {
                cmd.Parameters.Add(new SqlParameter("@dataCreationStart", filters.startDate.Value));
            }
            if (filters != null && filters.endDate.HasValue)
            {
                cmd.Parameters.Add(new SqlParameter("@dataCreationEnd", filters.endDate.Value));
            }
            //cmd.Parameters.Add(new SqlParameter("@dateStart", bakReq.clientId));
            //cmd.Parameters.Add(new SqlParameter("@dateEnd", bakReq.clientId));

            cmd.Connection.Open();

            DataSet ds = new DataSet();
            SqlDataAdapter adapter = new SqlDataAdapter();
            adapter.SelectCommand = cmd;
            adapter.Fill(ds);
            var attachment_table = ds.Tables[0];

            //expected output
            List<ClientBackupResult> result = new List<ClientBackupResult>();

            //file size calculation
            long maxResultSize = (long)1024 /*KB*/ * 1024 /*MB*/ * 1024 /*GB*/ * 5 /*qtd desejada de GB em cada parte do backup*/;
            long accumulatedByteCount = 0;

            int attachmentRankMin = 0;
            int attachmentRankMax;
            int backupResCount = 1;
            List<int> containedAttachmentIds = new List<int>();

            for (int i = 0; i < attachment_table.Rows.Count; i++)
            {
                //add to attachmentId array
                containedAttachmentIds.Add(Convert.ToInt32(attachment_table.Rows[i]["attachmentId"].ToString()));

                //if Zero, sets the current attachmentId as the first
                if (attachmentRankMin == 0)
                {
                    attachmentRankMin = Convert.ToInt32(attachment_table.Rows[i]["rank_no"].ToString());
                }

                long currentFileSize = Convert.ToInt64(attachment_table.Rows[i]["bytesCount"].ToString());

                accumulatedByteCount += currentFileSize;

                if (accumulatedByteCount >= maxResultSize || i == attachment_table.Rows.Count - 1)
                {
                    attachmentRankMax = Convert.ToInt32(attachment_table.Rows[i]["rank_no"].ToString());

                    //create a new BackupResult
                    result.Add(new ClientBackupResult()
                    {
                        bakReqId = bakReq.bakReqId,
                        clientId = bakReq.clientId,
                        attachmentRankMin = attachmentRankMin,
                        attachmentRankMax = attachmentRankMax,
                        attachmentIdStart = containedAttachmentIds.Min(),
                        attachmentIdEnd = containedAttachmentIds.Max(),
                        estimatedByteCount = accumulatedByteCount,
                        processed = false,
                        fileNumber = backupResCount,
                    });

                    //erases the variable to capture the next
                    attachmentRankMin = 0;
                    accumulatedByteCount = 0;
                    containedAttachmentIds = new List<int>();
                    //increase the backup result part count
                    backupResCount++;
                }

            }

            if (attachment_table.Rows.Count == 0)
            {
                result.Add(new ClientBackupResult()
                {
                    bakReqId = bakReq.bakReqId,
                    clientId = bakReq.clientId,
                    attachmentRankMin = 0,
                    attachmentRankMax = 0,
                    attachmentIdStart = 0,
                    attachmentIdEnd = 0,
                    estimatedByteCount = 50 * 1024 * 1024, //dummy ammount of data to avoid 0.00gb downloads
                    processed = false,
                    fileNumber = 1,
                });
            }

            bakReq.ClientBackupResults = result;
            bakReq.dataExportResult = ds;
            return bakReq;
        }

        public async Task<ClientBackupRequest> GetEstimatedKBBackupResults(ClientBackupRequest bakReq)
        {
            ClientBackupFiltersViewModel filters = null;

            if (!string.IsNullOrEmpty(bakReq.requestFilters))
            {
                try
                {
                    filters = Newtonsoft.Json.JsonConvert.DeserializeObject<Business.ViewModels.ClientBackup.ClientBackupFiltersViewModel>(bakReq.requestFilters);
                }
                catch
                {
                    Console.WriteLine("Unable to parse requestFilters JSON: " + bakReq.requestFilters);
                    filters = null;
                }
            }

            using (var dbContext = new AtlasModelCore())
            {
                try
                {
                    KnowledgeBaseRepository kbRepo = new KnowledgeBaseRepository();
                    List<KnowledgeBaseBackupResult> queryResult = new List<KnowledgeBaseBackupResult>();

                    var showKbDefaultFoldersEnabled = dbContext.Client.Find(bakReq.clientId).showKbDefaultFoldersEnabled;

                    switch (filters.entity)
                    {
                        case "workgroup"://workgroupId
                            queryResult.AddRange(kbRepo.GetKBBackupQuery(filters.workgroupId, filters.contentId, _currentUser, filters.entity, true));
                            if (showKbDefaultFoldersEnabled == true)
                            {
                                queryResult.AddRange(kbRepo.GetBoardTasks(_currentUser, filters.workgroupId.Value));

                                queryResult.AddRange(kbRepo.GetMeetingMaterials(_currentUser, filters.workgroupId.Value));

                                queryResult.AddRange(kbRepo.GetMeetingMinutes(_currentUser, filters.workgroupId.Value));
                            }
                            
                            break;
                        case "knowledgeDirectory": //contentId
                        case "knowledgeBase": //workgroupId
                        case "workgroupRoot": //workgroupId
                            queryResult.AddRange(kbRepo.GetKBBackupQuery(filters.workgroupId, filters.contentId, _currentUser, filters.entity, true));
                            break;
                        case "meetingMaterials": //workgroupId
                            if (showKbDefaultFoldersEnabled == true)
                            {
                                queryResult.AddRange(kbRepo.GetMeetingMaterials(_currentUser, filters.workgroupId.Value));
                            }
                            break;
                        case "meetingMinutes": //workgroupId
                            if (showKbDefaultFoldersEnabled == true)
                            {
                                queryResult.AddRange(kbRepo.GetMeetingMinutes(_currentUser, filters.workgroupId.Value));
                            }
                            break;
                        case "workgroupActions": //workgroupId
                            if (showKbDefaultFoldersEnabled == true)
                            {
                                queryResult.AddRange(kbRepo.GetBoardTasks(_currentUser, filters.workgroupId.Value));
                            }
                            break;
                        default:
                            break;
                    }

                    AtlasModelCore _md = new AtlasModelCore();
                    _md.Database.SetCommandTimeout(6000);
                    var currentUserObj = _md.User.Find(_currentUser);


                    //expected output
                    List<ClientBackupResult> result = new List<ClientBackupResult>();

                    //file size calculation
                    long maxResultSize = (long)1024 /*KB*/ * 1024 /*MB*/ * 1024 /*GB*/ * 5 /*qtd desejada de GB em cada parte do backup*/;
                    long accumulatedByteCount = 0;

                    int attachmentRankMin = 1;
                    int backupResCount = 1;
                    int index = 0;
                    List<int> containedAttachmentIds = new List<int>();

                    foreach (var attachment in queryResult)
                    {
                        //add to contentAttachmentId array
                        containedAttachmentIds.Add(Convert.ToInt32(attachment.contentAttachmentId));

                        long currentFileSize = Convert.ToInt64(attachment.bytesCount);

                        accumulatedByteCount += currentFileSize;

                        if (accumulatedByteCount >= maxResultSize || (index == queryResult.Count - 1))
                        {
                            result.Add(new ClientBackupResult()
                            {
                                bakReqId = bakReq.bakReqId,
                                clientId = bakReq.clientId,
                                attachmentRankMin = attachmentRankMin,
                                attachmentRankMax = index + 1,
                                attachmentIdStart = containedAttachmentIds.Min(),
                                attachmentIdEnd = containedAttachmentIds.Max(),
                                estimatedByteCount = accumulatedByteCount,
                                processed = false,
                                fileNumber = backupResCount,
                            });

                            //erases the variable to capture the next
                            attachmentRankMin = index + 2;
                            accumulatedByteCount = 0;
                            containedAttachmentIds = new List<int>();
                            //increase the backup result part count
                            backupResCount++;
                        }

                        index++;
                    }


                    if (queryResult.Count == 0)
                    {
                        result.Add(new ClientBackupResult()
                        {
                            bakReqId = bakReq.bakReqId,
                            clientId = bakReq.clientId,
                            attachmentRankMin = 0,
                            attachmentRankMax = 0,
                            attachmentIdStart = 0,
                            attachmentIdEnd = 0,
                            estimatedByteCount = 50 * 1024 * 1024, //dummy ammount of data to avoid 0.00gb downloads
                            processed = false,
                            fileNumber = 1,
                        });
                    }

                    bakReq.ClientBackupResults = result;
                    return bakReq;
                }
                catch (Exception ex)
                {
                    throw ex;
                }
            }


        }

        static string[] table_names = new string[]
        {
            "Client",
            "ClientDomainPermission",
            "User",
            "Workgroup",
            "WorkgroupUser",
            "KnowledgeCategory",
            "Attachment",
            "Content",
            "ContentComment",
            "ContentAttachment",
            "Meeting",
            "MeetingAgendaItem",
            "MeetingMinute",
            "Announcement",
            "Task",
            "KnowledgeArticle",
            "ContentGuest",
            "Poll",
            "PollVote",
            "WorkgroupOwner",
            "WorkgroupTaskList"
        };


        private static string GetTableName(int index)
        {
            try
            {
                return table_names[index] + ".csv";
            }
            catch (Exception ex)
            {
                return "table_" + DateTime.Now.ToFileTimeUtc().ToString() + ".csv";
            }
        }

        public static string DataTableToCSV(DataTable dt)
        {
            StringBuilder sb = new StringBuilder();
            try
            {
                int count = 1;
                int totalColumns = dt.Columns.Count;
                foreach (DataColumn dr in dt.Columns)
                {
                    sb.Append(dr.ColumnName);

                    if (count != totalColumns)
                    {
                        sb.Append(",");
                    }

                    count++;
                }

                sb.AppendLine();

                string value = String.Empty;
                foreach (DataRow dr in dt.Rows)
                {
                    for (int x = 0; x < totalColumns; x++)
                    {
                        value = dr[x].ToString();

                        if (value.Contains(",") || value.Contains("\""))
                        {
                            value = '"' + value.Replace("\"", "\"\"") + '"';
                        }

                        // safe replace new lines (Windows and Unix) with a single space
                        value = RemoveNewlines(value);

                        sb.Append(value);

                        if (x != (totalColumns - 1))
                        {
                            sb.Append(",");
                        }
                    }

                    sb.AppendLine();
                }
            }
            catch (Exception ex)
            {
                // Do something
            }

            return sb.ToString();
        }

        /// <summary>
        /// Removes all newline characters from the given string.
        /// Supports Windows (\r\n), Unix/Linux (\n), and old Mac (\r) formats.
        /// </summary>
        /// <param name="input">The string to sanitize.</param>
        /// <returns>A string with all newlines removed.</returns>
        public static string RemoveNewlines(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            return input
                .Replace("\r\n", " ")  // Windows
                .Replace("\n", " ")    // Unix/Linux
                .Replace("\r", " ");   // Legacy Mac
        }
    }
}
