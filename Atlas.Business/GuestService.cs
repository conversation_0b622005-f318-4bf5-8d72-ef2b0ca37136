using Atlas.Business.Helpers;
using Atlas.CrossCutting.AppEnums;
using Atlas.Data.Entities;
using Atlas.Data.Repository;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security;
using System.Threading.Tasks;
using static Atlas.CrossCutting.AppEnums.ContentStatuses;

namespace Atlas.Business
{
    public class GuestService
    {
        int _currentUser;
        private AtlasModelCore _model;
        int notificationInterval = Int32.Parse(Environment.GetEnvironmentVariable("MeetingAgenda_ContentGuestNotification_Minutes") ?? "60");
        int notificationIntervalPostTransition = Int32.Parse(Environment.GetEnvironmentVariable("MeetingAgenda_ContentGuestNotification_Minutes_Post_Transition") ?? "0");

        public GuestService(int currentUser)
        {
            _currentUser = currentUser;
            _model = new AtlasModelCore();
        }

        public async Task<bool> Update(int contentId, List<ContentGuest> updatedGuestList)
        {
            ContentService contentService = new ContentService(_currentUser);
            Content content = await contentService.Get(contentId);

            this.Validate(_currentUser, content);
            return await this.AddOrRemove(contentId, content, updatedGuestList);
        }

        public async Task<bool> AddOrRemove(int contentId, Content content, List<ContentGuest> updatedGuestList, bool addOnly = false)
        {
            /*
             *  S-48
             *  Convidado Ouvinte
             *  Validações de Segurança
            */

            //  Convidados serã permitidos somente para Reuniões e Pautas, por enquanto
            if (content.type != ContentTypes.Meeting && content.type != ContentTypes.MeetingAgendaItem)
            {
                throw new SecurityException("Attempt to add/update guests to a invalid Content type");
            }

            /* 
             *  Validando o tipo de convidado
             *  Por hora, tipos são LISTENER e PRESENTER
             *  Reuniões sempre terão convidados do tipo LISTENER
             *  Pautas podem ser os 2
             *  
            */

            bool isMeeting = content.type == ContentTypes.Meeting;
            bool isMeetingAgenda = content.type == ContentTypes.MeetingAgendaItem;
            foreach (ContentGuest guest in updatedGuestList)
            {
                if (guest.type != GuestTypes.GUEST_LISTENER && guest.type != GuestTypes.GUEST_PRESENTER)
                {
                    throw new SecurityException("Attempt to add/update invalid guests");
                }

                if (isMeeting) guest.type = GuestTypes.GUEST_LISTENER;
                if (isMeetingAgenda) guest.type = GuestTypes.GUEST_LISTENER;
                if (string.IsNullOrWhiteSpace(guest.guestMail)) guest.guestMail = null;

                guest.contentId = contentId;
            }

            /*
             *  Adding/Removing guests logic
             * 
             */

            AtlasModelCore _md = new AtlasModelCore();

            var workgroupUserEmails = await _md.WorkgroupUser
                .Where(wu => wu.workgroupId == content.workgroupId)
                .Select(wu => wu.User.email.ToLower())
                .ToArrayAsync();

            bool guestIsWorkgroupUser = false;

            Content contentMeeting = null;

            if (isMeetingAgenda)
            {
                contentMeeting = await (
                    from c in _md.Content
                    join p in _md.ContentPermission on c.contentId equals p.contentId
                    where p.userId == this._currentUser && c.contentId == content.parentContentId && c.deleted != true
                    select c
                    )
                    .Include(c => c.ContentGuest)
                    .FirstOrDefaultAsync();
            }

            var guestsWithEmail = updatedGuestList.Where(g => !string.IsNullOrWhiteSpace(g.guestMail)).ToList();

            await ValidateRecurringGuests(content, contentMeeting, guestsWithEmail, isMeeting);

            ContentActivityService contentActivityService = new ContentActivityService(_currentUser);
            // Preparing guests to add
            foreach (ContentGuest updatedGuest in updatedGuestList)
            {

                if ((updatedGuest.guestName.Length > 100) ||
                    (!string.IsNullOrEmpty(updatedGuest.guestMail) && (updatedGuest.guestMail.Length > 200)))
                {
                    throw new FormatException("INVALID_LENGTH");
                }

                // Checks if the guest already exists in the database, validating the ones with a valid
                // email (and probably and active ics notification) for duplication.
                var guestAlreadyExists = content.ContentGuest
                    .Any(g => g.contentGuestId == updatedGuest.contentGuestId ||
                        (g.guestNotification == true && g.guestMail == updatedGuest.guestMail));

                if (!guestAlreadyExists)
                {
                    if (updatedGuest.guestNotification == true)
                    {
                        try
                        {
                            //validating using MailAddress Class, instead of regex
                            new System.Net.Mail.MailAddress(updatedGuest.guestMail);
                        }
                        catch
                        {
                            throw new Exception("INVALID_EMAIL");
                        }
                    }

                    if (workgroupUserEmails.Contains(updatedGuest.guestMail?.ToLower()))
                    {
                        // The guest user already is a workgroup member so do not add
                        guestIsWorkgroupUser = true;
                    }
                    else
                    {
                        //addingGuests.Add(updatedGuest);
                        _md.ContentGuest.Add(updatedGuest);

                        var isPastMeeting = content.Meeting.FirstOrDefault().date < DateTime.UtcNow;

                        contentActivityService.Add(contentId, new ContentActivity()
                        {
                            date = DateTime.UtcNow,
                            type = (isMeeting ? "MEETING_LISTENER_GUEST_ADD" : "MEETING_AGENDA_LISTENER_GUEST_ADD"),
                            activityUser = _currentUser,
                            subItemType = SubItems.GUEST,
                            newData = updatedGuest.guestName,
                            //hidden = true,
                            contentData = Newtonsoft.Json.JsonConvert.SerializeObject(new { updatedGuest.guestName, updatedGuest.guestMail }),
                            processed = isPastMeeting
                        });
                    }
                }
            }

            bool addSuccess = await _md.SaveChangesAsync() > 0;
            bool removeSuccess = false;

            MailNotificationHelper mailNotificationHelper = new MailNotificationHelper();

            if (!addOnly)
            {
                // Preparing guests to remove
                foreach (ContentGuest currentGuest in content.ContentGuest)
                {
                    if (!updatedGuestList.Any(g => g.contentGuestId == currentGuest.contentGuestId))
                    {
                        ContentGuest contentGuestRemoved = _md.ContentGuest.Where(cg => cg.contentGuestId == currentGuest.contentGuestId).FirstOrDefault();
                        _md.ContentGuest.Remove(contentGuestRemoved);

                        contentActivityService.Add(contentId, new ContentActivity()
                        {
                            date = DateTime.UtcNow,
                            type = (isMeeting ? "MEETING_LISTENER_GUEST_DELETE" : "MEETING_AGENDA_LISTENER_GUEST_DELETE"),
                            activityUser = _currentUser,
                            newData = contentGuestRemoved.guestName,
                            subItemType = SubItems.GUEST,
                            contentData = Newtonsoft.Json.JsonConvert.SerializeObject(contentGuestRemoved)
                        });

                        if (isMeetingAgenda)
                        {
                            var meetingAgendaItem = _md.MeetingAgendaItem.FirstOrDefault(p => p.contentId == currentGuest.contentId);

                            //Validation if the ICS is in the new rule. If yes, it uses the agenda ID to create the ContentGuestNotification. If not, it uses the meeting ID to create the ContentGuestNotification.
                            if (mailNotificationHelper.ValidateNewICSRuleAgenda(content.contentId))
                            {
                                RemoveScheduleNotification(content.contentId, contentGuestRemoved.guestMail, contentGuestRemoved.contentGuestNotificationId);
                            }
                            else
                            {
                                RemoveScheduleNotification(content.parentContentId.Value, contentGuestRemoved.guestMail, contentGuestRemoved.contentGuestNotificationId);
                            }
                        }

                        if (content.Meeting.FirstOrDefault().date <= DateTime.UtcNow)
                        {
                            RemoveScheduleNotification(content.contentId, contentGuestRemoved.guestMail, contentGuestRemoved.contentGuestNotificationId);
                        }
                    }
                }

                removeSuccess = await _md.SaveChangesAsync() > 0;
            }

            // Additional filter:
            // Do not insert recurring guest that already belongs to the workgroup.
            var recurringGuestList = updatedGuestList.Where(guest => guest.recurringGuest && !workgroupUserEmails.Contains(guest.guestMail?.ToLower()));
            await new RecurringGuestService().Add(recurringGuestList, content.workgroupId, _currentUser, isMeeting, content.contentId);

            if (isMeetingAgenda)
            {
                //Validation if the ICS is in the new rule. If yes, it uses the agenda ID to create the ContentGuestNotification. If not, it uses the meeting ID to create the ContentGuestNotification.
                if (mailNotificationHelper.ValidateNewICSRuleAgenda(content.contentId))
                {
                    LoadContentGuestNotificationAgendaMarco(content.parentContentId.Value);
                }
                else
                {
                    LoadContentGuestNotification(content.parentContentId.Value);
                }
            }

            return addSuccess || removeSuccess;
        }

        // this method name is misleading. it is actually validating every type  of guest. Needs attetion.
        private async System.Threading.Tasks.Task ValidateRecurringGuests(Content content, Content parentContent, List<ContentGuest> updatedGuestList, bool isMeeting = false)
        {
            if (!updatedGuestList.Any())
                return;

            var existingGuestsList = new List<ContentGuest>();

            if (isMeeting)
            {
                var childAgendas = await _model.Content
                                            .Where(c => c.parentContentId == content.contentId && c.deleted != true && c.type == ContentTypes.MeetingAgendaItem)
                                            .Include(MeetingAgendaItem => MeetingAgendaItem.ContentGuest)
                                            .ToListAsync();

                var childAgendasGuests = childAgendas.SelectMany(ca => ca.ContentGuest).Where(cg => cg.recurringGuest).ToList();

                existingGuestsList.AddRange(childAgendasGuests);
            }

            if (parentContent != null && parentContent.type == ContentTypes.Meeting)
            {
                existingGuestsList.AddRange(parentContent.ContentGuest);
            }

            existingGuestsList.AddRange(content.ContentGuest);

            if (!existingGuestsList.Any())
                return;

            var existingGuestEmails = existingGuestsList.Select(rg => rg.guestMail).ToList();

            var addedGuestsEmails = updatedGuestList.Where(g => g.recurringGuestId == 0 && g.contentGuestId == 0).Select(g => g.guestMail).ToList();

            if (!addedGuestsEmails.Any())
                return;

            var workgroupRecurringGuests = await _model.RecurringGuest.Where(rg => rg.workgroupId == content.workgroupId).Select(rg => rg.email).ToListAsync();

            if (content.type == ContentTypes.MeetingAgendaItem && parentContent != null && parentContent.type == ContentTypes.Meeting)
            {
                var parentGuestEmails = parentContent.ContentGuest.Select(g => g.guestMail).ToList();
                existingGuestEmails = existingGuestEmails.Except(parentGuestEmails).ToList();
            }

            if (existingGuestEmails.Intersect(addedGuestsEmails).Any() || workgroupRecurringGuests.Intersect(addedGuestsEmails).Any())
            {
                throw new InvalidOperationException("INVALID_GUEST");
            }
        }

        [Obsolete("Must use the version informing a ContentUuid instead.")]
        public async Task<bool> DeleteAll(int contentId)
        {
            AtlasModelCore _md = new AtlasModelCore();

            foreach (var contentGuest in _md.ContentGuest.Where(p => p.Content.parentContentId == contentId))
            {
                _md.Entry(contentGuest).State = Microsoft.EntityFrameworkCore.EntityState.Deleted;
            }

            var result = await _md.SaveChangesAsync() > 0;

            RemoveScheduleNotification(contentId);

            return result;
        }

        public async Task<bool> DeleteAll(Guid contentUuid)
        {
            AtlasModelCore _md = new AtlasModelCore();

            foreach (var contentGuest in _md.ContentGuest.Where(p => p.Content.parentContentUuid == contentUuid))
            {
                _md.Entry(contentGuest).State = Microsoft.EntityFrameworkCore.EntityState.Deleted;
            }

            var result = await _md.SaveChangesAsync() > 0;

            RemoveScheduleNotification(contentUuid);

            return result;
        }

        public async Task<bool> SendAll(int contentId)
        {
            LoadContentGuestNotification(contentId);

            AtlasModelCore _md = new AtlasModelCore();

            foreach (var cgm in _md.ContentGuestNotification.Where(p => p.contentId == contentId))
            {
                cgm.whenToSend = DateTime.UtcNow.AddMinutes(-1);
                _md.Entry(cgm).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
            }

            var result = await _md.SaveChangesAsync() > 0;

            return result;
        }

        public void Validate(int currentUser, Content content)
        {
            ContentRepository contentRepository = new ContentRepository(_currentUser);

            // Not allowed if meeting is cancelled
            if (content.parent_status == ContentMeetingStatus.CANCELLED || (content.type == ContentTypes.Meeting && content.status == ContentMeetingStatus.CANCELLED))
            {
                throw new SecurityException("NOT_ALLOWED");
            }

            // Checking workgroup permission
            if (!contentRepository.CheckPermissionsForWorkgroup(operationKey: null, content_id: content.contentId))
            {
                throw new SecurityException("Unauthorized attempt to add guest into a content in workgroup.");
            }

            //  Somente o owner pode adicionar/gerenciar guests
            if (!content.UAC.manage_guests)
            {
                throw new SecurityException("Attempt to add/update guests without being the content owner");
            }
        }

        [Obsolete("Must use the version informing a ContentUuid instead.")]
        public void LoadContentGuestNotification(int contentId, IEnumerable<int> sendTo = null)
        {
            AtlasModelCore _md = new AtlasModelCore();

            var needToNotify = false;

            var content = _md.Content
                .Include(o => o.Meeting)
                .Where(o => o.contentId == contentId).FirstOrDefault();

            var meeting = content.Meeting.FirstOrDefault();

            var agenda = _md.Content
                .Where(o => o.parentContentId == contentId
                          && !(o.Parent_Content.deleted ?? false)
                          && !(o.deleted ?? false)
                          && o.type == ContentTypes.MeetingAgendaItem)
                .SelectMany(o => o.MeetingAgendaItem)
                .Include(p => p.Content)
                .OrderBy(o => o.itemOrder)
                .ToList();


            var contentGuests = _md.ContentGuest
                .Where(p => contentId == p.Content.parentContentId)
                .Distinct()
                .ToList();

            if (sendTo != null && sendTo.Any())
                contentGuests = contentGuests.FindAll(cg => sendTo.Contains(cg.contentGuestId));

            if (!contentGuests.Any())
                return;

            var contentGuestsEmails = contentGuests
                .Where(cge => !string.IsNullOrEmpty(cge.guestMail))
                .Select(cge => cge.guestMail.ToLower())
                .Distinct();

            var sendRightAway = sendTo?.Any() == true;

            if (meeting != null && meeting.date > DateTime.UtcNow)
            {
                foreach (var guestEmail in contentGuestsEmails)
                {
                    foreach (var cgm in _md.ContentGuestNotification.Where(p => p.contentId == contentId && p.guestEmail == guestEmail && p.sentDate == null).ToList())
                    {
                        _md.Entry(cgm).State = EntityState.Deleted;
                    }
                    _md.SaveChanges();

                    var icsToSend = new List<dynamic>();
                    DateTime? time_start = null;
                    DateTime? time_end = null;
                    int accumulatedTime = 0;
                    var contentGuestList = new List<int>();

                    foreach (var pauta in agenda)
                    {
                        if ((pauta.Content.deleted ?? false))
                        {
                            continue;
                        }

                        var contentGuest = _md.ContentGuest.FirstOrDefault(p => p.contentId == pauta.contentId && p.guestMail == guestEmail);

                        if (pauta.time > 0)
                        {
                            if (contentGuest != null)
                            {
                                if (time_start == null)
                                {
                                    time_start = meeting.date.AddMinutes(accumulatedTime);
                                }

                                time_end = meeting.date.AddMinutes(accumulatedTime + pauta.time);
                                contentGuestList.Add(contentGuest.contentGuestId);
                            }
                            else
                            {
                                if (time_start != null && time_end != null)
                                {
                                    icsToSend.Add(new
                                    {
                                        time_start,
                                        time_end,
                                        contentGuestList
                                    });

                                    time_start = null;
                                    time_end = null;
                                    contentGuestList = new List<int>();
                                }
                            }

                            accumulatedTime += pauta.time;
                        }

                    }

                    if (time_start != null && time_end != null)
                    {
                        icsToSend.Add(new
                        {
                            time_start,
                            time_end,
                            contentGuestList
                        });
                        time_start = null;
                        time_end = null;
                    }

                    foreach (var cgm in _md.ContentGuestNotification.Where(p => p.contentId == contentId && p.guestEmail == guestEmail && p.sentDate != null).ToList())
                    {
                        if (!icsToSend.Any(p => p.time_start == cgm.dateStart && p.time_end == cgm.dateEnd))
                        {
                            cgm.whenToSend = DateTime.UtcNow;
                            cgm.sendOperation = "DELETE";
                            _md.Entry(cgm).State = EntityState.Modified;
                        }
                        else if (sendRightAway && icsToSend.Any(p => p.time_start == cgm.dateStart && p.time_end == cgm.dateEnd))
                        {
                            cgm.whenToSend = DateTime.UtcNow;
                            _md.Entry(cgm).State = EntityState.Modified;
                            var icsRemove = icsToSend.Where(i => i.time_start == cgm.dateStart && i.time_end == cgm.dateEnd).FirstOrDefault();
                            icsToSend.Remove(icsRemove);
                        }
                        else if (sendRightAway)
                        {
                            _md.Entry(cgm).State = EntityState.Deleted;
                        }
                    }
                    _md.SaveChanges();

                    foreach (var ics in icsToSend)
                    {
                        DateTime dateStart = ics.time_start;
                        DateTime dateEnd = ics.time_end;

                        if (!_md.ContentGuestNotification
                                .Any(p => p.contentId == contentId &&
                                          p.guestEmail == guestEmail &&
                                          p.dateStart == dateStart &&
                                          p.dateEnd == dateEnd &&
                                          p.sendOperation == "ADD"))
                        {
                            var icsKey = Guid.NewGuid();

                            var n = new ContentGuestNotification()
                            {
                                contentId = contentId,
                                guestEmail = guestEmail,
                                dateStart = ics.time_start,
                                dateEnd = ics.time_end,
                                icsKey = icsKey,
                                whenToSend = DateTime.UtcNow.AddMinutes(sendRightAway ? 0 : (content.status == "READY" ? 5 : notificationInterval)),
                                sendOperation = "ADD"
                            };

                            _md.ContentGuestNotification.Add(n);
                            _md.SaveChanges();

                            foreach (var contentGuestId in (List<int>)ics.contentGuestList)
                            {
                                var cg = _md.ContentGuest.Find(contentGuestId);
                                cg.contentGuestNotificationId = n.contentGuestNotificationId;

                                _md.Entry(cg).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
                                _md.SaveChanges();
                            }

                            needToNotify = true;
                        }
                    }
                }
            }
        }
        public void LoadContentGuestNotification(Guid contentUuid, IEnumerable<int> sendTo = null)
        {
            AtlasModelCore _md = new AtlasModelCore();

            var needToNotify = false;

            var content = _md.Content
                .Include(o => o.Meeting)
                .Where(o => o.contentUuid == contentUuid).FirstOrDefault();

            var meeting = content.Meeting.FirstOrDefault();

            var agenda = _md.Content
                .Where(o => o.parentContentUuid == contentUuid
                          && !(o.Parent_Content.deleted ?? false)
                          && !(o.deleted ?? false)
                          && o.type == ContentTypes.MeetingAgendaItem)
                .SelectMany(o => o.MeetingAgendaItem)
                .Include(p => p.Content)
                .OrderBy(o => o.itemOrder)
                .ToList();


            var contentGuests = _md.ContentGuest
                .Where(p => contentUuid == p.Content.parentContentUuid)
                .Distinct()
                .ToList();

            if (sendTo != null && sendTo.Any())
                contentGuests = contentGuests.FindAll(cg => sendTo.Contains(cg.contentGuestId));

            if (!contentGuests.Any())
                return;

            var contentGuestsEmails = contentGuests
                .Where(cge => !string.IsNullOrEmpty(cge.guestMail))
                .Select(cge => cge.guestMail.ToLower())
                .Distinct();

            var sendRightAway = sendTo?.Any() == true;

            if (meeting != null && meeting.date > DateTime.UtcNow)
            {
                foreach (var guestEmail in contentGuestsEmails)
                {
                    foreach (var cgm in _md.ContentGuestNotification.Where(p => p.contentUuid == contentUuid && p.guestEmail == guestEmail && p.sentDate == null).ToList())
                    {
                        _md.Entry(cgm).State = EntityState.Deleted;
                    }
                    _md.SaveChanges();

                    var icsToSend = new List<dynamic>();
                    DateTime? time_start = null;
                    DateTime? time_end = null;
                    int accumulatedTime = 0;
                    var contentGuestList = new List<int>();

                    foreach (var pauta in agenda)
                    {
                        if ((pauta.Content.deleted ?? false))
                        {
                            continue;
                        }

                        var contentGuest = _md.ContentGuest.FirstOrDefault(p => p.contentId == pauta.contentId && p.guestMail == guestEmail);

                        if (pauta.time > 0)
                        {
                            if (contentGuest != null)
                            {
                                if (time_start == null)
                                {
                                    time_start = meeting.date.AddMinutes(accumulatedTime);
                                }

                                time_end = meeting.date.AddMinutes(accumulatedTime + pauta.time);
                                contentGuestList.Add(contentGuest.contentGuestId);
                            }
                            else
                            {
                                if (time_start != null && time_end != null)
                                {
                                    icsToSend.Add(new
                                    {
                                        time_start,
                                        time_end,
                                        contentGuestList
                                    });

                                    time_start = null;
                                    time_end = null;
                                    contentGuestList = new List<int>();
                                }
                            }

                            accumulatedTime += pauta.time;
                        }

                    }

                    if (time_start != null && time_end != null)
                    {
                        icsToSend.Add(new
                        {
                            time_start,
                            time_end,
                            contentGuestList
                        });
                        time_start = null;
                        time_end = null;
                    }

                    foreach (var cgm in _md.ContentGuestNotification.Where(p => p.contentUuid == contentUuid && p.guestEmail == guestEmail && p.sentDate != null).ToList())
                    {
                        if (!icsToSend.Any(p => p.time_start == cgm.dateStart && p.time_end == cgm.dateEnd))
                        {
                            cgm.whenToSend = DateTime.UtcNow;
                            cgm.sendOperation = "DELETE";
                            _md.Entry(cgm).State = EntityState.Modified;
                        }
                        else if (sendRightAway && icsToSend.Any(p => p.time_start == cgm.dateStart && p.time_end == cgm.dateEnd))
                        {
                            cgm.whenToSend = DateTime.UtcNow;
                            _md.Entry(cgm).State = EntityState.Modified;
                            var icsRemove = icsToSend.Where(i => i.time_start == cgm.dateStart && i.time_end == cgm.dateEnd).FirstOrDefault();
                            icsToSend.Remove(icsRemove);
                        }
                        else if (sendRightAway)
                        {
                            _md.Entry(cgm).State = EntityState.Deleted;
                        }
                    }
                    _md.SaveChanges();

                    foreach (var ics in icsToSend)
                    {
                        DateTime dateStart = ics.time_start;
                        DateTime dateEnd = ics.time_end;

                        if (!_md.ContentGuestNotification
                                .Any(p => p.contentUuid == contentUuid &&
                                          p.guestEmail == guestEmail &&
                                          p.dateStart == dateStart &&
                                          p.dateEnd == dateEnd &&
                                          p.sendOperation == "ADD"))
                        {
                            var icsKey = Guid.NewGuid();

                            var n = new ContentGuestNotification()
                            {
                                contentUuid = contentUuid,
                                contentId = content.contentId,
                                guestEmail = guestEmail,
                                dateStart = ics.time_start,
                                dateEnd = ics.time_end,
                                icsKey = icsKey,
                                whenToSend = DateTime.UtcNow.AddMinutes(sendRightAway ? 0 : (content.status == "READY" ? 5 : notificationInterval)),
                                sendOperation = "ADD"
                            };

                            _md.ContentGuestNotification.Add(n);
                            _md.SaveChanges();

                            foreach (var contentGuestId in (List<int>)ics.contentGuestList)
                            {
                                var cg = _md.ContentGuest.Find(contentGuestId);
                                cg.contentGuestNotificationId = n.contentGuestNotificationId;

                                _md.Entry(cg).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
                                _md.SaveChanges();
                            }

                            needToNotify = true;
                        }
                    }
                }
            }
        }

        #region Agenda Marco

        [Obsolete("Must use the version informing a ContentUuid instead.")]
        public async Task<bool> DeleteAllAgendaMarco(int contentId, int[] contentAgendaId)
        {
            AtlasModelCore _md = new AtlasModelCore();

            foreach (var contentGuest in _md.ContentGuest.Where(p => p.Content.parentContentId == contentId))
            {
                _md.Entry(contentGuest).State = EntityState.Deleted;
            }

            var result = await _md.SaveChangesAsync() > 0;

            RemoveScheduleNotificationAgendaMarco(contentAgendaId);

            return result;
        }

        public async Task<bool> DeleteAllAgendaMarco(Guid contentUuid, Guid[] contentAgendaUuid)
        {
            AtlasModelCore _md = new AtlasModelCore();

            foreach (var contentGuest in _md.ContentGuest.Where(p => p.Content.parentContentUuid == contentUuid))
            {
                _md.Entry(contentGuest).State = EntityState.Deleted;
            }

            var result = await _md.SaveChangesAsync() > 0;

            RemoveScheduleNotificationAgendaMarco(contentAgendaUuid);

            return result;
        }


        public async Task<bool> SendAllAgendaMarco(int contentId, List<MeetingAgendaItem> agendas)
        {
            LoadContentGuestNotificationAgendaMarco(contentId);

            AtlasModelCore _md = new AtlasModelCore();

            foreach (var item in agendas)
            {
                foreach (var cgm in _md.ContentGuestNotification.Where(p => p.contentId == item.contentId && p.sentDate == null))
                {
                    cgm.whenToSend = DateTime.UtcNow.AddMinutes(-1);
                    _md.Entry(cgm).State = EntityState.Modified;
                }

                var result = await _md.SaveChangesAsync() > 0;
            }
            return true;
        }

        public void LoadContentGuestNotificationAgendaMarco(int contentId, int? contentAgendaDeleteId = null, IEnumerable<int> sendTo = null, string scenario = null, string updatedFields = null)
        {
            scenario = scenario != null ? scenario.ToLower() : null;

            var model = new AtlasModelCore();

            // Fetch primary content and related meeting
            var content = model.Content
                .Include(o => o.Meeting)
                .FirstOrDefault(o => o.contentId == contentId);

            if (content == null)
                return;

            var meeting = content.Meeting.FirstOrDefault();
            if (meeting == null || meeting.date <= DateTime.UtcNow)
                return;

            var agenda = GetAgendaItems(model, contentId);
            var contentGuests = GetContentGuests(model, contentId, sendTo);

            if (!contentGuests.Any())
                return;

            var guestEmails = contentGuests
                .Where(g => !string.IsNullOrEmpty(g.guestMail))
                .Select(g => g.guestMail.ToLower())
                .Distinct()
                .ToList();

            var sendRightAway = sendTo != null && sendTo.Any();

            foreach (var guestEmail in guestEmails)
            {
                ProcessGuestNotifications(model, guestEmail, contentAgendaDeleteId, agenda, meeting, content.status, sendRightAway, scenario, updatedFields);
            }
        }

        public void LoadContentGuestNotificationAgendaMarco(Guid contentUuid, Guid? contentAgendaDeleteId = null, IEnumerable<int> sendTo = null, string scenario = null, string updatedFields = null)
        {
            scenario = scenario != null ? scenario.ToLower() : null;

            var model = new AtlasModelCore();

            // Fetch primary content and related meeting
            var content = model.Content
                .Include(o => o.Meeting)
                .FirstOrDefault(o => o.contentUuid == contentUuid);

            if (content == null)
                return;

            var meeting = content.Meeting.FirstOrDefault();
            if (meeting == null || meeting.date <= DateTime.UtcNow)
                return;

            var agenda = GetAgendaItems(model, contentUuid);
            var contentGuests = GetContentGuests(model, contentUuid, sendTo);

            if (!contentGuests.Any())
                return;

            var guestEmails = contentGuests
                .Where(g => !string.IsNullOrEmpty(g.guestMail))
                .Select(g => g.guestMail.ToLower())
                .Distinct()
                .ToList();

            var sendRightAway = sendTo != null && sendTo.Any();

            foreach (var guestEmail in guestEmails)
            {
                ProcessGuestNotifications(model, guestEmail, contentAgendaDeleteId, agenda, meeting, content.status, sendRightAway, scenario, updatedFields);
            }
        }

        [Obsolete("Must use the version informing a ContentUuid instead.")]
        private List<MeetingAgendaItem> GetAgendaItems(AtlasModelCore model, int contentId)
        {
            return model.Content
                .Where(o => o.parentContentId == contentId
                          && !(o.Parent_Content.deleted ?? false)
                          && !(o.deleted ?? false)
                          && o.type == ContentTypes.MeetingAgendaItem)
                .SelectMany(o => o.MeetingAgendaItem)
                .Include(p => p.Content)
                .OrderBy(o => o.itemOrder)
                .ToList();
        }

        private List<MeetingAgendaItem> GetAgendaItems(AtlasModelCore model, Guid contentUuid)
        {
            return model.Content
                .Where(o => o.parentContentUuid == contentUuid
                          && !(o.Parent_Content.deleted ?? false)
                          && !(o.deleted ?? false)
                          && o.type == ContentTypes.MeetingAgendaItem)
                .SelectMany(o => o.MeetingAgendaItem)
                .Include(p => p.Content)
                .OrderBy(o => o.itemOrder)
                .ToList();
        }

        [Obsolete("Must use the version informing a ContentUuid instead.")]
        private List<ContentGuest> GetContentGuests(AtlasModelCore model, int contentId, IEnumerable<int> sendTo)
        {
            var guests = model.ContentGuest
                .Where(p => p.Content.parentContentId == contentId)
                .Distinct()
                .ToList();

            return sendTo != null && sendTo.Any()
                ? guests.Where(g => sendTo.Contains(g.contentGuestId)).ToList()
                : guests;
        }

        private List<ContentGuest> GetContentGuests(AtlasModelCore model, Guid contentUuid, IEnumerable<int> sendTo)
        {
            var guests = model.ContentGuest
                .Where(p => p.Content.parentContentUuid == contentUuid)
                .Distinct()
                .ToList();

            return sendTo != null && sendTo.Any()
                ? guests.Where(g => sendTo.Contains(g.contentGuestId)).ToList()
                : guests;
        }

        [Obsolete("Must use the version informing a ContentUuid instead.")]
        private void ProcessGuestNotifications(
            AtlasModelCore model,
            string guestEmail,
            int? contentAgendaDeleteId,
            List<MeetingAgendaItem> agendas,
            Meeting meeting,
            string contentStatus,
            bool sendRightAway,
            string scenario = null,
            string updatedFields = null)
        {
            RemoveOldNotifications(model, guestEmail, agendas, contentAgendaDeleteId);

            var icsData = BuildIcsData(agendas, meeting, guestEmail, model);
            UpdateExistingNotifications(model, guestEmail, contentAgendaDeleteId, agendas, icsData, contentStatus, sendRightAway, updatedFields);
            CreateNewNotifications(model, guestEmail, agendas, icsData, contentStatus, sendRightAway, scenario);
        }

        private void ProcessGuestNotifications(
            AtlasModelCore model,
            string guestEmail,
            Guid? contentAgendaDeleteId,
            List<MeetingAgendaItem> agendas,
            Meeting meeting,
            string contentStatus,
            bool sendRightAway,
            string scenario = null,
            string updatedFields = null)
        {
            RemoveOldNotifications(model, guestEmail, agendas, contentAgendaDeleteId);

            var icsData = BuildIcsData(agendas, meeting, guestEmail, model);
            UpdateExistingNotifications(model, guestEmail, contentAgendaDeleteId, agendas, icsData, contentStatus, sendRightAway, updatedFields);
            CreateNewNotifications(model, guestEmail, agendas, icsData, contentStatus, sendRightAway, scenario);
        }

        [Obsolete("Must use the version informing a ContentUuid instead.")]
        private void RemoveOldNotifications(AtlasModelCore model, string guestEmail, List<MeetingAgendaItem> agendas, int? contentAgendaDeleteId)
        {
            List<int> agendaIds = agendas.Select(a => a.contentId).ToList();
            if (contentAgendaDeleteId != null)
            {
                agendaIds.Add((int)contentAgendaDeleteId);
            }

            var oldNotifications = model.ContentGuestNotification
                .Where(n => agendaIds.Contains(n.contentId) && n.guestEmail == guestEmail && n.sentDate == null)
                .ToList();

            model.ContentGuestNotification.RemoveRange(oldNotifications);
            model.SaveChanges();
        }

        private void RemoveOldNotifications(AtlasModelCore model, string guestEmail, List<MeetingAgendaItem> agendas, Guid? contentAgendaDeleteId)
        {
            List<Guid> agendaIds = agendas.Select(a => a.contentUuid).ToList();
            if (contentAgendaDeleteId != null)
            {
                agendaIds.Add((Guid)contentAgendaDeleteId);
            }

            var oldNotifications = model.ContentGuestNotification
                .Where(n => agendaIds.Contains(n.contentUuid) && n.guestEmail == guestEmail && n.sentDate == null)
                .ToList();

            model.ContentGuestNotification.RemoveRange(oldNotifications);
            model.SaveChanges();
        }

        private List<IcsData> BuildIcsData(
            List<MeetingAgendaItem> agendas,
            Meeting meeting,
            string guestEmail,
            AtlasModelCore model)
        {
            var icsData = new List<IcsData>();
            DateTime? timeStart = null;
            DateTime? timeEnd = null;
            int accumulatedTime = 0;
            var guestList = new List<int>();

            foreach (var item in agendas)
            {
                if (item.Content.deleted ?? false)
                    continue;

                var guest = model.ContentGuest.FirstOrDefault(g => g.contentId == item.contentId && g.guestMail == guestEmail);

                if (item.time > 0 && guest != null)
                {
                    if (timeStart == null)
                        timeStart = meeting.date.AddMinutes(accumulatedTime);

                    timeEnd = meeting.date.AddMinutes(accumulatedTime + item.time);
                    guestList.Add(guest.contentGuestId);

                    icsData.Add(new IcsData { timeStart = (DateTime)timeStart, timeEnd = (DateTime)timeEnd, guestList = guestList, contentId = item.contentId });
                    timeStart = null;
                    timeEnd = null;
                    guestList = new List<int>();
                }

                accumulatedTime += item.time;
            }

            return icsData;
        }

        [Obsolete("Must use the version informing a ContentUuid instead.")]
        private void UpdateExistingNotifications(
            AtlasModelCore model,
            string guestEmail,
            int? contentAgendaDeleteId,
            List<MeetingAgendaItem> agendas,
            List<IcsData> icsData,
            string contentStatus,
            bool sendRightAway,
            string updatedFields)
        {
            List<int> agendaIds = agendas.Select(a => a.contentId).ToList();

            if (contentAgendaDeleteId != null)
            {
                agendaIds.Add((int)contentAgendaDeleteId);
            }

            var existingNotifications = model.ContentGuestNotification
                .Where(n => agendaIds.Contains(n.contentId) && n.guestEmail == guestEmail && n.sentDate != null)
                .ToList();

            foreach (var notification in existingNotifications)
            {
                if (!icsData.Any(i => i.contentId == notification.contentId && i.timeStart == notification.dateStart && i.timeEnd == notification.dateEnd))
                {
                    notification.whenToSend = DateTime.UtcNow.AddMinutes(contentStatus == "READY" ? 0 : notificationIntervalPostTransition);
                    notification.sendOperation = "DELETE";
                    model.Entry(notification).State = EntityState.Modified;
                }
                else if ((sendRightAway || updatedFields == "location;" || contentStatus == "READY") &&
                    icsData.Any(i => i.contentId == notification.contentId && i.timeStart == notification.dateStart && i.timeEnd == notification.dateEnd))
                {
                    notification.whenToSend = DateTime.UtcNow.AddMinutes(contentStatus == "READY" ? 0 : notificationIntervalPostTransition);
                    model.Entry(notification).State = EntityState.Modified;
                    IcsData icsRemove = icsData.Where(i => i.contentId == notification.contentId && i.timeStart == notification.dateStart && i.timeEnd == notification.dateEnd).FirstOrDefault();
                    icsData.Remove(icsRemove);
                }
                else if (sendRightAway)
                {
                    model.Entry(notification).State = EntityState.Deleted;
                }
            }

            model.SaveChanges();
        }

        private void UpdateExistingNotifications(
            AtlasModelCore model,
            string guestEmail,
            Guid? contentAgendaDeleteId,
            List<MeetingAgendaItem> agendas,
            List<IcsData> icsData,
            string contentStatus,
            bool sendRightAway,
            string updatedFields)
        {
            List<Guid> agendaIds = agendas.Select(a => a.contentUuid).ToList();

            if (contentAgendaDeleteId != null)
            {
                agendaIds.Add(contentAgendaDeleteId.Value);
            }

            var existingNotifications = model.ContentGuestNotification
                .Where(n => agendaIds.Contains(n.contentUuid) && n.guestEmail == guestEmail && n.sentDate != null)
                .ToList();

            foreach (var notification in existingNotifications)
            {
                if (!icsData.Any(i => i.contentId == notification.contentId && i.timeStart == notification.dateStart && i.timeEnd == notification.dateEnd))
                {
                    notification.whenToSend = DateTime.UtcNow.AddMinutes(contentStatus == "READY" ? 0 : notificationIntervalPostTransition);
                    notification.sendOperation = "DELETE";
                    model.Entry(notification).State = EntityState.Modified;
                }
                else if ((sendRightAway || updatedFields == "location;" || contentStatus == "READY") &&
                    icsData.Any(i => i.contentId == notification.contentId && i.timeStart == notification.dateStart && i.timeEnd == notification.dateEnd))
                {
                    notification.whenToSend = DateTime.UtcNow.AddMinutes(contentStatus == "READY" ? 0 : notificationIntervalPostTransition);
                    model.Entry(notification).State = EntityState.Modified;
                    IcsData icsRemove = icsData.Where(i => i.contentId == notification.contentId && i.timeStart == notification.dateStart && i.timeEnd == notification.dateEnd).FirstOrDefault();
                    icsData.Remove(icsRemove);
                }
                else if (sendRightAway)
                {
                    model.Entry(notification).State = EntityState.Deleted;
                }
            }

            model.SaveChanges();
        }

        private void CreateNewNotifications(
            AtlasModelCore model,
            string guestEmail,
            List<MeetingAgendaItem> agendas,
            List<IcsData> icsData,
            string contentStatus,
            bool sendRightAway,
            string scenario = null)
        {
            foreach (var ics in icsData)
            {
                if (!model.ContentGuestNotification.Any(n =>
                    n.contentId == ics.contentId &&
                    n.guestEmail == guestEmail &&
                    n.dateStart == ics.timeStart &&
                    n.dateEnd == ics.timeEnd &&
                    n.sendOperation == "ADD"))
                {
                    var notification = new ContentGuestNotification
                    {
                        contentId = ics.contentId,
                        guestEmail = guestEmail,
                        dateStart = ics.timeStart,
                        dateEnd = ics.timeEnd,
                        icsKey = Guid.NewGuid(),
                        whenToSend = DateTime.UtcNow.AddMinutes(sendRightAway ? 0 : (contentStatus == "READY" ? 5 : notificationIntervalPostTransition)),
                        sendOperation = "ADD"
                    };

                    model.ContentGuestNotification.Add(notification);
                    model.SaveChanges();

                    foreach (var guestId in ics.guestList)
                    {
                        var guest = model.ContentGuest.Find(guestId);
                        guest.contentGuestNotificationId = notification.contentGuestNotificationId;
                        model.Entry(guest).State = EntityState.Modified;
                    }

                    model.SaveChanges();
                }
            }
        }

        public class IcsData
        {
            public DateTime timeStart { get; set; }
            public DateTime timeEnd { get; set; }
            public List<int> guestList { get; set; }
            public int contentId { get; set; }
        }

        [Obsolete("Must use the version informing a ContentUuid instead.")]
        private void RemoveScheduleNotificationAgendaMarco(int[] contentAgendaId, string guestMail = null, int? contentGuestNotificationId = null)
        {
            AtlasModelCore _md = new AtlasModelCore();

            foreach (var cgm in _md.ContentGuestNotification.Where(p => contentAgendaId.Contains(p.contentId) &&
                (!contentGuestNotificationId.HasValue || (contentGuestNotificationId.HasValue && p.contentGuestNotificationId == contentGuestNotificationId)) &&
                (String.IsNullOrEmpty(guestMail) || (!String.IsNullOrEmpty(guestMail) && p.guestEmail == guestMail))).ToList())
            {
                if (cgm.sentDate == null)
                {
                    _md.Entry(cgm).State = EntityState.Deleted;
                }
                else
                {
                    cgm.sendOperation = "DELETE";
                    cgm.whenToSend = DateTime.UtcNow.AddMinutes(-1);
                    _md.Entry(cgm).State = EntityState.Modified;
                }

                _md.SaveChanges();
            }
        }

        private void RemoveScheduleNotificationAgendaMarco(Guid[] contentAgendaId, string guestMail = null, int? contentGuestNotificationId = null)
        {
            AtlasModelCore _md = new AtlasModelCore();

            foreach (var cgm in _md.ContentGuestNotification.Where(p => contentAgendaId.Contains(p.contentUuid) &&
                (!contentGuestNotificationId.HasValue || (contentGuestNotificationId.HasValue && p.contentGuestNotificationId == contentGuestNotificationId)) &&
                (String.IsNullOrEmpty(guestMail) || (!String.IsNullOrEmpty(guestMail) && p.guestEmail == guestMail))).ToList())
            {
                if (cgm.sentDate == null)
                {
                    _md.Entry(cgm).State = EntityState.Deleted;
                }
                else
                {
                    cgm.sendOperation = "DELETE";
                    cgm.whenToSend = DateTime.UtcNow.AddMinutes(-1);
                    _md.Entry(cgm).State = EntityState.Modified;
                }

                _md.SaveChanges();
            }
        }
        #endregion

        [Obsolete("Must use the version informing a ContentUuid instead.")]
        public void RemoveScheduleNotification(int contentId, string guestMail = null, int? contentGuestNotificationId = null)
        {
            AtlasModelCore _md = new AtlasModelCore();

            foreach (var cgm in _md.ContentGuestNotification.Where(p => p.contentId == contentId &&
                (!contentGuestNotificationId.HasValue || (contentGuestNotificationId.HasValue && p.contentGuestNotificationId == contentGuestNotificationId)) &&
                (String.IsNullOrEmpty(guestMail) || (!String.IsNullOrEmpty(guestMail) && p.guestEmail == guestMail))).ToList())
            {
                if (cgm.sentDate == null)
                {
                    _md.Entry(cgm).State = Microsoft.EntityFrameworkCore.EntityState.Deleted;
                }
                else
                {
                    cgm.sendOperation = "DELETE";
                    cgm.whenToSend = DateTime.UtcNow.AddMinutes(-1);
                    _md.Entry(cgm).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
                }

                _md.SaveChanges();
            }
        }

        public void RemoveScheduleNotification(Guid contentUuid, string guestMail = null, int? contentGuestNotificationId = null)
        {
            AtlasModelCore _md = new AtlasModelCore();

            foreach (var cgm in _md.ContentGuestNotification.Where(p => p.contentUuid == contentUuid &&
                (!contentGuestNotificationId.HasValue || (contentGuestNotificationId.HasValue && p.contentGuestNotificationId == contentGuestNotificationId)) &&
                (String.IsNullOrEmpty(guestMail) || (!String.IsNullOrEmpty(guestMail) && p.guestEmail == guestMail))).ToList())
            {
                if (cgm.sentDate == null)
                {
                    _md.Entry(cgm).State = Microsoft.EntityFrameworkCore.EntityState.Deleted;
                }
                else
                {
                    cgm.sendOperation = "DELETE";
                    cgm.whenToSend = DateTime.UtcNow.AddMinutes(-1);
                    _md.Entry(cgm).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
                }

                _md.SaveChanges();
            }
        }

        public async Task<Tuple<bool, string>> ResendIcsToAgendaPresenters(int contentId, IEnumerable<int> sendTo)
        {
            if (sendTo == null || !sendTo.Any())
                return new Tuple<bool, string>(false, "INVALID_GUESTS_LIST");

            var contentMeeting = await GetContentMeetingAndGuests(contentId);
            var isValid = ResendGuestIcsMeetingValidations(contentMeeting);

            if (!isValid.Item1)
                return new Tuple<bool, string>(false, isValid.Item2);

            List<MeetingAgendaItem> agendas = await GetAgendas(contentId);

            if (!agendas.Any())
                return new Tuple<bool, string>(false, "INVALID_AGENDA");

            sendTo = sendTo.Distinct();
            var sendToAgendas = agendas.Where(agenda =>
                                                agenda.guestNotification == true &&
                                                sendTo.Contains(agenda.contentId) &&
                                                !string.IsNullOrWhiteSpace(agenda.guestMail));

            if (!sendToAgendas.Any())
                return new Tuple<bool, string>(false, "INVALID_GUESTS_LIST");

            var result = ComputeAgendaPresentersActivity(contentId, sendToAgendas);

            if (!result)
            {
                return new Tuple<bool, string>(false, "FAILED_RESEND_ICS");
            }

            return new Tuple<bool, string>(true, "SUCCESS");
        }

        public async Task<Tuple<bool, string>> ResendIcsToGuests(int contentId, IEnumerable<int> sendTo)
        {
            if (sendTo == null || !sendTo.Any())
                return new Tuple<bool, string>(false, "INVALID_GUESTS_LIST");

            var contentMeeting = await GetContentMeetingAndGuests(contentId);
            var isValid = ResendGuestIcsMeetingValidations(contentMeeting);

            if (!isValid.Item1)
                return new Tuple<bool, string>(false, isValid.Item2);

            sendTo = sendTo.Distinct();

            var meetingGuests = contentMeeting.ContentGuest.ToList();
            meetingGuests = meetingGuests.Where(cg => sendTo.Contains(cg.contentGuestId)).ToList();

            var contentAgendas = await GetContentAgendas(contentId);

            List<ContentGuest> agendaGuests = new List<ContentGuest>();
            PrepareAgendasGuests(contentAgendas, agendaGuests, sendTo);

            if (!meetingGuests.Any() && !agendaGuests.Any())
                return new Tuple<bool, string>(false, "INVALID_GUESTS_LIST");

            meetingGuests = meetingGuests
                .Where(g => !string.IsNullOrWhiteSpace(g.guestMail))
                .Distinct()
                .ToList();

            var meetingGuestsEmail = meetingGuests.ToDictionary(guest => guest.guestMail, guest => guest.guestName);

            if (meetingGuestsEmail.Any())
            {
                ResendIcsToMeetingGuests(contentId, meetingGuestsEmail);
            }


            if (agendaGuests.Any())
            {
                var guestsId = agendaGuests.Select(guest => guest.contentGuestId);

                if (!ResendIcsToAgendaGuests(contentMeeting, guestsId))
                    return new Tuple<bool, string>(false, "INVALID_OPERATION");

                ContentActivityService contentActivityService = new ContentActivityService(_currentUser);

                contentActivityService.Add(contentId, new ContentActivity()
                {
                    date = DateTime.UtcNow,
                    type = "RESEND_ICS_AGENDA_GUESTS",
                    activityUser = _currentUser,
                    subItemType = SubItems.GUEST,
                    contentData = Newtonsoft.Json.JsonConvert.SerializeObject(guestsId),
                    processed = true
                });
            }

            return new Tuple<bool, string>(true, "SUCCESS");
        }

        public bool ResendIcsToAgendaGuests(Content content, IEnumerable<int> sendTo)
        {
            MailNotificationHelper mailNotificationHelper = new MailNotificationHelper();

            try
            {
                if (mailNotificationHelper.ValidateNewICSRule(content.createDate))
                {
                    LoadContentGuestNotificationAgendaMarco(content.contentId, sendTo: sendTo);
                }
                else
                {
                    LoadContentGuestNotification(content.contentId, sendTo: sendTo);
                }

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public void ResendIcsToMeetingGuests(int contentId, Dictionary<string, string> sendTo)
        {
            ContentActivityService contentActivityService = new ContentActivityService(_currentUser);

            contentActivityService.Add(contentId, new ContentActivity()
            {
                date = DateTime.UtcNow,
                type = "RESEND_ICS_MEETING_GUESTS",
                activityUser = _currentUser,
                subItemType = SubItems.GUEST,
                contentData = Newtonsoft.Json.JsonConvert.SerializeObject(sendTo),
                processed = false
            });
        }

        public bool ComputeAgendaPresentersActivity(int contentId, IEnumerable<MeetingAgendaItem> agendas)
        {
            var since = DateTime.UtcNow.AddHours(-4);
            var contentIds = agendas.Select(a => a.contentId);

            var sendTo = agendas
                .GroupBy(a => a.guestMail, (iGroupKey, agenda) => agenda.First())
                .ToDictionary(agenda => agenda.guestMail, agenda => agenda.guestName);

            var contentActivityList = _model.ContentActivity
                .Where(ca => !ca.processed &&
                    ca.date > since &&
                    (ca.type == "MEETING_GUEST_ADD" || ca.type == "MEETING_UPDATED_GUESTS" || ca.type == Operations.UPDATED_AGENDA_PRESENTER_GUEST) &&
                    contentIds.Contains(ca.contentId))
                .ToArray();

            var guestEmails = sendTo.Keys;

            short total = 0;
            foreach (var activity in contentActivityList)
            {
                if (!guestEmails.Any(email => activity.contentData.Contains(email)))
                {
                    continue;
                }

                activity.processed = true;
                // activity.processedDate = DateTime.UtcNow;
                total++;
            }

            if (total > 0 && _model.SaveChanges() == 0)
            {
                // Don't continue as it is extremely important to update previous related
                // content activities.
                return false;
            }

            foreach (var agenda in agendas)
            {
                var sendToGuest = new Dictionary<string, string> { { agenda.guestMail, agenda.guestName } };
                new ContentActivityService(_currentUser).Add(contentId, new ContentActivity()
                {
                    date = DateTime.UtcNow,
                    type = "RESEND_ICS_AGENDA_PRESENTERS",
                    activityUser = _currentUser,
                    subItemType = SubItems.GUEST,
                    contentData = Newtonsoft.Json.JsonConvert.SerializeObject(sendTo),
                    processed = false
                });
            }

            return true;
        }

        private Tuple<bool, string> ResendGuestIcsMeetingValidations(Content content)
        {
            if (content == null)
                return new Tuple<bool, string>(false, "CONTENT_NOT_FOUND");

            if (!content.ContentOwner.Select(owner => owner.userId).Contains(_currentUser))
                return new Tuple<bool, string>(false, "INVALID_GRANT");

            if (content.status != "OPEN" && content.status != "READY")
                return new Tuple<bool, string>(false, "INVALID_MEETING");

            var meeting = content.Meeting.FirstOrDefault();

            if (meeting == null)
                return new Tuple<bool, string>(false, "MEETING_NOT_FOUND");

            var isPastMeeting = meeting.date <= DateTime.UtcNow;

            if (isPastMeeting)
                return new Tuple<bool, string>(false, "INVALID_MEETING");

            bool sendICS = meeting.sendICS ?? false;

            if (content.RecurringMeeting != null && content.RecurringMeeting.sendIcs.HasValue)
                sendICS = content.RecurringMeeting.sendIcs.Value;

            if (!sendICS)
                return new Tuple<bool, string>(false, "INVALID_MEETING");

            return new Tuple<bool, string>(true, "");
        }

        private void PrepareAgendasGuests(List<Content> contentAgendas, List<ContentGuest> guestsList, IEnumerable<int> sendTo)
        {
            var agendaGuests = contentAgendas.SelectMany(content => content.ContentGuest);

            var sendToGuests = agendaGuests
                .Where(guest => sendTo.Contains(guest.contentGuestId))
                .GroupBy(g => g.contentGuestId, (iGroupKey, contentGuest) => contentGuest.First());

            guestsList.AddRange(sendToGuests);
        }

        private async Task<Content> GetContentMeetingAndGuests(int contentId)
        {
            try
            {
                var content = await _model.Content
                    .Where(c => c.contentId == contentId)
                    .Include(c => c.ContentPermission)
                    .Include(c => c.ContentOwner)
                    .FirstOrDefaultAsync();

                content.Meeting = await _model.Meeting
                    .Where(meet => meet.contentId == contentId)
                    .Select(meet => new List<Meeting> { meet })
                    .FirstOrDefaultAsync();

                content.RecurringMeeting = await _model.Content
                    .Where(c => c.contentId == contentId)
                    .Include(c => c.RecurringMeeting)
                    .Select(c => c.RecurringMeeting)
                    .FirstOrDefaultAsync();

                content.ContentGuest = await _model.ContentGuest.Where(cg => cg.contentId == contentId).ToListAsync();

                return content;
            }
            catch (Exception)
            {
                return new Content { };
            }
        }

        private async Task<List<Content>> GetContentAgendas(int meetingContentId)
        {
            return await _model.Content
                .Where(content => content.parentContentId == meetingContentId
                    && !(content.deleted ?? false)
                    && content.type == ContentTypes.MeetingAgendaItem)
                //.Include(agenda => agenda.MeetingAgendaItem)
                .Include(agenda => agenda.ContentGuest)
                .ToListAsync();
        }

        private async Task<List<MeetingAgendaItem>> GetAgendas(int meetingContentId)
        {
            var agendasIdList = await _model.Content
                .Where(content => content.parentContentId == meetingContentId
                    && !(content.deleted ?? false)
                    && content.type == ContentTypes.MeetingAgendaItem)
                .Select(content => content.contentId)
                .ToListAsync();

            return await _model.MeetingAgendaItem
                .Where(agenda => agendasIdList.Contains(agenda.contentId)
                      && agenda.agendaItemType == "GUEST"
                      && agenda.guestNotification == true)
                .ToListAsync();
        }

        internal bool hasPendingActivityAgendaPresenter(int agendaContentId, int? meetingContentId, string guestMail)
        {
            var since = DateTime.UtcNow.AddHours(-4);

            var contentActivityList = _model.ContentActivity
                .Where(ca => !ca.processed &&
                    ca.date > since &&
                    (ca.type == "MEETING_GUEST_ADD" && ca.contentId == agendaContentId) ||
                    (ca.type == "MEETING_UPDATED_GUESTS" && ca.contentId == meetingContentId))
                .ToArray();

            return contentActivityList.Any(ca => ca.contentData.Contains(guestMail));
        }
    }
}
