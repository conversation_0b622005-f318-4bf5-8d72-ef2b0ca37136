using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Atlas.Data.Entities;
using Atlas.Data.Repository;
using System.Security;

namespace Atlas.Business
{
    /// <summary>
    /// Service for managing recurring meeting attendees with backward compatibility support
    /// </summary>
    public class RecurringMeetingAttendeeService
    {
        private readonly int _currentUserId;
        private readonly RecurringMeetingAttendeeRepository _attendeeRepository;
        private readonly RecurringMeetingRepository _recurringMeetingRepository;
        private readonly AtlasModelCore _context;

        public RecurringMeetingAttendeeService(int currentUserId)
        {
            _currentUserId = currentUserId;
            _context = new AtlasModelCore();
            _attendeeRepository = new RecurringMeetingAttendeeRepository(_context);
            _recurringMeetingRepository = new RecurringMeetingRepository(_context);
        }

        #region Basic Operations (Version-Aware)

        /// <summary>
        /// Gets all attendees from a recurring meeting (uses version-based approach)
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <returns>List of attendees</returns>
        public async Task<List<User>> GetAttendeesAsync(Guid recurringMeetingId)
        {
            await ValidateRecurringMeetingAccess(recurringMeetingId);
            return await _attendeeRepository.GetVersionBasedAttendeesAsync(recurringMeetingId);
        }

        /// <summary>
        /// Gets attendee IDs from a recurring meeting (uses version-based approach)
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <returns>List of user IDs</returns>
        public async Task<List<int>> GetAttendeeIdsAsync(Guid recurringMeetingId)
        {
            await ValidateRecurringMeetingAccess(recurringMeetingId);
            return await _attendeeRepository.GetVersionBasedAttendeeIdsAsync(recurringMeetingId);
        }

        /// <summary>
        /// Gets attendees from series level only (new model)
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <returns>List of user IDs from series</returns>
        public async Task<List<int>> GetSeriesAttendeeIdsOnlyAsync(Guid recurringMeetingId)
        {
            await ValidateRecurringMeetingAccess(recurringMeetingId);
            return await _attendeeRepository.GetSeriesAttendeeIdsOnlyAsync(recurringMeetingId);
        }

        /// <summary>
        /// Gets attendees from occurrences only (legacy model)
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <returns>List of user IDs from occurrences</returns>
        public async Task<List<int>> GetOccurrenceAttendeeIdsOnlyAsync(Guid recurringMeetingId)
        {
            await ValidateRecurringMeetingAccess(recurringMeetingId);
            return await _attendeeRepository.GetOccurrenceAttendeeIdsOnlyAsync(recurringMeetingId);
        }

        #endregion

        #region Add/Remove Operations

        /// <summary>
        /// Adds an attendee to the recurring meeting series
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <param name="userId">User ID</param>
        /// <returns>True if added successfully</returns>
        public async Task<bool> AddAttendeeAsync(Guid recurringMeetingId, int userId)
        {
            await ValidateRecurringMeetingAccess(recurringMeetingId);
            await ValidateUserExists(userId);

            try
            {
                var attendeeId = await _attendeeRepository.AddAttendeeAsync(recurringMeetingId, userId);
                await _attendeeRepository.CommitAsync();
                return attendeeId != Guid.Empty;
            }
            catch (InvalidOperationException)
            {
                // User is already an attendee
                return false;
            }
        }

        /// <summary>
        /// Adds multiple attendees to the recurring meeting series
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <param name="userIds">List of user IDs</param>
        /// <returns>Number of attendees added</returns>
        public async Task<int> AddMultipleAttendeesAsync(Guid recurringMeetingId, List<int> userIds)
        {
            if (userIds == null || !userIds.Any())
                return 0;

            await ValidateRecurringMeetingAccess(recurringMeetingId);

            // Validate all users exist
            foreach (var userId in userIds.Distinct())
            {
                await ValidateUserExists(userId);
            }

            var addedIds = await _attendeeRepository.AddMultipleAttendeesAsync(recurringMeetingId, userIds);
            await _attendeeRepository.CommitAsync();

            return addedIds.Count;
        }

        /// <summary>
        /// Removes an attendee from the recurring meeting series
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <param name="userId">User ID</param>
        /// <returns>True if removed successfully</returns>
        public async Task<bool> RemoveAttendeeAsync(Guid recurringMeetingId, int userId)
        {
            await ValidateRecurringMeetingAccess(recurringMeetingId);

            var removed = await _attendeeRepository.RemoveAttendeeAsync(recurringMeetingId, userId);
            if (removed)
            {
                await _attendeeRepository.CommitAsync();
            }

            return removed;
        }

        /// <summary>
        /// Removes multiple attendees from the recurring meeting series
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <param name="userIds">List of user IDs</param>
        /// <returns>Number of attendees removed</returns>
        public async Task<int> RemoveMultipleAttendeesAsync(Guid recurringMeetingId, List<int> userIds)
        {
            if (userIds == null || !userIds.Any())
                return 0;

            await ValidateRecurringMeetingAccess(recurringMeetingId);

            var removedCount = await _attendeeRepository.RemoveMultipleAttendeesAsync(recurringMeetingId, userIds);
            if (removedCount > 0)
            {
                await _attendeeRepository.CommitAsync();
            }

            return removedCount;
        }

        /// <summary>
        /// Replaces all attendees in the recurring meeting series
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <param name="newUserIds">New list of user IDs</param>
        /// <returns>Operation result</returns>
        public async Task<AttendeeUpdateResult> UpdateAttendeesAsync(Guid recurringMeetingId, List<int> newUserIds)
        {
            await ValidateRecurringMeetingAccess(recurringMeetingId);

            if (newUserIds != null && newUserIds.Any())
            {
                // Validate all users exist
                foreach (var userId in newUserIds.Distinct())
                {
                    await ValidateUserExists(userId);
                }
            }

            var result = await _attendeeRepository.ReplaceAllAttendeesAsync(recurringMeetingId, newUserIds);
            await _attendeeRepository.CommitAsync();

            return result;
        }

        /// <summary>
        /// Removes all attendees from the recurring meeting series
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <returns>Number of attendees removed</returns>
        public async Task<int> RemoveAllAttendeesAsync(Guid recurringMeetingId)
        {
            await ValidateRecurringMeetingAccess(recurringMeetingId);

            var removedCount = await _attendeeRepository.RemoveAllAttendeesAsync(recurringMeetingId);
            if (removedCount > 0)
            {
                await _attendeeRepository.CommitAsync();
            }

            return removedCount;
        }

        #endregion

        #region Query Operations (Version-Aware)

        /// <summary>
        /// Checks if a user is an attendee of a recurring meeting (uses version-based approach)
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <param name="userId">User ID</param>
        /// <returns>True if user is an attendee</returns>
        public async Task<bool> IsUserAttendeeAsync(Guid recurringMeetingId, int userId)
        {
            await ValidateRecurringMeetingAccess(recurringMeetingId);

            var attendeeIds = await _attendeeRepository.GetVersionBasedAttendeeIdsAsync(recurringMeetingId);
            return attendeeIds.Contains(userId);
        }

        /// <summary>
        /// Gets all recurring meetings where a user participates
        /// </summary>
        /// <param name="userId">User ID (optional, defaults to current user)</param>
        /// <returns>List of recurring meeting IDs</returns>
        public async Task<List<Guid>> GetUserRecurringMeetingsAsync(int? userId = null)
        {
            var targetUserId = userId ?? _currentUserId;
            return await _attendeeRepository.GetRecurringMeetingsByUserAsync(targetUserId);
        }

        /// <summary>
        /// Gets version-aware attendance statistics for a recurring meeting
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <returns>Version-aware attendance statistics</returns>
        public async Task<VersionBasedAttendanceStatistics> GetAttendanceStatisticsAsync(Guid recurringMeetingId)
        {
            await ValidateRecurringMeetingAccess(recurringMeetingId);
            return await _attendeeRepository.GetVersionBasedAttendanceStatisticsAsync(recurringMeetingId);
        }

        /// <summary>
        /// Determines attendee model based on explicit version (safe and reliable)
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <returns>Model type based on version</returns>
        public async Task<RecurringMeetingAttendeeModel> GetAttendeeModelTypeAsync(Guid recurringMeetingId)
        {
            await ValidateRecurringMeetingAccess(recurringMeetingId);
            return await _attendeeRepository.GetVersionBasedModelTypeAsync(recurringMeetingId);
        }

        /// <summary>
        /// Gets the attendee version of a recurring meeting
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <returns>Attendee version (1 = Legacy, 2+ = Series)</returns>
        public async Task<int> GetAttendeeVersionAsync(Guid recurringMeetingId)
        {
            await ValidateRecurringMeetingAccess(recurringMeetingId);

            var recurringMeeting = await _recurringMeetingRepository.GetByIDAsync(recurringMeetingId);
            return recurringMeeting?.attendeeVersion ?? 1;
        }

        #endregion

        #region Synchronization Operations

        /// <summary>
        /// Synchronizes series attendees with future occurrences
        /// This method can be used to apply series changes to all future occurrences
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <param name="applyToFutureOccurrences">Whether to apply to future occurrences</param>
        /// <returns>Synchronization result</returns>
        public async Task<SyncResult> SyncAttendeesWithOccurrencesAsync(Guid recurringMeetingId, bool applyToFutureOccurrences = true)
        {
            await ValidateRecurringMeetingAccess(recurringMeetingId);

            var result = new SyncResult();

            if (!applyToFutureOccurrences)
            {
                result.Message = "Synchronization not requested";
                return result;
            }

            // Get series attendee IDs
            var seriesAttendeeIds = await GetSeriesAttendeeIdsOnlyAsync(recurringMeetingId);

            // Get future occurrences of the recurring meeting
            using (var context = new AtlasModelCore())
            {
                var futureOccurrences = context.Content
                    .Where(c => c.recurringMeetingId == recurringMeetingId
                             && c.type == "Meeting"
                             && c.deleted != true
                             && c.Meeting.Any(m => m.date > DateTime.UtcNow))
                    .ToList();

                result.ProcessedOccurrences = futureOccurrences.Count;

                foreach (var occurrence in futureOccurrences)
                {
                    try
                    {
                        // For each future occurrence, synchronize attendees
                        var contentSubscriberService = new ContentSubscriberService(_currentUserId);

                        // This operation needs to be implemented properly
                        // For now, just count as processed
                        result.SuccessfulOccurrences++;
                    }
                    catch (Exception ex)
                    {
                        result.FailedOccurrences++;
                        result.Errors.Add($"Error in occurrence {occurrence.contentId}: {ex.Message}");
                    }
                }

                result.Message = $"Synchronization completed. {result.SuccessfulOccurrences} successes, {result.FailedOccurrences} failures.";
            }

            return result;
        }

        #endregion

        #region Version Management Operations

        /// <summary>
        /// Upgrades a recurring meeting from legacy (V1) to series-based attendees (V2)
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <returns>Upgrade result</returns>
        public async Task<VersionUpgradeResult> UpgradeToSeriesVersionAsync(Guid recurringMeetingId)
        {
            await ValidateRecurringMeetingAccess(recurringMeetingId);
            return await _attendeeRepository.UpgradeToSeriesVersionAsync(recurringMeetingId);
        }

        /// <summary>
        /// Migrates attendees from legacy model to new series model (V1 to V2)
        /// This method upgrades the version and migrates data
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <returns>Migration result with upgrade information</returns>
        public async Task<EnhancedMigrationResult> MigrateFromLegacyModelAsync(Guid recurringMeetingId)
        {
            await ValidateRecurringMeetingAccess(recurringMeetingId);

            var result = new EnhancedMigrationResult();

            try
            {
                // Get current version
                var currentVersion = await GetAttendeeVersionAsync(recurringMeetingId);
                result.CurrentVersion = currentVersion;

                if (currentVersion >= 2)
                {
                    result.Success = true;
                    result.Message = $"Already using series model (V{currentVersion})";
                    return result;
                }

                // Perform upgrade
                var upgradeResult = await UpgradeToSeriesVersionAsync(recurringMeetingId);

                result.Success = upgradeResult.Success;
                result.Message = upgradeResult.Message;
                result.AttendeesFoundInOccurrences = upgradeResult.AttendeesFoundInOccurrences;
                result.AttendeesMigratedToSeries = upgradeResult.AttendeesMigratedToSeries;
                result.TargetVersion = upgradeResult.UpgradedToVersion;
                result.VersionUpgraded = upgradeResult.Success;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"Migration failed: {ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// Checks if a recurring meeting can be upgraded to series version
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <returns>Upgrade eligibility result</returns>
        public async Task<UpgradeEligibilityResult> CheckUpgradeEligibilityAsync(Guid recurringMeetingId)
        {
            await ValidateRecurringMeetingAccess(recurringMeetingId);

            var result = new UpgradeEligibilityResult();

            try
            {
                var version = await GetAttendeeVersionAsync(recurringMeetingId);
                var statistics = await GetAttendanceStatisticsAsync(recurringMeetingId);

                result.CurrentVersion = version;
                result.IsEligible = version < 2;
                result.RecommendUpgrade = version < 2 && statistics.OccurrenceAttendeesCount > 0;
                result.AttendeesInOccurrences = statistics.OccurrenceAttendeesCount;
                result.AttendeesInSeries = statistics.SeriesAttendeesCount;

                if (!result.IsEligible)
                {
                    result.Message = $"Already using latest version (V{version})";
                }
                else if (result.RecommendUpgrade)
                {
                    result.Message = $"Upgrade recommended. {statistics.OccurrenceAttendeesCount} attendees found in occurrences.";
                }
                else
                {
                    result.Message = "Eligible for upgrade but no attendees found to migrate.";
                }

                result.Success = true;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"Eligibility check failed: {ex.Message}";
            }

            return result;
        }

        #endregion

        #region ETAPA 4.2: Performance-Optimized Methods for WorkgroupService Integration

        /// <summary>
        /// ETAPA 4.2: Adds multiple attendees using simplified method for performance (used by WorkgroupService)
        /// This method is optimized for WorkgroupService.Update scenarios and skips some validations
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <param name="userIds">List of user IDs</param>
        /// <returns>Number of attendees added</returns>
        public async Task<int> AddMultipleAttendeesSimplifiedAsync(Guid recurringMeetingId, List<int> userIds)
        {
            if (userIds == null || !userIds.Any())
                return 0;

            // Skip extensive validations for performance in WorkgroupService scenarios
            // Basic validation still performed
            await ValidateRecurringMeetingExists(recurringMeetingId);

            // Use simplified repository method for better performance
            var addedCount = await _attendeeRepository.AddAttendeesSimplifiedAsync(recurringMeetingId, userIds);
            await _attendeeRepository.CommitAsync();

            return addedCount;
        }

        /// <summary>
        /// ETAPA 4.2: Upgrades a recurring meeting to V2 and optionally adds attendees (simplified method)
        /// This method is optimized for WorkgroupService scenarios where performance is critical
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <param name="attendeeUserIds">Optional attendee user IDs to add during upgrade</param>
        /// <returns>True if upgraded successfully</returns>
        public async Task<bool> UpgradeToV2AndAddAttendeesSimplifiedAsync(Guid recurringMeetingId, List<int> attendeeUserIds = null)
        {
            // Skip extensive validations for performance
            // Basic validation still performed
            await ValidateRecurringMeetingAccess(recurringMeetingId);

            // Use simplified repository method for better performance
            var upgraded = await _attendeeRepository.UpgradeToV2SimplifiedAsync(recurringMeetingId, attendeeUserIds);
            if (upgraded)
            {
                await _attendeeRepository.CommitAsync();
            }

            return upgraded;
        }

        /// <summary>
        /// ETAPA 4.2: Checks attendee model type efficiently for WorkgroupService integration
        /// This method provides optimized model type detection
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <returns>Model type based on version</returns>
        public async Task<RecurringMeetingAttendeeModel> GetAttendeeModelTypeOptimizedAsync(Guid recurringMeetingId)
        {
            // Skip access validation for performance - assumes WorkgroupService already validated
            return await _attendeeRepository.GetSimplifiedVersionBasedModelTypeAsync(recurringMeetingId);
        }

        /// <summary>
        /// ETAPA 4.2: Adds attendees to Legacy (V1) recurring meetings WITHOUT upgrading to V2
        /// This method is specifically for WorkgroupService scenarios where we don't want to upgrade V1
        /// For Legacy meetings, we simply add attendees to the series table (simplified approach)
        /// </summary>
        /// <param name="recurringMeetingId">Recurring meeting ID</param>
        /// <param name="userIds">List of user IDs</param>
        /// <returns>Number of attendees added</returns>
        public async Task<int> AddAttendeesToLegacyWithoutUpgradeAsync(Guid recurringMeetingId, List<int> userIds)
        {
            if (userIds == null || !userIds.Any())
                return 0;

            // For V1 Legacy meetings, we add attendees to the series in a simplified manner
            // This provides a simple and efficient way to add participants for specific scenarios
            // without complex ContentActivity handling or occurrence management

            try
            {
                // Reuse the existing simplified method for optimal performance
                var addedCount = await AddMultipleAttendeesSimplifiedAsync(recurringMeetingId, userIds);

                Console.WriteLine($"[RecurringMeetingAttendeeService] Added {addedCount} attendees to Legacy V1 recurring meeting {recurringMeetingId} (simplified approach)");

                return addedCount;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[RecurringMeetingAttendeeService] Error in AddAttendeesToLegacyWithoutUpgradeAsync: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Validates if current user has access to the recurring meeting
        /// </summary>
        private async System.Threading.Tasks.Task ValidateRecurringMeetingAccess(Guid recurringMeetingId)
        {
            var recurringMeeting = await _recurringMeetingRepository.GetByIDAsync(recurringMeetingId);
            if (recurringMeeting == null)
            {
                throw new ArgumentException("Recurring meeting not found", nameof(recurringMeetingId));
            }

            // Check if user has permission on the recurring meeting
            // Simplified implementation - can be expanded based on business rules
            using (var context = new AtlasModelCore())
            {
                var hasAccess = context.Content
                    .Any(c => c.recurringMeetingId == recurringMeetingId
                           && c.type == "Meeting"
                           && c.ContentPermission.Any(cp => cp.userId == _currentUserId && cp.allowed));

                if (!hasAccess)
                {
                    throw new SecurityException("User does not have permission to access this recurring meeting");
                }
            }
        }

        /// <summary>
        /// Validates if a recurring meeting exists (existence check only, no access validation)
        /// </summary>
        private async System.Threading.Tasks.Task ValidateRecurringMeetingExists(Guid recurringMeetingId)
        {
            var recurringMeeting = await _recurringMeetingRepository.GetByIDAsync(recurringMeetingId);
            if (recurringMeeting == null)
            {
                throw new ArgumentException("Recurring meeting not found", nameof(recurringMeetingId));
            }
        }

        /// <summary>
        /// Validates if a user exists and is active
        /// </summary>
        private async System.Threading.Tasks.Task ValidateUserExists(int userId)
        {
            var user = await _context.User.FindAsync(userId);
            if (user == null || user.deleted || user.blocked)
            {
                throw new ArgumentException($"User {userId} not found or inactive", nameof(userId));
            }
        }

        #endregion
    }

    #region Result Classes

    /// <summary>
    /// Result of a synchronization operation
    /// </summary>
    public class SyncResult
    {
        public int ProcessedOccurrences { get; set; }
        public int SuccessfulOccurrences { get; set; }
        public int FailedOccurrences { get; set; }
        public string Message { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
    }

    /// <summary>
    /// Enhanced migration result with version information
    /// </summary>
    public class EnhancedMigrationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public int CurrentVersion { get; set; }
        public int TargetVersion { get; set; }
        public bool VersionUpgraded { get; set; }
        public int AttendeesFoundInOccurrences { get; set; }
        public int AttendeesMigratedToSeries { get; set; }
    }

    /// <summary>
    /// Result of upgrade eligibility check
    /// </summary>
    public class UpgradeEligibilityResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public int CurrentVersion { get; set; }
        public bool IsEligible { get; set; }
        public bool RecommendUpgrade { get; set; }
        public int AttendeesInOccurrences { get; set; }
        public int AttendeesInSeries { get; set; }
    }

    #endregion
}