using Atlas.Business.ViewModels;
using Atlas.CrossCutting.AppEnums;
using Atlas.Data.Entities;
using Atlas.Data.Repository;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security;
using System.Threading.Tasks;


namespace Atlas.Business
{
    public class ContentPermissionService
    {
        private AtlasModelCore _md;
        private IDbContextTransaction _transaction;
        ContentRepository _repo;
        ContentOwnerService _svcContentOwner;
        ContentSubscriberService _svcContentSubscriber;

        int _currentUser;
        int content_id;
        private Guid _contentUuId;
        private string _userAgent;
        public ContentPermissionService(int userId, int _content_id, string userAgent)
        {
            _repo = new ContentRepository(userId);

            _currentUser = userId;
            content_id = _content_id;
            _userAgent = userAgent;
        }

        public ContentPermissionService(int userId)
        {
            _repo = new ContentRepository(userId);
            _currentUser = userId;
        }

        public ContentPermissionService(int userId, AtlasModelCore _md, IDbContextTransaction _transaction)
        {
            this._md = _md;
            this._transaction = _transaction;
            _repo = new ContentRepository(userId, _md, _transaction);

            _currentUser = userId;
        }

        public ContentPermissionService(int userId, int _content_id, AtlasModelCore _md, IDbContextTransaction _transaction)
        {
            this._md = _md;
            this._transaction = _transaction;
            _repo = new ContentRepository(userId, _md, _transaction);

            _currentUser = userId;
            content_id = _content_id;
        }

        public void SetContentUuId(Guid contentUuid)
        {
            _contentUuId = contentUuid;
        }

        public void SetUserAgent(string userAgent)
        {

        }

        // REVISAR
        [Obsolete("This method has been deprecated.")]
        public bool Delete(int user_id)
        {
            //check permissions
            if (!_repo.CheckPermissionsForWorkgroup(Operations.PERMISSIONS_DELETE, content_id))
            {
                //todo: logar tentativa de acesso
                throw new SecurityException("Unauthorized attempt to delete content in workgroup.");
            }

            if (_repo.Delete_Permission(content_id, user_id) > 0)
            {

                new ContentActivityService(_currentUser).Add(content_id, new ContentActivity()
                {
                    contentId = content_id,
                    date = DateTime.UtcNow,
                    processed = false,
                    type = Operations.PERMISSIONS_DELETE,
                    activityUser = _currentUser,
                    contentData = JsonConvert.SerializeObject(new { type = SubItems.PERMISSIONS, id = user_id }),
                    subItemId = user_id,
                    subItemType = SubItems.PERMISSIONS,
                    hidden = true
                });

                return true;
            }
            else
            {
                return false;
            }
        }


        public async Task<int> Update(List<PermissionListViewModel> list, bool grantedByAdmin = false)
        {
            var users_add = new List<ContentPermission>();
            var users_remove = new List<ContentPermission>();

            Content _currentContent = null;

            bool shouldManageTransaction = _transaction == null && !grantedByAdmin;
            IDbContextTransaction localTransaction = null;
            var contentActivityService = new ContentActivityService(_currentUser);

            try
            {
                if (shouldManageTransaction)
                {
                    if (_md == null)
                        _md = new AtlasModelCore();

                    localTransaction = await _md.Database.BeginTransactionAsync();
                    _transaction = localTransaction;

                    contentActivityService = new ContentActivityService(_currentUser, _md, _transaction);

                    _repo = new ContentRepository(_currentUser, _md, _transaction);
                }

                if (grantedByAdmin)
                {
                    var _currentPermissions = _repo.List_Permissions(content_id);

                    foreach (var item in list)
                    {
                        // determine if the item should be added or removed of the current permissions list
                        if (!_currentPermissions.Select(o => o.userId).Contains(item.ContentPermission.userId))
                        {
                            if (item.Checked) //if not exists and its CHECKED... Add it
                            {
                                item.ContentPermission.allowed = true;
                                users_add.Add(item.ContentPermission);
                            }
                        }
                    }
                }
                else
                {
                    _currentContent = await new ContentService(_currentUser).Get(_contentUuId);
                    await this.ValidateContent(_currentContent);

                    bool isPoll = _currentContent.type == ContentTypes.Poll;
                    bool isTask = _currentContent.type == ContentTypes.Task;
                    bool isKB = _currentContent.type == ContentTypes.KnowledgeDirectory;
                    bool isMeetingAgenda = _currentContent.type == ContentTypes.MeetingAgendaItem;
                    bool isMeeting = _currentContent.type == ContentTypes.Meeting;
                    bool isPastMeeting = _currentContent.Meeting.FirstOrDefault()?.date < DateTime.UtcNow;

                    var _currentPermissions = _currentContent.ContentPermission;

                    if (!_currentPermissions.Any())
                        _currentPermissions = await _repo.List_Permissions(_contentUuId);


                    var original_usersID_permissions = _currentPermissions.Select(o => o.userId).ToArray();
                    var permissions_to_remove = list.FindAll(u => original_usersID_permissions.Contains(u.ContentPermission.userId) && !u.Checked);
                    var permissions_to_add = list.FindAll(u => !original_usersID_permissions.Contains(u.ContentPermission.userId) && u.Checked);

                    permissions_to_add.ForEach(p => p.ContentPermission.allowed = true);

                    List<User> _wkg_users_allowed = await new WorkgroupService(_currentUser).GetUsers(_currentContent.workgroupId);

                    this.ValidateUsers(_wkg_users_allowed.Select(u => u.userId), permissions_to_add);

                    if (isMeetingAgenda)
                    {
                        MeetingMinute minute = _currentContent.Meeting.FirstOrDefault().MeetingMinute.FirstOrDefault(o => o.published == true);

                        if (minute != null)
                            await this.CheckIfUserIsNotASigner(minute, permissions_to_remove);
                    }

                    if (isTask && _currentContent.parentContentId.HasValue)
                    {
                        ICollection<ContentOwner> _meeting_owners = new ContentOwnerRepository(_currentUser, _currentContent.parentContentId.Value).GetCurrentOwners();

                        if (list.Any(u => !u.Checked && _meeting_owners.Any(o => o.userId == u.ContentPermission.userId)))
                            throw new InvalidOperationException("MEMBER_CANNOT_BE_REMOVED");
                    }


                    bool shouldRemoveSubscribers = true;
                    bool shouldAddSubscribers = true;

                    shouldRemoveSubscribers = !(isPoll || isKB || (isMeeting && isPastMeeting));
                    shouldAddSubscribers = !(isPoll || isTask || isKB);

                    if (shouldRemoveSubscribers)
                        await this.RemoveSubscribers(permissions_to_remove);

                    if (shouldAddSubscribers)
                        this.AddSubscribers(_currentContent, permissions_to_add);

                    if (!isKB)
                        await this.RemoveOwners(_currentContent, permissions_to_remove);

                    // double-check: talvez a gente consiga utilizar as permissions diretamente (verificar isso)
                    users_add = permissions_to_add.Select(p => p.ContentPermission).ToList();
                    users_remove = permissions_to_remove.Select(p => p.ContentPermission).ToList();
                }

                int permissionsUpdated = await _repo.Set_Permission(_contentUuId, _currentContent.contentId, users_add, users_remove);
                if (permissionsUpdated == 0)
                {
                    if (shouldManageTransaction)
                    {
                        await localTransaction.RollbackAsync();
                        return permissionsUpdated;
                    }
                }

                if (_currentContent == null)
                {
                    //important: do not use content repository get when grantedByAdmin==true due to transaction issues risk
                    _currentContent = await _md.Content.Where(o => o.contentId == content_id).Include(o => o.Child_Content).FirstOrDefaultAsync();
                }

                var updated_childcontent_users_summary = new List<ContentPermission>();

                if (_currentContent.Child_Content.Any())
                {
                    updated_childcontent_users_summary = this.ManageChildPermissions(_currentContent, _currentContent.contentId, users_add, users_remove);
                }

                if (!grantedByAdmin)
                {
                    if (_currentContent.parentContentId.HasValue)
                    {
                        //important: do not use content repository get when grantedByAdmin==true due to transaction issues risk
                        var _currentContent_Parent = await _repo.Get(_currentContent.parentContentUuid.Value);
                        this.ManageMinutePermission(_currentContent, users_add, users_remove, _currentContent_Parent);
                    }
                    this.RegisterAuditLog(_currentContent.contentId, _contentUuId, users_add, users_remove, contentActivityService, updated_childcontent_users_summary);
                }

                // Confirma a transação se gerenciada localmente
                if (shouldManageTransaction)
                {
                    await _md.SaveChangesAsync();
                    await localTransaction.CommitAsync();
                }

                return permissionsUpdated;
            }
            catch (Exception)
            {
                // Reverte a transação em caso de erro
                if (shouldManageTransaction && localTransaction != null)
                {
                    await localTransaction.RollbackAsync();
                }
                throw;
            }
            finally
            {
                // Limpa a transação local
                if (shouldManageTransaction)
                {
                    localTransaction?.Dispose();
                    if (_md != null)
                    {
                        _md.Dispose();
                    }
                }
            }
        }

        [Obsolete]
        public void RegisterAuditLog(int content_id, List<ContentPermission> users_add, List<ContentPermission> users_remove, List<ContentPermission> updated_childcontent_users_summary = null)
        {
            var contentActivity = new ContentActivityService(_currentUser);
            #region Create AuditLog for the main/parent Content
            var originalContentActivityId = contentActivity.Add(content_id, new ContentActivity()
            {
                contentId = content_id,
                date = DateTime.UtcNow,
                processed = false,
                type = Operations.PERMISSIONS_UPDATE,
                activityUser = _currentUser,
                contentData = JsonConvert.SerializeObject(new
                {
                    type = SubItems.PERMISSIONS,
                    added = users_add.Select(x => new { x.User.userId, x.User.name }),
                    removed = users_remove.Select(x => new { x.User.userId, x.User.name })
                }),
                hidden = true,
                subItemType = SubItems.PERMISSIONS
            });

            foreach (var item in users_add)
            {
                contentActivity.Add(content_id, new ContentActivity()
                {
                    contentId = content_id,
                    date = DateTime.UtcNow,
                    processed = false,
                    type = "PERMISSIONS_ADDED",
                    activityUser = _currentUser,
                    hidden = true,
                    subItemType = "USER",
                    subItemId = item.userId,
                    originalContentActivityId = originalContentActivityId
                });
            }
            foreach (var item in users_remove)
            {
                contentActivity.Add(content_id, new ContentActivity()
                {
                    contentId = content_id,
                    date = DateTime.UtcNow,
                    processed = false,
                    type = "PERMISSIONS_REMOVED",
                    activityUser = _currentUser,
                    hidden = true,
                    subItemType = "USER",
                    subItemId = item.userId,
                    originalContentActivityId = originalContentActivityId
                });
            }
            #endregion

            if (updated_childcontent_users_summary is null)
                return;

            #region Create AuditLog for the child Contents in the contentTree

            var grouped_contents = updated_childcontent_users_summary.GroupBy(o => o.contentId);

            foreach (var content in grouped_contents)
            {
                foreach (var user_perm in content)
                {
                    contentActivity.Add(user_perm.contentId, new ContentActivity()
                    {
                        contentId = user_perm.contentId,
                        date = DateTime.UtcNow,
                        processed = false,
                        type = user_perm.allowed ? "PERMISSIONS_ADDED" : "PERMISSIONS_REMOVED",
                        activityUser = _currentUser,
                        hidden = true,
                        subItemType = "USER",
                        subItemId = user_perm.userId,
                        originalContentActivityId = originalContentActivityId
                    });
                }
            }
            #endregion
        }

        public void RegisterAuditLog(int content_id, Guid contentUuId, List<ContentPermission> users_add, List<ContentPermission> users_remove, ContentActivityService contentActivityService, List<ContentPermission> updated_childcontent_users_summary = null)
        {
            #region Create AuditLog for the main/parent Content
            var originalContentActivityId = contentActivityService.Add(content_id, contentUuId, new ContentActivity()
            {
                contentUuid = contentUuId,
                contentId = content_id,
                date = DateTime.UtcNow,
                processed = false,
                type = Operations.PERMISSIONS_UPDATE,
                activityUser = _currentUser,
                contentData = JsonConvert.SerializeObject(new
                {
                    type = SubItems.PERMISSIONS,
                    contentUuId = contentUuId,
                    added = users_add.Select(x => new { x.User.userId, x.User.name }),
                    removed = users_remove.Select(x => new { x.User.userId, x.User.name })
                }),
                hidden = true,
                subItemType = SubItems.PERMISSIONS
            }, _userAgent);

            foreach (var item in users_add)
            {
                contentActivityService.Add(content_id, contentUuId, new ContentActivity()
                {
                    contentUuid = contentUuId,
                    contentId = content_id,
                    date = DateTime.UtcNow,
                    processed = false,
                    type = "PERMISSIONS_ADDED",
                    activityUser = _currentUser,
                    hidden = true,
                    subItemType = "USER",
                    subItemId = item.userId,
                    originalContentActivityId = originalContentActivityId
                }, _userAgent);
            }
            foreach (var item in users_remove)
            {
                contentActivityService.Add(content_id, contentUuId, new ContentActivity()
                {
                    contentUuid = contentUuId,
                    contentId = content_id,
                    date = DateTime.UtcNow,
                    processed = false,
                    type = "PERMISSIONS_REMOVED",
                    activityUser = _currentUser,
                    hidden = true,
                    subItemType = "USER",
                    subItemId = item.userId,
                    originalContentActivityId = originalContentActivityId
                }, _userAgent);
            }
            #endregion

            if (updated_childcontent_users_summary is null || !updated_childcontent_users_summary.Any())
                return;

            #region Create AuditLog for the child Contents in the contentTree

            var grouped_contents = updated_childcontent_users_summary.GroupBy(o => o.contentId);

            foreach (var content in grouped_contents)
            {
                foreach (var user_perm in content)
                {
                    contentActivityService.Add(user_perm.contentId, user_perm.contentUuid, new ContentActivity()
                    {
                        contentId = user_perm.contentId,
                        date = DateTime.UtcNow,
                        processed = false,
                        type = user_perm.allowed ? "PERMISSIONS_ADDED" : "PERMISSIONS_REMOVED",
                        activityUser = _currentUser,
                        hidden = true,
                        subItemType = "USER",
                        subItemId = user_perm.userId,
                        originalContentActivityId = originalContentActivityId
                    }, _userAgent);
                }
            }
            #endregion
        }

        private List<ContentPermission> ManageChildPermissions(Content _currentContent, int content_id, List<ContentPermission> users_add, List<ContentPermission> users_remove)
        {
            #region Gestão de child Permissions
            if (_currentContent.type == "Pipeline" || _currentContent.type == "Meeting" || _currentContent.type == "KnowledgeDirectory")
            {
                //todo: ao inves de setar TODO MUNDO ao mesmo tempo, passa 1 por 1
                return _repo.UpdateChildPermissions(_currentContent, users_add.Select(o => o.userId).ToList(), users_remove.Select(o => o.userId).ToList());
            }
            return new List<ContentPermission>();
            #endregion
        }

        private void ManageMinutePermission(Content _currentContent, List<ContentPermission> users_add, List<ContentPermission> users_remove, Content _currentContent_Parent)
        {
            #region Gestão de parent/sibling permissions - Pauta
            //S-29 - Rule to Remove permissions if an user is removed from the agendas
            if (_currentContent.type == ContentTypes.MeetingAgendaItem)
            {

                // Agenda being manipulated
                // a) check if users are being removed
                // b) check if users have access to all agendas
                bool changeMinutePermissions = false;
                var allAgendas = _currentContent_Parent.Child_Content.Where(o => o.type == ContentTypes.MeetingAgendaItem && o.deleted != true).ToList();
                var allAgendas_permissions = new List<ContentPermission>();

                //new list of users that will have access to the minutes
                List<ContentPermission> user_to_add_minute = new List<ContentPermission>();

                if (users_remove.Count() > 0)
                {
                    changeMinutePermissions = true;
                }

                if (users_add.Count() > 0)
                {
                    //get updated permissions to Agendas from the database
                    allAgendas_permissions = _repo.GetAllForContents(users_add.Select(o => o.userId).ToList(), allAgendas.Select(o => o.contentId).ToList());

                    //each users should be checked if has access to all agendas
                    foreach (var _loopUser in users_add)
                    {
                        // all agendas the loop user currently HAS permission
                        var allAgendas_permissions_user = allAgendas_permissions.Where(o => o.userId == _loopUser.userId);

                        //select all agendas remaining agendas the loop user DONT HAVE permissions
                        var agendas_without_permissions = allAgendas.Where(o => !allAgendas_permissions_user.Select(a => a.contentId).Contains(o.contentId));

                        //if = 0, this user have access to all agendas, so it should have access to the minute
                        if (agendas_without_permissions.Count() == 0)
                        {
                            user_to_add_minute.Add(_loopUser);
                        }
                    }
                }

                if (user_to_add_minute.Count() > 0)
                {
                    changeMinutePermissions = true;
                }


                if (changeMinutePermissions)
                {
                    //get all minutes for the content
                    var allminutes = _currentContent_Parent.Child_Content.Where(o => o.type == ContentTypes.MeetingMinute).ToList();

                    foreach (var item in allminutes)
                    {
                        _repo.Set_Permission(item.contentId, user_to_add_minute, users_remove, "PSvc_AllAgendas_GrantMinute");
                        this.RegisterAuditLog(item.contentId, user_to_add_minute, users_remove);
                    }
                }

            }
            #endregion
            #region Gestão de parent/sibling permissions - Poll
            //Atualiza as permissões da ata se o usuário perder a permissão de alguma votação pertecente a reunião
            if (_currentContent.type == ContentTypes.Poll)
            {

                bool changeMinutePermissions = false;
                if (_currentContent_Parent != null)
                {
                    var allPolls = _currentContent_Parent.Child_Content.Where(o => o.type == ContentTypes.Poll && o.deleted != true).ToList();
                    var allPollPermissions = new List<ContentPermission>();

                    //new list of users that will have access to the minutes
                    List<ContentPermission> user_to_add_minute = new List<ContentPermission>();
                    List<ContentPermission> user_to_remove_minute = new List<ContentPermission>();

                    if (users_remove.Any())
                    {
                        var content_agenda_permissions = _currentContent_Parent.Child_Content
                            .Where(c => c.type == ContentTypes.MeetingAgendaItem && c.deleted != true);

                        // currently if the user has permission on all agenda items, we shouldnt remove his permission for the meeting minute
                        if (content_agenda_permissions.Any())
                        {
                            user_to_remove_minute = users_remove.Where(u => !content_agenda_permissions.All
                                    (a => a.ContentPermission.Any(cp => cp.userId == u.userId)))
                                .ToList();
                        }

                        changeMinutePermissions = true;
                    }

                    if (users_add.Count() > 0)
                    {
                        allPollPermissions = _repo.GetAllForContents(users_add.Select(o => o.userId).ToList(), allPolls.Select(o => o.contentId).ToList());


                        foreach (var _loopUser in users_add)
                        {
                            var allPollPermissions_user = allPollPermissions.Where(o => o.userId == _loopUser.userId);

                            var agendas_without_permissions = allPolls.Where(o => !allPollPermissions_user.Select(a => a.contentId).Contains(o.contentId));

                            if (agendas_without_permissions.Count() == 0)
                            {
                                user_to_add_minute.Add(_loopUser);
                            }
                        }
                    }

                    if (user_to_add_minute.Count() > 0)
                    {
                        changeMinutePermissions = true;
                    }

                    //Eu preciso tirar a permissão da pessoa na ata se ela tiver alguma votação sem permissão
                    if (changeMinutePermissions)
                    {
                        //get all minutes for the content
                        var allminutes = _currentContent_Parent.Child_Content.Where(o => o.type == ContentTypes.MeetingMinute).ToList();

                        foreach (var item in allminutes)
                        {
                            _repo.Set_Permission(item.contentId, user_to_add_minute, user_to_remove_minute, "PSvc_AllAgendas_GrantMinute");
                        }
                    }

                }
            }

            #endregion
            #region Gestão de parent/sibling permissions - Task
            //Atualiza as permissões da ata se o usuário perder a permissão de alguma ação pertecente a reunião
            if (_currentContent.type == ContentTypes.Task)
            {

                if (_currentContent.parentContentId != null)
                {
                    bool changeMinutePermissions = false;
                    var allTasks = _currentContent_Parent.Child_Content.Where(o => o.type == ContentTypes.Task && o.deleted != true).ToList();
                    var allTasksPermission = new List<ContentPermission>();

                    //new list of users that will have access to the minutes
                    List<ContentPermission> user_to_add_minute = new List<ContentPermission>();
                    List<ContentPermission> user_to_remove_minute = new List<ContentPermission>();

                    if (users_remove.Any())
                    {
                        var content_agenda_permissions = _currentContent_Parent.Child_Content
                            .Where(c => c.type == ContentTypes.MeetingAgendaItem && c.deleted != true);

                        // currently if the user has permission on all agenda items, we shouldnt remove his permission for the meeting minute
                        if (content_agenda_permissions.Any())
                        {
                            user_to_remove_minute = users_remove.Where(u => !content_agenda_permissions.All
                                    (a => a.ContentPermission.Any(cp => cp.userId == u.userId)))
                                .ToList();
                        }

                        changeMinutePermissions = true;
                    }

                    if (users_add.Count() > 0)
                    {
                        allTasksPermission = _repo.GetAllForContents(users_add.Select(o => o.userId).ToList(), allTasks.Select(o => o.contentId).ToList());

                        foreach (var _loopUser in users_add)
                        {
                            var allTasks_permissions_user = allTasksPermission.Where(o => o.userId == _loopUser.userId);

                            var tasks_without_permissions = allTasks.Where(o => !allTasks_permissions_user.Select(a => a.contentId).Contains(o.contentId));

                            if (tasks_without_permissions.Count() == 0)
                            {
                                user_to_add_minute.Add(_loopUser);
                            }
                        }
                    }

                    //Eu preciso tirar a permissão da pessoa na ata se ela tiver alguma ação sem permissão
                    if (user_to_add_minute.Count() > 0)
                    {
                        changeMinutePermissions = true;
                    }

                    //Eu preciso tirar a permissão da pessoa na ata se ela tiver alguma ação sem permissão
                    if (changeMinutePermissions)
                    {
                        //get all minutes for the content
                        var allminutes = _currentContent_Parent.Child_Content.Where(o => o.type == ContentTypes.MeetingMinute).ToList();

                        foreach (var item in allminutes)
                        {
                            _repo.Set_Permission(item.contentId, user_to_add_minute, user_to_remove_minute, "PSvc_AllAgendas_GrantMinute");
                        }
                    }

                }
            }
            #endregion
        }

        public async Task<int> AddRangeAsync(int contentId, List<ContentPermission> add_users)
        {
            var permissions = _repo.List_Permissions(contentId);

            foreach (var item in add_users)
            {
                if (!permissions.Select(x => x.userId).Contains(item.userId))
                {
                    var y = await this.AddAsync(item.userId);
                }
            }

            return 1;
        }

        [Obsolete("The knowledge directory validation is checked in the Update method.")]
        public void ValidateKnowledgeDirectoryUpdate(Content content, WorkgroupViewModel workgroup)
        {
            // Implements the required validation according to the following scenarios
            // Content/Object validation
            if (content == null || content.type != ContentTypes.KnowledgeDirectory)
            {
                throw new SecurityException("Invalid type or directory not found");
            }

            // 1. Business rules
            if (!workgroup.UAC.kb_update || workgroup.archived)
            {
                throw new SecurityException("User without privileges or workgroup archived");
            }
            // 2. Pentest rules
        }

        [Obsolete("This method has been deprecated.")]
        public async Task<int> AddAsync(int newPermission_userId)
        {
            //check permissions
            if (!_repo.CheckPermissionsForWorkgroup(Operations.PERMISSIONS_ADD, content_id))
            {
                //todo: logar tentativa de acesso
                throw new SecurityException("Unauthorized attempt to add a permission into a content in workgroup.");
            }

            ContentPermission c = new ContentPermission();
            c.contentId = this.content_id;
            c.userId = newPermission_userId;
            c.createUser = _currentUser;
            c.createDate = DateTime.UtcNow;
            c.allowed = true;

            int added_key = 0;
            if (_repo.Add_Permission(this.content_id, c) > 0)
            {

                new ContentActivityService(_currentUser).Add(content_id, new ContentActivity()
                {
                    contentId = content_id,
                    date = DateTime.UtcNow,
                    processed = false,
                    type = Operations.PERMISSIONS_ADD,
                    activityUser = _currentUser,
                    contentData = JsonConvert.SerializeObject(new { type = SubItems.PERMISSIONS, id = c.contentId }),
                    subItemId = added_key,
                    subItemType = SubItems.PERMISSIONS,
                    hidden = true

                });

                return added_key;
            }
            else
            {
                return 0;
            }
        }
        public int Add(int newPermission_userId)
        {
            //check permissions
            if (!_repo.CheckPermissionsForWorkgroup(Operations.PERMISSIONS_ADD, content_id))
            {
                //todo: logar tentativa de acesso
                throw new SecurityException("Unauthorized attempt to add a permission into a content in workgroup.");
            }

            ContentPermission c = new ContentPermission();
            c.contentId = this.content_id;
            c.userId = newPermission_userId;
            c.createUser = _currentUser;
            c.createDate = DateTime.UtcNow;

            int added_key = 0;
            if (_repo.Add_Permission(this.content_id, c) > 0)
            {

                new ContentActivityService(_currentUser).Add(content_id, new ContentActivity()
                {
                    contentId = content_id,
                    date = DateTime.UtcNow,
                    processed = false,
                    type = Operations.PERMISSIONS_ADD,
                    activityUser = _currentUser,
                    contentData = JsonConvert.SerializeObject(new { type = SubItems.PERMISSIONS, id = c.contentId }),
                    subItemId = added_key,
                    subItemType = SubItems.PERMISSIONS,
                    hidden = true

                });

                return added_key;
            }
            else
            {
                return 0;
            }
        }

        public async Task<List<ContentPermission>> GetUsersWithPermissionsInfo()
        {

            ContentService contentService = new ContentService(_currentUser);
            Content content = await contentService.Get(content_id);

            List<ContentPermission> permissions = content.ContentPermission.ToList();

            var permissionCurrentUser = permissions.Where(p => p.userId == _currentUser).FirstOrDefault();
            permissions.Remove(permissionCurrentUser);

            return permissions;

        }

        private async System.Threading.Tasks.Task ValidateContent(Content _currentContent)
        {
            if (!(await _repo.CheckPermissionsForWorkgroup(Operations.PERMISSIONS_UPDATE, _contentUuId)))
                throw new SecurityException("Unauthorized attempt to add comment into a content in workgroup.");


            FeatureManagerService featureManagerService = new FeatureManagerService(_currentUser);
            bool isPermissionManagementEnabled = await featureManagerService.isEnabledByContent(_currentContent, PlanFeatureNames.PERMISSION_MANAGEMENT);

            if (!isPermissionManagementEnabled)
                throw new InvalidOperationException("FEATURE_NOT_INCLUDED");

            if (_currentContent.status == "CLOSED" && _currentContent.type == ContentTypes.Poll)
                throw new SecurityException("Unauthorized attempt to edit permission in a closed poll.");

        }

        private void ValidateUsers(IEnumerable<int> usersID_allowed_in_workgroup, List<PermissionListViewModel> permissions_to_add)
        {
            permissions_to_add.RemoveAll(p => !usersID_allowed_in_workgroup.Contains(p.ContentPermission.userId));
        }

        private async System.Threading.Tasks.Task CheckIfUserIsNotASigner(MeetingMinute minute, List<PermissionListViewModel> permissions_to_remove)
        {
            ContentAttachment minuteAttach = minute.Content.ContentAttachment.FirstOrDefault();
            SignatureContext signatureContext = await new SignatureService(_currentUser).GetSignatureContext(minute.contentId, minuteAttach.contentAttachmentId, false);

            var at_least_one_is_signer = signatureContext != null && signatureContext.signers
                .Any(signer => permissions_to_remove.Exists(per => per.ContentPermission.userId == signer.userId && signer.signDate == null));

            if (signatureContext?.status != "OPEN" || at_least_one_is_signer)
                throw new InvalidOperationException("MEMBER_CANNOT_BE_REMOVED");
        }

        private async System.Threading.Tasks.Task RemoveSubscribers(IEnumerable<PermissionListViewModel> permissions_to_remove)
        {
            List<ContentSubscriber> subscribers_to_remove = permissions_to_remove
                .Select(p => new ContentSubscriber { userId = p.ContentPermission.userId, contentId = p.ContentPermission.contentId }).ToList();

            _svcContentSubscriber = new ContentSubscriberService(_currentUser, content_id);
            _svcContentSubscriber.SetContentUuId(_contentUuId);
            _svcContentSubscriber.SetUserAgent(_userAgent);
            var resultSubscriber = await _svcContentSubscriber.RemoveRangeAsync(_contentUuId, subscribers_to_remove);
        }

        private void AddSubscribers(Content _currentContent, IEnumerable<PermissionListViewModel> permissions_to_add)
        {
            var alreadySubscriberList = _currentContent.ContentSubscriber.Select(o => o.userId);
            var permissions = permissions_to_add.Where(p => !alreadySubscriberList.Contains(p.ContentPermission.userId));

            foreach (var permission in permissions)
            {
                var contentSubscriberService = new ContentSubscriberService(_currentUser, _currentContent.contentId);
                contentSubscriberService.SetContentUuId(_contentUuId);
                contentSubscriberService.SetUserAgent(_userAgent);

                contentSubscriberService.Add(permission.ContentPermission.userId, createInvite: false, grantedByAdmin: false, _currentContent);
            }
        }

        private async System.Threading.Tasks.Task RemoveOwners(Content _currentContent, IEnumerable<PermissionListViewModel> permissions_to_remove)
        {
            var _currentOwners = _currentContent.ContentOwner;

            var owners_to_remove = permissions_to_remove.Select(p => new ContentOwner
            {
                userId = p.ContentPermission.userId,
                contentId = p.ContentPermission.contentId,
                createUser = _currentUser
            }).ToList();

            if (_currentOwners.Count() == 1 && owners_to_remove.Any(owner => owner.userId == _currentOwners.First().userId))
                throw new Exception("ONLY_ONE_OWMER");

            _svcContentOwner = new ContentOwnerService(_currentUser, _userAgent, null);
            _svcContentOwner.SetContentUuId(_contentUuId);
            await _svcContentOwner.RemoveRangeAsync(owners_to_remove);
        }

    }
}
