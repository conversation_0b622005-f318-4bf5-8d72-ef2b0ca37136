using Atlas.Business.Helpers;
using Atlas.Business.Helpers.Queue;
using Atlas.Business.ViewModels.ClientBackup;
using Atlas.Business.ViewModels.KnowledgeBase;
using Atlas.CrossCutting.AppEnums;
using Atlas.CrossCutting.DTO;
using Atlas.CrossCutting.DTO.KnowledgeBase;
using Atlas.CrossCutting.DTO.Workgroup;
using Atlas.CrossCutting.Exceptions;
using Atlas.CrossCutting.Exceptions.ErrorCodes;
using Atlas.CrossCutting.Settings;
using Atlas.Data.Entities;
using Atlas.Data.Repository;
using i18next_net;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Security;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Atlas.Business
{
    public class KnowledgeBaseServiceV2 : KnowledgeBaseService
    {
        protected ClientService _clientService;
        protected User _user;
        protected i18next _i18;
        protected WorkgroupService _workgroupService;
        protected ContentService _contentService;
        protected ContentRepository _contentRepository;
        protected FeatureManagerService _featureManagerService;
        protected StorageSettings _storageSettings;
        private readonly AtlasModelCore _md = new AtlasModelCore();

        static string[] attachmentNotificationTypes = { "EMAIL" };

        public KnowledgeBaseServiceV2(User user) : base(user.userId)
        {
            this._clientService = new ClientService(user.userId);
            this._workgroupService = new WorkgroupService(user.userId);
            this._contentService = new ContentService(user.userId);
            this._contentRepository = new ContentRepository(user.userId);
            this._featureManagerService = new FeatureManagerService(user.userId);
            this._user = user;
        }

        public KnowledgeBaseServiceV2(User user, StorageSettings storageSettings) : base(user.userId)
        {
            this._clientService = new ClientService(user.userId);
            this._workgroupService = new WorkgroupService(user.userId);
            this._contentService = new ContentService(user.userId);
            this._contentRepository = new ContentRepository(user.userId);
            this._featureManagerService = new FeatureManagerService(user.userId);
            this._storageSettings = storageSettings;
            this._user = user;
        }

        public async Task<RootViewModel> GetRoot()
        {
            var clients = await this._clientService.GetAllForProfile();
            var defaultFolders = new List<ItemViewModel>();
            Dictionary<string, ClientViewModel> clientsTree = new Dictionary<string, ClientViewModel>();

            var activeClients = clients.Where(c => !c.blocked && !c.deleted);

            foreach (var c in activeClients)
            {
                var folders = await this.GetDefaultFoldersAsync(c);

                if (folders != null)
                {
                    var clientViewModel = new ClientViewModel()
                    {
                        Id = c.clientId,
                        Name = c.name,
                        Entity = "client",
                        Logo = c.defaultLogo,
                        UAC = new UAC() { IsOwner = false, CanView = true, CanUpdate = false, CanDelete = false, CanRequestBackup = false },
                        Folders = folders.Select(f => f.Guid).Take(3).ToArray(),
                    };

                    clientsTree.Add(clientViewModel.Guid, clientViewModel);
                    defaultFolders.AddRange(folders);
                }
            }

            var clientTreeView = new ClientTreeViewModel() { AllIds = clientsTree.Keys.ToArray(), Data = clientsTree };
            var foldersTreeView = new ItemTreeViewModel(defaultFolders);

            return new RootViewModel { clients = clientTreeView, folders = foldersTreeView };
        }

        private async Task<ItemViewModel[]> GetKnowledgeAncestorFolders(IEnumerable<DirectoryNode> directories, Workgroup workgroup)
        {
            List<ItemViewModel> result = new List<ItemViewModel>();
            bool isOwner = workgroup.UAC.isOwner;
            var isClientAdmin = await new UserRoleService(_currentUser).IsAdminOnClient(workgroup.clientId);
            bool isBlockDownloadAttachmentEnabled = await _featureManagerService.isEnabledByWorkgroup(workgroup.workgroupId, PlanFeatureNames.BLOCK_DOWNLOAD_DOCUMENTS);
            var canDownloadAttachments = isBlockDownloadAttachmentEnabled && workgroup.Client.blockFileDownload ? false : true;

            if (workgroup.type.Equals(WorkgroupTypes.ROOT))
            {
                ItemViewModel previous = null;
                foreach (var directory in directories)
                {
                    var folder = new ItemViewModel()
                    {
                        Entity = "knowledgeDirectory",
                        Id = directory.contentId,
                        contentId = directory.contentId,
                        contentUuid = directory.contentUuid,
                        Name = directory.name,
                        Folders = (previous is null) ? null : new string[] { previous.Guid },
                        UAC = new UAC { CanView = true, CanUpdate = isOwner, CanDelete = isOwner, CanAddFiles = true, CanAddFolder = isOwner, CanRequestBackup = canDownloadAttachments, CanExportToExcel = isClientAdmin },
                        workgroupId = workgroup.workgroupId,
                        ClientId = workgroup.clientId
                    };

                    result.Add(folder);
                    previous = folder;
                }

                if (previous != null)
                {
                    result.Add(new ItemViewModel()
                    {
                        Entity = "workgroupRoot",
                        Id = workgroup.workgroupId,
                        Name = "sharedFolders",
                        Folders = new string[] { previous.Guid },
                        UAC = new UAC { CanView = true, CanUpdate = false, CanDelete = false, CanRequestBackup = canDownloadAttachments, CanExportToExcel = isClientAdmin },
                        workgroupId = workgroup.workgroupId,
                        ClientId = workgroup.clientId
                    });
                }
            }
            else
            {
                ItemViewModel previous = null;
                foreach (var directory in directories)
                {
                    var folder = new ItemViewModel()
                    {
                        Entity = "knowledgeDirectory",
                        Id = directory.contentId,
                        contentId = directory.contentId,
                        contentUuid = directory.contentUuid,
                        Name = directory.name,
                        Folders = (previous is null) ? null : new string[] { previous.Guid },
                        UAC = new UAC { CanView = canDownloadAttachments, CanUpdate = isOwner, CanDelete = isOwner, CanAddFiles = canDownloadAttachments, CanAddFolder = isOwner, CanRequestBackup = canDownloadAttachments, CanExportToExcel = isOwner },
                        workgroupId = workgroup.workgroupId,
                        ClientId = workgroup.clientId
                    };

                    result.Add(folder);
                    previous = folder;
                }

                if (previous != null)
                {
                    var systemFolders = await GetSystemAncestorFolders("knowledgeBase", workgroup);
                    systemFolders.Last().Folders = new string[] { previous.Guid };
                    result.AddRange(systemFolders);
                }
            }

            return result.ToArray();
        }

        private async Task<ItemViewModel[]> GetSystemAncestorFolders(string entity, Content content)
        {
            string folderName;
            var workgroup = await _workgroupService.Get(content.workgroupId);
            bool isBlockDownloadAttachmentEnabled = await _featureManagerService.isEnabledByContent(content, PlanFeatureNames.BLOCK_DOWNLOAD_DOCUMENTS);
            bool canDownloadAttachments = isBlockDownloadAttachmentEnabled && content.Workgroup.Client.blockFileDownload ? false : true;

            if (entity == "workgroupActionItem")
            {
                StringBuilder builder = new StringBuilder();
                builder.Append(content.Task.FirstOrDefault()?.dueDate.ToString("yyyy-MM-dd") ?? "");
                builder.AppendFormat(" {1} - {0}", content.title, content.User_Assigned.name);

                folderName = builder.ToString();
            }
            else
            {
                string meetingDate = content.Meeting.FirstOrDefault()?.date.ToString("yyyy-MM-dd ") ?? "";
                folderName = meetingDate + content.title;
            }

            var systemFolder = new ItemViewModel()
            {
                Entity = entity,
                Id = content.contentId,
                Name = folderName,
                UAC = new UAC { CanView = true, CanUpdate = false, CanDelete = false }
            };
            var parentEntityName = entity.Remove(entity.Length - 4) + 's';
            var parentEntity = entity.Remove(entity.Length - 4) + 's';

            if (entity == "workgroupActionItem")
            {
                if (content.Workgroup.type == "PROJECT")
                {
                    parentEntityName = "projectActions";
                }
                else if (content.Workgroup.type == "DEFAULT")
                {
                    parentEntityName = "boardActions";
                }

                parentEntity = "workgroupActions";
            }

            var parentSystemFolder = new ItemViewModel()
            {
                Entity = parentEntity,
                Id = content.workgroupId,
                Name = parentEntityName,
                Folders = new string[] { systemFolder.Guid },
                UAC = new UAC { CanView = true, CanUpdate = false, CanDelete = false }
            };
            var workgroupFolder = new ItemViewModel()
            {
                Entity = "workgroup",
                Id = content.workgroupId,
                Name = content.Workgroup.name,
                Folders = new string[] { parentSystemFolder.Guid },
                UAC = new UAC { CanView = true, CanUpdate = false, CanDelete = false, CanRequestBackup = canDownloadAttachments, CanExportToExcel = workgroup.UAC.isOwner }
            };

            ItemViewModel[] folders = { workgroupFolder, parentSystemFolder, systemFolder };
            return folders;
        }

        private async Task<ItemViewModel[]> GetSystemAncestorFolders(string entity, Workgroup workgroup)
        {
            bool isBlockDownloadAttachmentEnabled = await _featureManagerService.isEnabledByWorkgroup(workgroup.workgroupId, PlanFeatureNames.BLOCK_DOWNLOAD_DOCUMENTS);
            var canRequestBackup = true;
            var canExportToExcel = entity == "workgroupRoot" ? false : workgroup.UAC.isOwner;
            var name = entity;

            if ((isBlockDownloadAttachmentEnabled && workgroup.Client.blockFileDownload) || (entity == "boards" || entity == "projects"))
            {
                canRequestBackup = false;
            }

            if (entity == "workgroupActions")
            {
                if (workgroup.type == "PROJECT")
                {
                    name = "projectActions";
                }
                else if (workgroup.type == "DEFAULT")
                {
                    name = "boardActions";
                }
            }

            if (name == "knowledgeBase")
            {
                if (workgroup.type == "PROJECT")
                {
                    name = "projectFolders";
                }
                else if (workgroup.type == "DEFAULT")
                {
                    name = "boardFolders";
                }
            }

            var systemFolder = new ItemViewModel()
            {
                Entity = entity,
                Id = workgroup.workgroupId,
                Name = name,
                UAC = new UAC { CanView = true, CanUpdate = false, CanDelete = false, CanRequestBackup = canRequestBackup, CanExportToExcel = canExportToExcel },
                ClientId = workgroup.clientId
            };

            var workgroupFolder = new ItemViewModel()
            {
                Entity = "workgroup",
                Id = workgroup.workgroupId,
                Name = workgroup.name,
                Folders = new string[] { systemFolder.Guid },
                UAC = new UAC { CanView = true, CanUpdate = false, CanDelete = false, CanRequestBackup = canRequestBackup, CanExportToExcel = canExportToExcel },
                ClientId = workgroup.clientId
            };

            ItemViewModel[] folders = { workgroupFolder, systemFolder };
            return folders;
        }

        static bool DecodeFolderKey(string key, out string entity, out int folderId)
        {
            entity = null;
            folderId = 0;
            byte[] textAsBytes = Convert.FromBase64String(key);
            string[] parts = Encoding.ASCII.GetString(textAsBytes).Split('_');

            if (parts.Length >= 2)
            {
                entity = parts[0];
                if (!int.TryParse(parts[1], out folderId))
                {
                    return false;
                }

                return true;
            }

            return false;
        }

        public async Task<bool> RestoreTrashFolder(Guid contentUuid, string userAgent)
        {
            var content = await _contentRepository.Get(contentUuid);

            if (content is null)
            {
                throw new InvalidOperationException("INVALID_FOLDER_ID");
            }
            if (!(content.deleted ?? false))
            {
                throw new InvalidOperationException("FOLDER_NOT_DELETED");
            }
            if (content.type != ContentTypes.KnowledgeDirectory)
            {
                throw new InvalidOperationException("INVALID_FOLDER_CONTENT_TYPE");
            }

            var result = await _contentService.Restore(content, userAgent);
            return result;
        }

        public async Task<bool> NotifyNewAttachment(AttachmentNotificationViewModel viewModel)
        {
            ValidateNotifyNewAttachment(viewModel);

            string type = viewModel.NotificationType;

            var service = new ContentAttachmentService(_currentUser);
            await service.NotifyAttachments(viewModel);
            return true;
        }

        private void ValidateNotifyNewAttachment(AttachmentNotificationViewModel attachmentNotification)
        {
            if (!attachmentNotificationTypes.Contains(attachmentNotification.NotificationType))
            {
                throw new InvalidOperationException("INVALID_NOTIFICATION_TYPE");
            }
        }

        public async Task<ItemTreeViewModel> GetFolders(string key, int clientId)
        {
            if (!DecodeFolderKey(key, out var folderEntity, out var folderId))
            {
                throw new ArgumentException("INVALID_FOLDER_KEY");
            }

            return await GetFolders(folderId, folderEntity, clientId);
        }

        public async Task<ItemTreeViewModel> GetFolders(int id, string entity, int clientId = 0)
        {
            ItemTreeViewModel result = new ItemTreeViewModel();
            switch (entity)
            {
                case "meetingMaterials":
                case "meetingMinutes":
                    {
                        var workgroup = await this._workgroupService.GetWorkgroupAsync(id, false);
                        var foldersMeetings = await this.GetMeetings(workgroup, entity);
                        var folders = foldersMeetings.ToDictionary(f => f.Guid, f => f);
                        var path = await GetSystemAncestorFolders(entity, workgroup);

                        result.AllIds = folders.Keys.ToArray();
                        result.Data = folders;
                        result.Path = new ItemTreeViewModel(path);
                        break;
                    }
                case "meetingMaterialItem":
                case "meetingMinuteItem":
                case "workgroupActionItem":
                    {
                        var content = await _contentService.Get(id);
                        var attachs = await this.GetAttachmentsAsync(content, entity);
                        var fileViewModels = attachs.ToDictionary(f => f.Guid, f => f);
                        var path = await GetSystemAncestorFolders(entity, content);

                        result.AllIds = fileViewModels.Keys.ToArray();
                        result.Data = fileViewModels;
                        result.Path = new ItemTreeViewModel(path);
                        break;
                    }
                case "knowledgeBase":
                case "workgroupRoot":
                    {
                        var workgroup = await _workgroupService.GetWorkgroupAsync(id, false);
                        var folders = await this.GetFoldersInWorkgroup(workgroup, entity, clientId);
                        var foldersViewModels = folders.ToDictionary(f => f.Guid, f => f);

                        result.AllIds = foldersViewModels.Keys.ToArray();
                        result.Data = foldersViewModels;

                        if (entity == "knowledgeBase")
                        {
                            var path = await GetSystemAncestorFolders(entity, workgroup);
                            result.Path = new ItemTreeViewModel(path);
                        }
                        break;
                    }
                case "knowledgeDirectory":
                    {
                        var directory = await _contentService.GetDirectoryAsync(id);
                        var workgroup = await _workgroupService.GetWorkgroupAsync(directory.workgroupId, false);
                        var directoryNodes = _contentService.GetDirectoryAncestors(id);
                        var path = await GetKnowledgeAncestorFolders(directoryNodes, workgroup);

                        result = await this.GetChildsKnowledgeDirectory(id, workgroup, clientId);
                        result.Path = new ItemTreeViewModel(path);
                        break;
                    }
                case "workgroupActions":
                    {
                        var workgroup = await this._workgroupService.GetWorkgroupAsync(id, false);
                        var folders = (await this.GetWorkgroupActions(workgroup)).ToDictionary(f => f.Guid, f => f);

                        var path = await GetSystemAncestorFolders(entity, workgroup);

                        result.AllIds = folders.Keys.ToArray();
                        result.Data = folders;
                        result.Path = new ItemTreeViewModel(path);
                        break;
                    }
                case "workgroup":
                    {
                        var workgroup = await this._workgroupService.GetWorkgroupAsync(id, false);
                        result = await this.GetDefaultFoldersByWorkgroup(id, workgroup.type);
                        break;
                    }
                case "boards":
                    {
                        var boards = await this.GetBoardsAsync(clientId, hideArchived: true);
                        var isClientAdmin = await new UserRoleService(_currentUser).IsAdminOnClient(clientId);

                        var list = new List<ItemViewModel>();

                        list.Add(new ItemViewModel()
                        {
                            Id = clientId,
                            Name = "boards",
                            Entity = "boards",
                            Folders = boards.Select(b => b.Guid).ToArray(),
                            UAC = new UAC() { IsOwner = false, CanView = true, CanUpdate = false, CanDelete = false, CanRequestBackup = false, CanExportToExcel = false },
                        });

                        list.AddRange(boards);

                        var folders = list.ToArray().ToDictionary(f => f.Guid, f => f);

                        result.AllIds = folders.Keys.ToArray();
                        result.Data = folders;

                        break;
                    }
                case "projects":
                    {
                        var projects = await this.GetProjectsAsync(clientId, hideArchived: true);
                        var isClientAdmin = await new UserRoleService(_currentUser).IsAdminOnClient(clientId);

                        var list = new List<ItemViewModel>();

                        list.Add(new ItemViewModel()
                        {
                            Id = clientId,
                            Name = "projects",
                            Entity = "projects",
                            Folders = projects.Select(b => b.Guid).ToArray(),
                            UAC = new UAC() { IsOwner = false, CanView = true, CanUpdate = false, CanDelete = false, CanRequestBackup = false, CanExportToExcel = false },
                        });

                        list.AddRange(projects);

                        var folders = list.ToArray().ToDictionary(f => f.Guid, f => f);

                        result.AllIds = folders.Keys.ToArray();
                        result.Data = folders;

                        break;
                    }
                default:
                    {
                        result = new ItemTreeViewModel(Array.Empty<ItemViewModel>());
                        break;
                    }
            }

            return result;
        }

        private async Task<ItemViewModel[]> GetWorkgroupActions(Workgroup workgroup)
        {
            var actions = await this._contentService.GetAllWorkgroupActions(workgroup.workgroupId);

            var children = actions.Select(a =>
            {
                StringBuilder folderName = new StringBuilder();
                folderName.Append(a.Task.FirstOrDefault()?.dueDate.ToString("yyyy-MM-dd") ?? "");
                folderName.AppendFormat(" {1} - {0}", a.title, a.User_Assigned.name);

                return new ItemViewModel()
                {
                    Id = a.contentId,
                    Name = folderName.ToString(),
                    Entity = "workgroupActionItem",
                    UAC = new UAC() { CanView = true, CanUpdate = false, CanDelete = false },
                    CreatedAt = a.createDate,
                    CreatedBy = a.User_Create.name,
                    UpdatedAt = a.lastUpdate ?? a.createDate,
                    contentId = a.contentId,
                    contentUuid = a.contentUuid,
                    workgroupId = a.workgroupId
                };
            }).OrderBy(p => p.Name).ToArray();

            var folders = new List<ItemViewModel>()
            {
                new ItemViewModel()
                {
                    Entity = "workgroupActions",
                    Id = workgroup.workgroupId,
                    Name = (workgroup.type == "PROJECT" ? "projectActions" : "boardActions"),
                    UAC = new UAC { CanView = true, CanUpdate = false, CanDelete = false },
                    Folders = children.Select(f => f.Guid).ToArray(),
                    workgroupId = workgroup.workgroupId
                }
            };

            folders.AddRange(children);
            return folders.ToArray();
        }

        public async Task<ItemViewModel[]> GetMeetings(Workgroup workgroup, string entity)
        {
            var meetings = this._contentService.GetAll_Meetings(new ContentRequestFilter() { workgroups = new int[] { workgroup.workgroupId } }, this._user, entity);
            var isWorkgroupOwner = workgroup.UAC.isOwner;
            var isDownloadAttachmentsEnabled = await _featureManagerService.isEnabledByWorkgroup(workgroup.workgroupId, PlanFeatureNames.BLOCK_DOWNLOAD_DOCUMENTS);
            var canRequestBackup = isDownloadAttachmentsEnabled && workgroup.Client.blockFileDownload ? false : true;


            var children = meetings.Select(m => new ItemViewModel()
            {
                Id = m.contentId,
                Name = m.date.ToString("yyyy-MM-dd ") + m.title,
                Entity = entity.Remove(entity.Length - 1, 1) + "Item",
                UAC = new UAC() { CanView = true, CanRequestBackup = canRequestBackup, CanExportToExcel = isWorkgroupOwner },
                CreatedAt = m.createDate,
                CreatedBy = m.createUserName,
                UpdatedAt = m.lastUpdate ?? m.createDate,
                TotalFiles = m.totalFiles
            })
                                    .Where(p => p.TotalFiles > 0)
                                    .OrderBy(p => p.Name).ToArray();

            var folders = new List<ItemViewModel>()
            {
                new ItemViewModel()
                {
                    Id = workgroup.workgroupId,
                    Name = entity,
                    Entity = entity,
                    UAC = new UAC { CanView = true, CanUpdate = false, CanDelete = false, CanRequestBackup = canRequestBackup, CanExportToExcel = isWorkgroupOwner },
                    Folders = children.Select(f => f.Guid).ToArray(),
                }
            };
            folders.AddRange(children);

            return folders.ToArray();
        }

        public async Task<ItemViewModel[]> GetAttachmentsAsync(Content content, string entity)
        {
            List<ContentAttachment> contentAttachments = null;
            int contentId = content.contentId;
            var service = new ContentAttachmentService(this._currentUser, contentId);

            int signatureRequestId = 0;
            string signerRequestStatus = "";
            bool canViewESignatureSummary = false;

            if (entity == "meetingMaterialItem")
            {
                string[] contentTypes = { ContentTypes.MeetingAgendaItem, ContentTypes.Poll, ContentTypes.Task };
                contentAttachments = await service.GetFilteredFromChildItems(contentTypes);
            }
            else if (entity == "meetingMinuteItem")
            {
                Tuple<string, int, bool, List<ContentAttachment>> minuteData = await service.GetMeetingMinutesFromChildItems(true, false, true);
                signerRequestStatus = minuteData.Item1;
                signatureRequestId = minuteData.Item2;
                canViewESignatureSummary = minuteData.Item3;
                contentAttachments = minuteData.Item4;
            }
            else if (entity == "workgroupActionItem")
            {
                contentAttachments = await service.GetWorkgroupActionsFromChildItems();
            }

            if (contentAttachments is null)
            {
                return Array.Empty<ItemViewModel>();
            }

            string folderName;
            if (entity == "workgroupActionItem")
            {
                string dueDate = content.Task.FirstOrDefault()?.dueDate.ToString("yyyy-MM-dd ") ?? "";
                string assignedUser = content.User_Assigned.name;
                folderName = dueDate + assignedUser + " - " + content.title;
            }
            else
            {
                string meetingDate = content.Meeting.FirstOrDefault()?.date.ToString("yyyy-MM-dd ") ?? "";
                folderName = meetingDate + content.title;
            }

            var files = contentAttachments.Select(ca => new ItemViewModel()
            {
                Entity = "file",
                Id = ca.contentAttachmentId,
                Name = ca.Attachment.fileName,
                CreatedAt = ca.Attachment.createDate,
                FolderId = ca.contentId,
                LockedFile = ca.locked,
                UpdatedAt = ca.Attachment.createDate,
                UAC = new UAC() { CanView = true, CanViewESignatureSummary = canViewESignatureSummary },
                FileContentType = ca.Content?.type,
                ContentSignatureRequestId = signatureRequestId,
                RequestStatusSignatures = signerRequestStatus
            }).OrderBy(p => p.Name).ToArray();

            var folders = new List<ItemViewModel>()
            {
                new ItemViewModel()
                {
                    Entity = entity,
                    Id = contentId,
                    Name = folderName,
                    Folders = files.Select(f => f.Guid).ToArray(),
                    UAC = new UAC() { CanView = true, CanUpdate = false, CanDelete = false },
                }
            };
            folders.AddRange(files);

            return folders.ToArray();
        }

        public async Task<ItemTreeViewModel> GetDefaultFoldersByWorkgroup(int workgroupId, string type)
        {
            var workgroup = await _workgroupService.GetWorkgroupAsync(workgroupId);
            var isWorkgroupOwner = workgroup.UAC.isOwner;
            var isDownloadAttachmentsEnabled = await _featureManagerService.isEnabledByWorkgroup(workgroup.workgroupId, PlanFeatureNames.BLOCK_DOWNLOAD_DOCUMENTS);
            var canRequestBackup = isDownloadAttachmentsEnabled && workgroup.Client.blockFileDownload ? false : true;

            ItemViewModel[] children;

            if (workgroup.Client.showKbDefaultFoldersEnabled == true)
            {
                children = new ItemViewModel[]
            {
                new ItemViewModel() {
                    Id = workgroupId,
                    Name = (type == "PROJECT" ? "projectFolders" : "boardFolders"),
                    Entity = "knowledgeBase",
                    Folders = new string[] { },
                    UAC = new UAC() { IsOwner = false, CanView = true, CanUpdate = false, CanDelete = false, CanAddFolder = workgroup.UAC.isOwner, CanRequestBackup = canRequestBackup, CanExportToExcel = isWorkgroupOwner },
                    Order = 1,
                    ClientId = workgroup.Client.clientId,
                    clientUuid = workgroup.Client.clientUuid,
                    workgroupId = workgroupId
                },
                new ItemViewModel() {
                    Id = workgroupId,
                    Name = "meetingMaterials",
                    Entity = "meetingMaterials",
                    Folders = new string[] { },
                    UAC = new UAC() { IsOwner = false, CanView = true, CanUpdate = false, CanDelete = false, CanRequestBackup = canRequestBackup, CanExportToExcel = isWorkgroupOwner },
                    Order = 2,
                    ClientId = workgroup.Client.clientId,
                    clientUuid = workgroup.Client.clientUuid,
                    workgroupId = workgroupId
                },
                new ItemViewModel() {
                    Id = workgroupId,
                    Name = "meetingMinutes",
                    Entity = "meetingMinutes",
                    Folders = new string[] { },
                    UAC = new UAC() { IsOwner = false, CanView = canRequestBackup, CanUpdate = false, CanDelete = false, CanRequestBackup = canRequestBackup, CanExportToExcel = isWorkgroupOwner},
                    Order = 3,
                    ClientId = workgroup.Client.clientId,
                    clientUuid = workgroup.Client.clientUuid,
                    workgroupId = workgroupId
                },
                new ItemViewModel() {
                    Id = workgroupId,
                    Name = (type == "PROJECT" ? "projectActions" : "boardActions"),
                    Entity = "workgroupActions",
                    Folders = new string[] { },
                    UAC = new UAC() { IsOwner = false, CanView = true, CanUpdate = false, CanDelete = false, CanRequestBackup = canRequestBackup, CanExportToExcel = isWorkgroupOwner},
                    Order = 4,
                    ClientId = workgroup.Client.clientId,
                    clientUuid = workgroup.Client.clientUuid,
                    workgroupId = workgroupId
                },
            };
            }
            else
            {
                children = new ItemViewModel[]
            {
                new ItemViewModel() {
                    Id = workgroupId,
                    Name = (type == "PROJECT" ? "projectFolders" : "boardFolders"),
                    Entity = "knowledgeBase",
                    Folders = new string[] { },
                    UAC = new UAC() { IsOwner = false, CanView = true, CanUpdate = false, CanDelete = false, CanAddFolder = workgroup.UAC.isOwner, CanRequestBackup = canRequestBackup, CanExportToExcel = isWorkgroupOwner },
                    Order = 1,
                    workgroupId = workgroupId
                }
            };
            }

            var folders = new List<ItemViewModel>() {
                new ItemViewModel() {
                    Entity = "workgroup",
                    Id = workgroupId,
                    Name = workgroup.name,
                    Color = workgroup.bulletColor,
                    UAC = new UAC { CanView = true, CanUpdate = false, CanDelete = false, CanRequestBackup = canRequestBackup, CanExportToExcel = isWorkgroupOwner },
                    Folders = children.OrderBy(p => p.Order).Select(f => f.Guid).ToArray(),
                    ClientId = workgroup.Client.clientId,
                    clientUuid = workgroup.Client.clientUuid,
                    workgroupId = workgroupId
                }
            };

            folders.AddRange(children);

            return new ItemTreeViewModel(folders);
        }

        public async Task<ItemViewModel> AddFolder(Content directory)
        {
            Guid contentId = await _contentService.Add(directory.workgroupId, directory, null, null, null, this._user);

            // The just created content
            var content = await this._contentRepository.Get(contentId);


            var workgroup = await _workgroupService.Get(directory.workgroupId);
            var isWorkgroupOwner = workgroup.WorkgroupOwner.Select(o => o.userId).Contains(_currentUser);
            var isClientAdmin = await new UserRoleService(_currentUser).IsAdminOnClient(workgroup.clientId);
            var canExportToExcel = workgroup.type == "ROOT" ? isClientAdmin : isWorkgroupOwner;
            var isDownloadAttachmentsEnabled = await _featureManagerService.isEnabledByContent(content, PlanFeatureNames.BLOCK_DOWNLOAD_DOCUMENTS);
            var canRequestBackup = isDownloadAttachmentsEnabled && content.Workgroup.Client.blockFileDownload ? false : true;

            var folder = new ItemViewModel()
            {
                Id = content.contentId,
                Name = content.title,
                Entity = "knowledgeDirectory",
                CreatedAt = content.createDate,
                CreatedBy = content.User_Create.name,
                UpdatedAt = content.lastUpdate,
                UAC = new UAC() { IsOwner = true, CanView = true, CanUpdate = true, CanAddFiles = true, CanDelete = true, CanAddFolder = true, CanRequestBackup = canRequestBackup, CanExportToExcel = canExportToExcel },
                Code = directory.contentId,
                contentId = content.contentId,
                contentUuid = content.contentUuid,
                parentContentId = content.parentContentId,
                parentContentUuid = content.parentContentUuid
            };
            return folder;
        }

        public async Task<List<ItemViewModel>> AddMultipleFolders(Content directories, CancellationToken cancellationToken)
        {
            var listFolders = new List<ItemViewModel>();

            // Add content Folder
            var result = await AddFolder(directories);
            listFolders.Add(result);

            // Add each child content Folder
            foreach (var child in directories.Child_Content)
            {
                child.type = ContentTypes.KnowledgeDirectory;
                child.parentContentId = result.Id;
                child.parentContentUuid = result.contentUuid;

                var resultChild = await AddFolder(child);
                listFolders.Add(resultChild);

                // Add recursively each child of child content Folder
                foreach (var item in child.Child_Content)
                {
                    item.type = ContentTypes.KnowledgeDirectory;
                    item.parentContentId = resultChild.Id;
                    item.parentContentUuid = resultChild.contentUuid;

                    var childFolders = await AddMultipleFolders(item, cancellationToken);
                    foreach (var cf in childFolders)
                    {
                        listFolders.Add(cf);
                    }

                    if (cancellationToken.IsCancellationRequested)
                        throw new OperationCanceledException(cancellationToken);
                }

                if (cancellationToken.IsCancellationRequested)
                    throw new OperationCanceledException(cancellationToken);
            }
            return listFolders;
        }

        public async Task<List<ItemViewModel>> AddFolders(Content directories, CancellationToken cancellationToken)
        {
            List<ItemViewModel> result = null;

            using (IDbContextTransaction dbContext = _md.Database.BeginTransaction())
            {
                try
                {
                    _md.Database.SetCommandTimeout(300);
                    _md.ChangeTracker.AutoDetectChangesEnabled = false;

                    /* PLG_MIGRATION UNCOMMENT THIS
                     * this._contentService = new ContentService(this._user.userId, this._md, dbContext);
                    this._contentRepository = new ContentRepository(this._user.userId, this._md, dbContext);*/

                    result = await this.AddMultipleFolders(directories, cancellationToken);

                    dbContext.Commit();
                }
                catch (Exception)
                {
                    dbContext.Rollback();
                }
                finally
                {
                    _md.ChangeTracker.AutoDetectChangesEnabled = true;
                }
            }

            return result;
        }

        private async Task<ItemViewModel> AddFileFinishUpload(string blobKey, string filename, string extension, string userAgent, Guid contentId, bool importedFromFolder = false, int byteCount = 0, bool? overrideLocked = null)
        {
            var contentAttachmentService = new ContentAttachmentService(_currentUser, contentId, _storageSettings);
            var uploadResult = await contentAttachmentService.FinishUploadFile(blobKey, filename, extension, userAgent, contentId, false, importedFromFolder, null, null, null, true, false, byteCount);

            if (uploadResult.status == System.Net.HttpStatusCode.InternalServerError)
            {
                return null;
            }

            bool locked = false;

            // Check if the file can be converted or optimized.
            var hasPdfConverterExtension = ConverterExtensions.included_exts.Contains((extension ?? "").ToLower());

            if (hasPdfConverterExtension || ConverterExtensions.included_metaDataexts.Contains((extension ?? "").ToLower()) || (extension ?? "").ToLower() == "pdf")
            {
                locked = true;
            }

            var content = await this._contentService.Get(contentId);
            var workgroup = await this._workgroupService.Get(content.workgroupId);

            return new ItemViewModel()
            {
                Id = uploadResult.contentAttachmentId,
                Name = uploadResult.filename,
                Entity = "file",
                CreatedAt = uploadResult.createDate,
                UpdatedAt = uploadResult.createDate,
                LockedFile = overrideLocked ?? locked,
                UAC = new UAC()
                {
                    CanView = true,
                    CanUpdate = false,
                    CanDelete = workgroup.UAC.isOwner,
                    CanLockFile = workgroup.UAC.isOwner && hasPdfConverterExtension,
                    CanRequestESignature = workgroup.UAC.kbRequestESignature && !workgroup.archived && hasPdfConverterExtension
                }
            };
        }

        private async Task<ItemViewModel> AddFile(byte[] file, string filename, string extension, string userAgent, Guid contentId, bool importedFromFolder = false, bool? overrideLocked = null)
        {
            var contentAttachmentService = new ContentAttachmentService(_currentUser, contentId, _storageSettings);
            var uploadResult = await contentAttachmentService.Add(file, filename, extension, "", userAgent, contentUuid: contentId, importedFromFolder: importedFromFolder);

            if (uploadResult.status == System.Net.HttpStatusCode.InternalServerError)
            {
                return null;
            }

            bool locked = false;

            // Check if the file can be converted or optimized.
            var hasPdfConverterExtension = ConverterExtensions.included_exts.Contains((extension ?? "").ToLower());

            if (hasPdfConverterExtension || ConverterExtensions.included_metaDataexts.Contains((extension ?? "").ToLower()) || (extension ?? "").ToLower() == "pdf")
            {
                locked = true;
            }

            var content = await this._contentService.Get(contentId);
            var workgroup = await this._workgroupService.Get(content.workgroupId);

            return new ItemViewModel()
            {
                Id = uploadResult.contentAttachmentId,
                Name = uploadResult.filename,
                Entity = "file",
                CreatedAt = uploadResult.createDate,
                UpdatedAt = uploadResult.createDate,
                LockedFile = overrideLocked ?? locked,
                UAC = new UAC()
                {
                    CanView = true,
                    CanUpdate = false,
                    CanDelete = workgroup.UAC.isOwner,
                    CanLockFile = workgroup.UAC.isOwner && hasPdfConverterExtension,
                    CanRequestESignature = workgroup.UAC.kbRequestESignature && !workgroup.archived && hasPdfConverterExtension
                }
            };
        }

        public async Task<ItemViewModel> AddFileOrChuncks(
            Guid contentId,
            string? qquuid,

            string fileName,

            long? fileLength,
            long? qqtotalfilesize,

            int? qqpartindex,
            int? qqtotalparts,

            string originalPath,
            string tempPath,

            string userAgent,

            bool importedFromFolder = false,
            bool? overrideLocked = null)
        {

            if (string.IsNullOrEmpty(qquuid))
            {
                qquuid = Guid.NewGuid().ToString();
            }

            var destinationPath = originalPath + qquuid;

            if (!Directory.Exists(destinationPath))
            {
                Directory.CreateDirectory(destinationPath);
            }

            qqpartindex = qqpartindex ?? 0;
            qqtotalparts = qqtotalparts ?? 0;
            qqtotalfilesize = qqtotalfilesize ?? 0;

            qqtotalfilesize = qqtotalfilesize == 0 ? fileLength : qqtotalfilesize;

            if (qqtotalfilesize > 0 && qqtotalfilesize > FileUtils.MaxFileSize)
            {
                throw new ArgumentOutOfRangeException(nameof(qqtotalfilesize), "File size exceeded the maximum limit.");
            }

            var extension = fileName.Split('.').Last();

            ItemViewModel res = null;

            UploadResult resUpload = null;

            if (BannedFileTypes.Extensions.Contains(extension))
            {
                throw new SecurityException("Banned file type");
            }

            if (!CrossCutting.AppEnums.ConverterExtensions.included_exts.Contains((extension ?? "").ToLower()))
            {
                overrideLocked = null;
            }

            //Validate if reach the last file part or if it is one part file
            if (((qqtotalparts - 1) == qqpartindex) || qqtotalparts == 0)
            {
                if (importedFromFolder)
                {
                    // S51 - Importing files from Folders
                    // If we upload a folder via specific folder selection (webkitdirectory), 'FineUploader' will send the folder name in the filename string
                    // we need to remove the folder name in this case
                    // This doesnt happen using Drag & Drop though
                    fileName = fileName.Split('/').LastOrDefault();
                }

                if (qqtotalparts > 0)
                {
                    //append last (or unique) part
                    resUpload = await this.AppendChunks(tempPath, fileName, qquuid);

                    // Cast the long qqtotalfilesize to integer but if it exceedes the maximum integer, uses the maximum integer value
                    int totalFileSize = FileUtils.ConvertLongToInt(qqtotalfilesize.Value);

                    //save on database
                    res = await this.AddFileFinishUpload(resUpload.downloadKey, fileName, extension, userAgent, contentId, importedFromFolder, totalFileSize, overrideLocked: overrideLocked);


                    //delete local temp file
                    if (File.Exists(tempPath))
                    {
                        File.Delete(tempPath);
                    }
                }
                else
                {
                    //fix to work on mobile and older versions from frontend
                    byte[] byteArray;
                    byteArray = File.ReadAllBytes(tempPath);

                    //delete local temp file
                    File.Delete(tempPath);

                    if (byteArray != null && byteArray.Length > 1)
                    {
                        if (BannedFileTypes.CheckIsExecutable(new byte[] { byteArray[0], byteArray[1] }))
                        {
                            throw new SecurityException("Disguised executable");
                        }
                    }

                    res = await this.AddFile(byteArray, fileName, extension, userAgent, contentId, importedFromFolder, overrideLocked: overrideLocked);
                }

                //After upload, eliminate no needed folders.
                if (res != null)
                {
                    if (Directory.Exists(destinationPath))
                    {
                        Directory.Delete(destinationPath, true);
                    }
                }
            }
            else
            {
                await this.AppendChunks(tempPath, fileName, qquuid);

                if (File.Exists(tempPath))
                {
                    File.Delete(tempPath);
                }
            }

            return res;
        }


        private void TreatAllChuncks(
            ref long qqtotalfilesize,
            ref string fileName,
            ref string localFilePath,
            ref bool importedFromFolder,
            ref int qqpartindex,
            ref int qqtotalparts,
            string destination_path,
            IFormFile providedFile)
        {
            var contentDisposition = providedFile.ContentDisposition;
            var name = providedFile.Name;





            File.Delete(providedFile.FileName);

            switch (name)
            {

                case "qqpartindex":

                    File.Delete(providedFile.FileName);
                    break;

                case "qqtotalparts":

                    File.Delete(providedFile.FileName);
                    break;

                case "qqchunksize":
                    File.Delete(providedFile.FileName);
                    break;

                case "qqfilename":
                    fileName = System.Text.Encoding.UTF8.GetString(File.ReadAllBytes(providedFile.FileName));
                    File.Delete(providedFile.FileName);
                    break;

                case "qqpartbyteoffset":
                    File.Delete(providedFile.FileName);
                    break;

                // Look for the piece with name qqfile. That's the piece that contains the file we want to upload
                case "qqfile":
                    string filePart = providedFile.FileName.Split('\\').Last();
                    string fileNameBlob = providedFile.FileName.Replace("\"", "").Split('/').Last();

                    fileName = fileName == "" ? fileNameBlob : fileName;
                    localFilePath = providedFile.FileName;
                    //do not delete this file

                    break;

                case "importedFromFolder":
                    // This file came from the imported content from folders feature
                    importedFromFolder = true;
                    File.Delete(providedFile.FileName);
                    break;

                default:
                    break;
            }
        }

        public async Task<UploadResult> AppendChunks(string localPath, string filename, string guid)
        {

            AzureHelper az = new AzureHelper(_storageSettings);
            UploadResult res;

            //S-47 WI#2739 - remove % char from filename
            if (filename.Contains("%"))
            {
                filename = filename.Replace("%", "");
            }
            //2021-2 - WI#6218 Removing all illegal file chars
            filename = string.Join("_", filename.Split(Path.GetInvalidFileNameChars()));

            string nome_blob = "v2/" + guid + "/" + filename;
            //string nome_blob = "g_" + pendencia.GrupoEconomicoId + "/pend_" + pendencia.PendenciaId.ToString() + "/" + Guid.NewGuid().ToString() + "/" + arquivos.FileName;

            res = await az.AppendPartFromLocalFile(localPath, nome_blob, false);

            return res;

        }

        public async Task<IEnumerable<ItemViewModel>> GetBoardsAsync(int clientId, bool hideArchived = true)
        {
            var workgroups = await this._workgroupService.GetWorkgroupsByClientAsync(clientId, WorkgroupTypes.DEFAULT, hideArchived);
            var isBlockDownloadAttachmentEnabled = await _featureManagerService.isEnabledByClient(PlanFeatureNames.BLOCK_DOWNLOAD_DOCUMENTS, clientId);
            ClientService clientService = new ClientService(_currentUser);
            var client = await clientService.Get(clientId);
            var canRequestBackup = isBlockDownloadAttachmentEnabled && client.blockFileDownload ? false : true;

            return workgroups.Select(w => new ItemViewModel()
            {
                Id = w.workgroupId,
                Name = w.name,
                Entity = "workgroup",
                Color = w.color,
                UAC = new UAC() { IsOwner = w.UAC.isOwner, CanView = true, CanUpdate = false, CanDelete = false, CanRequestBackup = canRequestBackup, CanExportToExcel = w.UAC.isOwner },
                Folders = new string[] { },
                ClientId = clientId,
                clientUuid = client.clientUuid,
                workgroupId = w.workgroupId
            }
            ).ToList();
        }

        public async Task<bool> RenameDirectory(Guid contentUuid, Content directory)
        {
            return await this._contentService.RenameDirectory(contentUuid, directory);
        }

        public async Task<IEnumerable<ItemViewModel>> GetProjectsAsync(int clientId, bool hideArchived = true)
        {
            var workgroups = await this._workgroupService.GetWorkgroupsByClientAsync(clientId, WorkgroupTypes.PROJECT, hideArchived);
            var isBlockDownloadAttachmentEnabled = await _featureManagerService.isEnabledByClient(PlanFeatureNames.BLOCK_DOWNLOAD_DOCUMENTS, clientId);
            ClientService clientService = new ClientService(_currentUser);
            var client = await clientService.Get(clientId);
            var canRequestBackup = isBlockDownloadAttachmentEnabled && client.blockFileDownload ? false : true;

            return workgroups.Select(w => new ItemViewModel()
            {
                Id = w.workgroupId,
                Name = w.name,
                Entity = "workgroup",
                Color = w.color,
                UAC = new UAC() { IsOwner = w.UAC.isOwner, CanView = true, CanUpdate = false, CanDelete = false, CanRequestBackup = canRequestBackup, CanExportToExcel = w.UAC.isOwner },
                Folders = new string[] { },
                ClientId = clientId,
                clientUuid = client.clientUuid,
                workgroupId = w.workgroupId
            }
            ).ToList();
        }

        public async Task<IEnumerable<ItemViewModel>> GetFoldersInWorkgroup(Workgroup workgroup, string entity, int clientId)
        {
            int workgroupId = workgroup.workgroupId;
            var contents = await this._contentService.GetAllKnowledgeDirectoriesFromWorkgroupAsync(workgroupId);
            var isClientAdmin = await new UserRoleService(_currentUser).IsAdminOnClient(workgroup.clientId);
            var isWorkgroupOwner = workgroup.UAC.isOwner;
            var canExportToExcel = entity == "workgroupRoot" ? isClientAdmin : isWorkgroupOwner;
            canExportToExcel = entity == "boards" || entity == "projects" ? false : canExportToExcel;
            var canRequestBackup = true;
            var isBlockDownloadAttachmentEnabled = await _featureManagerService.isEnabledByWorkgroup(workgroup.workgroupId, PlanFeatureNames.BLOCK_DOWNLOAD_DOCUMENTS);

            if ((isBlockDownloadAttachmentEnabled && workgroup.Client.blockFileDownload) || (entity == "boards" || entity == "projects"))
            {
                canRequestBackup = false;
            }

            var children = contents.Select(c => new ItemViewModel()
            {
                Id = c.contentId,
                Name = c.title,
                Entity = "knowledgeDirectory",
                CreatedAt = c.createDate,
                CreatedBy = c.User_Create.name,
                UpdatedAt = c.lastUpdate,
                UAC = new UAC()
                {
                    IsOwner = c.UAC.isOwner,
                    CanView = true,
                    CanUpdate = _currentUser == c.createUser ? true : c.UAC.update,
                    CanAddFiles = _currentUser == c.createUser ? true : c.UAC.update,
                    CanDelete = _currentUser == c.createUser ? true : c.UAC.delete,
                    CanAddFolder = _currentUser == c.createUser ? true : c.UAC.update,
                    CanRequestBackup = canRequestBackup,
                    CanExportToExcel = canExportToExcel
                },
                ClientId = workgroup.clientId,
                contentId = c.contentId,
                contentUuid = c.contentUuid,
                workgroupId = c.workgroupId
            }).OrderBy(p => p.Name).ToArray();

            string folderName;
            bool canAddFolder;

            if (entity == "workgroupRoot")
            {
                folderName = "sharedFolders";
                canAddFolder = isClientAdmin;
            }
            else
            {
                folderName = entity;

                if (entity == "workgroupActionItem")
                {
                    if (workgroup.type == "PROJECT")
                    {
                        folderName = "projectActions";
                    }
                    else if (workgroup.type == "DEFAULT")
                    {
                        folderName = "boardActions";
                    }
                }
                else if (entity == "knowledgeBase")
                {
                    if (workgroup.type == "PROJECT")
                    {
                        folderName = "projectFolders";
                    }
                    else if (workgroup.type == "DEFAULT")
                    {
                        folderName = "boardFolders";
                    }
                }

                canAddFolder = !workgroup.archived;
            }

            var folders = new List<ItemViewModel>()
            {
                new ItemViewModel()
                {
                    Entity = entity,
                    Id = workgroupId,
                    Name = folderName,
                    UAC = new UAC { CanView = true, CanUpdate = false, CanDelete = false, CanAddFolder = canAddFolder, CanRequestBackup = canRequestBackup, CanExportToExcel = canExportToExcel },
                    Folders = children.Select(f => f.Guid).ToArray(),
                    workgroupId = workgroupId,
                    ClientId = workgroup.clientId
                }
            };


            folders.AddRange(children);

            return folders.OrderBy(p => p.Name).ToArray();
        }

        public async Task<ItemTreeViewModel> GetChildsKnowledgeDirectory(int id, Workgroup workgroup, int clientId)
        {
            var directory = await this._contentService.GetDirectoryAsync(id);
            var isWorkgroupOwner = workgroup.UAC.isOwner;
            var isClientAdmin = await new UserRoleService(_currentUser).IsAdminOnClient(workgroup.clientId);
            var canExportToExcel = workgroup.type == "ROOT" ? isClientAdmin : isWorkgroupOwner;
            var archived = workgroup.archived;
            var isBlockDownloadAttachmentEnabled = await _featureManagerService.isEnabledByWorkgroup(workgroup.workgroupId, PlanFeatureNames.BLOCK_DOWNLOAD_DOCUMENTS);
            var canRequestBackup = isBlockDownloadAttachmentEnabled && workgroup.Client.blockFileDownload ? false : true;

            var kbRepository = new KnowledgeBaseRepository();
            kbRepository.computeHasSignatureInFiles(directory);

            var children = directory.Child_Content.Select(c => new ItemViewModel()
            {
                Id = c.contentId,
                contentId = c.contentId,
                contentUuid = c.contentUuid,
                workgroupId = c.workgroupId,
                Name = c.title,
                Entity = "knowledgeDirectory",
                CreatedAt = c.createDate,
                UpdatedAt = c.lastUpdate,
                UAC = new UAC()
                {
                    IsOwner = c.UAC.isOwner,
                    CanView = true,
                    CanUpdate = c.UAC.update && !archived,
                    CanAddFiles = !archived,
                    CanDelete = c.UAC.delete && !archived,
                    CanAddFolder = c.UAC.update && !archived,
                    CanRequestBackup = canRequestBackup,
                    CanExportToExcel = canExportToExcel
                }
            }).ToList();

            bool esignEnabled = await _featureManagerService.isEnabledByClient(PlanFeatureNames.ESIGNATURE, workgroup.clientId);

            children.AddRange(
                directory.ContentAttachment.Where(ca => !(ca.deleted ?? false))
                .Select(ca =>
                {
                    var hasPdfConverterExtension = ConverterExtensions.included_exts.Contains((ca.Attachment?.extension ?? "").ToLower());
                    return new ItemViewModel()
                    {
                        Id = ca.contentAttachmentId,
                        contentId = ca.contentId,
                        contentUuid = ca.contentUuid,
                        workgroupId = ca.Content.workgroupId,
                        Name = ca.Attachment.fileName,
                        Entity = "file",
                        CreatedAt = ca.Attachment.createDate,
                        LockedFile = ca.locked,
                        FileHasSignature = ca.hasSignature,
                        PendingSignatures = ca.pendingSignatures,
                        RequestStatusSignatures = ca.signatureStatus == "ABANDONED" ? "CANCELLED" : ca.signatureStatus,
                        ContentSignatureRequestId = ca.contentSignatureRequestId,
                        TotalSigners = ca.totalSigners,
                        UpdatedAt = ca.Attachment.createDate,
                        UAC = new UAC()
                        {
                            IsOwner = ca.Attachment.createUser == this._currentUser,
                            CanView = true,
                            CanUpdate = false,
                            CanDelete = ca.UAC.delete,
                            CanLockFile = ca.UAC.lockable && hasPdfConverterExtension,
                            CanRequestESignature = directory.UAC.request_esignature && esignEnabled && !workgroup.archived && hasPdfConverterExtension,
                            CanViewESignatureSummary = ca.UAC.view_esignature_summary,
                            CanNotifyAttachment = ca.UAC.notify,
                            canShare = isWorkgroupOwner,
                        }
                    };
                })
            );

            bool fileSignaturePending = children.Any(f => f.FileHasSignature == false);
            children.Sort((vm1, vm2) => String.Compare(vm1.Name, vm2.Name));

            var folder = new ItemViewModel()
            {
                Entity = "knowledgeDirectory",
                Id = directory.contentId,
                contentId = directory.contentId,
                contentUuid = directory.contentUuid,
                workgroupId = directory.workgroupId,
                Name = directory.title,
                CreatedAt = directory.createDate,
                CreatedBy = directory.User_Create.name,
                UpdatedAt = directory.lastUpdate,
                UAC = new UAC
                {
                    CanView = true,
                    CanUpdate = _currentUser == directory.User_Create.userId ? !archived : directory.UAC.update && !archived,
                    CanAddFiles = _currentUser == directory.User_Create.userId ? !archived : directory.UAC.add_attachment && !archived,
                    CanDelete = _currentUser == directory.User_Create.userId ? !archived : directory.UAC.delete && !archived,
                    CanAddFolder = isClientAdmin ? isClientAdmin : workgroup.type == "DEFAULT" || workgroup.type == "PROJECT",
                    CanRequestESignature = directory.UAC.request_esignature && fileSignaturePending && esignEnabled && !workgroup.archived,
                    CanManagePermission = directory.UAC.manage_permissions,
                    CanRequestBackup = canRequestBackup,
                    CanExportToExcel = canExportToExcel
                },
                Folders = children.Select(f => f.Guid).ToArray(),
            };

            var list = new List<ItemViewModel>();
            list.Add(folder);
            list.AddRange(children);

            list = list.OrderBy(p => p.Name).ToList();

            return new ItemTreeViewModel(list);
        }

        private async Task<IEnumerable<ItemViewModel>> GetDefaultFoldersAsync(Client client)
        {
            DateTime startDate = DateTime.Now;
            Console.WriteLine($"Iniciando o processamento {startDate.ToString()}");

            int clientId = client.clientId;
            Guid clientUuid = client.clientUuid;
            var boards = await this.GetBoardsAsync(clientId, hideArchived: true);
            var projects = await this.GetProjectsAsync(clientId, hideArchived: true);
            Console.WriteLine($"Carregou board {(DateTime.Now).ToString()} - diff {(startDate - DateTime.Now).TotalSeconds.ToString()}");

            var boardRoot = await this._workgroupService.GetWorkgroupRootByClientAsync(clientId);
            Console.WriteLine($"Carregou root {(DateTime.Now).ToString()} - diff {(startDate - DateTime.Now).TotalSeconds.ToString()}");

            if (boardRoot == null)
                return null;

            bool isClientAdmin = await new UserRoleService(_currentUser).IsAdminOnClient(clientId);
            Console.WriteLine($"Is clientAdmin {(DateTime.Now).ToString()} - diff {(startDate - DateTime.Now).TotalSeconds.ToString()}");

            bool isBlockDownloadAttachmentEnabled = await _featureManagerService.isEnabledByClient(PlanFeatureNames.BLOCK_DOWNLOAD_DOCUMENTS, clientId);
            bool canDownloadAttachments = isBlockDownloadAttachmentEnabled && client.blockFileDownload ? false : true;

            var folders = new List<ItemViewModel> {
                new ItemViewModel() {
                    Id = boardRoot.workgroupId,
                    Name="sharedFolders",
                    Entity="workgroupRoot",
                    Folders = new string[] { },
                    UAC = new UAC() { IsOwner = boardRoot.UAC.isOwner, CanView = true, CanUpdate = false, CanDelete = false, CanAddFolder = isClientAdmin, CanRequestBackup = canDownloadAttachments, CanExportToExcel = isClientAdmin },
                    ClientId = clientId,
                    clientUuid = clientUuid,
                    workgroupId = boardRoot.workgroupId
                },
                new ItemViewModel() {
                    Id = clientId,
                    Name="boards",
                    Entity="boards",
                    Folders = boards.Select(b => b.Guid).ToArray(),
                    UAC = new UAC() { IsOwner = false, CanView = true, CanUpdate = false, CanDelete = false, CanRequestBackup = false, CanExportToExcel = false },
                    ClientId = clientId,
                    clientUuid = clientUuid
                },
                new ItemViewModel() {
                    Id = clientId,
                    Name="projects",
                    Entity="projects",
                    Folders = projects.Select(p => p.Guid).ToArray(),
                    UAC = new UAC() { IsOwner = false, CanView = true, CanUpdate = false, CanDelete = false, CanRequestBackup = false, CanExportToExcel = false },
                    ClientId = clientId,
                    clientUuid = clientUuid
                },
            };

            folders.AddRange(boards);
            folders.AddRange(projects);

            return folders;
        }

        public async Task<ItemTreeViewModel> GetRecycleBin(int clientId)
        {
            var allWorkgroups = await this._workgroupService.Get(loadTask: false, withRootType: true);
            var isClientAdmin = await new UserRoleService(_currentUser).IsAdminOnClient(clientId);

            // Boards/projects that current user owns
            // Workgroup Root if the user is a client admin
            var targetWorkgroups = allWorkgroups.Where(w =>
               w.clientId == clientId && !w.archived && (
               (w.type != WorkgroupTypes.ROOT && w.UAC.isOwner) ||
               (w.type == WorkgroupTypes.ROOT && isClientAdmin))).ToList();

            var exceptWorkgroups = allWorkgroups.Where(w => w.clientId == clientId && !w.archived).Except(targetWorkgroups).ToList();

            if (targetWorkgroups.Count == 0 && exceptWorkgroups.Count == 0)
            {
                return new ItemTreeViewModel(Array.Empty<ItemViewModel>());
            }

            var recycleBin = new List<KnowledgeBaseRecycleBinDTO>();
            
            if (targetWorkgroups.Count > 0)
            {
                var workgroupIds = targetWorkgroups.Select(w => w.workgroupId).ToArray();

                recycleBin = await _contentRepository.GetRecycleBinAsync(workgroupIds);
            }

            if (exceptWorkgroups.Count > 0)
            {
                // Trash file items for workgroups when the user is only a member
                var memberWorkgroupIds = exceptWorkgroups.Select(w => w.workgroupId).ToArray();
                
                var allRecycleBin = await _contentRepository.GetRecycleBinAsync(memberWorkgroupIds);

                var myItems = allRecycleBin.Where(r => r.createUser == this._currentUser);

                recycleBin.AddRange(myItems);
            }

            var kbRepository = new KnowledgeBaseRepository();
            kbRepository.computeTrashPath(recycleBin);
            
            var culture = new System.Globalization.CultureInfo("pt-BR");

            i18next_net.i18next i18n = new i18next_net.i18next(new i18next_net.InitOptions()
            {
                defaultNS = "common",
                localeFileType = LocaleFileTypeEnum.Path,
                fallbackLng = "en"
            });

            AtlasModelCore _md = new AtlasModelCore();
            var user = _md.User.Where(u => u.userId == _currentUser).FirstOrDefault();

            string lang = (user.defaultLanguage ?? "pt").ToLower();
            i18n.changeLanguage(lang);

            Dictionary<int, string> workgroupPathDictionary = new Dictionary<int, string>();
            
            List<ItemViewModel> trashListView = new List<ItemViewModel>();
            
            foreach (var trashItem in recycleBin)
            {
                if (!workgroupPathDictionary.ContainsKey(trashItem.workgroupId))
                {
                    var workgroupFound = allWorkgroups.Find(w => w.workgroupId == trashItem.workgroupId);
                    if (workgroupFound.type == WorkgroupTypes.ROOT)
                        workgroupPathDictionary[trashItem.workgroupId] = $@"{i18n.t("activities:auditLog.KB_SHARED_FOLDERS")}\";
                    else if (workgroupFound.type == WorkgroupTypes.DEFAULT)
                        workgroupPathDictionary[trashItem.workgroupId] = $@"Boards\{workgroupFound.name}\{i18n.t("activities:Type.boardFolders")}\";
                    else
                        workgroupPathDictionary[trashItem.workgroupId] = $@"{i18n.t("activities:Type.projects")}\{workgroupFound.name}\{i18n.t("activities:Type.projectFolders")}\";
                }

                string trashPathPrefix = workgroupPathDictionary[trashItem.workgroupId];

                trashListView.Add(new ItemViewModel()
                {
                    Entity = trashItem.folderName is null ? "file" : "knowledgeDirectory",
                    Id = trashItem.contentId,
                    Name = trashItem.folderName is null ? trashItem.fileName : trashItem.folderName,
                    UAC = new UAC { CanView = true, CanUpdate = false, CanDelete = false },
                    TrashPath = trashPathPrefix + trashItem.trashPath,
                    CreatedAt = trashItem.createDate,
                    UpdatedAt = trashItem.lastUpdate,
                    clientUuid = trashItem.clientUuid,
                    ClientId = trashItem.clientId,
                    contentUuid = trashItem.contentUuid,
                    workgroupId = trashItem.workgroupId
                });
            }
            
            var final_res = new ItemTreeViewModel(trashListView);
            
            return final_res;
        }

        public async Task<object> BackupAttachments(ClientBackupFiltersViewModel backupFilters, string userAgent)
        {
            int? clientId = 0;
            string workgroupType = "DEFAULT";
            string contentType = string.Empty;

            if (!backupFilters.workgroupId.HasValue && (!backupFilters.contentId.HasValue || !backupFilters.contentUuid.HasValue))
            {
                throw new HttpCustomException(KnowledgeBaseErrorCodes.MustInformWorkgroupOrContentUuid, HttpStatusCode.BadRequest);
            }

            if (backupFilters.entity == null)
            {
                throw new HttpCustomException(KnowledgeBaseErrorCodes.MustInformContentType, HttpStatusCode.BadRequest);
            }

            ClientBackupRepository clientBackupRepo = new ClientBackupRepository(_currentUser);

            var filtersJson = JsonConvert.SerializeObject(backupFilters);

            var hasAnyOpenRequest = await clientBackupRepo.HasAnyOpenRequest(backupFilters.clientId.Value, type: "ATTACHMENTS_KB", _currentUser, filtersJson);

            if (hasAnyOpenRequest)
            {
                throw new HttpCustomException(KnowledgeBaseErrorCodes.HasOpenBackupRequest, HttpStatusCode.UnprocessableContent);
            }

            if (backupFilters.workgroupId.HasValue)
            {
                var workgroup = await _workgroupService.Get(backupFilters.workgroupId.Value, false);

                if (!workgroup.Members.Select(c => c.userId).Contains(_currentUser))
                {
                    throw new HttpCustomException(KnowledgeBaseErrorCodes.NotAllowed, HttpStatusCode.Forbidden);
                }

                clientId = workgroup.clientId;
                workgroupType = workgroup.type;
            }
            else if (backupFilters.contentUuid.HasValue)
            {
                var content = await _contentService.Get(backupFilters.contentUuid.Value);
                var workgroup = await _workgroupService.Get(content.workgroupId, false);


                if (!workgroup.Members.Select(c => c.userId).Contains(_currentUser))
                {
                    throw new HttpCustomException(KnowledgeBaseErrorCodes.NotAllowed, HttpStatusCode.Forbidden);
                }

                clientId = content.Workgroup.clientId;
                workgroupType = content.Workgroup.type;
                contentType = content.type;
            }

            //get data
            var backupResult = await clientBackupRepo.AddRequest(clientId.Value, filtersJson, "ATTACHMENTS_KB");

            //add to queue
            MessageSender messageSender = new MessageSender(_storageSettings, "client-backup-request");
            messageSender.SendMessage(JsonConvert.SerializeObject(new { bakReqId = backupResult.bakReqId }));

            //Beginning the process to register the activity
            string message = "";

            if (backupFilters.workgroupId.HasValue)
            {
                message = JsonConvert.SerializeObject(new
                {
                    workgroupType = workgroupType,
                    workgroupId = backupFilters.workgroupId.Value,
                    backupRequestId = backupResult.bakReqId
                });
            }
            else if (backupFilters.contentUuid.HasValue)
            {
                message = JsonConvert.SerializeObject(new
                {
                    contentId = backupFilters.contentId.Value,
                    contentUuid = backupFilters.contentUuid.Value,
                    backupRequestId = backupResult.bakReqId
                });
            }

            //Define Atctivity
            string sActivityType = string.Empty;

            sActivityType = ((contentType == "KnowledgeDirectory" && workgroupType == "ROOT") || backupFilters.entity == "workgroupRoot") ?
                                "KB_BACKUP_REQUESTED_COMPANY" : //When Knowledge Directory Root
                                "KB_BACKUP_REQUESTED";          //Other types

            var success = await ActivityService.AddNewAsync(
                _storageSettings,
                sActivityType,
                clientId,
                backupFilters.workgroupId,
                _currentUser,
                message,
                userAgent);


            return new { success = success };
        }
        public async Task<bool> NotifyNewAttachment(AttachmentNotificationViewModel viewModel, string userAgent)
        {
            ValidateNotifyNewAttachment(viewModel);

            var service = new ContentAttachmentService(_currentUser, viewModel.contentUuid, _storageSettings);

            var notified = await service.NotifyAttachments(viewModel, userAgent);

            return notified;
        }
    }
}
