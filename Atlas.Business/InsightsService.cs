using Atlas.Business.Helpers;
using Atlas.Business.ViewModels.Insights;
using Atlas.CrossCutting.AppEnums;
using Atlas.Data.Repository;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Atlas.Business
{
    public class InsightsService
    {
        private readonly int _currentUser;
        private readonly InsightsRepository _repo;
        public InsightsService(int currentUser)
        {
            _currentUser = currentUser;
            _repo = new InsightsRepository();
        }

        private async Task<bool> ValidateFilters(InsightsFilters filters)
        {
            var enterpriseWorkgroups = await _repo.GetEnterpriseWorkgroupsByUserId(_currentUser);

            if (filters.WorkgroupId == null || !filters.WorkgroupId.Any())
            {
                filters.WorkgroupId = enterpriseWorkgroups;
            }
            else
            {
                filters.WorkgroupId = filters.WorkgroupId.Intersect(enterpriseWorkgroups).ToArray();
            }

            if (!filters.WorkgroupId.Any())
                return false;

            if (filters.StartDate >= filters.EndDate)
                return false;

            return true;
        }

        public async Task<CountersViewModel> GetCountersNew(InsightsFilters filters)
        {
            if (!await ValidateFilters(filters))
            {
                return null;
            }

            var counters = await _repo.GetCountersNew(filters.WorkgroupId, filters.StartDate, filters.EndDate);
            counters.TotalResolutions = await _repo.GetResolutionCounter(filters.WorkgroupId, filters.StartDate, filters.EndDate);

            return new CountersViewModel
            {
                TotalMeetings = counters.TotalMeetings,
                TotalMinutes = counters.TotalDurationMinutes,
                TotalParticipants = counters.TotalParticipants,
                TotalAgendas = counters.TotalAgendas,
                TotalBluebookPages = counters.TotalBluebookPages,
                TotalResolutions = counters.TotalResolutions
            };
        }

        public async Task<ReportGraphViewModel> GetReportsNew(InsightsFilters filters)
        {
            if (!await ValidateFilters(filters))
            {
                return null;
            }

            var reportData = await _repo.GetReportsNew(filters.WorkgroupId, filters.StartDate, filters.EndDate);
            var resolutionReport = await _repo.GetReportResolutions(filters.WorkgroupId, filters.StartDate, filters.EndDate);
            var commentsReport = await _repo.GetReportCommentsForGraph(filters.WorkgroupId, filters.StartDate, filters.EndDate);
            var bluebookViewsReport = await _repo.GetReportBlueBookViews(filters.WorkgroupId, filters.StartDate, filters.EndDate);
            var materialViewsReport = await _repo.GetReportMaterialViews(filters.WorkgroupId, filters.StartDate, filters.EndDate);

            var meetings = new List<MonthlyDataViewModel>();
            var agendas = new List<MonthlyDataViewModel>();
            var totalDuration = new List<MonthlyDataViewModel>();

            var bbViews = new List<MonthlyDataViewModel>();
            var materialViews = new List<MonthlyDataViewModel>();

            var minutes = new List<MonthlyDataViewModel>();

            reportData.ForEach(group =>
            {
                meetings.Add(new MonthlyDataViewModel(group.Period, group.Meetings));

                if (group.Agendas > 0)
                {
                    agendas.Add(new MonthlyDataViewModel(group.Period, group.Agendas));
                }

                totalDuration.Add(new MonthlyDataViewModel(group.Period, group.TotalMinutes));

                // minutes
                if (group.MinutesPublished > 0)
                {
                    minutes.Add(new MonthlyDataViewModel()
                    {
                        Period = group.Period,
                        Count = group.MinutesPublished,
                        Status = "PUBLISHED"
                    });
                }

                if (group.MinutesWaitingSignature > 0)
                {
                    minutes.Add(new MonthlyDataViewModel()
                    {
                        Period = group.Period,
                        Count = group.MinutesWaitingSignature,
                        Status = "WAITING_SIGNATURE"
                    });
                }

                if (group.MinutesSigned > 0)
                {
                    minutes.Add(new MonthlyDataViewModel()
                    {
                        Period = group.Period,
                        Count = group.MinutesSigned,
                        Status = "SIGNED"
                    });
                }
            });


            bluebookViewsReport.ForEach(group =>
            {
                if (group.AllBbViews > 0)
                {
                    bbViews.Add(new MonthlyDataViewModel()
                    {
                        Period = group.Period,
                        Count = group.AllBbViews,
                        Status = "TOTAL_VIEW"
                    });
                }

                if (group.SingleBbViews > 0)
                {
                    bbViews.Add(new MonthlyDataViewModel()
                    {
                        Period = group.Period,
                        Count = group.SingleBbViews,
                        Status = "UNIQUE_VIEW"
                    });
                }
            });


            materialViewsReport.ForEach(group =>
            {
                if (group.AllMaterialViews > 0)
                {
                    materialViews.Add(new MonthlyDataViewModel()
                    {
                        Period = group.Period,
                        Count = group.AllMaterialViews,
                        Status = "TOTAL_VIEW"
                    });
                }

                if (group.SingleMaterialViews > 0)
                {
                    materialViews.Add(new MonthlyDataViewModel()
                    {
                        Period = group.Period,
                        Count = group.SingleMaterialViews,
                        Status = "UNIQUE_VIEW"
                    });
                }
            });

            var resolutions = resolutionReport
                .Select(r => new MonthlyDataViewModel(r.Period, r.Total)).ToList();
            var comments = commentsReport
                .Select(c => new MonthlyDataViewModel(c.Period, c.Total)).ToList();


            var dashboardGraph = new ReportGraphViewModel
            {
                Meetings =
                    new Graph
                    {
                        Total = meetings.Sum(m => m.Count),
                        InfoGraph = meetings
                    },
                Agendas =
                        new Graph
                        {
                            Total = agendas.Sum(a => a.Count),
                            InfoGraph = agendas
                        },
                MeetingsDuration =
                    new Graph
                    {
                        Total = totalDuration.Sum(d => d.Count),
                        InfoGraph = totalDuration
                    },
                Resolutions =
                    new Graph
                    {
                        Total = resolutions.Sum(r => r.Count),
                        InfoGraph = resolutions
                    },
                BluebookViews =
                    new Graph
                    {
                        Total = bbViews.Where(bb => bb.Status == "TOTAL_VIEW").Sum(bb => bb.Count),
                        InfoGraph = bbViews
                    },
                MaterialViews =
                    new Graph
                    {
                        Total = materialViews.Where(bb => bb.Status == "TOTAL_VIEW").Sum(m => m.Count),
                        InfoGraph = materialViews
                    },
                Comments =
                    new Graph
                    {
                        Total = comments.Sum(c => c.Count),
                        InfoGraph = comments
                    },
                MinutesStatus =
                    new Graph
                    {
                        Total = minutes.Sum(m => m.Count),
                        InfoGraph = minutes
                    }
            };

            return dashboardGraph;

        }

        public async Task<byte[]> GenerateInsightsReportAsync(InsightsFilters filters)
        {
            if (!await ValidateFilters(filters)) { return null; }

            var meetings = await _repo.GetReportInsightsMeeting(filters.WorkgroupId, filters.StartDate, filters.EndDate);
            var agendasOrResolutions = await _repo.GetReportResolutionsAndAgendas(filters.WorkgroupId, filters.StartDate, filters.EndDate);
            var blueBooks = await _repo.GetReportBlueBooks(filters.WorkgroupId, filters.StartDate, filters.EndDate);
            var materials = await _repo.GetReportMaterials(filters.WorkgroupId, filters.StartDate, filters.EndDate);
            var comments = await _repo.GetReportComments(filters.WorkgroupId, filters.StartDate, filters.EndDate);

            var insightsReportData = new InsightsReportDataViewModel
            {
                Meetings = meetings,
                AgendasOrResolutions = agendasOrResolutions,
                BlueBooks = blueBooks,
                Materials = materials,
                Comments = comments,
            };

            var exportService = new ExportService();
            exportService.SetCurrentUser(_currentUser);

            var reportFile = await exportService.ExportInsightsReportAsync(insightsReportData);

            if (reportFile != null)
            {
                var workGroups = await _repo.GetWorkgroupId(filters.WorkgroupId);
                var wgGroupedByClient = workGroups
                        .GroupBy(wg => wg.clientId)
                        .Select(group => new
                        {
                            ClientId = group.Key,
                            WorkgroupIds = group.Select(wg => wg.workgroupId).ToList()
                        });

                var tasks = wgGroupedByClient.Select(async wg =>
                {
                    return await ActivityService.AddAsync(Operations.INSIGHTS_REPORT_DOWNLOAD, wg.ClientId, null, _currentUser, $"workgroups: {string.Join(",", wg.WorkgroupIds)}");
                });

                await Task.WhenAll(tasks);
            }

            return reportFile;
        }
    }
}
