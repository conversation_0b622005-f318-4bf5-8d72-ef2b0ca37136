using Atlas.Business.ViewModels;
using Atlas.Data.Abstract;
using Atlas.Data.Entities;
using Atlas.Data.Enums;
using Atlas.Data.Repository;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using Microsoft.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using Atlas.CrossCutting.DTO.UserInboxItem;

namespace Atlas.Business
{
    /// <summary>
    /// Provides InboxItem service dedicated to current user's scope
    /// </summary>
    public class UserInboxItemService : IUserInboxItemService
    {
        private readonly int _currentUserId;
        private readonly IRepository<InboxItemReceiver> _receiverRepository;
        private readonly InboxItemService _inboxItemService;
        private readonly UserRepository _userRepository;

        /// <summary>
        /// Creates a new instance of UserInboxItemService.
        /// </summary>
        /// <param name="currentUserId">The current user Id. All related operations will be done with this user</param>
        /// <param name="itemRep">InboxItem repository instance</param>
        /// <param name="receiverRep">InboxItemReceiver repository instance</param>
        /// <param name="userRepository">UserRepository repository instance</param>
        /// <param name="userDeviceService">UserDeviceService service instance</param>
        public UserInboxItemService(int currentUserId, IRepository<InboxItemReceiver> receiverRep,
            InboxItemService inboxItemService, UserRepository userRepository)
        {
            _currentUserId = currentUserId;
            _receiverRepository = receiverRep;
            _inboxItemService = inboxItemService;
            _userRepository = userRepository;
        }
        /// <summary>
        /// Creates a new instance of UserInboxItemService.
        /// </summary>
        /// <param name="userId">The current user Id. All related operations will be done with this user</param>
        public UserInboxItemService(int userId)
        {
            _currentUserId = userId;
            _receiverRepository = new InboxItemReceiverRepository(new AtlasModelCore());
            _inboxItemService = new InboxItemService();
            _userRepository = new UserRepository();
        }
        /// <summary>
        /// Gets all InboxItem objects from the current user.
        /// </summary>
        /// <returns>An InboxItem collection</returns>
        public async Task<IEnumerable<InboxItem>> GetUserInboxItems()
        {
            var inboxItems = (await _receiverRepository.GetAllAsync(x => x.receiverUserId == _currentUserId, null, "InboxItem")).Select(x => x.InboxItem);
            return inboxItems;
        }
        /// <summary>
        /// Excludes the current user's receiver entry attached to the given InboxItem. 
        /// This InboxItem will no longer be visible in the user's Inbox.
        /// </summary>
        /// <param name="inboxItemId">The Id of the InboxItem which will be dismissed</param>
        /// <returns>True if operation succeeds, false if operation fails</returns>
        public async Task<bool> ReceiverDismissInboxItem(Guid inboxItemId)
        {
            var inboxItemReceiver = (await _receiverRepository.GetAllAsync(x => x.inboxItemId == inboxItemId && x.receiverUserId == _currentUserId)).FirstOrDefault();
            if (inboxItemReceiver != null)
            {
                try
                {
                    _receiverRepository.Delete(inboxItemReceiver.inboxItemReceiverId);
                    await _receiverRepository.CommitAsync();

                    var uc = new UserClientService();
                    var clientsByUser = uc.ClientsByUser(_currentUserId);
                    ActivityService.Add("INBOX_ITEM_DISMISS", clientsByUser, _currentUserId, $"InboxItemId: {inboxItemReceiver.inboxItemId.ToString()}");
                    
                    if (await _inboxItemService.CheckInboxItemWithNoReceiver(inboxItemId))
                    {
                        await _inboxItemService.DeleteInboxItemAsync(inboxItemId);
                    }
                   
                    return true;
                }
                catch (Exception)
                {

                    return false;
                }
            }
            return false;
        }
        public async Task<bool> GenerateAllAdminUserInboxItems()
        {
            var user = _userRepository.GetProfile(_currentUserId);
            if (user == null || user.builtInboxItemHistory == true)
            {
                return false;
            }

            try
            {
                await BuildAdminPendingAccessUserBlockedItems();
                await BuildAdminPendingAccessUserApprovalItems();
                await BuildAdminPendingAccessDeviceApprovalItems();
                var result = _userRepository.SetUserBuiltInboxItemHistory(_currentUserId, true);
                return result;
            }
            catch (Exception)
            {

                return false;
            }

        }
        public async Task<bool> GenerateClientAdminUserInboxItems(int clientId)
        {
            try
            {
                await BuildAdminPendingAccessUserBlockedItems(clientId);
                await BuildAdminPendingAccessUserApprovalItems(clientId);
                await BuildAdminPendingAccessDeviceApprovalItems(clientId);
            }
            catch (Exception)
            {

                return false;
            }
            
            return true;
        }
        public async Task<bool> RemoveClientAdminUserInboxItems(int clientId)
        {
            try
            {
                await RemoveAdminPendingAccessUserBlockedItems(clientId);
                await RemoveAdminPendingAccessUserApprovalItems(clientId);
                await RemoveAdminPendingAccessDeviceApprovalItems(clientId);
            }
            catch (Exception)
            {

                return false;
            }

            return true;
        }

        public async Task<bool> RemoveClientAdminUserInboxItems(InboxAdminOperationRequest request)
        {
            try
            {
                if (request.blockedUsersItems != null
                    && request.blockedUsersItems.Any()
                    && request.clientBlockedUsersItems != null
                    && request.clientBlockedUsersItems.Any())
                {
                    await RemoveAdminPendingAccessUserBlockedItems(request);
                }
                if (request.pendingAccessItems != null
                    && request.pendingAccessItems.Any()
                    && request.clientPendingAccessItems != null
                    && request.clientPendingAccessItems.Any())
                {
                    await RemoveAdminPendingAccessUserApprovalItems(request);
                }
                if (request.deviceApprovalItems != null
                    && request.deviceApprovalItems.Any()
                    && request.clientDeviceApprovalItems != null
                    && request.clientDeviceApprovalItems.Any())
                {
                    await RemoveAdminPendingAccessDeviceApprovalItems(request);
                }
            }
            catch (Exception)
            {
                throw;
            }
            return true;
        }

        public async Task<bool> BuildAdminPendingAccessUserBlockedItems()
        {
            List<InboxOutboxViewModel> pendingUsers = await GetAllBlockedUsers();

            // If the user is blocked and it is multiclient, we will only show one pendency (the first one available)
            var totalPendingUsers = pendingUsers.GroupBy(pu => pu.pendingAccessUserId).Select(pu => pu.First()).ToList();
            foreach (var item in totalPendingUsers)
            {
                await _inboxItemService.CreatePendingAccessUserBlockedAsync(item.pendingAccessUserId, _currentUserId);
            }
            return true;
        }
        public async Task<bool> BuildAdminPendingAccessUserBlockedItems(int clientId)
        {
            List<InboxOutboxViewModel> pendingUsers = await GetClientBlockedUsers(clientId);

            // If the user is blocked and it is multiclient, we will only show one pendency (the first one available)
            var totalPendingUsers = pendingUsers.GroupBy(pu => pu.pendingAccessUserId).Select(pu => pu.First()).ToList();
            foreach (var item in totalPendingUsers)
            {
                await _inboxItemService.CreatePendingAccessUserBlockedAsync(item.pendingAccessUserId, _currentUserId);
            }
            return true;
        }
        public async Task<bool> RemoveAdminPendingAccessUserBlockedItems(int clientId)
        {
            //Get all blocked user inbox items, to compare with specific client items and find duplicated
            var allItems = await GetAllBlockedUsers();
            var clientItems = await GetClientBlockedUsers(clientId);
            // Initializes a list to add itens to be removed
            var itensToRemove = new List<InboxOutboxViewModel>();

            foreach (var clientItem in clientItems)
            {
                //Find duplicated items, wich means the blocked user belongs to more than one client where the current user is an admin, so this item can't be removed this time
                var duplicatedItem = allItems.Where(x => x.pendingAccessUserId == clientItem.pendingAccessUserId && x.type == clientItem.type && x.pendingAccessBlocked == clientItem.pendingAccessBlocked
                && x.pendingAccessClientId != clientItem.pendingAccessClientId);
                //Adds the NOT duplicated item to the items to remove list (wich means this item only exists in the given clientId, so it needs to be removed
                if (!duplicatedItem.Any())
                {
                    itensToRemove.Add(clientItem);
                }
            }
            //Remove each one of the items from InboxItemReceiver table
            foreach (var itemToRemove in itensToRemove)
            {
                await _inboxItemService.FindAndRemoveInboxItemReceiver(InboxItemType.PendingAccessUserBlocked, InboxItemTargetObjectType.User, 
                    itemToRemove.pendingAccessUserId.ToString(), _currentUserId);
            }

            return true;
        }

        public async Task<bool> RemoveAdminPendingAccessUserBlockedItems(InboxAdminOperationRequest request)
        {
            //Get all blocked user inbox items, to compare with specific client items and find duplicated
            var allItems = request.blockedUsersItems;
            var clientItems = request.clientBlockedUsersItems;
            // Initializes a list to add itens to be removed
            var itensToRemove = new List<InboxOutboxViewModel>();
            foreach (var clientItem in clientItems)
            {
                //Find duplicated items, wich means the blocked user belongs to more than one client where the current user is an admin, so this item can't be removed this time
                var duplicatedItem = allItems.Where(x => x.pendingAccessUserId == clientItem.pendingAccessUserId && x.type == clientItem.type && x.pendingAccessBlocked == clientItem.pendingAccessBlocked
                && x.pendingAccessClientId != clientItem.pendingAccessClientId);
                //Adds the NOT duplicated item to the items to remove list (wich means this item only exists in the given clientId, so it needs to be removed
                if (!duplicatedItem.Any())
                {
                    itensToRemove.Add(clientItem);
                }
            }
            //Remove each one of the items from InboxItemReceiver table
            foreach (var itemToRemove in itensToRemove)
            {
                await _inboxItemService.FindAndRemoveInboxItemReceiver(InboxItemType.PendingAccessUserBlocked, InboxItemTargetObjectType.User,
                    itemToRemove.pendingAccessUserId.ToString(), _currentUserId);
            }
            return true;
        }

        [Obsolete("This method tries to materialize the entities directly into a viewmodel. This can and will lead you to property/column mismatch errors")]
        private async Task<List<InboxOutboxViewModel>> GetAllBlockedUsers()
        {
            AtlasModelCore _md = new AtlasModelCore();
            // Retrieve the list of blocked users
            string sql = @"
                SELECT DISTINCT
                    UR.clientId 'pendingAccessClientId',
                    U.userId 'pendingAccessUserId',
                    U.name 'pendingAccessUserName',
                    U.email 'pendingAccessEmail',
                    U.blocked 'pendingAccessBlocked',
                    U.profilePic 'pendingAccessProfilePic',
                    U.requireApproval 'pendingAccessRequireApproval',
                    'PendingUserAccess' as type,
                    C.name 'clientName'
                FROM [UserRole] UR
                JOIN [role] R on (R.roleId = UR.roleId)
                JOIN [user] U on (U.userId = UR.userId)
                JOIN [Client] C ON (C.clientId = UR.clientId)
                WHERE R.roleId = 1002 and UR.clientId IN (
                 SELECT UR2.clientid from [UserRole] UR2
                 JOIN [role] R2 on (R2.roleId = UR2.roleId)
                 WHERE UR2.userId = @admin AND R2.roleId = 1004
                ) AND U.deleted = 0 
                  AND (U.blocked = 1)
                ORDER BY U.blocked desc, U.name";

            var parameters = new[]
            {
                    new SqlParameter("@admin", SqlDbType.Int) { Value = _currentUserId }
                };

            var pendingUsers = await _md.Database.SqlQueryRaw<InboxOutboxViewModel>(sql, parameters).ToListAsync();
            return pendingUsers;
        }

        private async Task<List<ClientSimpleUserDto>> GetAllBlockedUsersData()
        {
            AtlasModelCore _md = new AtlasModelCore();
            // Retrieve the list of blocked users
            string sql = @"
                DECLARE @AdminClients TABLE (
                    clientId INT PRIMARY KEY
                );

                INSERT INTO @AdminClients (clientId)
                SELECT DISTINCT UR.clientid 
                FROM [UserRole] UR
                WHERE UR.userId = @admin 
                  AND UR.roleId = 1004;

                SELECT DISTINCT
                    UR.clientId AS 'ClientId',
                    U.userId AS 'UserId',
                    U.name AS 'UserName',
                    U.email AS 'Email',
                    U.blocked AS 'IsBlocked',
                    U.profilePic AS 'ProfilePicUrl',
                    U.requireApproval AS 'RequireApproval',
                    'PendingUserAccess' AS Type,
                    C.name AS 'ClientName'
                FROM [UserRole] UR
                JOIN [user] U ON (U.userId = UR.userId)
                JOIN [Client] C ON (C.clientId = UR.clientId)
                JOIN @AdminClients AC ON UR.clientId = AC.clientId
                WHERE UR.roleId = 1002 
                    AND U.deleted = 0 
                    AND U.blocked = 1
                ORDER BY U.blocked DESC, U.name;";

            var parameters = new[]
            {
                    new SqlParameter("@admin", SqlDbType.Int) { Value = _currentUserId }
                };

            var pendingUsers = await _md.Database.SqlQueryRaw<ClientSimpleUserDto>(sql, parameters).ToListAsync();
            return pendingUsers;
        }

        [Obsolete("This method tries to materialize the entities directly into a viewmodel. This can and will lead you to property/column mismatch errors")]
        private async Task<List<InboxOutboxViewModel>> GetClientBlockedUsers(int clientId)
        {
            AtlasModelCore _md = new AtlasModelCore();
            // Retrieve the list of blocked users
            string sql = @"
                SELECT DISTINCT
                    UR.clientId 'pendingAccessClientId',
                    U.userId 'pendingAccessUserId',
                    U.name 'pendingAccessUserName',
                    U.email 'pendingAccessEmail',
                    U.blocked 'pendingAccessBlocked',
                    U.profilePic 'pendingAccessProfilePic',
                    U.requireApproval 'pendingAccessRequireApproval',
                    'PendingUserAccess' as type,
                    C.name 'clientName'
                FROM [UserRole] UR
                JOIN [role] R on (R.roleId = UR.roleId)
                JOIN [user] U on (U.userId = UR.userId)
                JOIN [Client] C ON (C.clientId = UR.clientId)
                WHERE R.roleId = 1002 and UR.clientId IN (
                 SELECT UR2.clientid from [UserRole] UR2
                 JOIN [role] R2 on (R2.roleId = UR2.roleId)
                 WHERE UR2.userId = @admin AND R2.roleId = 1004 AND UR.clientid = @clientId
                ) AND U.deleted = 0 
                  AND (U.blocked = 1)
                ORDER BY U.blocked desc, U.name";

            var parameters = new[]
            {
                    new SqlParameter("@admin", SqlDbType.Int) { Value = _currentUserId },
                    new SqlParameter("@clientId", SqlDbType.Int) { Value = clientId }
                };

            var pendingUsers = await _md.Database.SqlQueryRaw<InboxOutboxViewModel>(sql, parameters).ToListAsync();
            return pendingUsers;
        }

        public async Task<bool> BuildAdminPendingAccessUserApprovalItems()
        {
            List<InboxOutboxViewModel> pendingUsers = await GetAllApprovalUsers();

            // If the user is blocked and it is multiclient, we will only show one pendency (the first one available)
            var totalPendingUsers = pendingUsers.GroupBy(pu => pu.pendingAccessUserId).Select(pu => pu.First()).ToList();
            foreach (var item in totalPendingUsers)
            {
                await _inboxItemService.CreatePendingAccessUserApprovalAsync(item.pendingAccessUserId, _currentUserId);
            }
            return true;
        }
        public async Task<bool> BuildAdminPendingAccessUserApprovalItems(int clientId)
        {
            List<InboxOutboxViewModel> pendingUsers = await GetClientApprovalUsers(clientId);

            // If the user is blocked and it is multiclient, we will only show one pendency (the first one available)
            var totalPendingUsers = pendingUsers.GroupBy(pu => pu.pendingAccessUserId).Select(pu => pu.First()).ToList();
            foreach (var item in totalPendingUsers)
            {
                await _inboxItemService.CreatePendingAccessUserApprovalAsync(item.pendingAccessUserId, _currentUserId);
            }
            return true;
        }
        public async Task<bool> RemoveAdminPendingAccessUserApprovalItems(int clientId)
        {
            //Get all approval user inbox items, to compare with specific client items and find duplicated
            var allItems = await GetAllApprovalUsers();
            var clientItems = await GetClientApprovalUsers(clientId);
            // Initializes a list to add itens to be removed
            var itensToRemove = new List<InboxOutboxViewModel>();

            foreach (var clientItem in clientItems)
            {
                //Find duplicated items, wich means the pending user belongs to more than one client where the current user is an admin, so this item can't be removed this time
                var duplicatedItem = allItems.Where(x => x.pendingAccessUserId == clientItem.pendingAccessUserId && x.type == clientItem.type && x.pendingAccessRequireApproval == clientItem.pendingAccessRequireApproval
                && x.pendingAccessClientId != clientItem.pendingAccessClientId);
                //Adds the NOT duplicated item to the items to remove list (wich means this item only exists in the given clientId, so it needs to be removed
                if (!duplicatedItem.Any())
                {
                    itensToRemove.Add(clientItem);
                }
            }
            //Remove each one of the items from InboxItemReceiver table
            foreach (var itemToRemove in itensToRemove)
            {
                await _inboxItemService.FindAndRemoveInboxItemReceiver(InboxItemType.PendingAccessUserApproval, InboxItemTargetObjectType.User, 
                    itemToRemove.pendingAccessUserId.ToString(), _currentUserId);
            }

            return true;
        }

        public async Task<bool> RemoveAdminPendingAccessUserApprovalItems(InboxAdminOperationRequest request)
        {
            //Get all approval user inbox items, to compare with specific client items and find duplicated
            var allItems = request.pendingAccessItems;
            var clientItems = request.clientPendingAccessItems;
            // Initializes a list to add itens to be removed
            var itensToRemove = new List<InboxOutboxViewModel>();
            foreach (var clientItem in clientItems)
            {
                //Find duplicated items, wich means the pending user belongs to more than one client where the current user is an admin, so this item can't be removed this time
                var duplicatedItem = allItems.Where(x => x.pendingAccessUserId == clientItem.pendingAccessUserId && x.type == clientItem.type && x.pendingAccessRequireApproval == clientItem.pendingAccessRequireApproval
                && x.pendingAccessClientId != clientItem.pendingAccessClientId);
                //Adds the NOT duplicated item to the items to remove list (wich means this item only exists in the given clientId, so it needs to be removed
                if (!duplicatedItem.Any())
                {
                    itensToRemove.Add(clientItem);
                }
            }
            //Remove each one of the items from InboxItemReceiver table
            foreach (var itemToRemove in itensToRemove)
            {
                await _inboxItemService.FindAndRemoveInboxItemReceiver(InboxItemType.PendingAccessUserApproval, InboxItemTargetObjectType.User,
                    itemToRemove.pendingAccessUserId.ToString(), _currentUserId);
            }
            return true;
        }

        [Obsolete("This method tries to materialize the entities directly into a viewmodel. This can and will lead you to property/column mismatch errors")]
        private async Task<List<InboxOutboxViewModel>> GetAllApprovalUsers()
        {
            AtlasModelCore _md = new AtlasModelCore();
            // Retrieve the list of pending approval users
            string sql = @"
                SELECT DISTINCT
                    UR.clientId 'pendingAccessClientId',
                    U.userId 'pendingAccessUserId',
                    U.name 'pendingAccessUserName',
                    U.email 'pendingAccessEmail',
                    U.blocked 'pendingAccessBlocked',
                    U.profilePic 'pendingAccessProfilePic',
                    U.requireApproval 'pendingAccessRequireApproval',
                    'PendingUserAccess' as type,
                    C.name 'clientName'
                FROM [UserRole] UR
                JOIN [role] R on (R.roleId = UR.roleId)
                JOIN [user] U on (U.userId = UR.userId)
                JOIN [Client] C ON (C.clientId = UR.clientId)
                WHERE R.roleId = 1002 and UR.clientId IN (
                 SELECT UR2.clientid from [UserRole] UR2
                 JOIN [role] R2 on (R2.roleId = UR2.roleId)
                 WHERE UR2.userId = @admin AND R2.roleId = 1004
                ) AND U.deleted = 0 
                  AND (U.requireApproval = 1)
                ORDER BY U.requireApproval desc, U.name";

            var parameters = new[]
            {
                    new SqlParameter("@admin", SqlDbType.Int) { Value = _currentUserId }
                };

            var pendingUsers = await _md.Database.SqlQueryRaw<InboxOutboxViewModel>(sql, parameters).ToListAsync();
            return pendingUsers;
        }

        private async Task<List<ClientSimpleUserDto>> GetAllApprovalUsersData()
        {
            AtlasModelCore _md = new AtlasModelCore();
            // Retrieve the list of pending approval users
            string sql = @"
                DECLARE @AdminClients TABLE (
                    clientId INT PRIMARY KEY
                );

                INSERT INTO @AdminClients (clientId)
                SELECT DISTINCT UR.clientid 
                FROM [UserRole] UR
                WHERE UR.userId = @admin 
                  AND UR.roleId = 1004;

                SELECT DISTINCT
                    UR.clientId AS 'ClientId',
                    U.userId AS 'UserId',
                    U.name AS 'UserName',
                    U.email AS 'Email',
                    U.blocked AS 'IsBlocked',
                    U.profilePic AS 'ProfilePicUrl',
                    U.requireApproval AS 'RequireApproval',
                    'PendingUserAccess' AS Type,
                    C.name AS 'ClientName'
                FROM [UserRole] UR
                JOIN [user] U ON (U.userId = UR.userId)
                JOIN [Client] C ON (C.clientId = UR.clientId)
                JOIN @AdminClients AC ON UR.clientId = AC.clientid
                WHERE UR.roleId = 1002
                  AND U.deleted = 0
                  AND (U.requireApproval = 1)
                ORDER BY U.requireApproval DESC, U.name";

            var parameters = new[]
            {
                    new SqlParameter("@admin", SqlDbType.Int) { Value = _currentUserId }
                };

            var pendingUsers = await _md.Database.SqlQueryRaw<ClientSimpleUserDto>(sql, parameters).ToListAsync();
            return pendingUsers;
        }

        [Obsolete("This method tries to materialize the entities directly into a viewmodel. This can and will lead you to property/column mismatch errors")]
        private async Task<List<InboxOutboxViewModel>> GetClientApprovalUsers(int clientId)
        {
            AtlasModelCore _md = new AtlasModelCore();
            // Retrieve the list of pending approval users
            string sql = @"
                SELECT DISTINCT
                    UR.clientId 'pendingAccessClientId',
                    U.userId 'pendingAccessUserId',
                    U.name 'pendingAccessUserName',
                    U.email 'pendingAccessEmail',
                    U.blocked 'pendingAccessBlocked',
                    U.profilePic 'pendingAccessProfilePic',
                    U.requireApproval 'pendingAccessRequireApproval',
                    'PendingUserAccess' as type,
                    C.name 'clientName'
                FROM [UserRole] UR
                JOIN [role] R on (R.roleId = UR.roleId)
                JOIN [user] U on (U.userId = UR.userId)
                JOIN [Client] C ON (C.clientId = UR.clientId)
                WHERE R.roleId = 1002 and UR.clientId IN (
                 SELECT UR2.clientid from [UserRole] UR2
                 JOIN [role] R2 on (R2.roleId = UR2.roleId)
                 WHERE UR2.userId = @admin AND R2.roleId = 1004 AND UR2.clientId = @clientId
                ) AND U.deleted = 0 
                  AND (U.requireApproval = 1)
                ORDER BY U.requireApproval desc, U.name";

            var parameters = new[]
            {
                    new SqlParameter("@admin", SqlDbType.Int) { Value = _currentUserId },
                    new SqlParameter("@clientId", SqlDbType.Int) { Value = clientId }
                };

            var pendingUsers = await _md.Database.SqlQueryRaw<InboxOutboxViewModel>(sql, parameters).ToListAsync();
            return pendingUsers;
        }

        public async Task<bool> BuildAdminPendingAccessDeviceApprovalItems()
        {
            List<InboxOutboxViewModel> totalPendingDevices = await GetAllApprovalDevices();

            foreach (var item in totalPendingDevices)
            {
                await _inboxItemService.CreatePendingAccessDeviceApprovalAsync(item.pendingAccessDeviceId, _currentUserId);
            }
            return true;

        }
        public async Task<bool> BuildAdminPendingAccessDeviceApprovalItems(int clientId)
        {
            List<InboxOutboxViewModel> totalPendingDevices = await GetClientApprovalDevices(clientId);

            foreach (var item in totalPendingDevices)
            {
                await _inboxItemService.CreatePendingAccessDeviceApprovalAsync(item.pendingAccessDeviceId, _currentUserId);
            }
            return true;

        }
        public async Task<bool> RemoveAdminPendingAccessDeviceApprovalItems(int clientId)
        {
            //Get all approval device inbox items, to compare with specific client items and find duplicated
            var allItems = await GetAllApprovalDevices();
            var clientItems = await GetClientApprovalDevices(clientId);
            // Initializes a list to add itens to be removed
            var itensToRemove = new List<InboxOutboxViewModel>();

            foreach (var clientItem in clientItems)
            {
                //Find duplicated items, wich means the pending device user belongs to more than one client where the current user is an admin, so this item can't be removed this time
                var duplicatedItem = allItems.Where(x => x.pendingAccessDeviceId == clientItem.pendingAccessDeviceId && x.type == clientItem.type && x.pendingAccessClientId != clientItem.pendingAccessClientId);
                //Adds the NOT duplicated item to the items to remove list (wich means this item only exists in the given clientId, so it needs to be removed
                if (!duplicatedItem.Any())
                {
                    itensToRemove.Add(clientItem);
                }
            }
            //Remove each one of the items from InboxItemReceiver table
            foreach (var itemToRemove in itensToRemove)
            {
                await _inboxItemService.FindAndRemoveInboxItemReceiver(InboxItemType.PendingAccessDeviceApproval, InboxItemTargetObjectType.Device, 
                    itemToRemove.pendingAccessDeviceId.ToString(), _currentUserId);
            }

            return true;
        }

        public async Task<bool> RemoveAdminPendingAccessDeviceApprovalItems(InboxAdminOperationRequest request)
        {
            //Get all approval device inbox items, to compare with specific client items and find duplicated
            var allItems = request.deviceApprovalItems;
            var clientItems = request.clientDeviceApprovalItems;
            // Initializes a list to add itens to be removed
            var itensToRemove = new List<InboxOutboxViewModel>();
            foreach (var clientItem in clientItems)
            {
                //Find duplicated items, wich means the pending device user belongs to more than one client where the current user is an admin, so this item can't be removed this time
                var duplicatedItem = allItems.Where(x => x.pendingAccessDeviceId == clientItem.pendingAccessDeviceId && x.type == clientItem.type && x.pendingAccessClientId != clientItem.pendingAccessClientId);
                //Adds the NOT duplicated item to the items to remove list (wich means this item only exists in the given clientId, so it needs to be removed
                if (!duplicatedItem.Any())
                {
                    itensToRemove.Add(clientItem);
                }
            }
            //Remove each one of the items from InboxItemReceiver table
            foreach (var itemToRemove in itensToRemove)
            {
                await _inboxItemService.FindAndRemoveInboxItemReceiver(InboxItemType.PendingAccessDeviceApproval, InboxItemTargetObjectType.Device,
                    itemToRemove.pendingAccessDeviceId.ToString(), _currentUserId);
            }
            return true;
        }

        [Obsolete("This method tries to materialize the entities directly into a viewmodel. This can and will lead you to property/column mismatch errors")]
        private async Task<List<InboxOutboxViewModel>> GetAllApprovalDevices()
        {
            AtlasModelCore _md = new AtlasModelCore();
            string sql = @"
                SELECT DISTINCT
                    UD.deviceEntryId 'pendingAccessDeviceId',
                    UD.userid 'pendingAccessUserId',
                    U.name 'pendingAccessUserName',
                    UD.name 'pendingAccessDeviceName',
                    UD.date 'pendingAccessDate',
                    UR.clientId 'pendingAccessClientId',
                    'PendingDeviceAccess' as type,
                    C.name 'clientName',
                    C.planName,
                    PF.featureEnabled,
                    PF.featureName,
                    PF.planName
                FROM [UserRole] UR
                JOIN [Role] R ON (R.roleId = UR.roleId)
                JOIN [User] U on (U.userId = UR.userId)
                JOIN [Client] C ON (C.clientId = UR.clientId)
                JOIN [UserDevice] UD ON (UD.userId = UR.userId) 
                JOIN [PlanFeature] PF ON (PF.planName = C.planName)
                WHERE R.roleId = 1002 and UR.clientId IN (
                 SELECT UR2.clientid from [UserRole] UR2
                 JOIN [role] R2 on (R2.roleId = UR2.roleId)
                 WHERE UR2.userId = @admin AND R2.roleId = 1004
                ) AND UD.fingerprint IS NOT NULL AND UD.status = 'AWAITING_APPROVAL'
                  AND C.deviceApprovalAddOn = 1 AND C.deviceApprovalEnabled = 1

                ORDER BY UD.date desc";

            var parameters = new[]
                {
                    new SqlParameter("@admin", SqlDbType.Int) { Value = _currentUserId }
            };


            var totalPendingDevices = await _md.Database.SqlQueryRaw<InboxOutboxViewModel>(sql, parameters).ToListAsync();

            var result = totalPendingDevices.GroupBy(pu => new { pu.pendingAccessDeviceId, pu.pendingAccessClientId }).Select(pu => pu.First()).ToList();
            return result;
        }

        private async Task<List<PendingApprovalDeviceDto>> GetAllApprovalDevicesData()
        {
            AtlasModelCore _md = new AtlasModelCore();
            string sql = @"
                DECLARE @AdminClients TABLE (
                    clientId INT PRIMARY KEY
                );

                INSERT INTO @AdminClients (clientId)
                SELECT DISTINCT UR.clientid 
                FROM [UserRole] UR
                WHERE UR.userId = @admin 
                  AND UR.roleId = 1004;

                SELECT DISTINCT
                   UD.deviceEntryId AS 'DeviceId',
                   UD.userid AS 'UserId',
                   U.name AS 'UserName',
                   UD.name AS 'DeviceName',
                   UD.date AS 'RequestDate',
                   UR.clientId AS 'ClientId',
                   'PendingDeviceAccess' AS Type,
                   C.name AS 'ClientName',
                   C.planName AS 'ClientPlanName'
                FROM [UserRole] UR
                JOIN [User] U ON (U.userId = UR.userId)
                JOIN [Client] C ON (C.clientId = UR.clientId)
                JOIN [UserDevice] UD ON (UD.userId = UR.userId)
                JOIN @AdminClients AC ON UR.clientId = AC.clientid
                WHERE UR.roleId = 1002
                  AND UD.fingerprint IS NOT NULL
                  AND UD.status = 'AWAITING_APPROVAL'
                  AND C.deviceApprovalAddOn = 1
                  AND C.deviceApprovalEnabled = 1
                ORDER BY UD.date DESC;";

            var parameters = new[]
                {
                    new SqlParameter("@admin", SqlDbType.Int) { Value = _currentUserId }
            };

            var totalPendingDevices = await _md.Database.SqlQueryRaw<PendingApprovalDeviceDto>(sql, parameters).ToListAsync();

            var result = totalPendingDevices.GroupBy(pu => new { pu.DeviceId, pu.ClientId }).Select(pu => pu.First()).ToList();
            return result;
        }

        [Obsolete("This method tries to materialize the entities directly into a viewmodel. This can and will lead you to property/column mismatch errors")]
        private async Task<List<InboxOutboxViewModel>> GetClientApprovalDevices(int clientId)
        {
            AtlasModelCore _md = new AtlasModelCore();
            string sql = @"
                SELECT DISTINCT
                    UD.deviceEntryId 'pendingAccessDeviceId',
                    UD.userid 'pendingAccessUserId',
                    U.name 'pendingAccessUserName',
                    UD.name 'pendingAccessDeviceName',
                    UD.date 'pendingAccessDate',
                    UR.clientId 'pendingAccessClientId',
                    'PendingDeviceAccess' as type,
                    C.name 'clientName',
                    C.planName,
                    PF.featureEnabled,
                    PF.featureName,
                    PF.planName
                FROM [UserRole] UR
                JOIN [Role] R ON (R.roleId = UR.roleId)
                JOIN [User] U on (U.userId = UR.userId)
                JOIN [Client] C ON (C.clientId = UR.clientId)
                JOIN [UserDevice] UD ON (UD.userId = UR.userId) 
                JOIN [PlanFeature] PF ON (PF.planName = C.planName)
                WHERE R.roleId = 1002 and UR.clientId IN (
                 SELECT UR2.clientid from [UserRole] UR2
                 JOIN [role] R2 on (R2.roleId = UR2.roleId)
                 WHERE UR2.userId = @admin AND R2.roleId = 1004 AND UR2.clientId = @clientId
                ) AND UD.fingerprint IS NOT NULL AND UD.status = 'AWAITING_APPROVAL'
                  AND C.deviceApprovalAddOn = 1 AND C.deviceApprovalEnabled = 1

                ORDER BY UD.date desc";

            var parameters = new[]
                {
                    new SqlParameter("@admin", SqlDbType.Int) { Value = _currentUserId },
                    new SqlParameter("@clientId", SqlDbType.Int) { Value = clientId }
            };

            var totalPendingDevices = await _md.Database.SqlQueryRaw<InboxOutboxViewModel>(sql, parameters).ToListAsync();
            var result = totalPendingDevices.GroupBy(pu => new { pu.pendingAccessDeviceId, pu.pendingAccessClientId }).Select(pu => pu.First()).ToList();
            return result;
        }

        public async Task<InboxAdminOperationRequest> GetInboxAdminOperationRequestData(int clientId)
        {
            var blockedUsersData = await this.GetAllBlockedUsersData();
            var clientBlockedUsersData = blockedUsersData.Where(x => x.ClientId == clientId).ToList();
            var pendingApprovalUsersData = await this.GetAllApprovalUsersData();
            var clientPendingApprovalUsersData = pendingApprovalUsersData.Where(x => x.ClientId == clientId).ToList();
            var pendingDeviceApprovalData = await this.GetAllApprovalDevicesData();
            var clientPendingDeviceApprovalData = pendingDeviceApprovalData.Where(x => x.ClientId == clientId).ToList();

            var blockedUsersItems = ConvertBlockedUsersToViewModel(blockedUsersData);
            var clientBlockedUsersItems = ConvertBlockedUsersToViewModel(clientBlockedUsersData);
            var pendingAccessItems = ConvertPendingApprovalUsersToViewModel(pendingApprovalUsersData);
            var clientPendingAccessItems = ConvertPendingApprovalUsersToViewModel(clientPendingApprovalUsersData);
            var deviceApprovalItems = ConvertPendingApprovalDevicesToViewModel(pendingDeviceApprovalData);
            var clientDeviceApprovalItems = ConvertPendingApprovalDevicesToViewModel(clientPendingDeviceApprovalData);

            //I only did this because InboxAdminOperationRequest is expected on the caller, so I want to avoid further complications
            //Otherwise, a good thing to do is to get rid of it and to create a new proper dto for this method
            //That way we could avoid the need to convert the data to InboxOutboxViewModel
            return new InboxAdminOperationRequest()
            {
                clientId = clientId,
                userId = _currentUserId,
                blockedUsersItems = blockedUsersItems,
                clientBlockedUsersItems = clientBlockedUsersItems,
                deviceApprovalItems = deviceApprovalItems,
                clientDeviceApprovalItems = clientDeviceApprovalItems,
                pendingAccessItems = pendingAccessItems,
                clientPendingAccessItems = clientPendingAccessItems
            };
        }

        private static List<InboxOutboxViewModel> ConvertBlockedUsersToViewModel(List<ClientSimpleUserDto> users)
        {
            return users.Select(item => new InboxOutboxViewModel
            {
                pendingAccessClientId = item.ClientId,
                pendingAccessUserId = item.UserId,
                pendingAccessUserName = item.UserName,
                pendingAccessEmail = item.Email,
                pendingAccessBlocked = item.IsBlocked,
                pendingAccessProfilePic = item.ProfilePicUrl,
                pendingAccessRequireApproval = item.RequireApproval,
                type = item.Type,
                clientName = item.ClientName
            }).ToList();
        }

        private static List<InboxOutboxViewModel> ConvertPendingApprovalUsersToViewModel(List<ClientSimpleUserDto> users)
        {
            return users.Select(item => new InboxOutboxViewModel
            {
                pendingAccessClientId = item.ClientId,
                pendingAccessUserId = item.UserId,
                pendingAccessUserName = item.UserName,
                pendingAccessEmail = item.Email,
                pendingAccessBlocked = item.IsBlocked,
                pendingAccessProfilePic = item.ProfilePicUrl,
                pendingAccessRequireApproval = item.RequireApproval,
                type = item.Type,
                clientName = item.ClientName
            }).ToList();
        }

        private static List<InboxOutboxViewModel> ConvertPendingApprovalDevicesToViewModel(List<PendingApprovalDeviceDto> devices)
        {
            return devices.Select(item => new InboxOutboxViewModel
            {
                pendingAccessDeviceId = item.DeviceId,
                pendingAccessUserId = item.UserId,
                pendingAccessUserName = item.UserName,
                pendingAccessDeviceName = item.DeviceName,
                pendingAccessDate = item.RequestDate,
                pendingAccessClientId = item.ClientId,
                type = item.Type,
                clientName = item.ClientName,
                clientPlanName = item.ClientPlanName
            }).ToList();
        }
    }
}
