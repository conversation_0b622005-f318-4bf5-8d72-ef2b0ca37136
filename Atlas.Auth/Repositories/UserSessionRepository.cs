using Atlas.Auth.Data;
using Atlas.Auth.Models;
using Microsoft.EntityFrameworkCore;

namespace Atlas.Auth.Repositories;

public class UserSessionRepository : IUserSessionRepository
{
    private readonly ApplicationDbContext _context;
    private const int SESSION_LOCK_MINUTES = 15;
    private const int REFRESH_TOKEN_DAYS = 30;

    public UserSessionRepository(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<AspNetUserSessions?> GetByRefreshTokenAsync(string refreshToken)
    {
        return await _context.UserSessions
            .FirstOrDefaultAsync(s => 
                s.RefreshToken == refreshToken && 
                !s.Revoked && 
                s.RefreshTokenExpireDate > DateTime.UtcNow);
    }

    public async Task<AspNetUserSessions?> GetBySessionKeyAsync(string sessionKey)
    {
        return await _context.UserSessions
            .FirstOrDefaultAsync(s => s.SessionKey == sessionKey);
    }

    public async Task<AspNetUserSessions> CreateAsync(AspNetUserSessions session)
    {
        _context.UserSessions.Add(session);
        await _context.SaveChangesAsync();
        return session;
    }

    public async Task<AspNetUserSessions> UpdateAsync(AspNetUserSessions session)
    {
        _context.Entry(session).State = EntityState.Modified;
        await _context.SaveChangesAsync();
        return session;
    }

    public async Task<bool> RevokeAsync(string sessionKey, int revokeUserId)
    {
        var session = await GetBySessionKeyAsync(sessionKey);
        if (session == null) return false;

        session.Revoked = true;
        session.RevokeUser = revokeUserId;
        session.RevokeDate = DateTime.UtcNow;

        return await _context.SaveChangesAsync() > 0;
    }

    public async Task<bool> UpdateLastActivityAsync(string sessionKey)
    {
        var session = await GetBySessionKeyAsync(sessionKey);
        if (session == null) return false;

        var sessionLockDate = session.LastActivity?.AddMinutes(SESSION_LOCK_MINUTES);
        if (sessionLockDate.HasValue && DateTime.UtcNow >= sessionLockDate)
        {
            return false;
        }

        session.LastActivity = DateTime.UtcNow;
        return await _context.SaveChangesAsync() > 0;
    }

    public async Task<bool> IsLocationSuspiciousAsync(int userId, string newLocation, string newIp)
    {
        var lastSession = await _context.UserSessions
            .Where(s => s.UserId == userId && !s.Revoked)
            .OrderByDescending(s => s.LastActivity)
            .FirstOrDefaultAsync();

        if (lastSession == null) return false;

        return lastSession.LastIP != newIp && lastSession.LastLocation != newLocation;
    }

    public async Task<bool> IsSessionLockedAsync(int userId)
    {
        var sessions = await _context.UserSessions
            .Where(s => s.UserId == userId)
            .OrderByDescending(s => s.LastActivity)
            .ToListAsync();

        return sessions.Any(s => s.Revoked == true);
    }

    public async Task LockUserSessionsAsync(int userId)
    {
        var sessions = await _context.UserSessions
            .Where(s => s.UserId == userId && !s.Revoked)
            .ToListAsync();

        foreach (var session in sessions)
        {
            session.Revoked = true;
            session.RevokeDate = DateTime.UtcNow;
        }

        await _context.SaveChangesAsync();
    }

    public async Task UnlockUserSessionsAsync(int userId)
    {
        var sessions = await _context.UserSessions
            .Where(s => s.UserId == userId && s.Revoked)
            .ToListAsync();

        foreach (var session in sessions)
        {
            session.Revoked = false;
            session.RevokeDate = null;
        }

        await _context.SaveChangesAsync();
    }

    public async Task<AspNetUserSessions> GetByUserIdAsync(int userId)
    {
        return await _context.UserSessions
            .Where(s => s.UserId == userId && !s.Revoked)
            .OrderByDescending(s => s.LastActivity)
            .FirstOrDefaultAsync();
    }

    public async Task<bool> RevokeAllAsync(int userId)
    {
        var sessions = await _context.UserSessions
            .Where(s => s.UserId == userId && !s.Revoked)
            .ToListAsync();

        foreach (var session in sessions)
        {
            session.Revoked = true;
            session.RevokeDate = DateTime.UtcNow;
            session.RevokeUser = userId;
        }

        return await _context.SaveChangesAsync() > 0;
    }
}
