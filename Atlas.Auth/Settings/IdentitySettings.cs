using Atlas.Auth.Settings;

public class IdentitySettings
{
    public int DefaultLockoutTimeSpan { get; set; }

    public int MaxFailedAccessAttempts { get; set; }

    public int ZxcvbnDefaultScore { get; set; }

    public int ResendEmailTokenMinutes { get; set; }

    public int TokenLifespanMinutes { get; set; }

    public int LockoutTokenLifespanMinutes { get; set; }

    public string[] Roles { get; set; } = Array.Empty<string>();

    public IdentitySettingsCrypto Crypto { get; set; } = new();

    public IdentitySettingsKeyPhoneCode PhoneCode { get; set; } = new();
}
